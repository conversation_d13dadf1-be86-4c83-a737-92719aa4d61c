import { defineConfig } from 'vitepress'

export default defineConfig({
  title: '教师备授课平台',

  // GitLab Pages 部署路径配置
  base: '/teacher/fe/teacher-workbench/',

  // 开发服务器配置
  vite: {
    server: {
      port: 6173,
      host: '0.0.0.0'
    }
  },

  // 主题配置
  themeConfig: {
    siteTitle: '教师备授课平台',
    logo: '/assets/logo.png',

    nav: [
      { text: '首页', link: '/' },
      { text: '组件', link: '/components/icons' }
    ],

    sidebar: {
      '/components/': [
        {
          text: '组件文档',
          items: [
            { text: '图标组件', link: '/components/icons' }
          ]
        }
      ]
    },

    footer: {
      message: '基于 MIT 许可发布',
      copyright: 'Copyright © 2025 洋葱学园'
    }
  }
})
