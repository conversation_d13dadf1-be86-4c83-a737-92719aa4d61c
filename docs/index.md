# 教师备授课平台

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI 组件**: Naive UI + Onion UI Web
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite
- **样式**: SCSS + UnoCSS

## 快速开始

### 必需环境
- **Node.js**: >= 16.0.0
- **pnpm**: >= 8.0.0 (推荐) 或 npm >= 8.0.0

### 1. 克隆项目

```bash
git clone https://gitlab.yc345.tv/teacher/fe/teacher-workbench.git
cd teacher-workbench
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 启动开发服务器

```bash
# 启动项目主应用
pnpm run dev

# 启动文档网站
pnpm run docs:dev
```

### 4. 访问应用

- **主应用**: 默认端口：5173
- **文档网站**: 端口：6173

## 项目结构详解

```
teacher-workbench/
├── docs/                    # VitePress文档
├── scripts/                # 构建脚本
├── src/                    # 源代码
│   ├── assets/             # 静态资源
│   │   ├── svg/            # SVG图标
│   │   └── logo.png        # Logo
│   ├── components/         # 公共组件
│   ├── pages/              # 页面组件
│   ├── store/              # Pinia状态管理
│   ├── routers/            # 路由配置
│   ├── utils/              # 工具函数
│   ├── hooks/              # 自定义Hooks
│   ├── styles/             # 全局样式
│   ├── types/              # TypeScript类型
│   └── service/            # API服务
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
├── tsconfig.json           # TypeScript配置
└── README.md               # 项目说明
```

## 开发工作流

### 1. 创建功能分支

```bash
# 新功能
git checkout -b feature/your-feature-name  
# 修复问题  
git checkout -b fix/your-feature-name 
```

### 2. 开发和测试

```bash
# 启动开发服务器
pnpm run dev

# 运行代码检查
pnpm run lint

# 修复代码格式
pnpm run lint-fix
```

### 3. 构建和部署

#### 测试环境构建

```bash
pnpm run build:test
```

#### 预发布环境构建

```bash
pnpm run build:stage
```

#### 生产环境构建

```bash
pnpm run build
```

#### 文档网站构建

注意：文档构建及预览需要切换node版本，18及以上

```bash
# 构建文档网站
pnpm run docs:build

# 预览构建结果
pnpm run docs:preview
```

## 代码规范

[前端vue3项目开发实践](https://guanghe.feishu.cn/wiki/NUKFw2RkSiRiYtk7fqOcQZzgn1g)

### 命名规范
- **组件名**: PascalCase (如: `UserProfile.vue`)
- **文件名**: kebab-case (如: `user-profile.vue`)
- **变量名**: camelCase (如: `userName`)
- **常量名**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)

### 目录规范
- **页面组件**: 放在 `src/pages/` 目录
- **公共组件**: 放在 `src/components/` 目录
- **工具函数**: 放在 `src/utils/` 目录
- **类型定义**: 放在 `src/types/` 目录

### SVG图标命名规范

为了保持图标库的一致性和可维护性，请遵循以下SVG图标命名规范：

#### 基本规则
- **格式**: 使用 `kebab-case` 命名（小写字母，单词间用连字符分隔）
- **扩展名**: 统一使用 `.svg` 扩展名
- **语言**: 使用英文命名，避免中文或拼音
- **不要以icon/Icon结尾**: 复制图标时的组件名会自动添加`Icon`后缀，避免重复

#### 命名结构
```
[功能/类型]-[具体描述]-[状态/变体].svg
```

#### 注意事项
1. **避免重复**: 添加新图标前先检查是否已存在类似图标
2. **语义明确**: 图标名称应能清楚表达其用途
3. **保持简洁**: 避免过长的名称，优先使用常见英文单词
4. **版本管理**: 如需更新图标，考虑保留旧版本或使用版本号后缀

#### 使用方式

**推荐流程**：
1. **优先使用已有图标**: 访问 [图标组件文档](/components/icons) 查看所有可用图标
2. **一键复制代码**: 在图标文档中找到需要的图标，点击"复制"按钮获取引入代码
3. **避免重复添加**: 新增图标前务必确认不存在类似图标

**已有图标使用示例**：
```typescript
// 从图标文档页面复制的代码示例
import AddIcon from '~icons/yc/add'
```

**如确需新增图标**：
1. 将SVG文件放入 `src/assets/svg/` 目录
2. 运行 `pnpm run docs:dev` 启动文档预览
3. 访问 组件 -> 图标组件 查看新图标
4. 从文档页面复制正确的引入代码

> ⚠️ **注意**: 尽量避免新增图标，优先复用现有图标库中的117个图标

### Git提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

**类型说明**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 常见问题

### Q: 如何使用项目中的图标？
A: 访问 [图标组件文档](/components/icons)，找到需要的图标后点击"复制"按钮，直接获取引入代码。

### Q: 如何添加新的图标？
A:
1. **首先确认**: 检查现有117个图标是否满足需求
2. **添加文件**: 将SVG文件放入 `src/assets/svg/` 目录
3. **预览效果**: 运行 `pnpm run docs:dev`，访问 [图标组件文档](/components/icons)
4. **复制代码**: 从文档页面复制正确的引入代码

### Q: 如何更新文档？
A: 编辑 `docs/` 目录下的Markdown文件，然后运行 `pnpm run docs:dev` 预览效果。
