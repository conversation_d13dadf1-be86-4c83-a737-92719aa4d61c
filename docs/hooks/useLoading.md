# useLoading

## 概述

`useLoading` 是一个用于管理加载状态的 Hook，提供了简单易用的加载状态管理功能。

## 基本用法

```typescript
import { useLoading } from '@/hooks/useLoading'

export default {
  setup() {
    const { loading, setLoading, withLoading } = useLoading()
    
    const fetchData = async () => {
      setLoading(true)
      try {
        const data = await api.getData()
        // 处理数据
      } finally {
        setLoading(false)
      }
    }
    
    return {
      loading,
      fetchData
    }
  }
}
```

## 使用 withLoading 简化异步操作

```typescript
import { useLoading } from '@/hooks/useLoading'

export default {
  setup() {
    const { loading, withLoading } = useLoading()
    
    const fetchData = withLoading(async () => {
      const data = await api.getData()
      return data
    })
    
    return {
      loading,
      fetchData
    }
  }
}
```

## API

### 返回值

| 名称 | 类型 | 说明 |
|------|------|------|
| loading | `Ref<boolean>` | 当前加载状态 |
| setLoading | `(value: boolean) => void` | 设置加载状态 |
| withLoading | `<T>(fn: () => Promise<T>) => Promise<T>` | 包装异步函数，自动管理加载状态 |

### 参数

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| initialValue | 初始加载状态 | `boolean` | `false` |

## 完整示例

```vue
<template>
  <div>
    <n-button @click="handleSubmit" :loading="loading">
      提交
    </n-button>
    
    <n-spin :show="loading">
      <div v-if="data">
        {{ data }}
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useLoading } from '@/hooks/useLoading'
import { api } from '@/service'

const data = ref(null)
const { loading, withLoading } = useLoading()

const handleSubmit = withLoading(async () => {
  const result = await api.submitForm({
    // 表单数据
  })
  data.value = result
})
</script>
```

## 注意事项

1. `withLoading` 会自动处理异常情况，确保加载状态正确重置
2. 可以同时使用多个 `useLoading` 实例管理不同的加载状态
3. 建议在组件级别使用，避免全局状态污染

## 更新日志

### v1.0.0
- 初始版本，支持基本的加载状态管理
