# 文档建设指南

## 概述

本项目采用就近维护的文档策略，组件和 Hook 的文档直接放在对应的源码目录中，通过构建脚本自动同步到 VitePress 文档网站。

## 文档组织结构

```
src/
├── components/
│   ├── BackTop/
│   │   ├── BackTop.vue
│   │   └── README.md          # 组件文档
│   ├── ShareModal/
│   │   ├── ShareModal.vue
│   │   ├── useShare.ts
│   │   └── README.md          # 组件文档
├── hooks/
│   ├── useAuth.ts
│   ├── useAuth.md             # Hook 文档
│   ├── useLoading/
│   │   ├── useLoading.ts
│   │   └── README.md          # Hook 文档（目录形式）
```

## 快速开始

### 1. 生成文档模板

使用脚本快速生成标准化的文档模板：

```bash
# 为组件生成文档模板
npm run docs:template component MyComponent

# 为 Hook 生成文档模板
npm run docs:template hook useMyHook
```

### 2. 编写文档内容

编辑生成的 README.md 或 .md 文件，填写具体的文档内容。

### 3. 预览文档效果

```bash
# 启动文档开发服务器
npm run docs:dev

# 访问 http://localhost:6173 查看效果
```

## 文档规范

### 组件文档结构

```markdown
# 组件名称

## 概述
组件的功能描述和使用场景

## 基本用法
基础的使用示例

## Props
属性列表和说明

## Events
事件列表和说明

## Slots
插槽列表和说明

## 样式定制
CSS 变量和样式定制方法

## 注意事项
使用注意事项和最佳实践

## 更新日志
版本更新记录
```

### Hook 文档结构

```markdown
# Hook 名称

## 概述
Hook 的功能描述和使用场景

## 基本用法
基础的使用示例

## API
参数和返回值说明

## 完整示例
完整的使用示例

## 注意事项
使用注意事项和最佳实践

## 更新日志
版本更新记录
```

## 自动化工具

### 文档同步

文档会在以下时机自动同步到 VitePress：

1. 运行 `npm run docs:dev` 时
2. 运行 `npm run docs:build` 时
3. 运行 `npm run copy-assets` 时

### 文档监控

启动文档监控，当源码目录中的文档文件发生变化时自动重新生成：

```bash
npm run docs:watch
```

### 文档检查

在提交代码前检查文档同步状态：

```bash
node scripts/check-docs-sync.js
```

## 最佳实践

### 1. 文档与代码同步更新

- 新增组件或 Hook 时，同时创建对应的文档
- 修改 API 时，及时更新文档说明
- 使用文档模板保持格式一致性

### 2. 示例代码质量

- 提供完整可运行的示例
- 示例代码要简洁明了
- 包含常见使用场景

### 3. 文档维护

- 定期检查文档的准确性
- 及时更新过时的信息
- 收集用户反馈改进文档

## 工作流程

### 开发新组件

1. 创建组件文件
2. 生成文档模板：`npm run docs:template component ComponentName`
3. 编写文档内容
4. 预览文档效果：`npm run docs:dev`
5. 提交代码

### 开发新 Hook

1. 创建 Hook 文件
2. 生成文档模板：`npm run docs:template hook useHookName`
3. 编写文档内容
4. 预览文档效果：`npm run docs:dev`
5. 提交代码

### 更新现有文档

1. 直接编辑源码目录中的文档文件
2. 如果启用了文档监控，会自动重新生成
3. 否则手动运行：`npm run copy-assets`
4. 预览效果：`npm run docs:dev`

## 常见问题

### Q: 为什么选择就近维护而不是集中管理？

A: 就近维护有以下优势：
- 文档与代码在同一位置，便于维护
- 开发时可以直接查看文档
- 减少文档与代码不同步的风险
- 通过构建脚本保持 VitePress 的统一性

### Q: 如何处理复杂组件的文档？

A: 对于复杂组件：
- 可以创建多个 .md 文件分别描述不同方面
- 在主 README.md 中引用其他文档
- 使用 VitePress 的链接功能组织文档结构

### Q: 文档更新后如何通知团队？

A: 建议：
- 在 Git 提交信息中说明文档变更
- 在团队沟通中分享重要的文档更新
- 定期组织文档 Review 会议
