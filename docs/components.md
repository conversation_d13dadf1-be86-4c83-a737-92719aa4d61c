# 组件文档

## 基础组件

- [BackTop 回到顶部](/components/BackTop) - 提供回到页面顶部的功能按钮
- [Loading 加载组件](/components/Loading) - 自定义的波浪式加载动画组件
- [ParseText 文本解析](/components/ParseText) - 用于解析和渲染 HTML 文本内容

## 表单组件

- [CsvSelect 学科选择器](/components/CsvSelect) - 级联选择器，用于选择学科、学段、教材版本和学期
- [CsvSelectSpecial 培优课选择器](/components/CsvSelectSpecial) - 专门用于选择培优课程的三级级联选择器
- [FilterItem 筛选项容器](/components/FilterItem) - 筛选项的容器组件，提供标签和内容的标准化布局
- [FilterSelect 筛选选择器](/components/FilterSelects) - 标签式的筛选选择器组件

## 业务组件

- [ProblemSingle 题目组件](/components/ProblemSingle) - 功能完整的题目展示组件，支持收藏、课堂模式、AI讲解等
- [VideoPlayer 视频播放器](/components/VideoPlayer) - 功能完整的视频播放器，支持交互、字幕、关键点等高级特性
- [ShareModal 分享弹窗](/components/ShareModal) - 资源分享弹窗，支持生成分享链接、设置有效期、密码保护等
- [PaperChangeModal 试卷配置弹窗](/components/PaperChangeModal) - 试卷下载和备课配置的弹窗组件
- [TeacherTree 教师树形组件](/components/TeacherTree) - 教师资源树形展示组件，支持懒加载、搜索、选择等功能
- [ClassRoomMode 课堂模式](/components/ClassRoomMode) - 课堂展示模式组件，支持全屏显示、字体放大、高对比度等

## 布局组件

- [SideTools 侧边工具栏](/components/SideTools) - 侧边工具栏组件，提供常用的工具按钮和快捷操作
- [SideResource 侧边资源面板](/components/SideResource) - 侧边资源面板组件，用于展示和管理各类教学资源

## 图标组件

- [图标组件](/components/icons) - 项目中使用的所有图标组件

> 📅 最后更新时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
>
> 🔄 此页面包含了 src/components 目录下所有基础组件的完整文档
