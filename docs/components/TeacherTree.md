# TeacherTree 教师树形组件

## 概述

TeacherTree 是一个教师资源树形展示组件，用于显示教师的课程、章节、知识点等层级结构。支持懒加载、搜索、选择等功能。

## 基本用法

```vue
<template>
  <div>
    <TeacherTree 
      :treeData="treeData"
      @select="handleSelect"
      @load="handleLoad"
    />
  </div>
</template>

<script setup>
import TeacherTree from '@/components/TeacherTree/index.vue'

const treeData = ref([])

const handleSelect = (selectedKeys, info) => {
  console.log('选中节点:', selectedKeys, info)
}

const handleLoad = async (treeNode) => {
  // 懒加载子节点
  const children = await loadChildren(treeNode.key)
  return children
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| treeData | 树形数据 | `TreeNode[]` | `[]` |
| expandedKeys | 展开的节点 | `string[]` | `[]` |
| selectedKeys | 选中的节点 | `string[]` | `[]` |
| loadData | 懒加载函数 | `Function` | - |
| showSearch | 是否显示搜索框 | `boolean` | `false` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| select | 选择节点时触发 | `(selectedKeys, info) => void` |
| expand | 展开节点时触发 | `(expandedKeys, info) => void` |
| load | 懒加载时触发 | `(treeNode) => Promise<TreeNode[]>` |

## 使用场景

### 1. 课程章节树

```vue
<template>
  <TeacherTree 
    :treeData="courseTree"
    :showSearch="true"
    @select="handleChapterSelect"
  />
</template>

<script setup>
const courseTree = ref([
  {
    title: '第一章 函数',
    key: 'chapter-1',
    children: [
      { title: '1.1 函数的概念', key: 'section-1-1' },
      { title: '1.2 函数的性质', key: 'section-1-2' }
    ]
  }
])
</script>
```

### 2. 知识点树

```vue
<template>
  <TeacherTree 
    :treeData="knowledgeTree"
    :loadData="loadKnowledgePoints"
    @select="handleKnowledgeSelect"
  />
</template>

<script setup>
const loadKnowledgePoints = async (node) => {
  const response = await api.getKnowledgePoints(node.key)
  return response.data
}
</script>
```

## 更新日志

### v1.0.0
- 初始版本，支持基本的树形展示功能
- 支持懒加载和搜索功能
