# 图标资源
  
## 使用方式
### 通过unplugin-icons使用
项目已配置了`unplugin-icons`插件，可以直接使用自定义图标：

```typescript
// 在组件中自动导入
import IconAdd from '~icons/yc/add'
import IconEdit from '~icons/yc/edit'
```

## SVG图标库
### 基础操作图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="add-blue-new" data-filename="add-blue-new.svg">
    <div class="icon-preview">
      <img src="/assets/svg/add-blue-new.svg" alt="add-blue-new" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">add-blue-new.svg</span>
      <button class="copy-btn" data-copy-content="import AddBlueNewIcon from '~icons/yc/add-blue-new'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="add-blue" data-filename="add-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/add-blue.svg" alt="add-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">add-blue.svg</span>
      <button class="copy-btn" data-copy-content="import AddBlueIcon from '~icons/yc/add-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="add" data-filename="add.svg">
    <div class="icon-preview">
      <img src="/assets/svg/add.svg" alt="add" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">add.svg</span>
      <button class="copy-btn" data-copy-content="import AddIcon from '~icons/yc/add'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="check" data-filename="check.svg">
    <div class="icon-preview">
      <img src="/assets/svg/check.svg" alt="check" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">check.svg</span>
      <button class="copy-btn" data-copy-content="import CheckIcon from '~icons/yc/check'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="checked" data-filename="checked.svg">
    <div class="icon-preview">
      <img src="/assets/svg/checked.svg" alt="checked" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">checked.svg</span>
      <button class="copy-btn" data-copy-content="import CheckedIcon from '~icons/yc/checked'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="close-round-gray" data-filename="close-round-gray.svg">
    <div class="icon-preview">
      <img src="/assets/svg/close-round-gray.svg" alt="close-round-gray" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">close-round-gray.svg</span>
      <button class="copy-btn" data-copy-content="import CloseRoundGrayIcon from '~icons/yc/close-round-gray'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="close-round" data-filename="close-round.svg">
    <div class="icon-preview">
      <img src="/assets/svg/close-round.svg" alt="close-round" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">close-round.svg</span>
      <button class="copy-btn" data-copy-content="import CloseRoundIcon from '~icons/yc/close-round'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="close" data-filename="close.svg">
    <div class="icon-preview">
      <img src="/assets/svg/close.svg" alt="close" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">close.svg</span>
      <button class="copy-btn" data-copy-content="import CloseIcon from '~icons/yc/close'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="copy-blue" data-filename="copy-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/copy-blue.svg" alt="copy-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">copy-blue.svg</span>
      <button class="copy-btn" data-copy-content="import CopyBlueIcon from '~icons/yc/copy-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="copy" data-filename="copy.svg">
    <div class="icon-preview">
      <img src="/assets/svg/copy.svg" alt="copy" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">copy.svg</span>
      <button class="copy-btn" data-copy-content="import CopyIcon from '~icons/yc/copy'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="copyText" data-filename="copyText.svg">
    <div class="icon-preview">
      <img src="/assets/svg/copyText.svg" alt="copyText" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">copyText.svg</span>
      <button class="copy-btn" data-copy-content="import CopyTextIcon from '~icons/yc/copyText'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="delete" data-filename="delete.svg">
    <div class="icon-preview">
      <img src="/assets/svg/delete.svg" alt="delete" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">delete.svg</span>
      <button class="copy-btn" data-copy-content="import DeleteIcon from '~icons/yc/delete'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="edit" data-filename="edit.svg">
    <div class="icon-preview">
      <img src="/assets/svg/edit.svg" alt="edit" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">edit.svg</span>
      <button class="copy-btn" data-copy-content="import EditIcon from '~icons/yc/edit'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="editIcon" data-filename="editIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/editIcon.svg" alt="editIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">editIcon.svg</span>
      <button class="copy-btn" data-copy-content="import EditIconComponent from '~icons/yc/editIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="eye-close" data-filename="eye-close.svg">
    <div class="icon-preview">
      <img src="/assets/svg/eye-close.svg" alt="eye-close" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">eye-close.svg</span>
      <button class="copy-btn" data-copy-content="import EyeCloseIcon from '~icons/yc/eye-close'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="folder-add" data-filename="folder-add.svg">
    <div class="icon-preview">
      <img src="/assets/svg/folder-add.svg" alt="folder-add" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">folder-add.svg</span>
      <button class="copy-btn" data-copy-content="import FolderAddIcon from '~icons/yc/folder-add'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="minus" data-filename="minus.svg">
    <div class="icon-preview">
      <img src="/assets/svg/minus.svg" alt="minus" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">minus.svg</span>
      <button class="copy-btn" data-copy-content="import MinusIcon from '~icons/yc/minus'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="search" data-filename="search.svg">
    <div class="icon-preview">
      <img src="/assets/svg/search.svg" alt="search" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">search.svg</span>
      <button class="copy-btn" data-copy-content="import SearchIcon from '~icons/yc/search'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="self-add" data-filename="self-add.svg">
    <div class="icon-preview">
      <img src="/assets/svg/self-add.svg" alt="self-add" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">self-add.svg</span>
      <button class="copy-btn" data-copy-content="import SelfAddIcon from '~icons/yc/self-add'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="un-check" data-filename="un-check.svg">
    <div class="icon-preview">
      <img src="/assets/svg/un-check.svg" alt="un-check" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">un-check.svg</span>
      <button class="copy-btn" data-copy-content="import UnCheckIcon from '~icons/yc/un-check'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 文件类型图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="PPTIcon" data-filename="PPTIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/PPTIcon.svg" alt="PPTIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">PPTIcon.svg</span>
      <button class="copy-btn" data-copy-content="import PPTIcon from '~icons/yc/PPTIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="PPTXIcon-type" data-filename="PPTXIcon-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/PPTXIcon-type.svg" alt="PPTXIcon-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">PPTXIcon-type.svg</span>
      <button class="copy-btn" data-copy-content="import PPTXIconTypeIcon from '~icons/yc/PPTXIcon-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="PPTXIcon" data-filename="PPTXIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/PPTXIcon.svg" alt="PPTXIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">PPTXIcon.svg</span>
      <button class="copy-btn" data-copy-content="import PPTXIcon from '~icons/yc/PPTXIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="WordIcon" data-filename="WordIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/WordIcon.svg" alt="WordIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">WordIcon.svg</span>
      <button class="copy-btn" data-copy-content="import WordIcon from '~icons/yc/WordIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="exam-paper" data-filename="exam-paper.svg">
    <div class="icon-preview">
      <img src="/assets/svg/exam-paper.svg" alt="exam-paper" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">exam-paper.svg</span>
      <button class="copy-btn" data-copy-content="import ExamPaperIcon from '~icons/yc/exam-paper'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="file" data-filename="file.svg">
    <div class="icon-preview">
      <img src="/assets/svg/file.svg" alt="file" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">file.svg</span>
      <button class="copy-btn" data-copy-content="import FileIcon from '~icons/yc/file'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="folder" data-filename="folder.svg">
    <div class="icon-preview">
      <img src="/assets/svg/folder.svg" alt="folder" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">folder.svg</span>
      <button class="copy-btn" data-copy-content="import FolderIcon from '~icons/yc/folder'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="folderIcon" data-filename="folderIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/folderIcon.svg" alt="folderIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">folderIcon.svg</span>
      <button class="copy-btn" data-copy-content="import FolderIconComponent from '~icons/yc/folderIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="insertText" data-filename="insertText.svg">
    <div class="icon-preview">
      <img src="/assets/svg/insertText.svg" alt="insertText" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">insertText.svg</span>
      <button class="copy-btn" data-copy-content="import InsertTextIcon from '~icons/yc/insertText'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="paper" data-filename="paper.svg">
    <div class="icon-preview">
      <img src="/assets/svg/paper.svg" alt="paper" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">paper.svg</span>
      <button class="copy-btn" data-copy-content="import PaperIcon from '~icons/yc/paper'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="pptPlayIcon" data-filename="pptPlayIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/pptPlayIcon.svg" alt="pptPlayIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">pptPlayIcon.svg</span>
      <button class="copy-btn" data-copy-content="import PptPlayIcon from '~icons/yc/pptPlayIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="text-icon" data-filename="text-icon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/text-icon.svg" alt="text-icon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">text-icon.svg</span>
      <button class="copy-btn" data-copy-content="import TextIcon from '~icons/yc/text-icon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### AI功能图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="ai-1" data-filename="ai-1.svg">
    <div class="icon-preview">
      <img src="/assets/svg/ai-1.svg" alt="ai-1" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">ai-1.svg</span>
      <button class="copy-btn" data-copy-content="import Ai1Icon from '~icons/yc/ai-1'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="ai-pencil" data-filename="ai-pencil.svg">
    <div class="icon-preview">
      <img src="/assets/svg/ai-pencil.svg" alt="ai-pencil" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">ai-pencil.svg</span>
      <button class="copy-btn" data-copy-content="import AiPencilIcon from '~icons/yc/ai-pencil'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="ai-qa" data-filename="ai-qa.svg">
    <div class="icon-preview">
      <img src="/assets/svg/ai-qa.svg" alt="ai-qa" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">ai-qa.svg</span>
      <button class="copy-btn" data-copy-content="import AiQaIcon from '~icons/yc/ai-qa'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="ai-star" data-filename="ai-star.svg">
    <div class="icon-preview">
      <img src="/assets/svg/ai-star.svg" alt="ai-star" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">ai-star.svg</span>
      <button class="copy-btn" data-copy-content="import AiStarIcon from '~icons/yc/ai-star'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="ai" data-filename="ai.svg">
    <div class="icon-preview">
      <img src="/assets/svg/ai.svg" alt="ai" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">ai.svg</span>
      <button class="copy-btn" data-copy-content="import AiIcon from '~icons/yc/ai'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-ai-blue" data-filename="side-ai-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-ai-blue.svg" alt="side-ai-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-ai-blue.svg</span>
      <button class="copy-btn" data-copy-content="import SideAiBlueIcon from '~icons/yc/side-ai-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-ai" data-filename="side-ai.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-ai.svg" alt="side-ai" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-ai.svg</span>
      <button class="copy-btn" data-copy-content="import SideAiIcon from '~icons/yc/side-ai'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 教学相关图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="class-blue" data-filename="class-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/class-blue.svg" alt="class-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">class-blue.svg</span>
      <button class="copy-btn" data-copy-content="import ClassBlueIcon from '~icons/yc/class-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="class-gray" data-filename="class-gray.svg">
    <div class="icon-preview">
      <img src="/assets/svg/class-gray.svg" alt="class-gray" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">class-gray.svg</span>
      <button class="copy-btn" data-copy-content="import ClassGrayIcon from '~icons/yc/class-gray'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="exercise-type" data-filename="exercise-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/exercise-type.svg" alt="exercise-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">exercise-type.svg</span>
      <button class="copy-btn" data-copy-content="import ExerciseTypeIcon from '~icons/yc/exercise-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="problem-type" data-filename="problem-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/problem-type.svg" alt="problem-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">problem-type.svg</span>
      <button class="copy-btn" data-copy-content="import ProblemTypeIcon from '~icons/yc/problem-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="question-blue" data-filename="question-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/question-blue.svg" alt="question-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">question-blue.svg</span>
      <button class="copy-btn" data-copy-content="import QuestionBlueIcon from '~icons/yc/question-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="question-color" data-filename="question-color.svg">
    <div class="icon-preview">
      <img src="/assets/svg/question-color.svg" alt="question-color" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">question-color.svg</span>
      <button class="copy-btn" data-copy-content="import QuestionColorIcon from '~icons/yc/question-color'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="question" data-filename="question.svg">
    <div class="icon-preview">
      <img src="/assets/svg/question.svg" alt="question" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">question.svg</span>
      <button class="copy-btn" data-copy-content="import QuestionIcon from '~icons/yc/question'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-problem-blue" data-filename="side-problem-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-problem-blue.svg" alt="side-problem-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-problem-blue.svg</span>
      <button class="copy-btn" data-copy-content="import SideProblemBlueIcon from '~icons/yc/side-problem-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-problem" data-filename="side-problem.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-problem.svg" alt="side-problem" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-problem.svg</span>
      <button class="copy-btn" data-copy-content="import SideProblemIcon from '~icons/yc/side-problem'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="task-expand" data-filename="task-expand.svg">
    <div class="icon-preview">
      <img src="/assets/svg/task-expand.svg" alt="task-expand" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">task-expand.svg</span>
      <button class="copy-btn" data-copy-content="import TaskExpandIcon from '~icons/yc/task-expand'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="task-problem" data-filename="task-problem.svg">
    <div class="icon-preview">
      <img src="/assets/svg/task-problem.svg" alt="task-problem" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">task-problem.svg</span>
      <button class="copy-btn" data-copy-content="import TaskProblemIcon from '~icons/yc/task-problem'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="task-video" data-filename="task-video.svg">
    <div class="icon-preview">
      <img src="/assets/svg/task-video.svg" alt="task-video" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">task-video.svg</span>
      <button class="copy-btn" data-copy-content="import TaskVideoIcon from '~icons/yc/task-video'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="wrong-book-type" data-filename="wrong-book-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/wrong-book-type.svg" alt="wrong-book-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">wrong-book-type.svg</span>
      <button class="copy-btn" data-copy-content="import WrongBookTypeIcon from '~icons/yc/wrong-book-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="wrong" data-filename="wrong.svg">
    <div class="icon-preview">
      <img src="/assets/svg/wrong.svg" alt="wrong" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">wrong.svg</span>
      <button class="copy-btn" data-copy-content="import WrongIcon from '~icons/yc/wrong'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 视频播放图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="play-black" data-filename="play-black.svg">
    <div class="icon-preview">
      <img src="/assets/svg/play-black.svg" alt="play-black" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">play-black.svg</span>
      <button class="copy-btn" data-copy-content="import PlayBlackIcon from '~icons/yc/play-black'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="play-white" data-filename="play-white.svg">
    <div class="icon-preview">
      <img src="/assets/svg/play-white.svg" alt="play-white" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">play-white.svg</span>
      <button class="copy-btn" data-copy-content="import PlayWhiteIcon from '~icons/yc/play-white'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="play" data-filename="play.svg">
    <div class="icon-preview">
      <img src="/assets/svg/play.svg" alt="play" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">play.svg</span>
      <button class="copy-btn" data-copy-content="import PlayIcon from '~icons/yc/play'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-video-blue" data-filename="side-video-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-video-blue.svg" alt="side-video-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-video-blue.svg</span>
      <button class="copy-btn" data-copy-content="import SideVideoBlueIcon from '~icons/yc/side-video-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-video" data-filename="side-video.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-video.svg" alt="side-video" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-video.svg</span>
      <button class="copy-btn" data-copy-content="import SideVideoIcon from '~icons/yc/side-video'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="video-type" data-filename="video-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/video-type.svg" alt="video-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">video-type.svg</span>
      <button class="copy-btn" data-copy-content="import VideoTypeIcon from '~icons/yc/video-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 箭头导航图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="arrow-bind" data-filename="arrow-bind.svg">
    <div class="icon-preview">
      <img src="/assets/arrow-bind.svg" alt="arrow-bind" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">arrow-bind.svg</span>
      <button class="copy-btn" data-copy-content="import ArrowBindIcon from '~icons/yc/arrow-bind'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="arrow-up-two" data-filename="arrow-up-two.svg">
    <div class="icon-preview">
      <img src="/assets/svg/arrow-up-two.svg" alt="arrow-up-two" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">arrow-up-two.svg</span>
      <button class="copy-btn" data-copy-content="import ArrowUpTwoIcon from '~icons/yc/arrow-up-two'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="arrow-up" data-filename="arrow-up.svg">
    <div class="icon-preview">
      <img src="/assets/svg/arrow-up.svg" alt="arrow-up" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">arrow-up.svg</span>
      <button class="copy-btn" data-copy-content="import ArrowUpIcon from '~icons/yc/arrow-up'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="arrow" data-filename="arrow.svg">
    <div class="icon-preview">
      <img src="/assets/svg/arrow.svg" alt="arrow" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">arrow.svg</span>
      <button class="copy-btn" data-copy-content="import ArrowIcon from '~icons/yc/arrow'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="arrowIcon" data-filename="arrowIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/arrowIcon.svg" alt="arrowIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">arrowIcon.svg</span>
      <button class="copy-btn" data-copy-content="import ArrowIconComponent from '~icons/yc/arrowIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="back-top" data-filename="back-top.svg">
    <div class="icon-preview">
      <img src="/assets/svg/back-top.svg" alt="back-top" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">back-top.svg</span>
      <button class="copy-btn" data-copy-content="import BackTopIcon from '~icons/yc/back-top'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="back" data-filename="back.svg">
    <div class="icon-preview">
      <img src="/assets/svg/back.svg" alt="back" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">back.svg</span>
      <button class="copy-btn" data-copy-content="import BackIcon from '~icons/yc/back'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="conner-arrow-left" data-filename="conner-arrow-left.svg">
    <div class="icon-preview">
      <img src="/assets/svg/conner-arrow-left.svg" alt="conner-arrow-left" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">conner-arrow-left.svg</span>
      <button class="copy-btn" data-copy-content="import ConnerArrowLeftIcon from '~icons/yc/conner-arrow-left'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="dropdown" data-filename="dropdown.svg">
    <div class="icon-preview">
      <img src="/assets/svg/dropdown.svg" alt="dropdown" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">dropdown.svg</span>
      <button class="copy-btn" data-copy-content="import DropdownIcon from '~icons/yc/dropdown'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="menu-back" data-filename="menu-back.svg">
    <div class="icon-preview">
      <img src="/assets/svg/menu-back.svg" alt="menu-back" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">menu-back.svg</span>
      <button class="copy-btn" data-copy-content="import MenuBackIcon from '~icons/yc/menu-back'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="old-back" data-filename="old-back.svg">
    <div class="icon-preview">
      <img src="/assets/svg/old-back.svg" alt="old-back" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">old-back.svg</span>
      <button class="copy-btn" data-copy-content="import OldBackIcon from '~icons/yc/old-back'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="right-arrow" data-filename="right-arrow.svg">
    <div class="icon-preview">
      <img src="/assets/svg/right-arrow.svg" alt="right-arrow" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">right-arrow.svg</span>
      <button class="copy-btn" data-copy-content="import RightArrowIcon from '~icons/yc/right-arrow'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="sort-arrow-up" data-filename="sort-arrow-up.svg">
    <div class="icon-preview">
      <img src="/assets/svg/sort-arrow-up.svg" alt="sort-arrow-up" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">sort-arrow-up.svg</span>
      <button class="copy-btn" data-copy-content="import SortArrowUpIcon from '~icons/yc/sort-arrow-up'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 状态提示图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="correct" data-filename="correct.svg">
    <div class="icon-preview">
      <img src="/assets/svg/correct.svg" alt="correct" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">correct.svg</span>
      <button class="copy-btn" data-copy-content="import CorrectIcon from '~icons/yc/correct'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="correctAnswer" data-filename="correctAnswer.svg">
    <div class="icon-preview">
      <img src="/assets/svg/correctAnswer.svg" alt="correctAnswer" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">correctAnswer.svg</span>
      <button class="copy-btn" data-copy-content="import CorrectAnswerIcon from '~icons/yc/correctAnswer'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="correctIcon" data-filename="correctIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/correctIcon.svg" alt="correctIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">correctIcon.svg</span>
      <button class="copy-btn" data-copy-content="import CorrectIconComponent from '~icons/yc/correctIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="emptyIcon" data-filename="emptyIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/emptyIcon.svg" alt="emptyIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">emptyIcon.svg</span>
      <button class="copy-btn" data-copy-content="import EmptyIcon from '~icons/yc/emptyIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="info-round" data-filename="info-round.svg">
    <div class="icon-preview">
      <img src="/assets/svg/info-round.svg" alt="info-round" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">info-round.svg</span>
      <button class="copy-btn" data-copy-content="import InfoRoundIcon from '~icons/yc/info-round'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="success" data-filename="success.svg">
    <div class="icon-preview">
      <img src="/assets/svg/success.svg" alt="success" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">success.svg</span>
      <button class="copy-btn" data-copy-content="import SuccessIcon from '~icons/yc/success'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="tip-blue" data-filename="tip-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/tip-blue.svg" alt="tip-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">tip-blue.svg</span>
      <button class="copy-btn" data-copy-content="import TipBlueIcon from '~icons/yc/tip-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="tip-hover" data-filename="tip-hover.svg">
    <div class="icon-preview">
      <img src="/assets/svg/tip-hover.svg" alt="tip-hover" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">tip-hover.svg</span>
      <button class="copy-btn" data-copy-content="import TipHoverIcon from '~icons/yc/tip-hover'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="tip" data-filename="tip.svg">
    <div class="icon-preview">
      <img src="/assets/svg/tip.svg" alt="tip" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">tip.svg</span>
      <button class="copy-btn" data-copy-content="import TipIcon from '~icons/yc/tip'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="warning" data-filename="warning.svg">
    <div class="icon-preview">
      <img src="/assets/svg/warning.svg" alt="warning" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">warning.svg</span>
      <button class="copy-btn" data-copy-content="import WarningIcon from '~icons/yc/warning'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 收藏评分图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="collection-type" data-filename="collection-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/collection-type.svg" alt="collection-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">collection-type.svg</span>
      <button class="copy-btn" data-copy-content="import CollectionTypeIcon from '~icons/yc/collection-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="like-active" data-filename="like-active.svg">
    <div class="icon-preview">
      <img src="/assets/svg/like-active.svg" alt="like-active" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">like-active.svg</span>
      <button class="copy-btn" data-copy-content="import LikeActiveIcon from '~icons/yc/like-active'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-collect-blue" data-filename="side-collect-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-collect-blue.svg" alt="side-collect-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-collect-blue.svg</span>
      <button class="copy-btn" data-copy-content="import SideCollectBlueIcon from '~icons/yc/side-collect-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="side-collect" data-filename="side-collect.svg">
    <div class="icon-preview">
      <img src="/assets/svg/side-collect.svg" alt="side-collect" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">side-collect.svg</span>
      <button class="copy-btn" data-copy-content="import SideCollectIcon from '~icons/yc/side-collect'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="star-blank" data-filename="star-blank.svg">
    <div class="icon-preview">
      <img src="/assets/svg/star-blank.svg" alt="star-blank" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">star-blank.svg</span>
      <button class="copy-btn" data-copy-content="import StarBlankIcon from '~icons/yc/star-blank'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="star" data-filename="star.svg">
    <div class="icon-preview">
      <img src="/assets/svg/star.svg" alt="star" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">star.svg</span>
      <button class="copy-btn" data-copy-content="import StarIcon from '~icons/yc/star'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="thumb-up" data-filename="thumb-up.svg">
    <div class="icon-preview">
      <img src="/assets/svg/thumb-up.svg" alt="thumb-up" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">thumb-up.svg</span>
      <button class="copy-btn" data-copy-content="import ThumbUpIcon from '~icons/yc/thumb-up'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="top1" data-filename="top1.svg">
    <div class="icon-preview">
      <img src="/assets/svg/top1.svg" alt="top1" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">top1.svg</span>
      <button class="copy-btn" data-copy-content="import Top1Icon from '~icons/yc/top1'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="top2" data-filename="top2.svg">
    <div class="icon-preview">
      <img src="/assets/svg/top2.svg" alt="top2" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">top2.svg</span>
      <button class="copy-btn" data-copy-content="import Top2Icon from '~icons/yc/top2'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="top3" data-filename="top3.svg">
    <div class="icon-preview">
      <img src="/assets/svg/top3.svg" alt="top3" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">top3.svg</span>
      <button class="copy-btn" data-copy-content="import Top3Icon from '~icons/yc/top3'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 功能操作图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="download" data-filename="download.svg">
    <div class="icon-preview">
      <img src="/assets/svg/download.svg" alt="download" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">download.svg</span>
      <button class="copy-btn" data-copy-content="import DownloadIcon from '~icons/yc/download'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="dragable" data-filename="dragable.svg">
    <div class="icon-preview">
      <img src="/assets/svg/dragable.svg" alt="dragable" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">dragable.svg</span>
      <button class="copy-btn" data-copy-content="import DragableIcon from '~icons/yc/dragable'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="eye" data-filename="eye.svg">
    <div class="icon-preview">
      <img src="/assets/svg/eye.svg" alt="eye" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">eye.svg</span>
      <button class="copy-btn" data-copy-content="import EyeIcon from '~icons/yc/eye'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="look-over" data-filename="look-over.svg">
    <div class="icon-preview">
      <img src="/assets/svg/look-over.svg" alt="look-over" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">look-over.svg</span>
      <button class="copy-btn" data-copy-content="import LookOverIcon from '~icons/yc/look-over'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="redo" data-filename="redo.svg">
    <div class="icon-preview">
      <img src="/assets/svg/redo.svg" alt="redo" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">redo.svg</span>
      <button class="copy-btn" data-copy-content="import RedoIcon from '~icons/yc/redo'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="share-blue" data-filename="share-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/share-blue.svg" alt="share-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">share-blue.svg</span>
      <button class="copy-btn" data-copy-content="import ShareBlueIcon from '~icons/yc/share-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="share" data-filename="share.svg">
    <div class="icon-preview">
      <img src="/assets/svg/share.svg" alt="share" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">share.svg</span>
      <button class="copy-btn" data-copy-content="import ShareIcon from '~icons/yc/share'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="upload-img" data-filename="upload-img.svg">
    <div class="icon-preview">
      <img src="/assets/svg/upload-img.svg" alt="upload-img" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">upload-img.svg</span>
      <button class="copy-btn" data-copy-content="import UploadImgIcon from '~icons/yc/upload-img'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 界面元素图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="breadIcon" data-filename="breadIcon.svg">
    <div class="icon-preview">
      <img src="/assets/svg/breadIcon.svg" alt="breadIcon" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">breadIcon.svg</span>
      <button class="copy-btn" data-copy-content="import BreadIcon from '~icons/yc/breadIcon'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="dot" data-filename="dot.svg">
    <div class="icon-preview">
      <img src="/assets/svg/dot.svg" alt="dot" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">dot.svg</span>
      <button class="copy-btn" data-copy-content="import DotIcon from '~icons/yc/dot'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="list" data-filename="list.svg">
    <div class="icon-preview">
      <img src="/assets/svg/list.svg" alt="list" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">list.svg</span>
      <button class="copy-btn" data-copy-content="import ListIcon from '~icons/yc/list'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="threePoints" data-filename="threePoints.svg">
    <div class="icon-preview">
      <img src="/assets/svg/threePoints.svg" alt="threePoints" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">threePoints.svg</span>
      <button class="copy-btn" data-copy-content="import ThreePointsIcon from '~icons/yc/threePoints'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 特殊功能图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="clip-type" data-filename="clip-type.svg">
    <div class="icon-preview">
      <img src="/assets/svg/clip-type.svg" alt="clip-type" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">clip-type.svg</span>
      <button class="copy-btn" data-copy-content="import ClipTypeIcon from '~icons/yc/clip-type'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="customer" data-filename="customer.svg">
    <div class="icon-preview">
      <img src="/assets/svg/customer.svg" alt="customer" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">customer.svg</span>
      <button class="copy-btn" data-copy-content="import CustomerIcon from '~icons/yc/customer'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="fx" data-filename="fx.svg">
    <div class="icon-preview">
      <img src="/assets/svg/fx.svg" alt="fx" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">fx.svg</span>
      <button class="copy-btn" data-copy-content="import FxIcon from '~icons/yc/fx'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="is-used" data-filename="is-used.svg">
    <div class="icon-preview">
      <img src="/assets/svg/is-used.svg" alt="is-used" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">is-used.svg</span>
      <button class="copy-btn" data-copy-content="import IsUsedIcon from '~icons/yc/is-used'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="roll-call" data-filename="roll-call.svg">
    <div class="icon-preview">
      <img src="/assets/svg/roll-call.svg" alt="roll-call" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">roll-call.svg</span>
      <button class="copy-btn" data-copy-content="import RollCallIcon from '~icons/yc/roll-call'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="vip" data-filename="vip.svg">
    <div class="icon-preview">
      <img src="/assets/svg/vip.svg" alt="vip" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">vip.svg</span>
      <button class="copy-btn" data-copy-content="import VipIcon from '~icons/yc/vip'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="vipRank" data-filename="vipRank.svg">
    <div class="icon-preview">
      <img src="/assets/svg/vipRank.svg" alt="vipRank" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">vipRank.svg</span>
      <button class="copy-btn" data-copy-content="import VipRankIcon from '~icons/yc/vipRank'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>

### 其他图标

<div class="icon-grid">
  <div class="icon-item" data-icon-name="qq" data-filename="qq.svg">
    <div class="icon-preview">
      <img src="/assets/qq.svg" alt="qq" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">qq.svg</span>
      <button class="copy-btn" data-copy-content="import QqIcon from '~icons/yc/qq'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="expried" data-filename="expried.svg">
    <div class="icon-preview">
      <img src="/assets/svg/expried.svg" alt="expried" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">expried.svg</span>
      <button class="copy-btn" data-copy-content="import ExpriedIcon from '~icons/yc/expried'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="floder-blue" data-filename="floder-blue.svg">
    <div class="icon-preview">
      <img src="/assets/svg/floder-blue.svg" alt="floder-blue" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">floder-blue.svg</span>
      <button class="copy-btn" data-copy-content="import FloderBlueIcon from '~icons/yc/floder-blue'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="floder-default" data-filename="floder-default.svg">
    <div class="icon-preview">
      <img src="/assets/svg/floder-default.svg" alt="floder-default" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">floder-default.svg</span>
      <button class="copy-btn" data-copy-content="import FloderDefaultIcon from '~icons/yc/floder-default'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="revisal" data-filename="revisal.svg">
    <div class="icon-preview">
      <img src="/assets/svg/revisal.svg" alt="revisal" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">revisal.svg</span>
      <button class="copy-btn" data-copy-content="import RevisalIcon from '~icons/yc/revisal'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
  <div class="icon-item" data-icon-name="seewo-logo" data-filename="seewo-logo.svg">
    <div class="icon-preview">
      <img src="/assets/svg/seewo-logo.svg" alt="seewo-logo" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">seewo-logo.svg</span>
      <button class="copy-btn" data-copy-content="import SeewoLogoIcon from '~icons/yc/seewo-logo'" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>
</div>


### 图标统计

- **总计**: 117 个SVG图标
- **基础操作图标**: 20 个
- **文件类型图标**: 12 个
- **AI功能图标**: 7 个
- **教学相关图标**: 14 个
- **视频播放图标**: 6 个
- **箭头导航图标**: 13 个
- **状态提示图标**: 10 个
- **收藏评分图标**: 10 个
- **功能操作图标**: 8 个
- **界面元素图标**: 4 个
- **特殊功能图标**: 7 个
- **其他图标**: 6 个

> 📅 最后更新时间: 2025/8/4 00:03:57
>
> 🔄 此页面由构建脚本自动生成，每次运行 `pnpm run docs:dev` 或 `pnpm run docs:build` 时会自动更新

<style>
.logo-section {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
}

.logo-img {
  width: 160px;
  height: 40px;
  margin-bottom: 12px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
  margin: 20px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.icon-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  height: 40px;
}

.icon-preview img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.icon-item:hover .icon-preview img {
  transform: scale(1.1);
}

.icon-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.icon-filename {
  font-size: 12px;
  text-align: center;
  color: #606266;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  margin-bottom: 12px;
  line-height: 1.4;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  font-weight: 500;
  min-width: 60px;
  justify-content: center;
}

.copy-btn:hover {
  background: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.copy-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(64, 158, 255, 0.3);
}

.copy-btn.copied {
  background: #67c23a;
  transform: scale(1.05);
}

.copy-icon {
  fill: currentColor;
  flex-shrink: 0;
}

.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .icon-item {
    padding: 8px;
  }

  .icon-item img {
    width: 24px;
    height: 24px;
  }

  .icon-item span {
    font-size: 10px;
  }
}
</style>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  // 复制到剪贴板功能
  function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
      return navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((resolve, reject) => {
        try {
          const successful = document.execCommand('copy');
          textArea.remove();
          successful ? resolve() : reject(new Error('Copy command failed'));
        } catch (err) {
          textArea.remove();
          reject(err);
        }
      });
    }
  }

  // 显示复制成功提示
  function showCopySuccess(text) {
    const existingToast = document.querySelector('.copy-success');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = 'copy-success';
    toast.textContent = `已复制: ${text.length > 50 ? text.substring(0, 50) + '...' : text}`;
    document.body.appendChild(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.remove();
      }
    }, 2000);
  }

  // 为所有复制按钮绑定点击事件
  function handleCopyClick(e) {
    const button = e.target.closest('.copy-btn');
    if (!button) return;

    e.preventDefault();
    e.stopPropagation();

    const copyContent = button.getAttribute('data-copy-content');

    if (copyContent) {
      copyToClipboard(copyContent)
        .then(() => {
          // 显示成功提示
          showCopySuccess(copyContent);

          // 临时改变按钮样式和文本
          const originalHTML = button.innerHTML;
          button.innerHTML = `
            <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
              <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.75.75 0 0 1 1.06-1.06L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"></path>
            </svg>
            已复制
          `;
          button.classList.add('copied');

          setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('copied');
          }, 1500);
        })
        .catch(err => {
          console.error('复制失败:', err);
          // 降级到手动复制提示
          const fallbackText = `请手动复制以下代码:\n\n${copyContent}`;
          if (window.prompt) {
            window.prompt(fallbackText, copyContent);
          } else {
            alert(fallbackText);
          }
        });
    }
  }

  // 绑定事件监听器
  document.addEventListener('click', handleCopyClick);

  // 清理函数
  return () => {
    document.removeEventListener('click', handleCopyClick);
  };
})
</script>
