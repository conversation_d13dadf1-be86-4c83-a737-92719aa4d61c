# BackTop 回到顶部

## 概述

BackTop 组件提供了一个回到页面顶部的功能按钮，当页面滚动到一定距离时自动显示。

## 基本用法

```vue
<template>
  <div>
    <!-- 页面内容 -->
    <div style="height: 2000px;">长页面内容...</div>
    
    <!-- 回到顶部按钮 -->
    <BackTop />
  </div>
</template>

<script setup>
import BackTop from '@/components/BackTop.vue'
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visibilityHeight | 滚动高度达到此参数值时才出现 | `number` | `400` |
| right | 控制其显示位置，距离页面右边距 | `number \| string` | `40` |
| bottom | 控制其显示位置，距离页面底部距离 | `number \| string` | `40` |
| duration | 回到顶部所需时间（ms） | `number` | `450` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 点击按钮时触发 | `(event: MouseEvent) => void` |

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.back-top {
  --back-top-bg-color: #409eff;
  --back-top-hover-bg-color: #66b1ff;
  --back-top-text-color: #fff;
  --back-top-border-radius: 6px;
  --back-top-box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
}
```

## 注意事项

1. 组件会自动监听页面滚动事件
2. 当页面滚动高度小于 `visibilityHeight` 时，按钮会自动隐藏
3. 点击按钮会平滑滚动到页面顶部
4. 组件在组件销毁时会自动清理事件监听器

## 更新日志

### v1.0.0
- 初始版本，支持基本的回到顶部功能
