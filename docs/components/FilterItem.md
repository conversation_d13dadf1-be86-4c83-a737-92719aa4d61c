# FilterItem 筛选项容器

## 概述

FilterItem 是一个筛选项的容器组件，提供了标签和内容的标准化布局。使用 CSS Grid 布局，确保标签和内容的对齐和响应式显示。

## 基本用法

```vue
<template>
  <div>
    <FilterItem name="学科:" label-width="56">
      <n-select 
        v-model:value="subject"
        :options="subjectOptions"
        placeholder="请选择学科"
      />
    </FilterItem>
  </div>
</template>

<script setup>
import FilterItem from '@/components/FilterItem/FilterItem.vue'

const subject = ref('')
const subjectOptions = [
  { label: '数学', value: 'math' },
  { label: '语文', value: 'chinese' },
  { label: '英语', value: 'english' }
]
</script>
```

## 多个筛选项组合

```vue
<template>
  <div class="filter-container">
    <FilterItem name="学科:" label-width="56" labelline-height="32">
      <CsvSelect @change="handleCsvChange" />
    </FilterItem>
    
    <FilterItem name="题型:" label-width="56" class="mt-12px">
      <FilterSelect 
        :value="examType"
        :options="examTypeOptions"
        @update:value="handleExamTypeChange"
      />
    </FilterItem>
    
    <FilterItem name="难度:" label-width="56" class="mt-12px">
      <FilterSelect 
        :value="difficulty"
        :options="difficultyOptions"
        @update:value="handleDifficultyChange"
      />
    </FilterItem>
  </div>
</template>

<script setup>
import FilterItem from '@/components/FilterItem/FilterItem.vue'
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'
import CsvSelect from '@/components/CsvSelect.vue'

const examType = ref('')
const difficulty = ref('')

const examTypeOptions = [
  { label: '全部', value: '' },
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' }
]

const difficultyOptions = [
  { label: '全部', value: '' },
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' }
]
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| name | 筛选项标签文本 | `string` | - |
| labelWidth | 标签宽度（像素） | `string` | - |
| labellineHeight | 标签行高（像素） | `string` | `'32'` |

## Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 筛选项内容区域 | - |

## 布局说明

组件使用 CSS Grid 布局，具有以下特点：

### Grid 区域定义
```css
grid-template-areas:
  'label content'
  'label .';
```

### 布局特性
- **标签区域**: 固定宽度，垂直居上对齐
- **内容区域**: 自适应宽度，最小高度 32px
- **响应式**: 内容区域会根据容器宽度自动调整

## 样式定制

### 基础样式变量

```css
.filter-item {
  --label-color: #393548;
  --label-font-size: 14px;
  --label-font-weight: 500;
  --content-min-height: 32px;
}
```

### 自定义标签样式

```vue
<template>
  <FilterItem name="特殊标签:" label-width="80" class="custom-filter">
    <div>内容</div>
  </FilterItem>
</template>

<style scoped>
.custom-filter .label {
  color: #ff6b6b;
  font-weight: bold;
}
</style>
```

### 自定义内容区域

```vue
<template>
  <FilterItem name="复杂内容:" label-width="80">
    <div class="complex-content">
      <n-space>
        <n-select v-model:value="value1" :options="options1" />
        <span>到</span>
        <n-select v-model:value="value2" :options="options2" />
      </n-space>
    </div>
  </FilterItem>
</template>

<style scoped>
.complex-content {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
```

## 使用场景

### 1. 搜索筛选页面

```vue
<template>
  <div class="search-filters">
    <FilterItem name="关键词:" label-width="60">
      <n-input 
        v-model:value="keyword"
        placeholder="请输入关键词"
        clearable
      />
    </FilterItem>
    
    <FilterItem name="时间:" label-width="60" class="mt-12px">
      <n-date-picker 
        v-model:value="dateRange"
        type="daterange"
        clearable
      />
    </FilterItem>
  </div>
</template>
```

### 2. 表单布局

```vue
<template>
  <div class="form-container">
    <FilterItem name="姓名:" label-width="60">
      <n-input v-model:value="form.name" />
    </FilterItem>
    
    <FilterItem name="年龄:" label-width="60" class="mt-12px">
      <n-input-number v-model:value="form.age" />
    </FilterItem>
    
    <FilterItem name="性别:" label-width="60" class="mt-12px">
      <n-radio-group v-model:value="form.gender">
        <n-radio value="male">男</n-radio>
        <n-radio value="female">女</n-radio>
      </n-radio-group>
    </FilterItem>
  </div>
</template>
```

### 3. 高级筛选面板

```vue
<template>
  <div class="advanced-filters">
    <FilterItem name="价格区间:" label-width="80">
      <n-space>
        <n-input-number v-model:value="priceMin" placeholder="最低价" />
        <span>-</span>
        <n-input-number v-model:value="priceMax" placeholder="最高价" />
      </n-space>
    </FilterItem>
    
    <FilterItem name="评分:" label-width="80" class="mt-12px">
      <n-rate v-model:value="rating" allow-half />
    </FilterItem>
  </div>
</template>
```

## 最佳实践

### 1. 标签宽度统一

```vue
<template>
  <div class="filter-group">
    <!-- 保持同一组筛选项的标签宽度一致 -->
    <FilterItem name="学科:" label-width="56">
      <CsvSelect />
    </FilterItem>
    <FilterItem name="题型:" label-width="56" class="mt-12px">
      <FilterSelect />
    </FilterItem>
    <FilterItem name="难度等级:" label-width="56" class="mt-12px">
      <FilterSelect />
    </FilterItem>
  </div>
</template>
```

### 2. 响应式设计

```vue
<template>
  <div class="responsive-filters">
    <FilterItem 
      name="学科:" 
      :label-width="isMobile ? '40' : '56'"
      :labelline-height="isMobile ? '28' : '32'"
    >
      <CsvSelect />
    </FilterItem>
  </div>
</template>

<script setup>
const isMobile = ref(window.innerWidth < 768)
</script>
```

### 3. 动态标签

```vue
<template>
  <FilterItem 
    :name="dynamicLabel" 
    label-width="80"
  >
    <component :is="dynamicComponent" />
  </FilterItem>
</template>

<script setup>
const dynamicLabel = computed(() => {
  return isAdvanced.value ? '高级筛选:' : '基础筛选:'
})

const dynamicComponent = computed(() => {
  return isAdvanced.value ? AdvancedFilter : BasicFilter
})
</script>
```

## 注意事项

1. **标签宽度**: 建议根据最长标签文本设置合适的宽度
2. **内容对齐**: 内容区域默认垂直居中对齐，适合大多数表单控件
3. **间距控制**: 使用 `mt-12px` 等工具类控制筛选项之间的间距
4. **文本换行**: 标签使用 `white-space: nowrap` 防止换行

## 更新日志

### v1.0.0
- 初始版本，支持基本的标签-内容布局
- 使用 CSS Grid 实现响应式布局
- 支持自定义标签宽度和行高
