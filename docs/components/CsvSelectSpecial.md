# CsvSelectSpecial 培优课选择器

## 概述

CsvSelectSpecial 是一个专门用于选择培优课程的级联选择器组件，支持学段、学科和培优课程的三级选择。基于 Naive UI 的 Cascader 组件封装，提供了培优课程场景下的选择功能。

## 基本用法

```vue
<template>
  <div>
    <CsvSelectSpecial 
      :specialCourseId="courseId"
      @change="handleChange" 
    />
  </div>
</template>

<script setup>
import CsvSelectSpecial from '@/components/CsvSelectSpecial/specialCsv.vue'

const courseId = ref('special-course-123')

const handleChange = (selection) => {
  console.log('选择的培优课信息:', selection)
  console.log('学段ID:', selection.stageId)
  console.log('学科ID:', selection.subjectId)
  console.log('培优课ID:', selection.specialId)
}
</script>
```

## 与树形组件结合使用

```vue
<template>
  <div class="special-tree-container">
    <CsvSelectSpecial 
      :specialCourseId="specialCourseId"
      @change="onCsvSelectChange" 
    />
    
    <div class="tree-content">
      <!-- 根据选择的培优课显示相关内容 -->
      <SpecialCourseTree 
        v-if="currentSelection"
        :stageId="currentSelection.stageId"
        :subjectId="currentSelection.subjectId"
        :specialId="currentSelection.specialId"
      />
    </div>
  </div>
</template>

<script setup>
import CsvSelectSpecial from '@/components/CsvSelectSpecial/specialCsv.vue'

const specialCourseId = ref('')
const currentSelection = ref(null)

const onCsvSelectChange = (selection) => {
  currentSelection.value = selection
  // 触发相关业务逻辑
  loadSpecialCourseContent(selection)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| specialCourseId | 培优课程ID，用于初始化选择 | `string` | - |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选择变化时触发 | `(selection: SpecialSelection) => void` |

### SpecialSelection 类型定义

```typescript
interface SpecialSelection {
  stageId: YcType.CsvId    // 学段ID
  subjectId: YcType.CsvId  // 学科ID
  specialId: string        // 培优课程ID
}
```

## 功能特性

### 1. 三级级联选择
- **第一级**: 学段选择（小学、初中、高中等）
- **第二级**: 学科选择（数学、语文、英语等）
- **第三级**: 培优课程选择（具体的培优课程）

### 2. 远程数据加载
组件支持远程加载培优课程数据：

```javascript
// 当选择学段和学科后，自动加载对应的培优课程
async function handleLoad(option) {
  const [stageId, subjectId] = option.treeId.split('/')
  const { data } = await getPreviewSpecialCourse({ subjectId, stageId })
  
  if (data && data.length > 0) {
    option.children = data.map(item => ({
      ...item,
      treeId: `${stageId}/${subjectId}/${item.id}`
    }))
  }
}
```

### 3. 智能默认选择
组件会根据用户当前的教材设置自动选择默认值：

```javascript
// 根据当前教材自动匹配默认选项
function findDefaultOption() {
  return options.value.flatMap(item =>
    item.children?.filter(element =>
      `${csvStore.currentTextBook?.stageId}/${csvStore.currentTextBook?.subjectId}` === element.treeId
    )
  )
}
```

## 样式定制

组件提供了专门的样式类名进行定制：

```css
.oiw-special-wrap {
  /* 第一级菜单 */
  .n-cascader-submenu {
    min-width: 100px;
  }
  
  /* 第二级菜单 */
  .n-cascader-submenu.n-cascader-submenu--virtual {
    width: 120px;
  }
  
  /* 第三级菜单（培优课程列表） */
  .n-cascader-submenu:nth-child(3) {
    width: 300px;
  }
}
```

### 自定义菜单宽度

```vue
<template>
  <CsvSelectSpecial 
    :specialCourseId="courseId"
    class="custom-special-select"
    @change="handleChange" 
  />
</template>

<style scoped>
.custom-special-select :deep(.oiw-special-wrap) {
  .n-cascader-submenu:nth-child(3) {
    width: 400px; /* 增加培优课程菜单宽度 */
  }
}
</style>
```

## 使用场景

### 1. 培优课程选择

```vue
<template>
  <div class="course-selection">
    <h3>选择培优课程</h3>
    <CsvSelectSpecial 
      :specialCourseId="selectedCourseId"
      @change="onCourseChange" 
    />
    
    <div v-if="courseInfo" class="course-info">
      <h4>{{ courseInfo.name }}</h4>
      <p>学段: {{ courseInfo.stageName }}</p>
      <p>学科: {{ courseInfo.subjectName }}</p>
    </div>
  </div>
</template>

<script setup>
const selectedCourseId = ref('')
const courseInfo = ref(null)

const onCourseChange = async (selection) => {
  // 加载课程详细信息
  courseInfo.value = await loadCourseInfo(selection.specialId)
}
</script>
```

### 2. 微课视频筛选

```vue
<template>
  <div class="video-filter">
    <CsvSelectSpecial 
      :specialCourseId="currentCourseId"
      @change="onFilterChange" 
    />
    
    <VideoList 
      v-if="filteredVideos.length"
      :videos="filteredVideos"
    />
  </div>
</template>

<script setup>
const currentCourseId = ref('')
const filteredVideos = ref([])

const onFilterChange = async (selection) => {
  // 根据选择的培优课程筛选视频
  filteredVideos.value = await getVideosBySpecialCourse(selection.specialId)
}
</script>
```

### 3. 资源管理

```vue
<template>
  <div class="resource-manager">
    <div class="filter-bar">
      <CsvSelectSpecial 
        :specialCourseId="resourceFilter.courseId"
        @change="onResourceFilterChange" 
      />
    </div>
    
    <ResourceGrid 
      :resources="filteredResources"
      @select="onResourceSelect"
    />
  </div>
</template>

<script setup>
const resourceFilter = reactive({
  courseId: '',
  stageId: '',
  subjectId: ''
})

const filteredResources = ref([])

const onResourceFilterChange = (selection) => {
  resourceFilter.courseId = selection.specialId
  resourceFilter.stageId = selection.stageId
  resourceFilter.subjectId = selection.subjectId
  
  // 重新加载资源列表
  loadResources(resourceFilter)
}
</script>
```

## API 接口

组件依赖以下 API 接口：

### getPreviewSpecialCourse

```typescript
interface SpecialCourseReq {
  subjectId?: string  // 学科ID
  stageId?: string    // 学段ID
}

interface SpecialCourseRes {
  id?: string         // 培优课程ID
  name?: string       // 培优课程名称
  [k: string]: unknown
}

// 获取培优课程列表
const getPreviewSpecialCourse = (
  params: SpecialCourseReq
): Promise<{ data: SpecialCourseRes[] }>
```

## 最佳实践

### 1. 缓存管理

```vue
<script setup>
// 缓存用户的选择，提升用户体验
const cacheKey = 'special-course-selection'

const saveSelection = (selection) => {
  localStorage.setItem(cacheKey, JSON.stringify(selection))
}

const loadCachedSelection = () => {
  const cached = localStorage.getItem(cacheKey)
  return cached ? JSON.parse(cached) : null
}

onMounted(() => {
  const cached = loadCachedSelection()
  if (cached) {
    specialCourseId.value = cached.specialId
  }
})
</script>
```

### 2. 错误处理

```vue
<script setup>
const onCourseChange = async (selection) => {
  try {
    setLoading(true)
    const courseData = await loadCourseData(selection)
    handleCourseData(courseData)
  } catch (error) {
    console.error('加载培优课程失败:', error)
    message.error('加载课程信息失败，请重试')
  } finally {
    setLoading(false)
  }
}
</script>
```

### 3. 权限控制

```vue
<script setup>
const { hasSpecialCoursePermission } = useAuth()

const availableCourses = computed(() => {
  return allCourses.value.filter(course => 
    hasSpecialCoursePermission(course.id)
  )
})
</script>
```

## 注意事项

1. **数据依赖**: 组件依赖全局的学科数据和培优课程 API
2. **异步加载**: 培优课程数据是异步加载的，需要处理加载状态
3. **权限控制**: 不同用户可能有不同的培优课程访问权限
4. **缓存策略**: 建议缓存用户的选择以提升体验

## 更新日志

### v1.0.0
- 初始版本，支持培优课程的三级选择
- 支持远程数据加载和智能默认选择
- 提供专门的样式定制选项
