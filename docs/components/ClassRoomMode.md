# ClassRoomMode 课堂模式

## 概述

ClassRoomMode 是一个课堂展示模式组件，用于将内容以适合课堂教学的方式进行展示。支持全屏显示、字体放大、高对比度等课堂友好的显示效果。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="enterClassroomMode">进入课堂模式</n-button>
    
    <ClassRoomMode 
      :visible="isClassroomMode"
      :content="classroomContent"
      @exit="exitClassroomMode"
    />
  </div>
</template>

<script setup>
import ClassRoomMode from '@/components/ClassRoomMode/index.vue'

const isClassroomMode = ref(false)
const classroomContent = ref({
  title: '二次函数的性质',
  content: '<p>二次函数是形如 y = ax² + bx + c 的函数...</p>'
})

const enterClassroomMode = () => {
  isClassroomMode.value = true
}

const exitClassroomMode = () => {
  isClassroomMode.value = false
}
</script>
```

## 题目课堂模式

```vue
<template>
  <div>
    <ClassRoomMode 
      :visible="showProblemMode"
      :content="problemContent"
      mode="problem"
      :showAnswer="showAnswer"
      :showExplanation="showExplanation"
      @toggleAnswer="handleToggleAnswer"
      @toggleExplanation="handleToggleExplanation"
      @exit="exitProblemMode"
    />
  </div>
</template>

<script setup>
const showProblemMode = ref(false)
const showAnswer = ref(false)
const showExplanation = ref(false)

const problemContent = {
  question: '解方程：2x + 3 = 7',
  answer: 'x = 2',
  explanation: '移项得：2x = 7 - 3 = 4，所以 x = 2'
}

const handleToggleAnswer = () => {
  showAnswer.value = !showAnswer.value
}

const handleToggleExplanation = () => {
  showExplanation.value = !showExplanation.value
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示课堂模式 | `boolean` | `false` |
| content | 展示内容 | `ClassroomContent` | - |
| mode | 展示模式 | `'normal' \| 'problem' \| 'video'` | `'normal'` |
| showAnswer | 是否显示答案（题目模式） | `boolean` | `false` |
| showExplanation | 是否显示解析（题目模式） | `boolean` | `false` |
| fontSize | 字体大小 | `'small' \| 'medium' \| 'large' \| 'xlarge'` | `'large'` |
| theme | 主题模式 | `'light' \| 'dark' \| 'high-contrast'` | `'light'` |

### ClassroomContent 类型定义

```typescript
interface ClassroomContent {
  title?: string           // 标题
  content?: string         // 主要内容
  question?: string        // 题目（题目模式）
  answer?: string          // 答案（题目模式）
  explanation?: string     // 解析（题目模式）
  videoUrl?: string        // 视频地址（视频模式）
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| exit | 退出课堂模式时触发 | `() => void` |
| toggleAnswer | 切换答案显示时触发 | `() => void` |
| toggleExplanation | 切换解析显示时触发 | `() => void` |
| fullscreen | 进入/退出全屏时触发 | `(isFullscreen: boolean) => void` |

## 功能特性

### 1. 显示优化
- **大字体**: 适合远距离观看的字体大小
- **高对比度**: 提供高对比度主题选项
- **全屏显示**: 支持全屏展示模式
- **无干扰**: 隐藏不必要的界面元素

### 2. 交互控制
```vue
<script setup>
// 键盘快捷键支持
const handleKeydown = (event) => {
  switch (event.key) {
    case 'Escape':
      exitClassroomMode()
      break
    case 'a':
    case 'A':
      toggleAnswer()
      break
    case 'e':
    case 'E':
      toggleExplanation()
      break
    case 'f':
    case 'F':
      toggleFullscreen()
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
```

### 3. 主题切换
- **明亮主题**: 适合明亮环境的白色背景
- **暗色主题**: 适合暗环境的深色背景
- **高对比度**: 黑白高对比度，适合视力不佳的学生

## 使用场景

### 1. 题目讲解

```vue
<template>
  <div class="problem-teaching">
    <ProblemSingle 
      :problem="currentProblem"
      :index="1"
    />
    
    <n-button @click="showInClassroom">课堂展示</n-button>
    
    <ClassRoomMode 
      :visible="classroomVisible"
      :content="problemClassroomContent"
      mode="problem"
      :showAnswer="answerVisible"
      :showExplanation="explanationVisible"
      @toggleAnswer="answerVisible = !answerVisible"
      @toggleExplanation="explanationVisible = !explanationVisible"
      @exit="classroomVisible = false"
    />
  </div>
</template>

<script setup>
const problemClassroomContent = computed(() => ({
  question: currentProblem.value.content,
  answer: currentProblem.value.answer,
  explanation: currentProblem.value.explanation
}))
</script>
```

### 2. 课件展示

```vue
<template>
  <div class="courseware-presentation">
    <ClassRoomMode 
      :visible="presentationMode"
      :content="slideContent"
      mode="normal"
      :fontSize="currentFontSize"
      :theme="currentTheme"
      @exit="exitPresentation"
    />
  </div>
</template>

<script setup>
const slideContent = computed(() => ({
  title: currentSlide.value.title,
  content: currentSlide.value.content
}))

const currentFontSize = ref('large')
const currentTheme = ref('light')
</script>
```

### 3. 视频播放

```vue
<template>
  <div class="video-classroom">
    <ClassRoomMode 
      :visible="videoClassroomMode"
      :content="videoContent"
      mode="video"
      @exit="exitVideoClassroom"
    />
  </div>
</template>

<script setup>
const videoContent = {
  title: '二次函数图像性质',
  videoUrl: 'https://example.com/video.mp4'
}
</script>
```

## 样式定制

### 主题样式

```css
/* 明亮主题 */
.classroom-mode.light {
  background-color: #ffffff;
  color: #000000;
}

/* 暗色主题 */
.classroom-mode.dark {
  background-color: #1a1a1a;
  color: #ffffff;
}

/* 高对比度主题 */
.classroom-mode.high-contrast {
  background-color: #000000;
  color: #ffffff;
  font-weight: bold;
}
```

### 字体大小

```css
.classroom-content.small { font-size: 16px; }
.classroom-content.medium { font-size: 20px; }
.classroom-content.large { font-size: 24px; }
.classroom-content.xlarge { font-size: 32px; }
```

### 布局样式

```css
.classroom-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.classroom-content {
  max-width: 90%;
  text-align: center;
  line-height: 1.6;
}

.classroom-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 12px;
}
```

## 最佳实践

### 1. 响应式设计

```vue
<script setup>
// 根据屏幕大小调整字体
const adaptiveFontSize = computed(() => {
  const screenWidth = window.innerWidth
  if (screenWidth < 768) return 'medium'
  if (screenWidth < 1200) return 'large'
  return 'xlarge'
})

// 监听屏幕方向变化
const handleOrientationChange = () => {
  // 重新计算布局
  nextTick(() => {
    adjustLayout()
  })
}

onMounted(() => {
  window.addEventListener('orientationchange', handleOrientationChange)
})
</script>
```

### 2. 无障碍支持

```vue
<template>
  <div 
    class="classroom-mode"
    role="dialog"
    aria-label="课堂展示模式"
    :aria-describedby="contentId"
  >
    <div 
      :id="contentId"
      class="classroom-content"
      tabindex="0"
    >
      {{ content.title }}
    </div>
  </div>
</template>
```

### 3. 性能优化

```vue
<script setup>
// 防止页面滚动
const preventScroll = (e) => {
  e.preventDefault()
}

watch(visible, (newVisible) => {
  if (newVisible) {
    document.body.style.overflow = 'hidden'
    document.addEventListener('touchmove', preventScroll, { passive: false })
  } else {
    document.body.style.overflow = ''
    document.removeEventListener('touchmove', preventScroll)
  }
})
</script>
```

## 注意事项

1. **全屏兼容性**: 不同浏览器的全屏 API 可能有差异
2. **键盘导航**: 确保键盘用户可以正常操作
3. **性能考虑**: 大内容时注意渲染性能
4. **退出机制**: 提供多种退出课堂模式的方式

## 更新日志

### v1.0.0
- 初始版本，支持基本的课堂展示功能
- 支持多种显示模式和主题切换
- 集成键盘快捷键和无障碍支持
