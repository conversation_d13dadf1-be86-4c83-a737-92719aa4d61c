# SlideFastRecite 幻灯片快速背诵

## 概述

SlideFastRecite 是一个专门用于快速背诵的幻灯片组件，支持文本隐藏/显示、语音播放、背诵模式切换等功能。适用于语文、英语等需要背诵的学科教学。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="startRecite">开始背诵</n-button>
    
    <SlideFastRecite 
      :visible="reciteVisible"
      :content="reciteContent"
      @close="handleClose"
      @complete="handleComplete"
    />
  </div>
</template>

<script setup>
import SlideFastRecite from '@/components/SlideFastRecite/index.vue'

const reciteVisible = ref(false)
const reciteContent = ref({
  title: '静夜思',
  author: '李白',
  content: [
    '床前明月光，',
    '疑是地上霜。',
    '举头望明月，',
    '低头思故乡。'
  ]
})

const startRecite = () => {
  reciteVisible.value = true
}

const handleClose = () => {
  reciteVisible.value = false
}

const handleComplete = (result) => {
  console.log('背诵完成:', result)
}
</script>
```

## 完整功能示例

```vue
<template>
  <div class="recite-container">
    <div class="recite-controls">
      <n-select 
        v-model:value="selectedPoem"
        :options="poemOptions"
        @update:value="loadPoem"
      />
      <n-button @click="startReciteMode">开始背诵</n-button>
    </div>
    
    <SlideFastRecite 
      :visible="reciteMode"
      :content="currentPoem"
      :mode="reciteSettings.mode"
      :autoPlay="reciteSettings.autoPlay"
      :playSpeed="reciteSettings.speed"
      :showHints="reciteSettings.showHints"
      @close="exitReciteMode"
      @progress="onReciteProgress"
      @complete="onReciteComplete"
      @error="onReciteError"
    />
  </div>
</template>

<script setup>
const reciteMode = ref(false)
const selectedPoem = ref('')
const currentPoem = ref(null)

const reciteSettings = reactive({
  mode: 'progressive',  // 渐进式背诵
  autoPlay: true,       // 自动播放
  speed: 1,            // 播放速度
  showHints: true      // 显示提示
})

const poemOptions = [
  { label: '静夜思', value: 'jingyesi' },
  { label: '春晓', value: 'chunxiao' },
  { label: '登鹳雀楼', value: 'dengguanquelou' }
]

const loadPoem = async (poemId) => {
  const poem = await fetchPoemContent(poemId)
  currentPoem.value = poem
}

const onReciteProgress = (progress) => {
  // 记录背诵进度
  saveReciteProgress(selectedPoem.value, progress)
}

const onReciteComplete = (result) => {
  // 背诵完成处理
  recordReciteResult(selectedPoem.value, result)
  message.success('背诵完成！')
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示背诵模式 | `boolean` | `false` |
| content | 背诵内容 | `ReciteContent` | - |
| mode | 背诵模式 | `'normal' \| 'progressive' \| 'random'` | `'normal'` |
| autoPlay | 是否自动播放 | `boolean` | `false` |
| playSpeed | 播放速度 | `number` | `1` |
| showHints | 是否显示提示 | `boolean` | `true` |
| enableVoice | 是否启用语音 | `boolean` | `true` |

### ReciteContent 类型定义

```typescript
interface ReciteContent {
  title: string           // 标题
  author?: string         // 作者
  content: string[]       // 内容行数组
  audio?: string          // 音频URL
  translation?: string[]  // 翻译（可选）
  notes?: string[]        // 注释（可选）
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭背诵模式时触发 | `() => void` |
| progress | 背诵进度变化时触发 | `(progress: ReciteProgress) => void` |
| complete | 背诵完成时触发 | `(result: ReciteResult) => void` |
| error | 背诵出错时触发 | `(error: ReciteError) => void` |

### 回调参数类型

```typescript
interface ReciteProgress {
  currentLine: number     // 当前行
  totalLines: number      // 总行数
  completedLines: number  // 已完成行数
  accuracy: number        // 准确率
}

interface ReciteResult {
  totalTime: number       // 总用时
  accuracy: number        // 准确率
  mistakes: number        // 错误次数
  score: number          // 得分
}
```

## 功能特性

### 1. 背诵模式
- **普通模式**: 逐行显示/隐藏
- **渐进模式**: 逐步减少提示
- **随机模式**: 随机隐藏文字

### 2. 交互功能
```vue
<script setup>
// 文字隐藏/显示控制
const toggleLineVisibility = (lineIndex) => {
  visibilityState.value[lineIndex] = !visibilityState.value[lineIndex]
}

// 语音播放控制
const playAudio = async (lineIndex) => {
  if (audioEnabled.value && content.value.audio) {
    await playLineAudio(content.value.audio, lineIndex)
  }
}

// 背诵检查
const checkRecitation = (userInput, targetLine) => {
  const similarity = calculateSimilarity(userInput, targetLine)
  return {
    correct: similarity > 0.8,
    similarity,
    suggestions: generateSuggestions(userInput, targetLine)
  }
}
</script>
```

### 3. 进度跟踪
- 实时记录背诵进度
- 统计准确率和用时
- 生成学习报告

## 使用场景

### 1. 古诗词背诵

```vue
<template>
  <div class="poetry-recite">
    <SlideFastRecite 
      :visible="poetryMode"
      :content="poemContent"
      mode="progressive"
      :enableVoice="true"
      @complete="onPoetryComplete"
    />
  </div>
</template>

<script setup>
const poemContent = {
  title: '望庐山瀑布',
  author: '李白',
  content: [
    '日照香炉生紫烟，',
    '遥看瀑布挂前川。',
    '飞流直下三千尺，',
    '疑是银河落九天。'
  ],
  translation: [
    '太阳照射香炉峰生出袅袅紫烟，',
    '远远望去瀑布像长河悬挂山前。',
    '仿佛三千尺水流飞奔直冲而下，',
    '莫非是银河从九天垂落山崖间。'
  ]
}
</script>
```

### 2. 英语课文背诵

```vue
<template>
  <div class="english-recite">
    <SlideFastRecite 
      :visible="englishMode"
      :content="englishContent"
      mode="normal"
      :showHints="showTranslation"
      @progress="trackEnglishProgress"
    />
  </div>
</template>

<script setup>
const englishContent = {
  title: 'My School Day',
  content: [
    'I get up at seven o\'clock every morning.',
    'I have breakfast with my family.',
    'I go to school by bus.',
    'I have six classes every day.'
  ],
  translation: [
    '我每天早上七点起床。',
    '我和家人一起吃早餐。',
    '我坐公交车去学校。',
    '我每天有六节课。'
  ]
}
</script>
```

### 3. 课文段落背诵

```vue
<template>
  <div class="text-recite">
    <SlideFastRecite 
      :visible="textMode"
      :content="textContent"
      mode="random"
      :autoPlay="false"
      @error="handleReciteError"
    />
  </div>
</template>

<script setup>
const textContent = {
  title: '春天的故事',
  content: [
    '春天来了，万物复苏。',
    '小草从地里钻出来，嫩嫩的，绿绿的。',
    '桃花、杏花、梨花，你不让我，我不让你，都开满了花赶趟儿。',
    '红的像火，粉的像霞，白的像雪。'
  ]
}
</script>
```

## 样式定制

```css
.slide-fast-recite {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1000;
}

.recite-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
}

.recite-title {
  font-size: 32px;
  color: white;
  margin-bottom: 20px;
  text-align: center;
}

.recite-author {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
}

.recite-lines {
  max-width: 800px;
  width: 100%;
}

.recite-line {
  font-size: 24px;
  line-height: 2;
  color: white;
  text-align: center;
  margin-bottom: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s;
}

.recite-line:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.recite-line.hidden {
  background: rgba(255, 255, 255, 0.05);
  color: transparent;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.recite-controls {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px;
}

.recite-button {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s;
}

.recite-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}
```

## 最佳实践

### 1. 自适应难度

```vue
<script setup>
// 根据用户表现调整难度
const adjustDifficulty = (accuracy) => {
  if (accuracy > 0.9) {
    // 提高难度：减少提示时间
    hintDuration.value = Math.max(1000, hintDuration.value - 500)
  } else if (accuracy < 0.6) {
    // 降低难度：增加提示时间
    hintDuration.value = Math.min(5000, hintDuration.value + 500)
  }
}

// 智能提示
const generateSmartHints = (line, userProgress) => {
  const hints = []
  
  if (userProgress.mistakes > 2) {
    hints.push('注意标点符号')
  }
  
  if (userProgress.speed < averageSpeed) {
    hints.push('可以适当加快速度')
  }
  
  return hints
}
</script>
```

### 2. 学习数据分析

```vue
<script setup>
// 学习数据收集
const collectLearningData = (reciteResult) => {
  const data = {
    contentId: content.value.id,
    userId: user.value.id,
    startTime: startTime.value,
    endTime: Date.now(),
    accuracy: reciteResult.accuracy,
    mistakes: reciteResult.mistakes,
    mode: props.mode
  }
  
  // 发送到分析服务
  sendLearningAnalytics(data)
}

// 生成学习建议
const generateLearningAdvice = (history) => {
  const advice = []
  
  const avgAccuracy = history.reduce((sum, item) => sum + item.accuracy, 0) / history.length
  
  if (avgAccuracy < 0.7) {
    advice.push('建议多练习基础内容')
  }
  
  return advice
}
</script>
```

### 3. 无障碍支持

```vue
<script setup>
// 键盘导航支持
const handleKeydown = (event) => {
  switch (event.key) {
    case ' ':
      toggleCurrentLine()
      break
    case 'ArrowRight':
      nextLine()
      break
    case 'ArrowLeft':
      previousLine()
      break
    case 'Enter':
      playCurrentLine()
      break
  }
}

// 语音反馈
const announceProgress = (progress) => {
  if (voiceEnabled.value) {
    const message = `已完成 ${progress.completedLines} 行，共 ${progress.totalLines} 行`
    speak(message)
  }
}
</script>
```

## 注意事项

1. **内容版权**: 确保背诵内容的版权合规
2. **语音支持**: 不同浏览器的语音API支持程度不同
3. **数据存储**: 及时保存用户的背诵进度
4. **性能优化**: 大量文本时注意渲染性能

## 更新日志

### v1.0.0
- 初始版本，支持基本的背诵功能
- 支持多种背诵模式和语音播放
- 集成进度跟踪和学习分析
