# PaperChangeModal 试卷配置弹窗

## 概述

PaperChangeModal 是一个试卷下载和备课配置的弹窗组件，允许用户选择附带内容、纸张大小和文件格式等选项。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="showModal = true">下载试卷</n-button>
    
    <PaperChangeModal
      v-model:show="showModal"
      type="download"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import PaperChangeModal from '@/components/PaperChangeModal.vue'

const showModal = ref(false)

const handleConfirm = (modalData) => {
  console.log('用户选择:', modalData)
  // 处理下载逻辑
}

const handleCancel = () => {
  console.log('用户取消操作')
  showModal.value = false
}
</script>
```

## 备课模式

```vue
<template>
  <div>
    <n-button @click="showPreparationModal = true">加入备课</n-button>
    
    <PaperChangeModal
      v-model:show="showPreparationModal"
      type="preparation"
      :fileFormat="['FILE_FORMAT_PDF']"
      @confirm="handlePreparationConfirm"
    />
  </div>
</template>

<script setup>
import PaperChangeModal from '@/components/PaperChangeModal.vue'

const showPreparationModal = ref(false)

const handlePreparationConfirm = (modalData) => {
  console.log('备课配置:', modalData)
  // 处理备课逻辑
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| show | 是否显示弹窗 | `boolean` | `false` |
| type | 弹窗类型 | `'download' \| 'preparation' \| null` | `null` |
| fileFormat | 支持的文件格式选项 | `('FILE_FORMAT_WORD' \| 'FILE_FORMAT_PDF')[]` | `['FILE_FORMAT_WORD', 'FILE_FORMAT_PDF']` |
| blockScroll | 是否阻止背景滚动 | `boolean` | `true` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:show | 弹窗显示状态变化 | `(show: boolean) => void` |
| confirm | 用户确认操作 | `(modalData: ModalData) => void` |
| cancel | 用户取消操作 | `() => void` |

## ModalData 类型定义

```typescript
interface ModalData {
  with: 'WITH_NONE' | 'WITH_ANSWER' | 'WITH_ANSWER_AND_EXPLAIN'  // 附带内容
  size: 'A4' | 'A3' | 'A5'                                        // 纸张大小
  fileFormat: 'FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF'              // 文件格式
}
```

### 选项说明

**附带内容 (with)**
- `WITH_NONE`: 不附加
- `WITH_ANSWER`: 答案
- `WITH_ANSWER_AND_EXPLAIN`: 答案+解析

**纸张大小 (size)**
- `A4`: A4 纸张
- `A3`: A3 纸张  
- `A5`: A5 纸张

**文件格式 (fileFormat)**
- `FILE_FORMAT_WORD`: Word 文档
- `FILE_FORMAT_PDF`: PDF 文档

## 完整示例

```vue
<template>
  <div class="paper-actions">
    <n-space>
      <n-button type="primary" @click="openDownloadModal">
        下载试卷
      </n-button>
      <n-button @click="openPreparationModal">
        加入备课
      </n-button>
    </n-space>

    <PaperChangeModal
      v-model:show="downloadModal.show"
      type="download"
      @confirm="handleDownload"
      @cancel="handleCancel"
    />

    <PaperChangeModal
      v-model:show="preparationModal.show"
      type="preparation"
      :fileFormat="['FILE_FORMAT_PDF']"
      :blockScroll="false"
      @confirm="handlePreparation"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import PaperChangeModal from '@/components/PaperChangeModal.vue'

const downloadModal = reactive({ show: false })
const preparationModal = reactive({ show: false })

const openDownloadModal = () => {
  downloadModal.show = true
}

const openPreparationModal = () => {
  preparationModal.show = true
}

const handleDownload = async (modalData) => {
  try {
    await downloadPaper({
      paperId: props.paperId,
      ...modalData
    })
    downloadModal.show = false
    // 显示成功提示
  } catch (error) {
    // 处理错误
  }
}

const handlePreparation = async (modalData) => {
  try {
    await addToPreparation({
      paperId: props.paperId,
      ...modalData
    })
    preparationModal.show = false
    // 显示成功提示
  } catch (error) {
    // 处理错误
  }
}

const handleCancel = () => {
  // 取消操作的通用处理
  console.log('用户取消了操作')
}
</script>
```

## 样式定制

组件提供了完整的样式类名，可以进行自定义：

```css
.paper-change-modal {
  /* 弹窗容器样式 */
}

.paper-change-content {
  /* 内容区域样式 */
  width: 520px;
  padding: 40px;
}

.change-header {
  /* 标题样式 */
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}

.change-list {
  /* 选项列表样式 */
}

.change-item {
  /* 单个选项样式 */
  display: flex;
  margin-bottom: 24px;
}

.item-left {
  /* 选项标签样式 */
  width: 100px;
  flex-shrink: 0;
}

.item-right {
  /* 选项内容样式 */
  flex: 1;
}
```

## 使用场景

1. **试卷下载**: 用户下载试卷时选择格式和内容
2. **备课准备**: 将试卷加入备课时的配置选择
3. **批量操作**: 批量处理试卷时的统一配置
4. **打印设置**: 打印试卷前的参数配置

## 最佳实践

### 1. 错误处理

```vue
<script setup>
const handleConfirm = async (modalData) => {
  try {
    setLoading(true)
    await processPaper(modalData)
    showModal.value = false
    message.success('操作成功')
  } catch (error) {
    message.error(error.message || '操作失败')
  } finally {
    setLoading(false)
  }
}
</script>
```

### 2. 状态管理

```vue
<script setup>
// 使用 reactive 管理多个弹窗状态
const modals = reactive({
  download: { show: false, loading: false },
  preparation: { show: false, loading: false }
})

const openModal = (type) => {
  modals[type].show = true
}

const closeModal = (type) => {
  modals[type].show = false
  modals[type].loading = false
}
</script>
```

### 3. 默认值配置

```vue
<script setup>
// 根据用户偏好设置默认值
const getDefaultConfig = () => {
  const userPrefs = getUserPreferences()
  return {
    with: userPrefs.defaultWith || 'WITH_ANSWER',
    size: userPrefs.defaultSize || 'A4',
    fileFormat: userPrefs.defaultFormat || 'FILE_FORMAT_WORD'
  }
}
</script>
```

## 注意事项

1. **状态重置**: 弹窗关闭时会自动重置选项为默认值
2. **文件格式限制**: 可以通过 `fileFormat` 属性限制可选的文件格式
3. **阻止滚动**: 默认会阻止背景页面滚动，可通过 `blockScroll` 控制
4. **类型安全**: 使用 TypeScript 确保 `ModalData` 类型的正确性

## 更新日志

### v1.0.0
- 初始版本，支持下载和备课两种模式
- 支持附带内容、纸张大小、文件格式选择
- 提供完整的事件回调和类型定义
