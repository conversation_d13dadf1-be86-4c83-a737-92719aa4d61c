# ParseText 文本解析组件

## 概述

ParseText 是一个用于解析和渲染 HTML 文本内容的组件，主要用于显示题目讲解等富文本内容。支持 HTML 标签解析和安全渲染。

## 基本用法

```vue
<template>
  <div>
    <ParseText :text="explanationText" />
  </div>
</template>

<script setup>
import ParseText from '@/components/ParseText.vue'

const explanationText = ref('<p>这是一段<strong>重要</strong>的讲解内容</p>')
</script>
```

## 处理空内容

```vue
<template>
  <div>
    <!-- 当 text 为空时，会显示"暂无" -->
    <ParseText :text="undefined" />
    <ParseText :text="''" />
    <ParseText :text="null" />
  </div>
</template>

<script setup>
import ParseText from '@/components/ParseText.vue'
</script>
```

## 数学公式支持

```vue
<template>
  <div>
    <ParseText :text="mathText" />
  </div>
</template>

<script setup>
import ParseText from '@/components/ParseText.vue'

const mathText = ref(`
  <p>根据勾股定理：</p>
  <p>$$a^2 + b^2 = c^2$$</p>
  <p>其中 $c$ 是斜边长度</p>
`)
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| text | 需要解析的 HTML 文本内容 | `string \| undefined` | - |

## 渲染逻辑

组件的渲染逻辑如下：

1. **有内容时**: 使用 `innerHTML` 渲染 HTML 内容
2. **无内容时**: 显示"暂无"文本
3. **HTML 解析**: 支持所有标准 HTML 标签
4. **样式继承**: 继承父容器的样式设置

## 样式说明

组件默认样式：
```css
.parse-text {
  margin-top: 8px;
  font-size: 14px;
  color: #9792AC;
  line-height: 22px;
}
```

## 安全性考虑

### XSS 防护建议

虽然组件使用 `innerHTML` 渲染内容，但在实际使用中应注意：

```vue
<script setup>
import ParseText from '@/components/ParseText.vue'
import DOMPurify from 'dompurify' // 推荐使用

const sanitizeText = (htmlText) => {
  return DOMPurify.sanitize(htmlText)
}

const safeText = computed(() => {
  return sanitizeText(rawText.value)
})
</script>

<template>
  <ParseText :text="safeText" />
</template>
```

## 使用场景

### 1. 题目讲解显示

```vue
<template>
  <div class="problem-explanation">
    <h4>题目解析</h4>
    <ParseText :text="problem.explanation" />
  </div>
</template>
```

### 2. 富文本内容展示

```vue
<template>
  <div class="content-display">
    <ParseText :text="article.content" />
  </div>
</template>
```

### 3. 动态内容渲染

```vue
<template>
  <div>
    <ParseText 
      v-for="item in contentList" 
      :key="item.id"
      :text="item.htmlContent" 
    />
  </div>
</template>
```

## 与其他文本组件的对比

| 特性 | ParseText | 普通文本 | Markdown 组件 |
|------|-----------|----------|---------------|
| HTML 支持 | ✅ | ❌ | ❌ |
| 数学公式 | ✅ | ❌ | ✅ |
| 安全性 | 需注意 | ✅ | ✅ |
| 性能 | 中 | 高 | 中 |

## 最佳实践

### 1. 内容预处理

```vue
<script setup>
const processedText = computed(() => {
  if (!rawText.value) return undefined
  
  // 处理特殊字符
  return rawText.value
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
})
</script>

<template>
  <ParseText :text="processedText" />
</template>
```

### 2. 错误处理

```vue
<script setup>
const safeText = computed(() => {
  try {
    return validateHtml(props.text)
  } catch (error) {
    console.warn('HTML 解析错误:', error)
    return '内容解析失败'
  }
})
</script>

<template>
  <ParseText :text="safeText" />
</template>
```

### 3. 样式隔离

```vue
<template>
  <div class="parse-text-container">
    <ParseText :text="content" />
  </div>
</template>

<style scoped>
.parse-text-container {
  /* 限制内部样式影响范围 */
  overflow: hidden;
}

.parse-text-container :deep(img) {
  max-width: 100%;
  height: auto;
}

.parse-text-container :deep(table) {
  border-collapse: collapse;
  width: 100%;
}
</style>
```

## 注意事项

1. **安全性**: 使用 `innerHTML` 存在 XSS 风险，确保内容来源可信
2. **性能**: 大量 HTML 内容可能影响渲染性能
3. **样式**: 内部 HTML 的样式可能会影响页面布局
4. **兼容性**: 复杂的 HTML 结构在不同浏览器中可能表现不一致

## 更新日志

### v1.0.0
- 初始版本，支持基本的 HTML 文本解析
- 支持空内容的默认显示
- 提供讲解文本的标准样式
