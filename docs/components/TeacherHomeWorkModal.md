# TeacherHomeWorkModal 教师作业弹窗

## 概述

TeacherHomeWorkModal 是一个教师作业管理弹窗组件，用于创建、编辑、布置和管理学生作业。支持多种作业类型、截止时间设置、学生分组等功能。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="openHomeworkModal">布置作业</n-button>
    
    <TeacherHomeWorkModal 
      :visible="homeworkVisible"
      :mode="modalMode"
      :homeworkData="currentHomework"
      @close="handleClose"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup>
import TeacherHomeWorkModal from '@/components/TeacherHomeWorkModal/index.vue'

const homeworkVisible = ref(false)
const modalMode = ref('create') // 'create' | 'edit' | 'view'
const currentHomework = ref(null)

const openHomeworkModal = () => {
  modalMode.value = 'create'
  currentHomework.value = null
  homeworkVisible.value = true
}

const handleClose = () => {
  homeworkVisible.value = false
}

const handleSubmit = async (homeworkData) => {
  try {
    if (modalMode.value === 'create') {
      await createHomework(homeworkData)
    } else {
      await updateHomework(currentHomework.value.id, homeworkData)
    }
    
    message.success('作业保存成功')
    homeworkVisible.value = false
  } catch (error) {
    message.error('保存失败，请重试')
  }
}
</script>
```

## 完整功能示例

```vue
<template>
  <div class="homework-management">
    <div class="homework-toolbar">
      <n-button type="primary" @click="createHomework">
        布置新作业
      </n-button>
      <n-button @click="createFromTemplate">
        从模板创建
      </n-button>
    </div>
    
    <div class="homework-list">
      <div 
        v-for="homework in homeworkList"
        :key="homework.id"
        class="homework-item"
      >
        <div class="homework-info">
          <h4>{{ homework.title }}</h4>
          <p>截止时间: {{ homework.deadline }}</p>
          <p>已提交: {{ homework.submittedCount }}/{{ homework.totalCount }}</p>
        </div>
        <div class="homework-actions">
          <n-button @click="viewHomework(homework)">查看</n-button>
          <n-button @click="editHomework(homework)">编辑</n-button>
          <n-button @click="duplicateHomework(homework)">复制</n-button>
        </div>
      </div>
    </div>
    
    <TeacherHomeWorkModal 
      :visible="modalVisible"
      :mode="currentMode"
      :homeworkData="selectedHomework"
      :classOptions="classOptions"
      :templateOptions="templateOptions"
      @close="closeModal"
      @submit="submitHomework"
      @preview="previewHomework"
      @saveTemplate="saveAsTemplate"
    />
  </div>
</template>

<script setup>
const modalVisible = ref(false)
const currentMode = ref('create')
const selectedHomework = ref(null)
const homeworkList = ref([])

const classOptions = ref([
  { label: '高一(1)班', value: 'class-1' },
  { label: '高一(2)班', value: 'class-2' }
])

const templateOptions = ref([
  { label: '数学练习模板', value: 'template-1' },
  { label: '物理实验模板', value: 'template-2' }
])

const createHomework = () => {
  currentMode.value = 'create'
  selectedHomework.value = null
  modalVisible.value = true
}

const editHomework = (homework) => {
  currentMode.value = 'edit'
  selectedHomework.value = homework
  modalVisible.value = true
}

const viewHomework = (homework) => {
  currentMode.value = 'view'
  selectedHomework.value = homework
  modalVisible.value = true
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示弹窗 | `boolean` | `false` |
| mode | 弹窗模式 | `'create' \| 'edit' \| 'view'` | `'create'` |
| homeworkData | 作业数据 | `HomeworkData` | `null` |
| classOptions | 班级选项 | `ClassOption[]` | `[]` |
| templateOptions | 模板选项 | `TemplateOption[]` | `[]` |
| allowDraft | 是否允许保存草稿 | `boolean` | `true` |

### HomeworkData 类型定义

```typescript
interface HomeworkData {
  id?: string                    // 作业ID
  title: string                  // 作业标题
  description: string            // 作业描述
  type: HomeworkType             // 作业类型
  content: HomeworkContent       // 作业内容
  deadline: string               // 截止时间
  assignedClasses: string[]      // 分配的班级
  settings: HomeworkSettings     // 作业设置
  status: 'draft' | 'published' | 'closed'  // 状态
}

interface HomeworkContent {
  problems?: Problem[]           // 题目列表
  attachments?: Attachment[]     // 附件列表
  instructions?: string          // 作业说明
}

interface HomeworkSettings {
  allowLateSubmission: boolean   // 允许迟交
  showAnswerAfterDeadline: boolean  // 截止后显示答案
  maxAttempts: number           // 最大尝试次数
  timeLimit?: number            // 时间限制（分钟）
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭弹窗时触发 | `() => void` |
| submit | 提交作业时触发 | `(homeworkData: HomeworkData) => void` |
| preview | 预览作业时触发 | `(homeworkData: HomeworkData) => void` |
| saveTemplate | 保存为模板时触发 | `(templateData: TemplateData) => void` |
| draft | 保存草稿时触发 | `(draftData: HomeworkData) => void` |

## 功能特性

### 1. 作业类型支持
- **练习作业**: 选择题、填空题、解答题
- **阅读作业**: 文章阅读、视频观看
- **实践作业**: 实验报告、调研作业
- **创作作业**: 作文、绘画、手工制作

### 2. 智能组题
```vue
<script setup>
// 智能推荐题目
const recommendProblems = async (subject, difficulty, count) => {
  const recommendations = await api.getRecommendedProblems({
    subject,
    difficulty,
    count,
    excludeRecent: true // 排除最近使用的题目
  })
  
  return recommendations
}

// 难度平衡检查
const checkDifficultyBalance = (problems) => {
  const difficultyCount = {
    easy: 0,
    medium: 0,
    hard: 0
  }
  
  problems.forEach(problem => {
    difficultyCount[problem.difficulty]++
  })
  
  return {
    isBalanced: Math.abs(difficultyCount.easy - difficultyCount.medium) <= 2,
    suggestion: generateBalanceSuggestion(difficultyCount)
  }
}
</script>
```

### 3. 班级管理
- 支持多班级同时布置
- 班级差异化设置
- 学生分组功能

### 4. 时间管理
- 灵活的截止时间设置
- 时区自动处理
- 延期申请处理

## 使用场景

### 1. 日常练习布置

```vue
<template>
  <div class="daily-practice">
    <TeacherHomeWorkModal 
      :visible="practiceModal"
      mode="create"
      :homeworkData="practiceTemplate"
      @submit="assignPractice"
    />
  </div>
</template>

<script setup>
const practiceTemplate = {
  title: '每日数学练习',
  type: 'practice',
  settings: {
    allowLateSubmission: false,
    showAnswerAfterDeadline: true,
    maxAttempts: 1,
    timeLimit: 60
  }
}

const assignPractice = (homeworkData) => {
  // 布置练习作业
  createDailyPractice(homeworkData)
}
</script>
```

### 2. 考试作业

```vue
<template>
  <div class="exam-homework">
    <TeacherHomeWorkModal 
      :visible="examModal"
      mode="create"
      :homeworkData="examTemplate"
      @submit="createExam"
    />
  </div>
</template>

<script setup>
const examTemplate = {
  title: '期中测试',
  type: 'exam',
  settings: {
    allowLateSubmission: false,
    showAnswerAfterDeadline: false,
    maxAttempts: 1,
    timeLimit: 120
  }
}
</script>
```

### 3. 项目作业

```vue
<template>
  <div class="project-homework">
    <TeacherHomeWorkModal 
      :visible="projectModal"
      mode="create"
      :homeworkData="projectTemplate"
      @submit="assignProject"
    />
  </div>
</template>

<script setup>
const projectTemplate = {
  title: '科学实验报告',
  type: 'project',
  settings: {
    allowLateSubmission: true,
    showAnswerAfterDeadline: false,
    maxAttempts: 3,
    timeLimit: null // 无时间限制
  }
}
</script>
```

## 样式定制

```css
.teacher-homework-modal {
  .n-modal {
    max-width: 800px;
    width: 90vw;
  }
}

.homework-form {
  padding: 24px;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #262626;
}

.problem-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.problem-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
}

.problem-item:hover {
  background-color: #fafafa;
}

.class-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.class-tag {
  padding: 4px 8px;
  background-color: #f6f6f6;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.class-tag.selected {
  background-color: #1890ff;
  color: white;
}

.homework-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.footer-left {
  display: flex;
  gap: 8px;
}

.footer-right {
  display: flex;
  gap: 8px;
}
```

## 最佳实践

### 1. 表单验证

```vue
<script setup>
// 作业数据验证
const validateHomework = (data) => {
  const errors = []
  
  if (!data.title?.trim()) {
    errors.push('作业标题不能为空')
  }
  
  if (!data.deadline) {
    errors.push('请设置截止时间')
  } else if (new Date(data.deadline) <= new Date()) {
    errors.push('截止时间不能早于当前时间')
  }
  
  if (!data.assignedClasses?.length) {
    errors.push('请选择要布置的班级')
  }
  
  if (data.type === 'practice' && !data.content?.problems?.length) {
    errors.push('练习作业至少需要一道题目')
  }
  
  return errors
}
</script>
```

### 2. 自动保存

```vue
<script setup>
// 自动保存草稿
const autoSaveDraft = debounce((formData) => {
  if (props.allowDraft && formData.title) {
    saveDraft(formData)
  }
}, 2000)

// 监听表单变化
watch(formData, (newData) => {
  autoSaveDraft(newData)
}, { deep: true })
</script>
```

### 3. 模板管理

```vue
<script setup>
// 保存为模板
const saveAsTemplate = (homeworkData) => {
  const template = {
    name: `${homeworkData.title} - 模板`,
    type: homeworkData.type,
    content: homeworkData.content,
    settings: homeworkData.settings,
    createdAt: new Date().toISOString()
  }
  
  saveTemplate(template)
  message.success('模板保存成功')
}

// 从模板加载
const loadFromTemplate = (templateId) => {
  const template = templates.value.find(t => t.id === templateId)
  if (template) {
    Object.assign(formData, {
      ...template,
      title: `${template.name} - ${new Date().toLocaleDateString()}`,
      deadline: null,
      assignedClasses: []
    })
  }
}
</script>
```

## 注意事项

1. **权限控制**: 确保教师只能管理自己班级的作业
2. **数据备份**: 重要作业数据需要及时备份
3. **时区处理**: 正确处理不同时区的截止时间
4. **性能优化**: 大量题目时注意加载性能

## 更新日志

### v1.0.0
- 初始版本，支持基本的作业创建和管理功能
- 支持多种作业类型和班级管理
- 集成模板系统和自动保存功能
