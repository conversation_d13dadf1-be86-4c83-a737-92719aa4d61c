# ReportClassroomMode 报告课堂模式

## 概述

ReportClassroomMode 是一个专门用于课堂报告展示的组件，支持数据可视化、图表展示、互动功能等。适用于课堂教学中的数据分析和结果展示。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="showReport">显示课堂报告</n-button>
    
    <ReportClassroomMode 
      :visible="reportVisible"
      :reportData="classroomReportData"
      @close="handleClose"
      @interact="handleInteract"
    />
  </div>
</template>

<script setup>
import ReportClassroomMode from '@/components/ReportClassroomMode/index.vue'

const reportVisible = ref(false)
const classroomReportData = ref({
  title: '第三章测试结果分析',
  studentCount: 45,
  averageScore: 78.5,
  problems: [
    { id: '1', correctRate: 85, difficulty: 'easy' },
    { id: '2', correctRate: 62, difficulty: 'medium' },
    { id: '3', correctRate: 43, difficulty: 'hard' }
  ]
})

const showReport = () => {
  reportVisible.value = true
}

const handleClose = () => {
  reportVisible.value = false
}

const handleInteract = (action, data) => {
  console.log('课堂互动:', action, data)
}
</script>
```

## 完整功能示例

```vue
<template>
  <div class="classroom-report-container">
    <div class="report-controls">
      <n-select 
        v-model:value="selectedReport"
        :options="reportOptions"
        @update:value="loadReport"
      />
      <n-button @click="enterClassroomMode">进入课堂模式</n-button>
    </div>
    
    <ReportClassroomMode 
      :visible="classroomMode"
      :reportData="currentReportData"
      :interactive="true"
      :showProblemList="true"
      :canvasDrawing="true"
      @close="exitClassroomMode"
      @problemSelect="handleProblemSelect"
      @canvasDraw="handleCanvasDraw"
      @statisticsView="handleStatisticsView"
    />
  </div>
</template>

<script setup>
const classroomMode = ref(false)
const selectedReport = ref('')
const currentReportData = ref(null)

const reportOptions = [
  { label: '第一章测试报告', value: 'chapter1' },
  { label: '期中考试报告', value: 'midterm' },
  { label: '课堂练习报告', value: 'practice' }
]

const loadReport = async (reportId) => {
  const data = await fetchReportData(reportId)
  currentReportData.value = data
}

const enterClassroomMode = () => {
  if (currentReportData.value) {
    classroomMode.value = true
  }
}

const handleProblemSelect = (problemId) => {
  // 选择题目进行详细分析
  showProblemAnalysis(problemId)
}

const handleCanvasDraw = (drawData) => {
  // 处理画布绘制数据
  saveDrawingData(drawData)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示课堂模式 | `boolean` | `false` |
| reportData | 报告数据 | `ReportData` | - |
| interactive | 是否启用交互功能 | `boolean` | `true` |
| showProblemList | 是否显示题目列表 | `boolean` | `true` |
| canvasDrawing | 是否启用画布绘制 | `boolean` | `false` |
| theme | 主题模式 | `'light' \| 'dark'` | `'light'` |

### ReportData 类型定义

```typescript
interface ReportData {
  title: string                    // 报告标题
  studentCount: number             // 学生总数
  averageScore: number             // 平均分
  problems: ProblemReport[]        // 题目报告
  statistics: StatisticsData       // 统计数据
  timestamp: string                // 报告时间
}

interface ProblemReport {
  id: string                       // 题目ID
  correctRate: number              // 正确率
  difficulty: 'easy' | 'medium' | 'hard'  // 难度
  commonErrors: string[]           // 常见错误
  timeSpent: number                // 平均用时
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭课堂模式时触发 | `() => void` |
| problemSelect | 选择题目时触发 | `(problemId: string) => void` |
| canvasDraw | 画布绘制时触发 | `(drawData: DrawData) => void` |
| statisticsView | 查看统计时触发 | `(type: string) => void` |
| interact | 交互操作时触发 | `(action: string, data: any) => void` |

## 功能特性

### 1. 数据可视化
- 成绩分布图表
- 正确率统计
- 时间分析图
- 难度分布

### 2. 题目分析
```vue
<script setup>
// 题目详细分析
const analyzeProblem = (problemId) => {
  const problem = reportData.value.problems.find(p => p.id === problemId)
  
  return {
    correctRate: problem.correctRate,
    commonErrors: problem.commonErrors,
    timeAnalysis: problem.timeSpent,
    difficultyLevel: problem.difficulty,
    recommendations: generateRecommendations(problem)
  }
}

// 生成教学建议
const generateRecommendations = (problem) => {
  const recommendations = []
  
  if (problem.correctRate < 60) {
    recommendations.push('建议重点讲解此题型')
  }
  
  if (problem.timeSpent > averageTime) {
    recommendations.push('学生解题时间较长，需要练习提高速度')
  }
  
  return recommendations
}
</script>
```

### 3. 画布绘制
- 支持在题目上标注
- 支持绘制解题过程
- 支持保存和分享绘制内容

### 4. 互动功能
- 实时投票
- 问答互动
- 小组讨论
- 即时反馈

## 使用场景

### 1. 考试结果分析

```vue
<template>
  <div class="exam-analysis">
    <ReportClassroomMode 
      :visible="analysisMode"
      :reportData="examReport"
      :showProblemList="true"
      @problemSelect="showProblemDetail"
    />
  </div>
</template>

<script setup>
const examReport = {
  title: '期中数学考试分析',
  studentCount: 48,
  averageScore: 82.3,
  problems: [
    {
      id: 'q1',
      correctRate: 92,
      difficulty: 'easy',
      commonErrors: ['计算错误', '符号错误']
    }
  ]
}
</script>
```

### 2. 课堂练习反馈

```vue
<template>
  <div class="practice-feedback">
    <ReportClassroomMode 
      :visible="feedbackMode"
      :reportData="practiceReport"
      :interactive="true"
      :canvasDrawing="true"
      @canvasDraw="saveTeacherAnnotation"
    />
  </div>
</template>

<script setup>
const saveTeacherAnnotation = (drawData) => {
  // 保存教师标注
  annotations.value.push({
    problemId: currentProblem.value.id,
    drawData,
    timestamp: Date.now()
  })
}
</script>
```

### 3. 学习进度展示

```vue
<template>
  <div class="progress-display">
    <ReportClassroomMode 
      :visible="progressMode"
      :reportData="progressReport"
      theme="dark"
      @statisticsView="showDetailedStats"
    />
  </div>
</template>

<script setup>
const progressReport = computed(() => ({
  title: '本周学习进度',
  studentCount: classSize.value,
  averageScore: weeklyAverage.value,
  statistics: {
    completionRate: 85,
    improvementRate: 12,
    strugglingStudents: 8
  }
}))
</script>
```

## 样式定制

```css
.report-classroom-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  z-index: 1000;
}

.report-header {
  background: #fff;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  height: calc(100vh - 80px);
  gap: 20px;
  padding: 20px;
}

.chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.problem-list {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
}

.problem-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.problem-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.canvas-overlay.drawing {
  pointer-events: all;
}
```

## 最佳实践

### 1. 数据处理

```vue
<script setup>
// 数据预处理
const processReportData = (rawData) => {
  return {
    ...rawData,
    problems: rawData.problems.map(problem => ({
      ...problem,
      correctRateColor: getCorrectRateColor(problem.correctRate),
      difficultyIcon: getDifficultyIcon(problem.difficulty)
    }))
  }
}

const getCorrectRateColor = (rate) => {
  if (rate >= 80) return '#52c41a'
  if (rate >= 60) return '#faad14'
  return '#ff4d4f'
}
</script>
```

### 2. 性能优化

```vue
<script setup>
// 虚拟滚动处理大量题目
const visibleProblems = computed(() => {
  const start = Math.max(0, scrollTop.value - bufferSize)
  const end = Math.min(problems.value.length, scrollTop.value + visibleCount + bufferSize)
  return problems.value.slice(start, end)
})

// 图表懒加载
const loadChart = async (chartType) => {
  if (!chartCache.has(chartType)) {
    const chartData = await generateChartData(chartType)
    chartCache.set(chartType, chartData)
  }
  return chartCache.get(chartType)
}
</script>
```

### 3. 交互优化

```vue
<script setup>
// 防抖处理画布绘制
const debouncedSave = debounce((drawData) => {
  saveDrawingData(drawData)
}, 500)

// 键盘快捷键
const handleKeydown = (event) => {
  switch (event.key) {
    case 'Escape':
      exitClassroomMode()
      break
    case 'n':
      nextProblem()
      break
    case 'p':
      previousProblem()
      break
  }
}
</script>
```

## 注意事项

1. **数据量**: 大量数据时注意性能优化
2. **画布绘制**: 绘制数据需要及时保存，防止丢失
3. **交互响应**: 确保交互操作的实时性
4. **屏幕适配**: 适配不同尺寸的显示设备

## 更新日志

### v1.0.0
- 初始版本，支持基本的报告展示功能
- 集成数据可视化和画布绘制
- 支持题目分析和互动功能
