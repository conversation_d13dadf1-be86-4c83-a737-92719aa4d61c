# SideResource 侧边资源面板

## 概述

SideResource 是一个侧边资源面板组件，用于展示和管理各类教学资源。支持资源分类、搜索、预览、插入等功能。

## 基本用法

```vue
<template>
  <div class="layout">
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 内容 -->
    </div>
    
    <!-- 侧边资源面板 -->
    <SideResource 
      :visible="showResourcePanel"
      :resourceType="currentResourceType"
      @close="showResourcePanel = false"
      @insert="handleResourceInsert"
    />
  </div>
</template>

<script setup>
import SideResource from '@/components/SideResource/index.vue'

const showResourcePanel = ref(false)
const currentResourceType = ref('image')

const handleResourceInsert = (resource) => {
  console.log('插入资源:', resource)
  // 处理资源插入逻辑
}
</script>
```

## 完整配置示例

```vue
<template>
  <div>
    <n-button @click="openResourcePanel('image')">
      插入图片
    </n-button>
    <n-button @click="openResourcePanel('video')">
      插入视频
    </n-button>
    
    <SideResource 
      :visible="resourcePanel.visible"
      :resourceType="resourcePanel.type"
      :filters="resourceFilters"
      :searchKeyword="searchKeyword"
      @close="closeResourcePanel"
      @insert="handleInsert"
      @search="handleSearch"
      @filter="handleFilter"
    />
  </div>
</template>

<script setup>
const resourcePanel = reactive({
  visible: false,
  type: 'image'
})

const resourceFilters = {
  subject: '',
  grade: '',
  category: ''
}

const searchKeyword = ref('')

const openResourcePanel = (type) => {
  resourcePanel.visible = true
  resourcePanel.type = type
}

const closeResourcePanel = () => {
  resourcePanel.visible = false
}

const handleInsert = (resource) => {
  console.log('插入资源:', resource)
  // 插入到编辑器或目标位置
  insertResourceToEditor(resource)
  closeResourcePanel()
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示面板 | `boolean` | `false` |
| resourceType | 资源类型 | `'image' \| 'video' \| 'audio' \| 'document'` | `'image'` |
| filters | 筛选条件 | `ResourceFilters` | `{}` |
| searchKeyword | 搜索关键词 | `string` | `''` |
| width | 面板宽度 | `number` | `400` |

### ResourceFilters 类型定义

```typescript
interface ResourceFilters {
  subject?: string     // 学科
  grade?: string       // 年级
  category?: string    // 分类
  tag?: string[]       // 标签
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭面板时触发 | `() => void` |
| insert | 插入资源时触发 | `(resource: Resource) => void` |
| search | 搜索时触发 | `(keyword: string) => void` |
| filter | 筛选时触发 | `(filters: ResourceFilters) => void` |
| preview | 预览资源时触发 | `(resource: Resource) => void` |

## 功能特性

### 1. 资源分类
- 图片资源：支持 JPG、PNG、GIF 等格式
- 视频资源：支持 MP4、WebM 等格式
- 音频资源：支持 MP3、WAV 等格式
- 文档资源：支持 PDF、DOC、PPT 等格式

### 2. 搜索和筛选
```vue
<script setup>
const handleSearch = async (keyword) => {
  const resources = await searchResources({
    keyword,
    type: resourcePanel.type,
    ...resourceFilters
  })
  
  updateResourceList(resources)
}

const handleFilter = async (filters) => {
  const resources = await getResources({
    type: resourcePanel.type,
    ...filters
  })
  
  updateResourceList(resources)
}
</script>
```

### 3. 资源预览
- 图片：支持大图预览和缩放
- 视频：支持在线播放预览
- 音频：支持播放控制
- 文档：支持缩略图预览

## 使用场景

### 1. 课件编辑器

```vue
<template>
  <div class="courseware-editor">
    <div class="toolbar">
      <n-button @click="insertImage">插入图片</n-button>
      <n-button @click="insertVideo">插入视频</n-button>
    </div>
    
    <div class="editor-content">
      <!-- 编辑器内容 -->
    </div>
    
    <SideResource 
      :visible="resourcePanel.show"
      :resourceType="resourcePanel.type"
      @insert="insertToSlide"
      @close="resourcePanel.show = false"
    />
  </div>
</template>

<script setup>
const insertToSlide = (resource) => {
  // 插入到当前幻灯片
  slideEditor.insert(resource)
}
</script>
```

### 2. 试卷编辑

```vue
<template>
  <div class="paper-editor">
    <SideResource 
      :visible="showResourcePanel"
      resourceType="image"
      :filters="{ subject: currentSubject }"
      @insert="insertToPaper"
    />
  </div>
</template>

<script setup>
const insertToPaper = (resource) => {
  // 插入到试卷中
  paperEditor.insertImage(resource.url)
}
</script>
```

### 3. 备课资源管理

```vue
<template>
  <div class="lesson-prep">
    <SideResource 
      :visible="resourcePanelVisible"
      :resourceType="selectedResourceType"
      :filters="lessonFilters"
      @insert="addToLessonPlan"
    />
  </div>
</template>

<script setup>
const lessonFilters = computed(() => ({
  subject: lesson.value.subject,
  grade: lesson.value.grade,
  chapter: lesson.value.chapter
}))

const addToLessonPlan = (resource) => {
  lessonPlan.resources.push(resource)
}
</script>
```

## 样式定制

```css
.side-resource-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.side-resource-panel.visible {
  transform: translateX(0);
}

.resource-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.resource-content {
  padding: 16px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.resource-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.resource-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}
```

## 最佳实践

### 1. 性能优化

```vue
<script setup>
// 虚拟滚动处理大量资源
const visibleResources = computed(() => {
  const start = Math.max(0, scrollTop.value - bufferSize)
  const end = Math.min(resources.value.length, scrollTop.value + visibleCount + bufferSize)
  return resources.value.slice(start, end)
})

// 图片懒加载
const lazyLoadImages = () => {
  const images = document.querySelectorAll('.resource-image[data-src]')
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        img.src = img.dataset.src
        img.removeAttribute('data-src')
        imageObserver.unobserve(img)
      }
    })
  })
  
  images.forEach(img => imageObserver.observe(img))
}
</script>
```

### 2. 缓存策略

```vue
<script setup>
// 缓存资源列表
const resourceCache = new Map()

const getResources = async (params) => {
  const cacheKey = JSON.stringify(params)
  
  if (resourceCache.has(cacheKey)) {
    return resourceCache.get(cacheKey)
  }
  
  const resources = await api.getResources(params)
  resourceCache.set(cacheKey, resources)
  
  return resources
}
</script>
```

## 注意事项

1. **权限控制**: 确保用户有访问相应资源的权限
2. **文件大小**: 限制上传和插入的文件大小
3. **格式支持**: 检查资源格式的兼容性
4. **网络优化**: 使用 CDN 和压缩优化资源加载

## 更新日志

### v1.0.0
- 初始版本，支持基本的资源面板功能
- 支持多种资源类型和筛选搜索
- 集成预览和插入功能
