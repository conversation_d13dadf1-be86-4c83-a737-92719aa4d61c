# SlideLecture 幻灯片讲解

## 概述

SlideLecture 是一个专门用于幻灯片讲解的组件，支持课件浏览、文件夹管理、讲解模式等功能。适用于教师进行课件展示和教学讲解。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="openLecture">开始讲解</n-button>
    
    <SlideLecture 
      :visible="lectureVisible"
      :slideData="currentSlide"
      @close="handleClose"
      @slideChange="handleSlideChange"
    />
  </div>
</template>

<script setup>
import SlideLecture from '@/components/SlideLecture/index.vue'

const lectureVisible = ref(false)
const currentSlide = ref({
  id: 'slide-123',
  title: '二次函数的图像与性质',
  slides: [
    { id: '1', title: '引入', content: '...' },
    { id: '2', title: '定义', content: '...' },
    { id: '3', title: '性质', content: '...' }
  ]
})

const openLecture = () => {
  lectureVisible.value = true
}

const handleClose = () => {
  lectureVisible.value = false
}

const handleSlideChange = (slideIndex) => {
  console.log('切换到幻灯片:', slideIndex)
}
</script>
```

## 完整功能示例

```vue
<template>
  <div class="lecture-container">
    <div class="lecture-sidebar">
      <FolderBreadcrumb 
        :path="currentPath"
        @navigate="handleNavigate"
      />
      
      <FolderDetail 
        :folders="currentFolders"
        :slides="currentSlides"
        @folderSelect="handleFolderSelect"
        @slideSelect="handleSlideSelect"
      />
    </div>
    
    <div class="lecture-main">
      <SlideLecture 
        :visible="lectureMode"
        :slideData="selectedSlide"
        :lectureSettings="lectureConfig"
        @close="exitLecture"
        @slideChange="onSlideChange"
        @annotate="onAnnotate"
        @share="onShare"
      />
    </div>
  </div>
</template>

<script setup>
const lectureMode = ref(false)
const currentPath = ref([])
const currentFolders = ref([])
const currentSlides = ref([])
const selectedSlide = ref(null)

const lectureConfig = reactive({
  autoAdvance: false,
  showNotes: true,
  enableAnnotation: true,
  fullscreen: false
})

const handleFolderSelect = async (folderId) => {
  const folderData = await loadFolderContent(folderId)
  currentFolders.value = folderData.subfolders
  currentSlides.value = folderData.slides
  currentPath.value.push({ id: folderId, name: folderData.name })
}

const handleSlideSelect = (slide) => {
  selectedSlide.value = slide
  lectureMode.value = true
}

const onSlideChange = (slideIndex, slideData) => {
  // 记录讲解进度
  recordLectureProgress(selectedSlide.value.id, slideIndex)
  
  // 同步到学生端（如果是在线课堂）
  if (isOnlineClass.value) {
    syncSlideToStudents(slideIndex)
  }
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示讲解模式 | `boolean` | `false` |
| slideData | 幻灯片数据 | `SlideData` | - |
| lectureSettings | 讲解设置 | `LectureSettings` | `{}` |
| currentSlide | 当前幻灯片索引 | `number` | `0` |
| enableAnnotation | 是否启用标注 | `boolean` | `true` |
| showThumbnails | 是否显示缩略图 | `boolean` | `true` |

### SlideData 类型定义

```typescript
interface SlideData {
  id: string                    // 幻灯片ID
  title: string                 // 标题
  description?: string          // 描述
  slides: SlideItem[]          // 幻灯片列表
  totalSlides: number          // 总页数
  createdAt: string            // 创建时间
  updatedAt: string            // 更新时间
}

interface SlideItem {
  id: string                   // 幻灯片页面ID
  title: string                // 页面标题
  content: string              // 页面内容
  notes?: string               // 讲解备注
  duration?: number            // 建议讲解时长
  annotations?: Annotation[]   // 标注数据
}
```

### LectureSettings 类型定义

```typescript
interface LectureSettings {
  autoAdvance?: boolean        // 自动翻页
  advanceInterval?: number     // 翻页间隔（秒）
  showNotes?: boolean          // 显示备注
  enableAnnotation?: boolean   // 启用标注
  fullscreen?: boolean         // 全屏模式
  theme?: 'light' | 'dark'     // 主题
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭讲解模式时触发 | `() => void` |
| slideChange | 幻灯片切换时触发 | `(slideIndex: number, slideData: SlideItem) => void` |
| annotate | 添加标注时触发 | `(annotation: Annotation) => void` |
| share | 分享幻灯片时触发 | `(shareData: ShareData) => void` |
| fullscreen | 全屏状态变化时触发 | `(isFullscreen: boolean) => void` |

## 功能特性

### 1. 文件夹管理
- 支持多级文件夹结构
- 面包屑导航
- 文件夹和幻灯片的混合展示

### 2. 讲解模式
```vue
<script setup>
// 自动翻页控制
const autoAdvanceTimer = ref(null)

const startAutoAdvance = () => {
  if (lectureSettings.autoAdvance) {
    autoAdvanceTimer.value = setInterval(() => {
      if (currentSlideIndex.value < totalSlides.value - 1) {
        nextSlide()
      } else {
        stopAutoAdvance()
      }
    }, lectureSettings.advanceInterval * 1000)
  }
}

const stopAutoAdvance = () => {
  if (autoAdvanceTimer.value) {
    clearInterval(autoAdvanceTimer.value)
    autoAdvanceTimer.value = null
  }
}

// 讲解备注显示
const showLectureNotes = computed(() => {
  return lectureSettings.showNotes && currentSlide.value?.notes
})
</script>
```

### 3. 标注功能
- 支持在幻灯片上添加标注
- 支持多种标注类型（文字、箭头、高亮等）
- 标注数据的保存和恢复

### 4. 缩略图导航
- 显示所有幻灯片的缩略图
- 快速跳转到指定幻灯片
- 显示当前讲解进度

## 使用场景

### 1. 课堂教学

```vue
<template>
  <div class="classroom-teaching">
    <SlideLecture 
      :visible="teachingMode"
      :slideData="lessonSlides"
      :lectureSettings="{
        showNotes: true,
        enableAnnotation: true,
        fullscreen: true
      }"
      @slideChange="recordTeachingProgress"
      @annotate="saveTeacherAnnotation"
    />
  </div>
</template>

<script setup>
const recordTeachingProgress = (slideIndex) => {
  // 记录教学进度
  updateLessonProgress(lessonId.value, slideIndex)
  
  // 发送给学生端
  broadcastToStudents('slide_change', { slideIndex })
}

const saveTeacherAnnotation = (annotation) => {
  // 保存教师标注
  annotations.value.push({
    ...annotation,
    teacherId: teacher.value.id,
    timestamp: Date.now()
  })
}
</script>
```

### 2. 在线直播课

```vue
<template>
  <div class="online-lecture">
    <SlideLecture 
      :visible="liveMode"
      :slideData="liveSlides"
      :lectureSettings="{
        autoAdvance: false,
        showNotes: false,
        enableAnnotation: true
      }"
      @slideChange="syncToLiveStream"
    />
  </div>
</template>

<script setup>
const syncToLiveStream = (slideIndex, slideData) => {
  // 同步到直播流
  liveStreamAPI.updateSlide({
    slideIndex,
    slideContent: slideData.content,
    timestamp: Date.now()
  })
}
</script>
```

### 3. 课件预览

```vue
<template>
  <div class="slide-preview">
    <SlideLecture 
      :visible="previewMode"
      :slideData="previewSlides"
      :lectureSettings="{
        showNotes: false,
        enableAnnotation: false,
        fullscreen: false
      }"
      @close="exitPreview"
    />
  </div>
</template>

<script setup>
const previewSlides = computed(() => ({
  ...originalSlides.value,
  slides: originalSlides.value.slides.map(slide => ({
    ...slide,
    notes: undefined // 预览模式不显示备注
  }))
}))
</script>
```

## 样式定制

```css
.slide-lecture-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 1000;
}

.lecture-main {
  display: flex;
  height: 100%;
}

.slide-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.slide-item {
  max-width: 90%;
  max-height: 90%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.slide-thumbnails {
  width: 200px;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  overflow-y: auto;
}

.thumbnail-item {
  width: 160px;
  height: 90px;
  background: white;
  border-radius: 4px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.thumbnail-item.active {
  border-color: #1890ff;
  box-shadow: 0 0 12px rgba(24, 144, 255, 0.5);
}

.thumbnail-item:hover {
  transform: scale(1.05);
}

.lecture-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  background: rgba(0, 0, 0, 0.7);
  padding: 12px 20px;
  border-radius: 25px;
}

.control-button {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.lecture-notes {
  position: absolute;
  bottom: 80px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
}
```

## 最佳实践

### 1. 性能优化

```vue
<script setup>
// 幻灯片预加载
const preloadSlides = (currentIndex) => {
  const preloadRange = 2
  const startIndex = Math.max(0, currentIndex - preloadRange)
  const endIndex = Math.min(slides.value.length - 1, currentIndex + preloadRange)
  
  for (let i = startIndex; i <= endIndex; i++) {
    if (!slideCache.has(i)) {
      loadSlideContent(i)
    }
  }
}

// 缩略图懒加载
const loadThumbnail = (slideIndex) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(img.src)
    img.src = generateThumbnailUrl(slideIndex)
  })
}
</script>
```

### 2. 状态管理

```vue
<script setup>
// 讲解状态持久化
const saveLectureState = () => {
  const state = {
    slideId: slideData.value.id,
    currentSlide: currentSlideIndex.value,
    annotations: annotations.value,
    settings: lectureSettings
  }
  
  localStorage.setItem('lecture-state', JSON.stringify(state))
}

// 恢复讲解状态
const restoreLectureState = () => {
  const saved = localStorage.getItem('lecture-state')
  if (saved) {
    const state = JSON.parse(saved)
    currentSlideIndex.value = state.currentSlide
    annotations.value = state.annotations
    Object.assign(lectureSettings, state.settings)
  }
}
</script>
```

### 3. 键盘快捷键

```vue
<script setup>
const handleKeydown = (event) => {
  if (!visible.value) return
  
  switch (event.key) {
    case 'ArrowRight':
    case ' ':
      nextSlide()
      break
    case 'ArrowLeft':
      previousSlide()
      break
    case 'f':
    case 'F':
      toggleFullscreen()
      break
    case 'n':
    case 'N':
      toggleNotes()
      break
    case 'Escape':
      if (isFullscreen.value) {
        exitFullscreen()
      } else {
        closeLecture()
      }
      break
  }
}
</script>
```

## 注意事项

1. **文件管理**: 确保幻灯片文件的正确组织和访问权限
2. **性能考虑**: 大量幻灯片时注意内存使用和加载性能
3. **网络依赖**: 在线模式需要稳定的网络连接
4. **兼容性**: 确保在不同设备和浏览器上的兼容性

## 更新日志

### v1.0.0
- 初始版本，支持基本的幻灯片讲解功能
- 集成文件夹管理和标注功能
- 支持多种讲解模式和设置选项
