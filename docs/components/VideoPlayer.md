# VideoPlayer 视频播放器

## 概述

VideoPlayer 是一个功能完整的视频播放器组件，基于洋葱视频播放器封装，支持多种视频格式、交互功能、字幕、关键点等高级特性。

## 基本用法

```vue
<template>
  <div>
    <VideoPlayer 
      :videoId="videoId"
      :baseVideo="videoConfig"
      @init="handlePlayerInit"
      @end="handleVideoEnd"
    />
  </div>
</template>

<script setup>
import VideoPlayer from '@/components/VideoPlayer/index.vue'

const videoId = ref('video-123')
const videoConfig = {
  autoplay: false,
  controls: true,
  width: 800,
  height: 450,
  poster: 'https://example.com/poster.jpg'
}

const handlePlayerInit = (player) => {
  console.log('播放器初始化完成', player)
}

const handleVideoEnd = (currentTime, playingTime) => {
  console.log('视频播放结束', { currentTime, playingTime })
}
</script>
```

## 完整配置示例

```vue
<template>
  <div>
    <VideoPlayer 
      :videoId="videoId"
      :baseVideo="baseVideoConfig"
      :interactions="interactions"
      :keyPoints="keyPoints"
      :interactionImages="interactionImages"
      :customVideo="customVideo"
      :handleDownload="handleDownload"
      @init="handleInit"
      @play="handlePlay"
      @pause="handlePause"
      @end="handleEnd"
      @timeupdate="handleTimeUpdate"
    />
  </div>
</template>

<script setup>
const baseVideoConfig = {
  autoplay: false,
  controls: true,
  fluid: true,
  width: 800,
  height: 450,
  poster: 'poster.jpg',
  startTime: 0,
  guide: true,
  shareButton: true,
  downloadButton: true,
  fullScreenButton: true,
  videoMarkButton: true,
  clarityButton: true,
  clarityButtonHint: '清晰度选择'
}

const interactions = ref([
  {
    id: '1',
    time: 30,
    type: 'question',
    content: '这里是一个问题'
  }
])

const keyPoints = ref([
  {
    id: '1',
    time: 60,
    title: '重点内容',
    description: '这是一个重点'
  }
])
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| videoId | 视频ID | `string` | - |
| baseVideo | 基础视频配置 | `BaseVideoConfig` | - |
| interactions | 交互数据 | `Interaction[]` | `[]` |
| keyPoints | 关键点数据 | `KeyPoint[]` | `[]` |
| interactionImages | 交互图片 | `InteractionImage[]` | `[]` |
| customVideo | 自定义视频配置 | `CustomVideo` | - |
| handleDownload | 下载处理函数 | `Function` | - |

### BaseVideoConfig 类型定义

```typescript
interface BaseVideoConfig {
  autoplay?: boolean          // 自动播放
  controls?: boolean          // 显示控制条
  fluid?: boolean            // 流式布局
  width?: number             // 宽度
  height?: number            // 高度
  poster?: string            // 封面图
  startTime?: number         // 开始时间
  guide?: boolean            // 显示引导
  shareButton?: boolean      // 分享按钮
  downloadButton?: boolean   // 下载按钮
  fullScreenButton?: boolean // 全屏按钮
  videoMarkButton?: boolean  // 标记按钮
  clarityButton?: boolean    // 清晰度按钮
  clarityButtonHint?: string // 清晰度提示
  customArgs?: any           // 自定义参数
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| init | 播放器初始化完成 | `(player: any) => void` |
| play | 开始播放 | `(currentTime: number, playingTime: number) => void` |
| pause | 暂停播放 | `(currentTime: number, playingTime: number) => void` |
| end | 播放结束 | `(currentTime: number, playingTime: number) => void` |
| timeupdate | 时间更新 | `(currentTime: number, playingTime: number) => void` |

## 功能特性

### 1. 多格式支持
- 支持 MP4、WebM、OGG 等主流视频格式
- 自动选择最佳播放源
- 支持多清晰度切换

### 2. 交互功能
```vue
<script setup>
const interactions = ref([
  {
    id: '1',
    time: 30,
    type: 'question',
    content: '请回答这个问题',
    options: ['选项A', '选项B', '选项C']
  },
  {
    id: '2',
    time: 120,
    type: 'note',
    content: '这里是重要提示'
  }
])
</script>
```

### 3. 关键点标记
```vue
<script setup>
const keyPoints = ref([
  {
    id: '1',
    time: 60,
    title: '重点1',
    description: '这是第一个重点内容',
    thumbnail: 'thumb1.jpg'
  },
  {
    id: '2',
    time: 180,
    title: '重点2',
    description: '这是第二个重点内容',
    thumbnail: 'thumb2.jpg'
  }
])
</script>
```

### 4. 字幕支持
- 自动加载字幕文件
- 支持多语言字幕
- 字幕样式自定义

## 使用场景

### 1. 课程视频播放

```vue
<template>
  <div class="course-video">
    <VideoPlayer 
      :videoId="courseVideo.id"
      :baseVideo="{
        autoplay: false,
        controls: true,
        guide: true,
        shareButton: true
      }"
      :keyPoints="courseKeyPoints"
      @end="handleCourseEnd"
    />
  </div>
</template>
```

### 2. 微课视频

```vue
<template>
  <div class="micro-video">
    <VideoPlayer 
      :videoId="microVideoId"
      :baseVideo="{
        autoplay: true,
        fluid: true,
        videoMarkButton: true
      }"
      :interactions="microInteractions"
    />
  </div>
</template>
```

### 3. 直播回放

```vue
<template>
  <div class="live-replay">
    <VideoPlayer 
      :videoId="liveReplayId"
      :baseVideo="{
        controls: true,
        fullScreenButton: true,
        clarityButton: true
      }"
      @timeupdate="updateProgress"
    />
  </div>
</template>
```

## 最佳实践

### 1. 性能优化

```vue
<script setup>
// 懒加载视频播放器
const shouldLoadPlayer = ref(false)

onMounted(() => {
  // 当视频进入视口时再加载
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      shouldLoadPlayer.value = true
      observer.disconnect()
    }
  })
  
  observer.observe(videoContainer.value)
})
</script>
```

### 2. 错误处理

```vue
<script setup>
const handlePlayerError = (error) => {
  console.error('视频播放错误:', error)
  message.error('视频加载失败，请刷新重试')
}

const handleInit = (player) => {
  player.on('error', handlePlayerError)
}
</script>
```

### 3. 播放统计

```vue
<script setup>
const playStats = reactive({
  totalTime: 0,
  playedTime: 0,
  playCount: 0
})

const handleTimeUpdate = (currentTime, playingTime) => {
  playStats.playedTime = playingTime
  
  // 每10秒上报一次播放进度
  if (currentTime % 10 === 0) {
    reportPlayProgress(videoId.value, currentTime)
  }
}
</script>
```

## 注意事项

1. **资源管理**: 组件销毁时会自动清理播放器资源
2. **网络适配**: 根据网络状况自动调整视频质量
3. **移动端适配**: 在移动设备上会使用原生播放器
4. **权限控制**: 某些功能需要相应的用户权限

## 更新日志

### v1.0.0
- 初始版本，支持基本的视频播放功能
- 集成交互、关键点、字幕等高级特性
- 支持多种播放模式和自定义配置
