# SideTools 侧边工具栏

## 概述

SideTools 是一个侧边工具栏组件，提供常用的工具按钮和快捷操作。通常固定在页面右侧，包含回到顶部、意见反馈、帮助等功能。

## 基本用法

```vue
<template>
  <div>
    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 内容 -->
    </div>
    
    <!-- 侧边工具栏 -->
    <SideTools 
      :tools="toolsConfig"
      @toolClick="handleToolClick"
    />
  </div>
</template>

<script setup>
import SideTools from '@/components/SideTools/index.vue'

const toolsConfig = [
  { key: 'feedback', icon: 'feedback', title: '意见反馈' },
  { key: 'help', icon: 'help', title: '帮助中心' },
  { key: 'top', icon: 'top', title: '回到顶部' }
]

const handleToolClick = (toolKey) => {
  switch (toolKey) {
    case 'feedback':
      openFeedbackModal()
      break
    case 'help':
      openHelpCenter()
      break
    case 'top':
      scrollToTop()
      break
  }
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tools | 工具配置数组 | `ToolConfig[]` | `[]` |
| position | 位置设置 | `'right' \| 'left'` | `'right'` |
| offset | 偏移量 | `{ top?: number, right?: number }` | `{ top: 200, right: 20 }` |

### ToolConfig 类型定义

```typescript
interface ToolConfig {
  key: string        // 工具唯一标识
  icon: string       // 图标名称
  title: string      // 工具提示文本
  badge?: number     // 徽章数字
  disabled?: boolean // 是否禁用
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| toolClick | 点击工具按钮时触发 | `(toolKey: string) => void` |

## 使用场景

### 1. 通用侧边工具栏

```vue
<template>
  <SideTools 
    :tools="commonTools"
    @toolClick="handleCommonTool"
  />
</template>

<script setup>
const commonTools = [
  { key: 'feedback', icon: 'message', title: '意见反馈' },
  { key: 'help', icon: 'question', title: '帮助' },
  { key: 'top', icon: 'arrow-up', title: '回到顶部' }
]
</script>
```

### 2. 编辑器工具栏

```vue
<template>
  <SideTools 
    :tools="editorTools"
    position="left"
    @toolClick="handleEditorTool"
  />
</template>

<script setup>
const editorTools = [
  { key: 'save', icon: 'save', title: '保存' },
  { key: 'preview', icon: 'eye', title: '预览' },
  { key: 'history', icon: 'history', title: '历史记录' }
]
</script>
```

## 样式定制

```css
.side-tools {
  position: fixed;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-item {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

## 更新日志

### v1.0.0
- 初始版本，支持基本的侧边工具栏功能
- 支持自定义工具配置和位置设置
