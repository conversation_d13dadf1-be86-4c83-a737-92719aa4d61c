{"name": "teacher-workbench", "private": true, "version": "0.0.2", "type": "module", "scripts": {"dev": "vue-tsc --noEmit && vite --host", "build:test": "cross-env NODE_ENV=production vite build --mode test", "build:stage": "cross-env NODE_ENV=production vite build --mode stage", "build": "cross-env NODE_ENV=production vite build --mode production", "preview": "vite preview", "docs:dev": "node scripts/copy-assets.js && vitepress dev docs", "docs:build": "node scripts/copy-assets.js && vitepress build docs", "docs:preview": "vitepress preview docs --port 6175 --host 0.0.0.0", "docs:clean": "rm -rf docs/.vitepress/dist docs/.vitepress/cache", "docs:check": "pnpm run docs:build && echo '✅ 文档构建检查通过'", "docs:watch": "node scripts/watch-docs.js", "docs:template": "node scripts/generate-doc-template.js", "copy-assets": "node scripts/copy-assets.js", "lint-fix": "npm run stylelint-fix && npm run eslint-fix && npm run prettier-fix", "lint": "cross-env NODE_ENV=production npm run stylelint && npm run eslint && npm run prettier", "prettier-fix": "npx prettier --write .", "prettier": "prettier -l .", "lint-staged": "lint-staged", "eslint-fix": "eslint --fix --ext .js,.ts,.tsx,.jsx,.vue .", "eslint": "eslint --ext .js,.ts,.tsx,.jsx,.vue .", "stylelint-fix": "stylelint --fix 'src/**/*.{css,scss,vue,html}'", "stylelint": "stylelint 'src/**/*.{css,scss,vue,html}'", "prepare": "husky install", "class": "echo '请使用这串字符作为全局样式class名的后缀：' && node -p 'Date.now().toString(36)'"}, "dependencies": {"@guanghe-pub/onion-problem-render": "1.4.11-beta.4", "@guanghe-pub/onion-ui-web": "2.0.0-beta.3", "@guanghe-pub/onion-utils": "2.16.2", "@guanghe-pub/yc-pc-upload-vue": "^0.3.5", "@guanghe-pub/yc-upload": "^0.3.3", "@guanghe/yc-pc-player-vue": "1.0.12-beta.2", "@icon-park/vue-next": "^1.4.2", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@vueuse/core": "^10.11.0", "axios": "^1.7.2", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.5.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "katex": "0.16.0", "lodash-es": "^4.17.21", "naive-ui": "^2.38.2", "pdfjs-dist": "2.12.313", "pinia": "^2.1.7", "qrcode": "^1.5.4", "qs": "^6.14.0", "ua-parser-js": "^1.0.33", "uuid": "^10.0.0", "vue": "^3.4.29", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.4.0", "vue3-lottie": "^3.3.1", "vueuc": "^0.4.64"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "18.4.3", "@guanghe-pub/eslint-config-vue3": "^0.0.2-beta.28", "@guanghe-pub/onion-oss-vite-plugin": "0.3.4-beta.0", "@guanghe-pub/stylelint-config": "^0.0.2-beta.28", "@postcss-plugins/console": "^0.2.5", "@types/eslint": "8.56.0", "@types/file-saver": "^2.0.7", "@types/jszip": "^3.4.1", "@types/node": "^20.14.9", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "commitizen": "^4.3.0", "commitlint": "18.4.3", "core-js": "^3.42.0", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-conventional-changelog-zh": "^0.0.2", "cz-customizable": "^7.0.0", "eslint": "8.56.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.1", "husky": "8.0.3", "lint-staged": "15.2.0", "postcss": "^8.4.39", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "3.1.1", "sass": "^1.77.6", "stylelint": "15.11.0", "terser": "^5.31.1", "typescript": "^5.2.2", "unocss": "^0.61.0", "unplugin-auto-import": "^0.17.6", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vitepress": "^1.6.3", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.0.21"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog-zh"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "iOS >= 9", "Chrome >= 37"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0", "pnpm": ">=8.0.0", "yarn": ">=1.22.0"}}