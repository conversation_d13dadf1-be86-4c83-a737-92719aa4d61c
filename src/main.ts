import { createApp, markRaw } from 'vue'
import '@/styles/index.scss'
import App from './App.vue'
import { createPinia } from 'pinia'
import router from '@/routers/index'
import 'uno.css'
import '@guanghe-pub/onion-ui-web/dist/onion-ui-web.min.css'
import { createWindowOpenProxy, getThirdPartyHandle } from '@/utils/windowOpen'
import '@/utils/electronBridge.util'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

const piniaStore = createPinia()

piniaStore.use(({ store }) => {
  store.router = markRaw(router)
})

// 初始化 window.open 代理
createWindowOpenProxy({
  beforeOpen: (url, target, features) => {
    return getThirdPartyHandle(url, target, features)
  },
})

createApp(App).use(router).use(piniaStore).mount('#app')
