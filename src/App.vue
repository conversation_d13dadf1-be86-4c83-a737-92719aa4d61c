<template>
  <NConfigProvider
    :locale="zhCN"
    :theme-overrides="themeOverrides"
    :date-locale="dateZhCN"
    inline-theme-disabled
  >
    <OIWDialogProvider>
      <OIWMessageProvider>
        <NModalProvider>
          <NDialogProvider>
            <ShareProvide>
              <RouterView />
              <SideTools />
            </ShareProvide>
          </NDialogProvider>
        </NModalProvider>
      </OIWMessageProvider>
    </OIWDialogProvider>
  </NConfigProvider>
</template>

<script setup lang="ts">
import { zhCN, dateZhCN, NConfigProvider, NModalProvider } from 'naive-ui'
import {
  OIWDialogProvider,
  OIWMessageProvider,
} from '@guanghe-pub/onion-ui-web'
import { themeOverrides } from '@/utils/themeOver'
import useAuthStore from '@/store/auth'
import useCvsStore from '@/store/csv'
import ShareProvide from '@/components/ShareModal/ShareProvide'
import SideTools from '@/components/SideTools/index.vue'

const authStore = useAuthStore()
const cvsStore = useCvsStore()
if (authStore.token) {
  authStore.getAIEntry()
  authStore.getAIClassAuth()
  authStore.getTeacherEEV2()
  authStore.fetchUserAuth()
  cvsStore.getCvsEnum()
}
</script>

<style lang="scss">
html {
  /** uncocss 基准单位1rem = 1px
   */
  font-size: 4px;
}

body {
  font-size: 16px;
}

.n-modal-mask {
  background-color: rgba(0, 0, 0, 0.7);
}

.button-group {
  display: inline-flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;
  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.home-nav-item-title {
  width: 160px;
  background-color: #fff;
  border: 1px solid #efeef3;
  border-radius: 12px;
  box-shadow: 0 4px 4px 0 rgba(151, 151, 151, 0.1) !important;

  ::v-deep(.n-popover__content) {
    width: 160px;
    background: #ffffff;
    border: 1px solid #efeef3;
    box-shadow: 0 4px 4px 0 rgba(151, 151, 151, 0.1);
  }
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease-out;

  &.active {
    font-weight: 500;
    color: #fff !important;
    background: #5e80ff;
    border-radius: 12px 12px 12px 12px;

    .dropdown-arrow {
      width: 16px;
      height: 16px;
      background: url('https://fp.yangcong345.com/onion-extension/xsz-170083cfa129f8c1924ad3ad644b8b86.png')
        no-repeat !important;
      background-size: contain !important;
    }
  }

  &:hover {
    color: #5e80ff;

    .dropdown-arrow {
      width: 16px;
      height: 16px;
      background: url('https://fp.yangcong345.com/onion-extension/dssdsd-31c08d9edbe936e1097da53b12ced8d2.png')
        no-repeat;
      background-size: contain;
      transition: all 0.3s ease;
      transform: rotate(180deg);
    }
  }

  .dropdown-arrow {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background: url('https://fp.yangcong345.com/onion-extension/dsd-a7cc3cbf076fc2dce0199efe294dc01b.png')
      no-repeat;
    background-size: contain;
    transition: all 0.3s ease;
  }

  &:not(:first-of-type) {
    margin-left: 12px;
  }
}

.nav-sub-list {
  padding: 8px;
  overflow: hidden;

  .nav-sub {
    padding: 16px 12px;
    font-size: 16px;
    font-weight: 400;
    color: #393548;
    cursor: pointer;
    transition: all 0.2s ease-out;

    a {
      font-weight: 400;
      color: #393548;
    }

    &.active {
      color: #393548;
      // background: #F4F6FF;
      border-radius: 12px;
    }

    &:hover {
      color: #393548;
      background: #f4f6ff;
      border-radius: 12px;
    }

    &.logout:hover {
      color: #fa5a65;
      background: #feeeef;
    }
  }
}
</style>
