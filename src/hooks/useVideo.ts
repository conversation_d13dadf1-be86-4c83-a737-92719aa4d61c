import {
  systemLessonPermission,
  specialCoursePermission,
} from '@/hooks/usePermission'
import {
  postSystemTopicDetails,
  postSpecialTopicDetails,
} from '@/pages/microVideo/service'
import { postSchoolVideoListByIdsApi } from '@/pages/schoolResource/schoolVideo/service'
import type { TopicDetail } from '@/pages/microVideo/service'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'

export interface Preview {
  id?: string
  sourceType?: string
  resourceType?: string
  /**
   * 资源子类型，非必需，如用于知识点/微课来区分系统课和培优课，system：系统课、special：培优课、system-point：系统课视频片段.
   */
  resourceSubType?: string
  resourceId?: string
  /**
   * 资源子id，如视频片段，resource_id存储知识点id，sub_id存储片段id.
   */
  resourceSubId?: string
  resourceName?: string
  userId?: string
  subject?: YcType.CsvId
  stage?: YcType.CsvId
  semester?: YcType.CsvId
  publisher?: YcType.CsvId
  createdAt?: string
  /**
   * 扩展字段，非必须，用于存储如视频片段时间点位等.
   */
  extra?: Record<string, Record<string, unknown>>
  [k: string]: unknown
}

/**
 * 为了通用使得最不可能的自动先出现，例如resourceSubType
 * @param videoSimple
 * @returns
 */
export const isSchoolVideo = (videoSimple: Preview) => {
  const type =
    videoSimple.resourceSubType?.toLowerCase() ||
    videoSimple.resourceType?.toLowerCase() ||
    videoSimple.sourceType?.toLowerCase()
  return type && type.includes('school')
}

export const isSpecialVideo = (videoSimple: Preview) => {
  const type =
    videoSimple.resourceSubType?.toLowerCase() ||
    videoSimple.resourceType?.toLowerCase() ||
    videoSimple.sourceType?.toLowerCase()
  return type && type.includes('special')
}

export const isSystemVideo = (videoSimple: Preview) => {
  const type =
    videoSimple.resourceSubType?.toLowerCase() ||
    videoSimple.resourceType?.toLowerCase() ||
    videoSimple.sourceType?.toLowerCase()
  return type && type.includes('system')
}

export const useVideo = () => {
  /**
   * @description: 获取视频详情
   * @description: 由于会覆盖原id字段，请使用originalIdName参数
   * @param {Preview} videoSimples
   * @param {string} originalIdName
   * @return {*}
   */
  async function fetchVideoDetails<
    T extends Preview & Record<string, any>,
    R = TopicDetail | SchoolVideoDetail,
  >(videoSimples: T[], originalIdName: string): Promise<R[]> {
    if (videoSimples.length === 0) {
      return []
    }

    let schoolIds =
      videoSimples
        .filter((item) => isSchoolVideo(item))
        .map((item) => item.resourceId || item.id) || []
    let systemIds =
      videoSimples
        .filter((item) => isSystemVideo(item))
        .map((item) => item.resourceId || item.id) || []
    let specialIds =
      videoSimples
        .filter((item) => isSpecialVideo(item))
        .map((item) => item.resourceId || item.id) || []
    schoolIds = [...new Set(schoolIds)]
    systemIds = [...new Set(systemIds)]
    specialIds = [...new Set(specialIds)]

    const [schoolVideos, systemTopics, specialTopics] = await Promise.all([
      schoolIds.length > 0
        ? postSchoolVideoListByIdsApi({ ids: schoolIds as string[] })
            .then((res) => {
              return res && res.schoolVideos
            })
            .catch(() => {
              return []
            })
        : Promise.resolve([]),
      systemIds.length > 0
        ? postSystemTopicDetails({ topicIds: systemIds as string[] })
            .then((res) => {
              return res && res.data
            })
            .catch(() => {
              return []
            })
        : Promise.resolve([]),
      specialIds.length > 0
        ? postSpecialTopicDetails({ topicIds: specialIds as string[] })
            .then((res) => {
              return res && res.data
            })
            .catch(() => {
              return []
            })
        : Promise.resolve([]),
    ])
    const resultListData = []
    for (const item of videoSimples) {
      if (originalIdName) {
        item[originalIdName as keyof T] = item.id as any
      }
      if (isSystemVideo(item)) {
        const auth = await systemLessonPermission({
          subjectId: item.subject || '',
          stageId: item.stage || '',
        })

        resultListData.push({
          ...item,
          auth,
          ...systemTopics?.find(
            (i: TopicDetail) => i.id === (item.resourceId || item.id),
          ),
        } as TopicDetail)
      } else if (item.resourceSubType === 'special') {
        const auth = await specialCoursePermission(item.resourceSubId as string)
        resultListData.push({
          ...item,
          auth,
          ...specialTopics?.find(
            (i: TopicDetail) => i.id === (item.resourceId || item.id),
          ),
        } as TopicDetail)
      } else if (item.resourceSubType === 'school') {
        resultListData.push({
          ...item,
          ...schoolVideos.find(
            (i: SchoolVideoDetail) => i.id === (item.resourceId || item.id),
          ),
        } as SchoolVideoDetail)
      } else {
        resultListData.push({
          ...item,
        } as any)
      }
    }
    return resultListData
  }

  return {
    fetchVideoDetails,
  }
}
