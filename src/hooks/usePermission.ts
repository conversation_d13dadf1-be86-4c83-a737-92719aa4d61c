import useAuthStore from '@/store/auth'
import { useSpecialCourseInfo } from '@/hooks/useSpecialCourseInfoWithCache.ts'
import type { AuthData, UserAuth } from '@/service/auth'
// import type { TopicDetail } from '@/pages/microVideo/service.ts'

interface UserPermissionParams {
  stageId: YcType.CsvId
  subjectId: YcType.CsvId
  // topic: TopicDetail
}

interface PermissionQuery {
  stageId: YcType.CsvId
  subjectId: YcType.CsvId
  auth?: UserAuth
  orderAuth?: AuthData
}

export const PermissionResult = ({
  stageId,
  subjectId,
  auth,
  orderAuth,
}: PermissionQuery) => {
  if (
    orderAuth?.vip.some(
      (vip) => vip.id === `vip#${stageId}-${subjectId}` && !vip.expired,
    )
  ) {
    return true
  }

  const Permission = auth?.permissions.find(
    (Permission) => Permission.name === 'vip' && Permission.enabled,
  )
  if (
    Permission?.stageSubjects?.some(
      (el) =>
        el.stageId === String(stageId) && el.subjectId === String(subjectId),
    )
  ) {
    return true
  }

  return false
}

export const systemLessonPermission = async (
  params: UserPermissionParams,
): Promise<boolean> => {
  const authStore = useAuthStore()

  await Promise.all([authStore.fetchOrderAuth(), authStore.fetchUserAuth()])

  const stageId = params.stageId
  const subjectId = params.subjectId

  return PermissionResult({
    stageId,
    subjectId,
    auth: authStore.userAuth,
    orderAuth: authStore.orderAuth,
  })
}

// 新版培优课鉴权
export const specialCoursePermission = async (specialCourseId: string) => {
  if (!specialCourseId) {
    return false
  }
  const authStore = useAuthStore()
  await Promise.all([authStore.fetchUserSpcicalCourseList()])
  const hasSpecialCourse =
    authStore.userSpecialCourse &&
    authStore.userSpecialCourse.some((el) => el.id === specialCourseId)
  // console.log('hasSpecialCourse>>>', hasSpecialCourse)
  if (hasSpecialCourse) {
    return true
  }
  const { getSpecialCourseInfo } = useSpecialCourseInfo()
  const info = await getSpecialCourseInfo(specialCourseId)
  // console.log('info>>>', info)
  let hasSystemAuth = false
  if (info) {
    const { stageIds, subjectIds } = info
    // 将培优课的学科ID数组与学段ID数组，分别枚举配对，与同步课权限进行校验，如果有权限则返回true
    if (
      !stageIds ||
      !subjectIds ||
      stageIds.length === 0 ||
      subjectIds.length === 0
    ) {
      return hasSystemAuth
    }
    for (const stageId of stageIds) {
      for (const subjectId of subjectIds) {
        hasSystemAuth = await systemLessonPermission({ stageId, subjectId })
        if (hasSystemAuth) {
          return true
        }
      }
    }
  }
  // console.log('hasSystemAuth>>>', hasSystemAuth)
  return hasSystemAuth
}

// 精品课件权限
export const couserwareFineAuth = async ({
  stageId,
  subjectId,
}: {
  stageId: number
  subjectId: number
}) => {
  const authStore = useAuthStore()
  await authStore.fetchCouserwareAuth({ stageId, subjectId })
  return authStore.userCouserwareAuth
}

// 精品试卷权限
export const examBestAuth = async (
  pkgIds: string[],
  stageId: YcType.CsvId,
  subjectId: YcType.CsvId,
) => {
  const authStore = useAuthStore()
  await authStore.fetchOrderAuth()
  const examPaperIds = authStore.orderAuth?.examPaper.map((vip) => vip.id) || []
  const examPaperAuth = examPaperIds.some((id) => pkgIds.includes(id))
  const vipAuth = PermissionResult({
    stageId,
    subjectId,
    auth: authStore.userAuth,
    orderAuth: authStore.orderAuth,
  })
  return examPaperAuth || vipAuth
}
