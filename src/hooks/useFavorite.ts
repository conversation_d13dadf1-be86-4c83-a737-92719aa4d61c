import type {
  AddFavoriteBody,
  DelFavoriteQuery,
  Favorite<PERSON>tem,
  FavoriteListQuery,
} from '@/service/common'
import {
  addFavoriteApi,
  delFavoriteApi,
  getFavoriteApi,
} from '@/service/common'
import { useAuth } from './useAuth'
import type { Ref } from 'vue'
import { debounce } from 'lodash-es'
import { useLoading } from './useLoading'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type AddFavoriteParams = Optional<
  AddFavoriteBody,
  'extra' | 'userId' | 'subject' | 'stage' | 'semester' | 'publisher'
>

type FilterQuery = Omit<FavoriteListQuery, 'page' | 'pageSize'>

interface FilterOptions {
  initFetch?: boolean
  pageSize?: number
}

export const useAddFavorite = () => {
  const { userId } = useAuth()
  const message = useOIWMessage()
  async function addFavorite(params: AddFavoriteParams) {
    await addFavoriteApi({
      ...params,
      userId: userId.value!,
    })
    console.log('params', params)
    const resourceName = params.resourceType.includes('problem')
      ? '题目'
      : params.resourceType.includes('paper')
        ? '试卷'
        : '微课'
    message.success(`${resourceName}收藏成功，请前往我的收藏中查看`)
  }

  async function deleteFavorite(params: DelFavoriteQuery) {
    await delFavoriteApi(params)
    message.success('已取消收藏')
  }

  return {
    addFavorite,
    deleteFavorite,
  }
}

export const useFavorite = (
  params: Ref<FilterQuery>,
  options?: FilterOptions,
) => {
  const page = ref(1)
  const pageSize = options?.pageSize || 10
  const total = ref(0)
  const favorites = ref<FavoriteItem[]>([])
  const { loading, startLoading, endLoading } = useLoading(true)

  async function fetch() {
    startLoading()
    const res = await getFavoriteApi({
      ...params.value,
      page: page.value,
      pageSize,
    })
    favorites.value = res.data
    page.value = res.page
    total.value = res.total
    endLoading()
  }

  const debounceFetch = debounce(fetch, 300)

  async function resetPage() {
    page.value = 1
  }

  async function pageChange(val: number) {
    page.value = val
    await fetch()
  }

  if (options?.initFetch) {
    fetch()
  }

  return {
    favorites,
    page,
    total,
    fetch,
    debounceFetch,
    resetPage,
    pageChange,
    fetchLoading: loading,
  }
}
