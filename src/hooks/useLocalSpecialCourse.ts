import type { Ref } from 'vue'

export interface TreeType {
  id: string
  name: string
  children: TreeType[] | null
  type?: string
  [key: string]: any
}

/**
 * 培优课本地记录同步
 * @param csv
 * @returns
 */
export const useLocalSpecialCourse = (
  csv: Ref<
    | {
        publisherId?: YcType.CsvId
        semesterId?: YcType.CsvId
        subjectId: YcType.CsvId
        stageId: YcType.CsvId
      }
    | undefined
  >,
) => {
  const specialCourseId = ref<string>('')
  watch(csv, (val) => {
    const localCsv = window.localStorage.getItem('csvSpecialCourse')?.split('_')
    if (
      localCsv &&
      localCsv[0] === val?.stageId?.toString() &&
      localCsv[1] === val?.subjectId?.toString() &&
      localCsv[2]
    ) {
      specialCourseId.value = localCsv[2]
    }
  })
  return {
    specialCourseId,
  }
}
