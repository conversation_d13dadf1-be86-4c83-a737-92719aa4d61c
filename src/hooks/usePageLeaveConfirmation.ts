import { ref, onBeforeUnmount } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'
import { useRouter } from 'vue-router'
import { useOIWDialog } from '@guanghe-pub/onion-ui-web'
import useAuthStore from '@/store/auth'

/**
 * 页面离开确认钩子
 * @param checkUnsavedChanges 检查是否有未保存的更改的函数
 * @param confirmMessage 确认消息
 * @param onConfirmLeave 确认离开时的回调
 * @returns
 */
export function usePageLeaveConfirmation(
  checkUnsavedChanges: () => boolean,
  confirmMessage = '确定要离开此页面吗？所做的更改都不会被保存',
  positiveText = '离开',
  negativeText = '留在当前页',
  onConfirmLeave?: () => void,
) {
  const authStore = useAuthStore()
  const router = useRouter()
  const dialog = useOIWDialog()
  const hasSetupBeforeUnload = ref(false)

  // 处理路由离开
  const handleBeforeRouteLeave = (to?: RouteLocationNormalized) => {
    if (authStore.token && checkUnsavedChanges()) {
      return new Promise<boolean>((resolve) => {
        dialog.create({
          showIcon: false,
          type: 'warning',
          title: '提示',
          content: confirmMessage,
          positiveText,
          negativeText,
          onPositiveClick: () => {
            if (onConfirmLeave) onConfirmLeave()
            resolve(true)
          },
          onNegativeClick: () => {
            resolve(false)
          },
        })
      })
    }
    return true
  }

  // 设置beforeunload事件（浏览器关闭/刷新）
  const setupBeforeUnload = () => {
    if (hasSetupBeforeUnload.value) return

    const handleBeforeUnload = async (e: BeforeUnloadEvent) => {
      if (authStore.token && checkUnsavedChanges()) {
        e.preventDefault()
        e.returnValue = confirmMessage
        return confirmMessage
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    hasSetupBeforeUnload.value = true

    onBeforeUnmount(() => {
      if (onConfirmLeave) onConfirmLeave()
      window.removeEventListener('beforeunload', handleBeforeUnload)
    })
  }

  // 设置路由守卫
  const setupRouteGuard = () => {
    const removeGuard = router.beforeEach((to, from, next) => {
      const canLeave = handleBeforeRouteLeave(to)
      if (canLeave instanceof Promise) {
        canLeave.then((result) => {
          if (result) next()
          else next(false)
        })
      } else if (canLeave) {
        next()
      } else {
        next(false)
      }
    })

    onBeforeUnmount(() => {
      removeGuard()
    })
  }

  setupBeforeUnload()
  setupRouteGuard()

  return {
    confirmLeave: (
      targetRoute: string | object,
      onConfirm?: () => void,
      leaveMessage?: string,
      leavePositiveText?: string,
      leavenNegativeText?: string,
    ) => {
      if (checkUnsavedChanges()) {
        dialog.create({
          showIcon: false,
          type: 'warning',
          title: '提示',
          content: leaveMessage || confirmMessage,
          positiveText: leavePositiveText || positiveText,
          negativeText: leavenNegativeText || negativeText,
          onPositiveClick: async () => {
            if (onConfirm) await onConfirm()
            router.push(targetRoute)
          },
        })
      } else {
        router.push(targetRoute)
      }
    },
  }
}
