import type { SchoolAiExplain } from '@/components/ProblemSingle/service.ts'
import {
  getSchoolAiExplainApi,
  putSchoolAIExplainApi,
} from '@/components/ProblemSingle/service.ts'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

export const useSchoolAIExplain = (problemId: string, isCBProblem: boolean) => {
  const apiProblemType = isCBProblem ? 'SceneCbProblem' : 'SceneSchoolProblem'
  const schoolAIExplain = ref<SchoolAiExplain | undefined>(undefined)
  const message = useOIWMessage()
  function getSchoolAiExplain() {
    if (!problemId) {
      return
    }
    getSchoolAiExplainApi(apiProblemType, problemId, false)
      .then((res: SchoolAiExplain) => {
        schoolAIExplain.value = res
        closeTimer()
      })
      .catch(() => {
        message.error('生成失败，请点击重新生成')
        closeTimer()
      })
  }

  async function regenerateAiExplain() {
    if (schoolAIExplain.value?.aiExplainProgress === 'ing' || !problemId) {
      return
    }
    const isSuccess = await putSchoolAIExplainApi({
      problemType: apiProblemType,
      problemId,
    })
    if (isSuccess) {
      schoolAIExplain.value = await getSchoolAiExplainApi(
        apiProblemType,
        problemId,
        true,
      )
    } else {
      message.error('重新生成失败')
    }
  }

  const timer = ref()
  function startTimer() {
    timer.value = setInterval(getSchoolAiExplain, 10000)
  }

  function closeTimer() {
    clearInterval(timer.value)
    timer.value = null
  }

  watch(schoolAIExplain, (newValue) => {
    if (newValue?.aiExplainProgress === 'ing' && !timer.value) {
      startTimer()
      return
    }
    if (newValue?.aiExplainProgress !== 'ing' && timer.value) {
      closeTimer()
    }
  })

  onBeforeUnmount(() => {
    closeTimer()
  })

  return {
    schoolAIExplain,
    getSchoolAiExplain,
    regenerateAiExplain,
  }
}
