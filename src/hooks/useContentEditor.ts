// src/hooks/useContentEditor.ts
import { ref, onMounted, nextTick, watch } from 'vue'
import katex from 'katex'
import { isStage, isProd } from '@/utils/apiUrl'
import { getOSSToken } from '@/pages/presentation/service.ts'
import ycUpload from '@guanghe-pub/yc-upload'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

const { onFileInputChange } = ycUpload

export const useContentEditor = (initialValue: string) => {
  const editor = ref<HTMLElement>()
  const message = useOIWMessage()

  const formattedContent = ref(initialValue)
  const showFormulaModal = ref(false)
  const editingFormula = ref('')
  let currentFormulaElement: HTMLElement | null = null

  let savedRange: Range | null = null
  let debounceTimer: ReturnType<typeof setTimeout> | null = null // 定义 debounceTimer

  const handleEditorInput = (emit: any) => {
    // 防抖逻辑
    clearTimeout(debounceTimer as ReturnType<typeof setTimeout>)
    debounceTimer = setTimeout(() => {
      formattedContent.value = parseEditorContent()
      emit('update:modelValue', formattedContent.value)
      emit('validate')
    }, 50)
  }

  const handlePaste = (event: ClipboardEvent, emit: any) => {
    // 阻止默认的粘贴行为
    event.preventDefault()

    // 获取粘贴的内容
    const clipboardData = event.clipboardData
    console.log('clipboardData', clipboardData)
    // 检查是否包含图片数据
    if (clipboardData && clipboardData.items) {
      for (const item of Array.from(clipboardData.items)) {
        // 检查是否为图片类型
        if (item.kind === 'file' && item.type.indexOf('image') !== -1) {
          console.log('检测到图片类型:', item, item.type)
          const blob = item.getAsFile()
          console.log('blob----->>>', blob)

          if (blob) {
            // 将Blob转换为Base64并上传
            processAndUploadImage(blob, emit)
            return // 处理完图片后直接返回，不再处理文本
          }
        }
      }
    }
    const pastedData = clipboardData?.getData('text/html') || ''
    console.log('pastedData', pastedData)

    // 使用 DOMParser 解析 HTML 内容
    const parser = new DOMParser()
    const doc = parser.parseFromString(pastedData, 'text/html')

    // 提取纯文本内容
    const textContent = doc.body.textContent || ''

    // 获取当前选区
    const selection = window.getSelection()
    const range = selection?.rangeCount ? selection.getRangeAt(0) : null

    console.log(' 阻止默认的粘贴行为')

    if (range) {
      // 清除当前选区内容
      range.deleteContents()

      // 插入纯文本内容
      const textNode = document.createTextNode(textContent)
      range.insertNode(textNode)
      console.log(textNode)

      // 移动光标到插入内容后面
      range.setStartAfter(textNode)
      range.setEndAfter(textNode)
      selection?.removeAllRanges()
      selection?.addRange(range)
    }
    handleEditorInput(emit)
  }

  // 处理并上传图片
  const processAndUploadImage = (blob: File, emit: any) => {
    console.log('processAndUploadImage----->>>', blob)

    // 保存当前选区，以便稍后插入图片
    saveCurrentRange()
    const size = blob.size
    console.log('size----->>>', size)
    if (size > 2097152) {
      message.info('上传失败，文件超出体积限制')
      return
    }

    // 转换为File对象
    const fileName = `pasted_image_${new Date().getTime()}.${
      blob.type.split('/')[1] || 'png'
    }`
    const file = new File([blob], fileName, { type: blob.type })

    const fileEvent = {
      target: {
        files: [file],
      },
    }

    // 定义上传完成的回调函数
    const fileUploadEnd = (response: any) => {
      if (response.code === 0 && response.data) {
        const imageUrl = `${response.data.domain}/${response.data.key}`

        // 插入上传后的图片到编辑器
        insertUploadedImage(imageUrl, emit)
      } else {
        console.error('图片上传失败', response)
      }
    }

    // 调用上传方法
    const env = isProd ? 'prod' : isStage ? 'stage' : 'test'
    const getToken = async () => {
      const { token } = await getOSSToken()
      return token
    }

    try {
      onFileInputChange(fileEvent, {
        env,
        getToken,
        size: 2097152, // 2MB
        webp: {
          webp: false,
        },
        filePath: 'b/classModeScreenShot',
        fileUploadEndHandler: fileUploadEnd,
      })
    } catch (e) {
      console.error('上传过程出错', e)
    }
  }

  // 保存当前选区位置
  let savedSelectionRange: Range | null = null

  const saveCurrentRange = () => {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      savedSelectionRange = selection.getRangeAt(0).cloneRange()
    }
  }
  // 插入上传后的图片
  const insertUploadedImage = (imageUrl: string, emit: any) => {
    // 恢复之前保存的选区
    if (savedSelectionRange) {
      const selection = window.getSelection()
      selection?.removeAllRanges()
      selection?.addRange(savedSelectionRange)

      // 创建图片HTML字符串
      const imgHtml = `<div><img src="${imageUrl}" /></div>`
      // 直接将图片HTML添加到编辑器内容中
      handleImageUpload(imgHtml, emit)

      savedSelectionRange = null

      // 触发内容更新
      handleEditorInput(emit)
    } else if (editor.value) {
      // 如果没有保存的选区，就追加到编辑器末尾
      const imgHtml = `<div><img src="${imageUrl}" /></div>`

      editor.value.textContent += imgHtml

      // 触发内容更新
      handleEditorInput(emit)
    }
  }

  const handleImageUpload = (newValue: string, emit: any) => {
    // const targetElement = element?.isConnected ? element : null

    // 确保编辑器聚焦
    editor.value?.focus()
    // 获取当前选区
    restoreSelection()
    const selection = window.getSelection()
    const range = selection?.rangeCount ? selection.getRangeAt(0) : null

    if (range) {
      // 删除当前选区的内容
      range.deleteContents()

      // 创建一个文本节点并插入
      const textNode = document.createTextNode(newValue)
      range.insertNode(textNode)

      // 移动光标到插入内容后面
      range.setStartAfter(textNode)
      range.setEndAfter(textNode)
      selection?.removeAllRanges()
      selection?.addRange(range)
    } else {
      // 如果没有选区，就追加到内容末尾
      if (editor.value) {
        // 直接在末尾插入新内容，而不是复制原有内容
        editor.value.textContent += newValue // 直接追加新内容
      }
    }

    // 更新格式化内容和触发更新
    formattedContent.value = parseEditorContent()
    handleEditorInput(emit)
  }

  const parseEditorContent = () => {
    if (!editor.value) return ''

    let result = ''
    const walker = document.createTreeWalker(
      editor.value,
      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_TEXT,
      {
        acceptNode(node) {
          // 关键修复：过滤公式节点内部内容
          if (node.parentElement?.closest('.formula-wrapper')) {
            return NodeFilter.FILTER_REJECT
          }
          return NodeFilter.FILTER_ACCEPT
        },
      },
    )

    while (walker.nextNode()) {
      const node = walker.currentNode as HTMLElement
      if (node.nodeType === Node.TEXT_NODE) {
        result += node.textContent
      } else if (node.classList?.contains('formula-wrapper')) {
        result += `$${node.dataset.latex}$`
        // 跳过整个公式子树
        if (node.childNodes.length > 0) {
          walker.currentNode = node.lastChild as Node
        }
      }
    }

    return result
  }

  const restoreSelection = () => {
    if (!savedRange) return

    // 动态验证选区有效性
    const isNodeValid = (node: Node) => {
      return (
        document.body.contains(node) ||
        (editor.value && editor.value.contains(node))
      )
    }

    if (!isNodeValid(savedRange.startContainer)) {
      // 自动定位到编辑器末尾
      const range = document.createRange()
      range.selectNodeContents(editor.value as Node)
      range.collapse(false)

      const selection = window.getSelection()
      selection?.removeAllRanges()
      selection?.addRange(range)
      return
    }

    // 正常恢复选区
    const selection = window.getSelection()
    selection?.removeAllRanges()
    selection?.addRange(savedRange.cloneRange())
    savedRange = null
  }

  const openFormulaModal = (element: HTMLElement | null) => {
    // 安全获取元素引用
    const targetElement = element?.isConnected ? element : null

    // 确保编辑器聚焦
    editor.value?.focus()

    // 安全保存选区
    try {
      if (targetElement) {
        const range = document.createRange()
        range.selectNodeContents(targetElement)
        range.collapse(false) // 定位到元素末尾

        const selection = window.getSelection()
        selection?.removeAllRanges()
        selection?.addRange(range)
      }
      saveSelection()
    } catch (e) {
      console.error('选区操作失败:', e)
      // 默认定位到编辑器末尾
      const range = document.createRange()
      range.selectNodeContents(editor.value as Node)
      range.collapse(false)
      const selection = window.getSelection()
      selection?.removeAllRanges()
      selection?.addRange(range)
      saveSelection()
    }
    // saveSelection()
    currentFormulaElement = targetElement
    editingFormula.value = targetElement?.dataset.latex || ''
    showFormulaModal.value = true
  }

  const handleFormulaConfirm = (latex: string, emit: any) => {
    if (currentFormulaElement) {
      currentFormulaElement.dataset.latex = latex
      renderFormula(currentFormulaElement)
    } else {
      insertFormula(latex, emit)
    }
    nextTick(() => {
      showFormulaModal.value = false
      editor.value?.focus()
    })
    formattedContent.value = parseEditorContent()
    emit('update:modelValue', formattedContent.value)
  }
  const insertFormula = (latex: string, emit: any) => {
    restoreSelection()
    const selection = window.getSelection()
    const range =
      selection?.rangeCount && selection.rangeCount > 0
        ? selection.getRangeAt(0)
        : document.createRange()

    // 创建并插入公式节点
    const formulaNode = createFormulaElement(latex)
    range.deleteContents()
    range.insertNode(formulaNode)

    // 设置光标到公式后
    const newRange = document.createRange()
    newRange.setStartAfter(formulaNode)
    newRange.collapse(true)
    selection?.removeAllRanges()
    selection?.addRange(newRange)

    formattedContent.value = parseEditorContent()
    emit('update:modelValue', formattedContent.value)
  }
  const createFormulaElement = (latex: string) => {
    const wrapper = document.createElement('span')
    wrapper.className = 'formula-wrapper'
    wrapper.contentEditable = 'false' // 关键修复：防止内容被误编辑
    wrapper.dataset.latex = latex

    try {
      // 修复MathML解析逻辑
      const html = katex.renderToString(latex, {
        output: 'mathml',
        throwOnError: false,
      })
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'application/xml') // 改用XML解析
      const mathML = doc.documentElement

      // 克隆并附加MathML节点
      const clonedMathML = mathML.cloneNode(true) as HTMLElement
      clonedMathML.setAttribute('display', 'inline')
      wrapper.appendChild(clonedMathML)
    } catch (e) {
      wrapper.textContent = '[公式错误]'
    }

    // 绑定点击事件
    wrapper.addEventListener('click', (e) => {
      e.stopPropagation()
      const range = document.createRange()
      range.selectNodeContents(wrapper)
      const sel = window.getSelection()
      sel?.removeAllRanges()
      sel?.addRange(range)
      openFormulaModal(wrapper)
    })

    return wrapper
  }
  const saveSelection = () => {
    const sel = window.getSelection() as Selection
    if (sel.rangeCount > 0) {
      savedRange = sel.getRangeAt(0).cloneRange()
    }
  }

  const renderFormula = (oldWrapper: HTMLElement) => {
    // 保存旧位置信息
    const parent = oldWrapper.parentNode
    const oldIndex = Array.from(parent?.childNodes || []).indexOf(oldWrapper)

    // 创建新元素并替换
    const newWrapper = createFormulaElement(oldWrapper.dataset.latex || '')
    oldWrapper.replaceWith(newWrapper)

    // 创建新选区（关键修复）
    const newRange = document.createRange()
    newRange.setStart(parent as Node, oldIndex + 1)
    newRange.collapse(true) // 定位到新元素开始位置

    // 更新全局选区
    const selection = window.getSelection()
    selection?.removeAllRanges()
    selection?.addRange(newRange)

    // 同步保存的选区
    savedRange = newRange.cloneRange()
  }
  watch(
    () => editor.value?.innerHTML,
    () => {
      formattedContent.value = parseEditorContent()
    },
    { deep: true },
  )
  onMounted(() => {
    nextTick(() => {
      if (editor.value) {
        editor.value.textContent = initialValue
      }
    })
  })

  return {
    currentFormulaElement,
    savedRange,
    editor,
    formattedContent,
    showFormulaModal,
    editingFormula,
    handleEditorInput,
    handlePaste,
    handleImageUpload,
    openFormulaModal,
    handleFormulaConfirm,
  }
}
