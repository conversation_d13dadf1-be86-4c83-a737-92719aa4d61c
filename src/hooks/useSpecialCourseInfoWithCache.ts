import type { SpecialCourseItem } from '@/service/specialCourse.ts'
import { getSpecialCourseDetailFromIdApi } from '@/service/specialCourse.ts'

const specialCourseList = ref<SpecialCourseItem[]>([])
export const useSpecialCourseInfo = () => {
  const fetchSpecialCourseInfo = async (id: string) => {
    const response: SpecialCourseItem =
      await getSpecialCourseDetailFromIdApi(id)
    if (response && response.id) {
      specialCourseList.value.push(response)
    }
    return response
  }
  const getSpecialCourseInfo = async (id: string) => {
    // console.log('specialCourseList>>>', specialCourseList.value)
    if (specialCourseList.value.length > 0) {
      // 检查当前培优课列表中是否有当前ID
      if (specialCourseList.value.findIndex((item) => item.id === id) !== -1) {
        return specialCourseList.value.find((item) => item.id === id)
      } else {
        return await fetchSpecialCourseInfo(id)
      }
    } else {
      return await fetchSpecialCourseInfo(id)
    }
  }

  onBeforeUnmount(() => {
    specialCourseList.value = []
  })

  return {
    getSpecialCourseInfo,
  }
}
