/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  //readonly VITE_MODE: 'test' | 'stage' | 'production' | undefined
  readonly VITE_APP_ENV: 'development' | 'stage' | 'production' | undefined
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module '@guanghe/yc-pc-player-vue' {
  const YcPcPlayer: ReturnType<typeof defineComponent> & {
    install: (app: App) => void
  }
  export default YcPcPlayer
}

declare module '@guanghe-pub/yc-upload'
declare module '@guanghe-pub/yc-pc-upload-vue'
declare module 'pdfjs-dist'
declare module 'katex'
