@font-face {
  font-family: hlan;
  font-style: normal;
  font-weight: normal;
  src: url('https://fp.yangcong345.com/middle/1.0.0/MiSans-Heavy.otf')
    format('opentype');
}

.hlan-font {
  font-family: hlan;
}

.teacher-v2 {
  /* // Button */

  .n-button {
    height: 46px;
    padding: 0 46px;
    font-size: 15px;
    font-weight: 600;
    color: #393548;
    background: #ffd633;
    border-radius: 12px 12px 12px 12px;
    outline: none !important;

    &:hover {
      color: #393548;
      background: #ffe066;
    }

    &:active {
      color: #393548;
      background: #ffcc00;
    }

    &:focus {
      color: #393548;
      background: #ffcc00;
    }

    .n-base-wave {
      display: none;
    }

    .n-button__state-border {
      display: none;
    }

    .n-button__border {
      border: none;
    }
  }

  .n-button--secondary {
    height: 46px;
    padding: 0 31px;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: 600;
    color: #393548;
    background: transparent;
    border: 1px solid #d5d2dd;
    border-radius: 23px;

    &:hover {
      color: #5e80fe;
      background: #ecf0fc;
      border: 1px solid #ecf0fc;
    }

    &:active {
      color: #5e80fe;
      background: #dfe7ff;
      border: 1px solid #dfe7ff;
    }

    &:focus {
      color: #5e80fe;
      background: transparent;
      border: 1px solid #dfe7ff;
    }
  }

  .n-button--tertiary {
    height: 46px;
    padding: 0 23px;
    font-weight: 500;
    color: #5e80fe;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 23px;

    &:hover {
      color: #5e80fe;
      background: #f5f7fe;
      border: 1px solid #f5f7fe;
    }

    &:active {
      color: #5e80fe;
      background: #dfe7ff;
      border: 1px solid #dfe7ff;
    }

    &:focus {
      color: #5e80fe;
      background: transparent;
      border: 1px solid transparent;
    }
  }

  /* // select */
  .n-select {
    height: 46px;
    background-color: #fff;
    border: 1px solid #d4d1dd;
    border-radius: 12px;

    ::v-deep(.n-base-suffix__arrow) {
      transform: scale(0.8);
    }

    .n-base-selection {
      height: 100%;
      --n-border-hover: none;
      --n-border: none;
      --n-border-active: none;
      --n-border-focus: none;
      --n-caret-color: none;
      --n-color: none;
      --n-color-active: none;
      --n-color-disabled: none;
      --n-box-shadow-active: none;
      --n-box-shadow-focus: none;
    }

    .n-base-selection:hover {
      border: none;
    }

    .n-base-selection-label {
      height: 100%;
      background-color: transparent;
    }
  }

  .n-base-select-menu {
    padding: 12px;
    font-size: 15px;
    line-height: 46px;
    background: #ffffff;
    border: 1px solid #efeef3;
    border-radius: 12px !important;
    box-shadow: 0px 4 4px 0px rgba(151, 151, 151, 0.1);
    opacity: 1;

    .n-base-select-option {
      font-size: 15px;
    }

    .n-base-select-option.n-base-select-option--show-checkmark {
      padding-right: 16px;
      padding-left: 16px;
    }

    .n-base-select-option::before {
      border-radius: 12px;
    }

    .n-base-select-option .n-base-select-option__check {
      display: none;
    }

    .n-base-select-option.n-base-select-option--selected {
      color: #393548;
    }

    .n-base-select-option.n-base-select-option--selected.n-base-select-option--pending::before {
      background-color: #f5f7fe;
    }
  }

  .oi-btn-select {
    height: 46px;
    background: #f7f7f9;
    border-radius: 23px;

    &:hover {
      background: #f5f7fe;
    }

    .n-base-select-menu {
      width: 200px;

      .n-base-select-option {
        &::after {
          position: absolute;
          right: 13px;
          z-index: 2;
          display: inline-block;
          width: 19px;
          height: 19px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/椭圆形@2x-59b0d7e109a76e652c3e234e632f3e0e.png');
          background-size: contain;
        }
      }

      .n-base-select-option--selected {
        &::after {
          position: absolute;
          right: 13px;
          z-index: 2;
          display: inline-block;
          width: 19px;
          height: 19px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/单选 <EMAIL>');
          background-size: contain;
        }
      }
    }

    .n-base-selection--focus {
      background: #f5f7fe;
      border-radius: 23px;
    }

    .n-base-selection {
      height: 100%;
      --n-border-hover: none;
      --n-border: none;
      --n-border-active: none;
      --n-border-focus: none;
      --n-caret-color: none;
      --n-color: none;
      --n-color-active: none;
      --n-color-disabled: none;
      --n-box-shadow-active: none;
      --n-box-shadow-focus: none;

      .n-base-suffix {
        top: 21px;
        right: 19px;
      }
    }

    .n-base-selection-label {
      height: 100%;

      .n-base-selection-input {
        padding: 0 19px;
      }
    }

    .btn-slide-left {
      display: inline-block;
      width: 19px;
      height: 19px;
      background: url('https://fp.yangcong345.com/onion-extension/组 <EMAIL>');
      background-size: contain;
    }
  }

  /* table */
  .oi-tea-table {
    width: 100%;

    .oi-tea-table__thead {
      height: 69px !important;
      background: #f5f7fe;
      border-radius: 12px 12px 12px 12px;
    }

    .oi-tea-table__tr {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
    }
  }

  .n-table {
    thead {
      tr {
        th {
          padding: 23px;
          font-size: 14px;
          font-weight: 400;
          line-height: 23px;
          color: #393548;
          background: #f5f7fe;
        }

        th:nth-child(1) {
          border-radius: 12px 0 0 12px;
        }

        th:last-child {
          border-radius: 0 12px 12px 0;
        }
      }
    }

    tbody {
      tr {
        td {
          padding: 23px;
          font-size: 14px;
          font-weight: 500;
          line-height: 23px;
          color: #393548;
        }
      }

      tr:last-child {
        td {
          border: none;
        }
      }
    }
  }

  tbody {
    tr:last-child {
      border: none;

      td {
        border: none;
      }
    }
  }

  .n-data-table {
    --n-merged-td-color-hover: #fff;
    --n-merged-th-color-hover: #f5f7fe;
    --n-merged-th-color: #f5f7fe;

    thead {
      tr {
        th {
          padding: 20px;
          font-size: 14px;
          font-weight: 400;
          line-height: 16px;
          color: #393548;
          background: #f5f7fe;
        }

        th:nth-child(1) {
          border-radius: 12px 0 0 12px;
        }

        th:last-child {
          border-radius: 0 12px 12px 0;
        }
      }
    }

    tbody {
      td {
        padding: 23px;
      }
    }

    .n-data-table-tr {
      height: 56px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #393548;
    }

    .n-data-table-tr:last-child {
      td {
        border: none;
      }
    }
  }

  .oi-footer-btns {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: end;
    width: 100%;
    height: 77px;
    padding: 15px 23px;
    background: #f5f7fe;
    border-radius: 15px 15px 15px 15px;
    opacity: 1;
  }

  .oi-cancel-btn {
    display: inline-block;
    // width: 115px;
    height: 46px;
    padding: 0 42px;
    margin: 0 8px;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: 500;
    line-height: 46px;
    color: #32373d;
    cursor: pointer;
    border: 1px solid #d4d1dd;
    border-radius: 23px;

    opacity: 1;
    transition: all 0.2s linear;

    &:hover {
      color: #5e80ff;
      background: #ecf0fc;
      border: 1px solid #ecf0fc;
    }

    &:active {
      color: #5e80ff;
      background: #dfe7ff;
      border: 1px solid #dfe7ff;
    }
  }

  .oi-confirm-btn {
    // width: 115px;
    display: inline-block;
    height: 46px;
    padding: 0 42px;
    margin: 0 8px;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: 500;
    line-height: 46px;
    color: #393548;
    cursor: pointer;
    background: #ffd633;
    border-radius: 23px;
    opacity: 1;
    transition: all 0.2s linear;

    &.disable {
      color: #fff;
      background: #d4d1dd;

      &:hover {
        color: #fff;
        background: #d4d1dd;
      }

      &:active {
        color: #fff;
        background: #d4d1dd;
      }
    }

    &:hover {
      background: #ffe066;
    }

    &:active {
      background: #ffcc00;
    }
  }
}
