.custom-tree {
  font-size: 14px;
  font-weight: 500;

  .n-tree-node-wrapper {
    min-height: 36px;

    .n-tree-node-switcher {
      height: 100%;
      min-height: 36px;
    }

    .n-tree-node-content {
      min-height: 36px;
    }

    .n-tree-node {
      border-radius: 8px;
    }
  }

  .n-tree-node-content {
    border-radius: 8px;
  }

  .n-tree-node--selected {
    .n-tree-node-content {
      color: #5e80ff;
    }
  }
}

.oiw-state-block__image-empty {
  width: unset !important;
  height: 150px !important;
}

.scrollbar-none {
  &::-webkit-scrollbar {
    display: none;
  }
}

.custom-dialog.n-dialog.n-modal {
  width: 490px;
  padding: 32px;

  .n-dialog__title {
    font-size: 20px;
    font-weight: 600;
    color: #393548;
  }

  .n-dialog .n-dialog__close {
    margin-top: 16px !important;
    margin-right: 16px !important;
  }

  .n-dialog__content {
    margin-top: 16px !important;
    margin-bottom: 32px !important;
    font-size: 14px;
  }

  .n-button {
    width: 104px;
    height: 40px;
    border-radius: 140px !important;
  }
}

.custom-pagination {
  &.n-pagination {
    justify-items: center;

    .n-pagination-item {
      width: 32px;
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: #504b64;
      border-radius: 10px 10px 10px 10px;

      &:hover {
        font-family: hlan;
        font-size: 14px;
        font-weight: 900;
        color: #5e80ff !important;
        background: #f5f7fe !important;
        border-radius: 10px 10px 10px 10px;
      }

      &.n-pagination-item--button {
        background-color: transparent !important;
        border: none !important;

        &:hover {
          background: #f5f7fe !important;
        }
      }

      &.n-pagination-item--disabled {
        &:hover {
          color: var(--n-item-text-color-disabled) !important;
        }
      }

      &:not(.n-pagination-item--disabled) {
        &.n-pagination-item--active {
          width: 32px;
          height: 32px;
          font-weight: 900;
          color: #fff !important;
          background: #5e80ff !important;
          border: none;
          border-radius: 12px 12px 12px 12px;
        }
      }
    }
  }
}

.n-checkbox-box-wrapper {
  // width: 24px;
  margin-right: 2px;
  // font-size: 18px;

  .n-checkbox-box {
    width: 16px;
    height: 16px;
    border-radius: 5px;
  }
}

.n-checkbox__label {
  height: 24px;
  padding: 0 6px;
  font-size: 14px;
  line-height: 24px;
}

.custom-multiple-option-menu {
  min-width: 85px;
  padding: 8px;
  font-size: 15px;
  line-height: 36px;
  background: #ffffff;
  border: 1px solid #c5c1d4;
  border-radius: 12px !important;
  box-shadow: 0px 3.2px 3.2px 0px rgba(151, 151, 151, 0.1);
  opacity: 1;
}

.custom-oiw-radio {
  &.n-radio {
    &:not(.n-radio--disabled) {
      &.n-radio--focus:not(:active) {
        .n-radio__dot {
          box-shadow: none;
        }
      }
    }

    .n-radio__dot-wrapper {
      line-height: 20px;
    }

    .n-radio__label {
      padding-right: 8px;
      padding-left: 8px;
      font-size: 14px;
      line-height: 20px;
      color: #393548;
    }

    &.n-radio--focus {
      .n-radio__dot {
        box-shadow: none;
      }
    }

    &:not(.n-radio--disabled):hover {
      .n-radio__dot {
        background-color: transparent;
        border: 2px solid #7290ff;
        box-shadow: none;
        transition: none;

        &.n-radio__dot--checked {
          background: url('https://fp.yangcong345.com/onion-extension/777-5ff8f107e188b42ed8c8d77c246b9d3b.png')
            no-repeat;
          background-size: 100%;
          border: none;
        }
      }
    }

    .n-radio__dot {
      width: 16px;
      height: 16px;
      border: 2px solid #c5c1d4;
      border-radius: 50%;
      box-shadow: none;
      transition: none;

      &.n-radio__dot--checked {
        background: url('https://fp.yangcong345.com/onion-extension/777-5ff8f107e188b42ed8c8d77c246b9d3b.png')
          no-repeat;
        background-color: transparent;
        background-size: 100%;
        border: none;
        box-shadow: none;
      }

      &::before {
        display: none;
      }
    }
  }
}

.oiw-cascader {
  .n-base-selection {
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 12px;
  }
}
