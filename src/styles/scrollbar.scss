.onion-scroll{
  overflow-x: hidden;
  overflow-y: auto;
  
}

/* 滚动条样式 - WebKit 浏览器 */
.onion-scroll::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
  height: 20px;
  background-color: transparent; /* 滚动条轨道透明 */

}

/* 滚动条滑块 */
.onion-scroll::-webkit-scrollbar-thumb {
  background-color: transparent; /* 滑块半透明深灰色 */
  border-radius: 4px; /* 滑块圆角 */
}

/* 鼠标悬停在滑块上 */
.onion-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.25); /* 滑块加深透明度 */
}

/* 滚动条轨道 */
.onion-scroll::-webkit-scrollbar-track {
  background-color: transparent; /* 轨道透明 */
}

/* 滚动条拐角 */
.onion-scroll::-webkit-scrollbar-corner {
  background-color: transparent; /* 滚动条交汇处透明 */
}

