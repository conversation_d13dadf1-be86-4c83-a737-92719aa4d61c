import request from '@/utils/request'

export interface PaperItemType {
  id: string
  name: string
  paperType: 'SchoolExamPaper' | 'UserTestPaper'
  problemNum: number
  createdAt: string
  scene:
    | 'UserTestPaperSceneSelect'
    | 'UserTestPaperSceneAIGen'
    | 'UserTestPaperSceneSchool'
  [key: string]: any
}
/**
 * @description 我的组卷列表（AI组卷，选题本生成试卷，校本试卷）
 * @param params - 请求参数对象
 * @returns 返回包含试卷列表和总数的响应对象
 * https://yapi.yc345.tv/project/2673/interface/api/125928
 *
 */
export const getPersonalPaperList = async (params: {
  name?: string
  page?: number
  pageSize?: number
}) => {
  return request.get<{
    total: number
    papers: PaperItemType[]
  }>('/teacher-desk/user-test-paper/list-with-school-paper', {
    params,
  })
}
