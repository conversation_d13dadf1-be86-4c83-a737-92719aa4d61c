<template>
  <section class="mx-auto pb-60px w-1200px">
    <div class="w-100% flex items-center justify-between mb-24px">
      <OIWRadioButtonGroup
        v-model:value="checkType"
        :on-update-value="handleChangeType"
      >
        <OIWRadioButton value="combinationPaper"> 我的组卷 </OIWRadioButton>
        <OIWRadioButton value="myCollect"> 我的收藏 </OIWRadioButton>
      </OIWRadioButtonGroup>
      <div class="flex items-center justify-end">
        <div class="w-282px">
          <OIWInput
            class="h-40px"
            :value="filterName"
            placeholder="输入搜索关键词搜索资源"
            clearable
            @update:value="onNameChange"
          >
            <template #suffix>
              <SearchIcon />
            </template>
          </OIWInput>
        </div>
        <OIWButton
          class="ml-16px"
          style="
            width: 125px;
            background: linear-gradient(
              138deg,
              #9582f4 0%,
              #6482ff 68%,
              #68c0f9 107%
            );
          "
          type="info"
          @click="handleToAICombinePaper"
        >
          AI组卷
          <template #icon>
            <AIIcon />
          </template>
        </OIWButton>
      </div>
    </div>
    <div class="flex items-center my-20px">
      <TipIcon class="w-24px h-24px mr-6px" />
      <div class="font-size-14px color-#393548 font-bold">
        注意：您在此处修改文件，会同步修改校本资源库的试卷哦～
      </div>
    </div>
    <div>
      <combinationList
        v-if="checkType === 'combinationPaper'"
        :filterName="filterName"
      />
      <collectionList
        v-if="checkType === 'myCollect'"
        :filterName="filterName"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWInput,
  OIWButton,
} from '@guanghe-pub/onion-ui-web'
import SearchIcon from '~icons/yc/search'
import AIIcon from '~icons/yc/ai'
import combinationList from '@/pages/personalResource/personalExamPaper/components/combinationList.vue'
import collectionList from '@/pages/personalResource/personalExamPaper/components/collectionList.vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import TipIcon from '~icons/yc/tip-blue'

const checkType = ref('combinationPaper')
const handleChangeType = (type: string) => {
  checkType.value = type
}

const filterName = ref('')
const onNameChange = (value: string) => {
  filterName.value = value
}

const router = useRouter()
const handleToAICombinePaper = () => {
  const href = router.resolve({
    name: 'AICombinePaper',
  }).href
  window.open(href, '_blank')
}

onMounted(() => {
  buryPoint('enterTeacherWorkBenchMySecTabPage', {
    browserResolution: `${screen.width}x${screen.height}`,
    option: 'exam',
  })
})
</script>
