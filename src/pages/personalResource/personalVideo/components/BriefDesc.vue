<template>
  <div
    :class="{ 'brief-desc-box': true, none: !isShow }"
    @click="handleClickDesc"
  >
    <div
      v-if="isShow"
      class="brief-desc-header"
      :style="{
        backgroundImage: `url(${videoCover})`,
      }"
    >
      <n-space justify="space-between" class="head-info">
        <div class="flex">
          <div
            v-if="
              briefData.typeTags &&
              briefData.typeTags.length &&
              formattedTypeTags(briefData.typeTags)
            "
            class="label"
          >
            {{ formattedTypeTags(briefData.typeTags) }}
          </div>
          <n-popover
            v-if="
              briefData.video.keyPoints &&
              briefData.video.keyPoints.length &&
              keyPointCount &&
              !briefData.resourceId
            "
            trigger="hover"
          >
            <template #trigger>
              <span class="label ml-8px"> 片段{{ keyPointCount }} </span>
            </template>
            <span>当前微课支持选择片段插入课件</span>
          </n-popover>

          <span v-if="briefData.levelNum !== 0" class="label ml-8px">
            题目{{ briefData.levelNum }}
          </span>
        </div>

        <div class="operate">
          <span
            :class="{ 'collect-icon': true, active: isFavorite }"
            @click.stop="handleClickCollect"
          />
          <n-popover v-if="aiEntry" trigger="hover">
            <template #trigger>
              <span class="ai-icon" @click.stop="handleClickAIEntry" />
            </template>
            <span>AI生成课件</span>
          </n-popover>
        </div>
      </n-space>
    </div>
    <div
      v-else
      class="brief-desc-header"
      :style="{
        backgroundImage: `url(https://fp.yangcong345.com/onion-extension/555-90f6ca756f41b5bad44b3f0516b98cb2.png)`,
      }"
    >
      <n-space justify="space-between" class="head-info">
        <div></div>
        <div class="operate">
          <span
            :class="{ 'collect-icon': true, active: isFavorite }"
            @click.stop="handleClickCollect"
          />
        </div>
      </n-space>
    </div>
    <div class="brief-desc-content">
      <div class="flex items-center" :class="{ title: true, grey: !isShow }">
        <img
          v-if="briefData.pay && auth"
          class="w-46px h-20px mr-6px"
          src="https://fp.yangcong345.com/onion-extension/编组 3备份@3x-6d6bc77e63f598316b41fc1d6f7a584d.png"
          alt=""
        />
        <img
          v-if="briefData.pay && !auth"
          class="w-46px h-20px mr-6px"
          src="https://fp.yangcong345.com/onion-extension/编组 3备份@3x (1)-35218ebc2a64d210c91e217b197acdac.png"
          alt=""
        />
        {{ briefData.resourceName || briefData.name }}
      </div>
      <n-space v-if="isShow" justify="space-between" class="info-list">
        <div v-if="briefData.resourceSubType !== 'system-point'" class="data">
          <span>使用{{ briefData.usageCount }}</span>
          <span>｜收藏{{ briefData.favoriteCount }}</span>
        </div>
        <div class="time">
          {{ durationFormat }}
        </div>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TopicDetail } from '@/pages/microVideo/service'
import {
  formattedDuration,
  formattedTypeTags,
} from '@/pages/microVideo/service'
import useAuthStore from '@/store/auth'
import { useVideoCover } from '@/hooks/useVideoCover'
import useCvsStore from '@/store/csv'

const props = defineProps<{
  briefData: TopicDetail
  auth: boolean
}>()

const emits = defineEmits<{
  (e: 'click', briefData: TopicDetail): void
  (e: 'collect', briefData: TopicDetail, isShow: boolean): void
}>()

const authStore = useAuthStore()
const csvStore = useCvsStore()
const { isShowAIEntry } = storeToRefs(authStore)

const { videoCover } = useVideoCover({
  video: props.briefData.video,
  resourceSubId: props.briefData.resourceSubId as string,
  resourceType: props.briefData.resourceSubType || 'system', // HACK: 由于部分资源没有resourceSubType字段
})

const router = useRouter()

const aiEntry = computed(() => {
  return (
    isShowAIEntry.value &&
    (props.briefData.resourceSubType === 'system-point' ||
      props.briefData.resourceSubType === 'system')
  )
})

const handleClickAIEntry = () => {
  const stageId = props.briefData?.stage || ''
  const subjectId = props.briefData?.subject || ''
  const publisherId = props.briefData?.publisher || ''
  const semesterId = props.briefData?.semester || ''
  // 点击AI入口时，要更新当前选择的版本和单元，供Ai生成课件时使用（以防在其他页面改变了当前单元）
  csvStore.updateCurrentTextBook({
    publisherId,
    stageId,
    subjectId,
    semesterId,
  })
  const fullPath = router.resolve({
    name: 'createAIPPT',
    query: {
      topicId: props.briefData.resourceId,
    },
  })
  window.open(fullPath.href, '_blank')
}

const isShow = computed(() => {
  if (props.briefData.resourceSubType !== 'system-point') {
    return !!props.briefData.name
  }
  return !!props.briefData.video?.keyPoints?.some(
    (keyPoint: any) => keyPoint.id === props.briefData.resourceSubId,
  )
})

const keyPointCount = computed(() => {
  if (props.briefData.video) {
    return props.briefData.video.keyPoints?.filter(
      (keyPoint: any) => keyPoint.content !== '片头' && keyPoint.time !== 0,
    ).length
  }
  return 0
})

const durationFormat = computed(() => {
  if (props.briefData.video) {
    if (props.briefData.resourceSubType !== 'system-point') {
      return formattedDuration(props.briefData.video.duration)
    }
    let time = 0
    if (props.briefData.video.keyPoints) {
      const keyIndex = props.briefData.video.keyPoints.findIndex(
        (keyPoint: any) => keyPoint.id === props.briefData.resourceSubId,
      )
      if (keyIndex > -1) {
        if (keyIndex === props.briefData.video.keyPoints.length - 1) {
          time =
            props.briefData.video.duration -
            props.briefData.video.keyPoints[keyIndex].time
        } else {
          time =
            props.briefData.video.keyPoints[keyIndex + 1].time -
            props.briefData.video.keyPoints[keyIndex].time
        }
      }
    }
    return formattedDuration(time)
  }
  return ''
})
const isFavorite = computed(() => {
  if (!isShow.value) {
    return true
  }
  if (props.briefData.resourceSubType !== 'system-point') {
    return props.briefData.isFavorite
  }
  return props.briefData.favoriteKeyPointIds?.some(
    (id: string) => id === props.briefData.resourceSubId,
  )
})

const handleClickDesc = () => {
  if (isShow.value) {
    emits('click', props.briefData)
  }
}
const handleClickCollect = () => {
  emits('collect', props.briefData, isShow.value)
}
</script>

<style lang="scss" scoped>
.brief-desc-box {
  width: 274px;
  height: 226px;
  margin: 0 0 21px;
  // margin-right: 16px;
  cursor: pointer;
  background: #fff;
  border-radius: 16px;

  &:hover {
    background: #f4f6ff;
  }

  &.none {
    &:hover {
      background: #fff;
    }
  }

  .brief-desc-header {
    position: relative;
    width: 274px;
    height: 154px;
    padding: 12px;
    background-size: cover;
    border-radius: 16px;

    .head-info {
      .label {
        padding: 6px 10px;
        font-size: 12px;
        font-weight: 500;
        line-height: 12px;
        color: #ffffff;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
      }

      .collect-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        cursor: pointer;
        background: rgba(0, 0, 0, 0.5)
          url('https://fp.yangcong345.com/onion-extension/111-0c2ec3eea9dc435a0f02c6a5c58ee85d.png')
          no-repeat;
        background-position: 4px 3px;
        background-size: 16px 16px;
        border-radius: 8px;

        &.active {
          background: rgba(0, 0, 0, 0.5)
            url('https://fp.yangcong345.com/onion-extension/333-f996430c0cc4ec2527195dd525f2beb5.png')
            no-repeat;
          background-position: 4px 3px;
          background-size: 16px 16px;
        }
      }
    }

    .ai-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      margin-left: 8px;
      background: url('https://fp.yangcong345.com/onion-extension/容器 <EMAIL>')
        no-repeat;
      background-size: cover;
    }
  }

  .brief-desc-content {
    padding: 12px;

    .title {
      overflow: hidden;
      font-size: 16px;
      font-weight: 500;
      line-height: 18px;
      color: #393548;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.grey {
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        color: #c5c1d4;
      }
    }

    .info-list {
      margin-top: 8px;
      font-size: 14px;
      line-height: 22px;
      color: #8a869e;
    }
  }
}
</style>
