<template>
  <section class="mx-auto flex pb-60px w-800px">
    <div class="flex-1 pl-24px">
      <div class="bg-#F7F7F9 rounded-16px text-size-14px font-500 p-16px">
        <FilterItem label-width="56" labelline-height="32" name="学科:">
          <CsvSelect
            publisherSemesterCanAll
            class="w-300px h-32px cascader-collection"
            @change="onCsvSelectChange"
          />
        </FilterItem>
        <FilterItem label-width="56" name="题型:" class="mt-12px">
          <FilterSelect
            :value="filterModel.examType"
            :options="examTypeFilters"
            @update:value="onExamTypeChange"
          />
        </FilterItem>
        <FilterItem label-width="56" name="来源:" class="mt-12px">
          <FilterSelect
            :value="source"
            :options="sourceOptions"
            @update:value="onSourceChange"
          />
        </FilterItem>
      </div>
      <div class="flex mt-24px justify-between items-center">
        <div>筛选到题目总数：{{ totalNumber }}</div>
        <div v-if="source === 'myUpload'" class="text-14px color-#9792AC">
          如需上传，请到校本资源库
        </div>
      </div>
      <div class="flex items-center my-20px">
        <TipIcon class="w-24px h-24px mr-6px" />
        <div class="font-size-14px color-#393548 font-bold">
          注意：您在此处修改文件，会同步修改校本资源库的题目哦～
        </div>
      </div>
      <OIWLoading :show="loading" width="200px" height="200px">
        <div v-if="problems.length">
          <div class="divide-y-[1px] divide-[#DCD8E7]">
            <ProblemItem
              v-for="(problem, index) of problems"
              :key="problem.id"
              :problem="problem"
              :index="index + 1"
              :booksIds="booksIds"
              :is-favorite="isFavorite(problem.id)"
              :isHideFavorite="source === 'myUpload'"
              :tagConfig="{
                isHideUsageCount:
                  source === 'myUpload' ||
                  problem?.resourceType === 'school_problem',
                isHideAccuracy:
                  source === 'myUpload' ||
                  problem?.resourceType === 'school_problem',
                isShowCreatedName: true,
              }"
              :showSchoolAiExplainButton="
                problem?.resourceType === 'school_problem'
              "
              @on-favorite="favoriteItem(problem)"
              @on-add-to-books="addToBooks(problem)"
            >
              <template #topTags>
                <div v-if="source === 'myUpload'" class="inline-flex">
                  <ProblemAction
                    v-if="problem.isEdit"
                    class="absolute right-0 mr-8px"
                    @on-move-click="handleMoveClick(problem.id)"
                    @on-delete-click="handleDeleteClick(problem.id)"
                  />
                </div>
              </template>
            </ProblemItem>
          </div>
          <div class="mt-59px flex justify-end">
            <n-pagination
              class="custom-pagination"
              :page="page"
              :page-size="pageSize"
              :item-count="totalNumber"
              @update:page="onPageChange"
            />
          </div>
          <BackTop />
        </div>
        <div v-else>
          <div class="pt-200px">
            <OIWStateBlock type="empty" title="暂无资源" />
          </div>
        </div>
      </OIWLoading>
    </div>
    <MoveModal
      v-model:show="moveModalShow"
      :resourceId="activeProblemId"
      @on-confirm="handleConfirmMoveClick"
    />
  </section>
</template>

<script setup lang="ts">
import BackTop from '@/components/BackTop.vue'
import {
  OIWLoading,
  OIWStateBlock,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import FilterItem from '@/components/FilterItem/FilterItem.vue'
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'
import { ExamProblemTypeEnums } from '@guanghe-pub/onion-problem-render'
import CsvSelect from '@/pages/problem/components/CsvSelect.vue'
import { useLoading } from '@/hooks/useLoading'
import type { ProblemDetailType } from '@/pages/problem/utils'
import type { FavoriteItem, FavoriteType } from '@/service/common'
import { getFavoriteIdsApi } from '@/service/common'
import { useAddFavorite, useFavorite } from '@/hooks/useFavorite'
import { useProblem } from '@/hooks/useProblem.ts'
import { buryPoint } from '@/utils/buryPoint'
import { groupBy } from 'lodash-es'
import type { ProblemSimpleType } from '@/pages/problem/utils.ts'
import type {
  SchoolProblemParams,
  SchoolProblemTypeSimple,
} from '@/pages/schoolResource/schoolProblem/service'
import {
  deleteSchoolProblemApi,
  getSchoolProblemDetailApi,
  getSchoolProblemsApi,
  updateSchoolProblemApi,
} from '@/pages/schoolResource/schoolProblem/service'
import { useAuth } from '@/hooks/useAuth.ts'
import useBooksProblem from '@/store/booksProblem'
import ProblemAction from '@/pages/schoolResource/schoolProblem/component/ProblemAction.vue'
import MoveModal from '@/pages/schoolResource/components/MoveModal.vue'
import TipIcon from '~icons/yc/tip-blue'

const message = useOIWMessage()
type ValueType = string | number | undefined | null

const booksProblemStore = useBooksProblem()
const { booksIds } = storeToRefs(booksProblemStore)
const { fetchDetails } = useProblem()
// 来源
const sourceOptions = [
  {
    label: '我的收藏',
    value: 'myCollect',
  },
  {
    label: '我的上传',
    value: 'myUpload',
  },
]
const source = ref('myCollect')

onMounted(() => {
  booksProblemStore.getBookProblems()
  buryPoint('enterTeacherWorkBenchMySecTabPage', {
    browserResolution: `${screen.width}x${screen.height}`,
    option: 'problem',
  })
})

const onSourceChange = async (value: ValueType) => {
  source.value = value as string
  startLoading()
  page.value = 1
  await fetchList()
  endLoading()
}

// 题型
const filterModel = reactive<{
  examType?: string
}>({
  examType: undefined,
})
const onExamTypeChange = (value: ValueType) => {
  filterModel.examType = value as string
  onFilterChange()
}
const resetFilterModel = () => {
  filterModel.examType = undefined
}

// 列表
const page = ref(1)
const pageSize = ref(10) // 我的上传列表使用
const total = ref(0) // 我的上传列表使用
const myUploadList = ref<SchoolProblemTypeSimple[]>([])
const problems = shallowRef<ProblemDetailType[]>([])

const { userId } = useAuth()

// 我的上传获取题目详情
async function fetchSchoolProblemDetails(
  simpleProblems: SchoolProblemTypeSimple[],
) {
  const problemIds = simpleProblems.map((item) => item.problemId)
  if (!problemIds.length) {
    problems.value = []
    return
  }
  const res = await getSchoolProblemDetailApi({
    problemsId: problemIds,
  })
  problems.value = res.data.map((detail) => {
    const sourceItem = simpleProblems.find((p) => p.problemId === detail.id)
    return {
      ...detail,
      ...sourceItem,
      createdName: sourceItem?.createdName || '未知创建者', // 添加兜底值
      isShowProblemType: false,
      isHideExamType: true,
      resourceType: 'school_problem', // 我的上传列表都是校本题
    }
  })
}
const addToBooks = async (problem: ProblemDetailType) => {
  const { id, resourceType } = problem
  if (booksIds.value.includes(id)) {
    await booksProblemStore.deleteBookProblems([id])
    message.success('已移出')
  } else {
    if (booksIds.value.length >= 100) {
      message.warning('无法加入，选题本最多支持100题')
      return
    }
    await booksProblemStore.addToBookProblems(
      id,
      'BOOK_PROBLEM_SOURCE_PROBLEM_POOL',
      resourceType === 'school_problem' ? 'SchoolProblem' : 'Problem',
    )
    message.success('已加入')
  }
}
const fetchMyUpload = async () => {
  const params: SchoolProblemParams = {
    difficulty: -1,
    stageId: currentCsv.value?.stageId || '-1',
    subjectId: currentCsv.value?.subjectId || '-1',
    examType: filterModel.examType,
    createdBy: userId.value,
    page: page.value,
    pageSize: pageSize.value,
  }
  if (currentCsv.value?.publisherId && currentCsv.value?.publisherId !== '0') {
    params.publisherId = currentCsv.value.publisherId
  }
  if (currentCsv.value?.semesterId && currentCsv.value?.semesterId !== '0') {
    params.semesterId = currentCsv.value.semesterId
  }
  const res = await getSchoolProblemsApi(params)
  if (res.data) {
    myUploadList.value = res.data
    total.value = res.total
    await fetchSchoolProblemDetails(res.data)
  }
}

const fetchList = async () => {
  await resetFilterModel()
  if (source.value === 'myCollect') {
    // 我的收藏列表，前端分页
    await fetch()
    filterProblemPool.value = favorites.value
    await filterCurrentProblemPool()
  } else {
    // 我的上传获取列表，后端分页
    await fetchMyUpload()
  }
  getFiltersFromSimpleProblem()
  await fetchFavorites()
}

const currentCsv = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}>()
const onCsvSelectChange = async ({
  publisherId,
  stageId,
  subjectId,
  semesterId,
}: {
  publisherId: YcType.CsvId
  stageId: YcType.CsvId
  subjectId: YcType.CsvId
  semesterId: YcType.CsvId
}) => {
  loading.value = true
  currentCsv.value = {
    publisherId,
    semesterId,
    subjectId,
    stageId,
  }
  page.value = 1
  await fetchList()
  endLoading()
}

// 查询到的题目总数
const totalNumber = computed(() => {
  return source.value === 'myCollect'
    ? filterProblemPool.value.length
    : total.value
})
// 当前页面的习题简单结构
const currentPageProblems = computed(() => {
  if (source.value === 'myCollect') {
    return filterProblemPool.value.slice((page.value - 1) * 10, page.value * 10)
  }
  return myUploadList.value
})

// 题型的筛选项，我的收藏与我的上传来源不一样
interface Option {
  label: string
  value: string | number | undefined
}
const examTypeFilters = ref<Option[]>([])
function getFiltersFromSimpleProblem() {
  if (source.value === 'myCollect') {
    examTypeFilters.value = [
      {
        label: '全部',
        value: undefined,
      },
      ...Object.keys(groupBy(favorites.value, 'extra.examType'))
        .map((el) => {
          return {
            value: el,
            label: ExamProblemTypeEnums[el],
          }
        })
        .filter((el) => !!el.value && !!el.label),
    ]
  } else {
    examTypeFilters.value = [
      {
        label: '全部',
        value: undefined,
      },
      {
        label: '单选题',
        value: 'single_choice',
      },
      {
        label: '简答题',
        value: 'shortAnswer',
      },
    ]
  }
}

const favoriteFilter = computed(() => {
  const baseParams: {
    resourceType: FavoriteType
    stage: YcType.CsvId
    subject: YcType.CsvId
    publisher?: YcType.CsvId
    semester?: YcType.CsvId
  } = {
    resourceType: 'problem,school_problem' as FavoriteType,
    stage: currentCsv.value?.stageId || '-1',
    subject: currentCsv.value?.subjectId || '-1',
  }
  if (currentCsv.value?.publisherId && currentCsv.value?.publisherId !== '0') {
    baseParams.publisher = currentCsv.value.publisherId
  }
  if (currentCsv.value?.semesterId && currentCsv.value?.semesterId !== '0') {
    baseParams.semester = currentCsv.value.semesterId
  }
  return {
    ...baseParams,
  }
})

const { favorites, fetch } = useFavorite(favoriteFilter, {
  pageSize: 99999,
})

const { loading, startLoading, endLoading } = useLoading(true)
const filterProblemPool = shallowRef<FavoriteItem[]>([])

async function onFilterChange() {
  page.value = 1
  startLoading()
  if (source.value === 'myCollect') {
    await filterCurrentProblemPool()
  } else {
    await fetchMyUpload()
  }
  endLoading()
}

// 我的收藏题型筛选逻辑
async function filterCurrentProblemPool() {
  let filterProblems: FavoriteItem[] = []
  filterProblems = favorites.value.filter((el) => {
    let examTypeFilter = true
    if (filterModel.examType) {
      examTypeFilter =
        el.extra?.examType === filterModel.examType ||
        el.extra?.type === filterModel.examType
    }
    return examTypeFilter
  })
  filterProblemPool.value = filterProblems
  if (filterProblemPool.value.length > 0) {
    await fetchProblemDetails()
  } else {
    problems.value = []
  }
}

// 获取我的收藏题目详情
async function fetchProblemDetails() {
  if (source.value === 'myUpload') {
    return
  }
  const currentPageSimpleProblem: ProblemSimpleType[] =
    currentPageProblems.value.map((el) => ({
      problemId: (el as any)?.resourceId,
      resourceType: (el as any)?.resourceType,
    }))
  if (currentPageSimpleProblem.length === 0) return
  const res = await fetchDetails(currentPageSimpleProblem)
  problems.value = res.map((el) => {
    return {
      ...el,
      accuracy:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.extra?.accuracy || 0,
      favoriteCount:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.favoriteCount || 0,
      usageCount:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.usageCount || 0,
      region:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.extra?.region || '',
    }
  })
}

const onPageChange = (val: number) => {
  page.value = val
  if (source.value === 'myCollect') {
    fetchProblemDetails()
  } else {
    fetchMyUpload()
  }
}

// 收藏
const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem,school_problem' as FavoriteType,
    stage: currentCsv.value?.stageId,
    subject: currentCsv.value?.subjectId,
    semester: currentCsv.value?.semesterId,
    publisher: currentCsv.value?.publisherId,
  })
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()

const updateProblemFavoriteCount = (problem: ProblemDetailType, add = true) => {
  if (!problem) return
  if (add) {
    const newCount = Number(problem.favoriteCount) + 1
    problem.favoriteCount = newCount.toString()
  } else {
    let newCount = Number(problem.favoriteCount) - 1
    newCount = newCount < 0 ? 0 : newCount
    problem.favoriteCount = newCount.toString()
  }
}

const favoriteItem = async (problem: ProblemDetailType) => {
  const { problemId, resourceType } = problem
  if (!isFavorite(problemId)) {
    await addFavorite({
      resourceType: resourceType,
      resourceId: problemId,
      publisher: currentCsv.value?.publisherId,
      subject: currentCsv.value?.subjectId,
      stage: currentCsv.value?.stageId,
      semester: currentCsv.value?.semesterId,
      extra: {
        accuracy: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.accuracy,
        difficulty: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.difficulty,
        examType: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.examType,
        type: favorites.value.find((el) => el.resourceId === problemId)?.extra
          ?.type,
        region: favorites.value.find((el) => el.resourceId === problemId)?.extra
          ?.region,
      },
    })
    updateProblemFavoriteCount(problem)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    favoriteIds.value.delete(problemId)
    updateProblemFavoriteCount(problem, false)
  }
  buryPoint('click_prepareCenter_collect6', {
    subjectId: parseInt(currentCsv.value?.subjectId as string),
    stageId: parseInt(currentCsv.value?.stageId as string),
    publisherId: parseInt(currentCsv.value?.publisherId as string),
    semesterId: parseInt(currentCsv.value?.semesterId as string),
    pageName: '2',
    problemId,
  })
}

const moveModalShow = ref(false)
const activeProblemId = ref<string>('')
const dialog = useOIWDialog()

const handleMoveClick = (id: string) => {
  moveModalShow.value = true
  activeProblemId.value = id
}

const handleConfirmMoveClick = (data: {
  resourceId: string
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  chapterId: string
  sectionId: string
  subSectionId: string
}) => {
  const { resourceId: problemId, ...baseParams } = data
  updateSchoolProblemApi({
    problemId,
    ...baseParams,
  }).then(() => {
    onFilterChange()
    moveModalShow.value = false
    message.success('移动成功')
  })
}

const handleDeleteClick = (id: string) => {
  activeProblemId.value = id
  dialog.create({
    title: '删除题目',
    content: '您确定要删除该题目吗？',
    negativeText: '取消',
    positiveText: '确定',
    positiveButtonProps: {
      type: 'error',
    },
    onPositiveClick: () => {
      deleteSchoolProblemApi(id).then(() => {
        onFilterChange()
        message.success('删除成功')
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;

  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.medium-select {
  width: 150px;
}

.cascader-collection {
  ::v-deep(.n-base-selection) {
    height: 34px !important;
    font-size: 12px;
    border-radius: 12px;
    --n-height: 32px !important;
  }
}
</style>
