<template>
  <section class="page-content teacher-v2">
    <Breadcrumb
      :data="[
        {
          name: '个性化学习',
          href: '/teacher-workbench/eeV2/list',
        },
        {
          name: `校级考点预览`,
          href: '/teacher-workbench/eeV2/schoolLearningTask/list',
        },
        {
          name: `详情`,
        },
      ]"
    />
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else>
      <div
        v-if="planInfo"
        class="flex mt-24px relative"
        style="align-items: flex-end"
      >
        <div class="plan-name">
          {{ SubjectEnum[planInfo.subjectId as keyof typeof SubjectEnum]






          }}{{ planInfo.sceneName }}
        </div>
        <div class="plan-name2">
          {{ GradeEnum[planInfo.grade as keyof typeof GradeEnum]






          }}{{ planInfo.term === 'last' ? '上学期' : '下学期' }}
        </div>
        <div
          class="get-sync-btn"
          :class="{ disabled: syncDisabled || isLocked }"
          @click="listSync"
        >
          获取区域配置
        </div>
      </div>
      <div v-if="planInfo" class="main-content">
        <div class="content-tip">
          <div class="flex">
            <n-tooltip placement="bottom" trigger="hover" raw>
              <template #trigger>
                <span class="syncClassCount"
                  >{{ planInfo.unSyncClassCount }}个班级未同步</span
                >
              </template>
              <div class="popover-tip">
                <div class="popover-tip-content">
                  未同步指班级学习计划考点配置与校级考点配置不一致，请查看后手动同步
                </div>
              </div>
            </n-tooltip>
            <span class="unSyncClassCount"
              >{{ planInfo.syncClassCount }}个班级已经同步</span
            >
            <span
              v-if="planInfo.lastSyncAt !== '0001-01-01T00:00:00Z'"
              class="lastSyncAt"
            >
              最后同步时间：{{
                dayjs(planInfo.lastSyncAt).format('YYYY-MM-DD HH:mm:ss')
              }}
            </span>
          </div>
          <NButton
            class="button-secondary"
            style="width: 104px; border-radius: 80px"
            secondary
            :loading="isLocked"
            :disabled="isLocked"
            @click="groupShow = true"
          >
            查看
          </NButton>
        </div>
        <div class="table-top mb-24px mt-24px">
          <div class="table-tab-box">
            <div
              class="table-tab-item"
              :class="difficult === 'PROBLEM_DIFFICULTY_HARD' ? 'active' : ''"
              @click="getPoints('PROBLEM_DIFFICULTY_HARD')"
            >
              压轴
            </div>
            <div
              class="table-tab-item"
              :class="difficult === 'PROBLEM_DIFFICULTY_MIDDLE' ? 'active' : ''"
              @click="getPoints('PROBLEM_DIFFICULTY_MIDDLE')"
            >
              中等
            </div>
            <div
              class="table-tab-item"
              :class="difficult === 'PROBLEM_DIFFICULTY_BASIC' ? 'active' : ''"
              @click="getPoints('PROBLEM_DIFFICULTY_BASIC')"
            >
              基础
            </div>
          </div>
          <span
            v-if="planInfo.lastUpdatedAt !== '0001-01-01T00:00:00Z'"
            class="lastUpdatedAt"
            >校级考点最后更新时间：{{
              dayjs(planInfo.lastUpdatedAt).format('YYYY-MM-DD HH:mm:ss')
            }}</span
          >
          <div class="flex align-center">
            <NButton
              class="button-secondary mr-16px"
              secondary
              :disabled="isLocked"
              @click="handleEdit"
            >
              {{ draggable ? '完成' : '调整考点' }}
            </NButton>
            <n-popover
              v-if="!downloading"
              to=".table-top"
              trigger="hover"
              placement="bottom-end"
              :show-arrow="false"
              class="download-select"
            >
              <template #trigger>
                <NButton
                  class="button-secondary download-btn"
                  secondary
                  style="padding: 0 22px"
                  :disabled="downloading"
                  :loading="downloading"
                >
                  下载学案
                  <img
                    class="ml-4px"
                    width="16"
                    src="https://fp.yangcong345.com/onion-extension/切图 2@1x (1)-750e8feb616045e0918299771b74f8ae.png"
                    alt=""
                  />
                </NButton>
              </template>
              <div>
                <div
                  class="select-item"
                  @click="download('PROBLEM_DIFFICULTY_HARD')"
                >
                  压轴考点学案
                </div>
                <div
                  class="select-item"
                  @click="download('PROBLEM_DIFFICULTY_MIDDLE')"
                >
                  中等考点学案
                </div>
                <div
                  class="select-item"
                  @click="download('PROBLEM_DIFFICULTY_BASIC')"
                >
                  基础考点学案
                </div>
              </div>
            </n-popover>
            <NButton
              v-else
              class="button-secondary download-btn downloading"
              secondary
              style="padding: 0 22px"
              :disabled="downloading"
              :loading="downloading"
            >
              下载中
            </NButton>
          </div>
        </div>
        <PreviewsTable
          v-if="!draggable"
          type="school"
          :points-data="pointsData"
          @point-video="toPointVideo"
          @point-pdf="toPointPdf"
        />
        <PreviewsDraggable
          v-else
          type="school"
          :points-data="pointsData"
          :un-learn-topic="unLearnTopic"
          @update-plan-topic="updatePlanTopic"
          @cancel-draggable="cancelDraggable"
          @all-sub-item-show="allSubItemShow"
          @drag-over-item="dragOverItem"
          @learn-topic-change="learnTopicChange"
        />
      </div>
    </div>
    <n-drawer
      v-model:show="groupShow"
      :width="580"
      placement="right"
      to=".teacher-v2"
    >
      <n-drawer-content title="班级考点配置" closable class="drawer-content">
        <n-data-table
          v-model:checked-row-keys="checkedRowKeysRef"
          :columns="columns"
          :data="groups"
          :bordered="false"
          :row-key="(row) => row.planId"
        />
        <div class="oi-footer-btns groups-footer">
          <div class="oi-cancel-btn" @click="groupShow = false">取消</div>
          <div
            class="oi-confirm-btn"
            :class="{ disable: checkedRowKeysRef.length === 0 || isLocked }"
            @click="syncSchoolGroupPlans"
          >
            现在同步
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </section>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { OIWLoading, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import dayjs from 'dayjs'
import type { DataTableColumns } from 'naive-ui'
import type {
  TeacherPoint,
  Difficult,
  SchoolLearningTaskDetail,
  SchoolLearningTaskGroup,
} from '../service'
import {
  GradeEnum,
  SubjectEnum,
  getSchoolLearningTaskDetailApi,
  getSchoolPointsApi,
  getTopicInfoApi,
  updateSchoolPointsApi,
  downloadSchoolPlanApi,
  getSchoolPlanUrlApi,
  getSchoolDetailSyncApi,
  getSchoolLearningTaskGroupApi,
  syncSchoolGroupPlansApi,
  getSyncLockStateApi,
  postSchoolLearningTaskPointCases,
} from '../service'
import { buryPoint } from '@/utils/buryPoint'
import PreviewsTable from '../components/previewsTable.vue'
import PreviewsDraggable from '../components/previewsDraggable.vue'

const loading = ref(true)
const isLocked = ref(false)
const isLockedTimer = ref()

const route = useRoute()
const router = useRouter()
const message = useOIWMessage()
const dialog = useDialog()

const maxPollingTime = 1800 // 最大轮询次数1800次，每秒一次，最大30min
const planInfo = ref<SchoolLearningTaskDetail>()
const points = ref<TeacherPoint[]>([])
const difficult = ref<Difficult>('PROBLEM_DIFFICULTY_HARD')
const draggable = ref(false)
const timer = ref()
const pollingTime = ref(0)
const groupShow = ref(false)
const groups = ref<SchoolLearningTaskGroup[]>()
const allItemShow = ref(false)
const unLearnTopic = ref(true)

const getTaskDetail = () => {
  getSchoolLearningTaskDetailApi({
    schoolLearningTaskId: route.query.id as string,
  }).then((res) => {
    planInfo.value = res
  })
}

const getPointsInfo = async (pointItem: TeacherPoint) => {
  const res = await getTopicInfoApi(
    pointItem.topics.map((i) => i.id) as string[],
  )
  if (res) {
    res.items.forEach((item) => {
      pointItem.topics.forEach((a) => {
        if (item.topicId === a.id) {
          a.topicName = item.topicName
        }
      })
    })
  }
}

const getGroups = () => {
  getSchoolLearningTaskGroupApi(route.query.id as string).then((res) => {
    groups.value = res.items
  })
}

const checkedRowKeysRef = ref<string[]>([])

const columns: DataTableColumns<SchoolLearningTaskGroup> = [
  {
    type: 'selection',
    multiple: true,
    disabled(row: SchoolLearningTaskGroup) {
      return row.synchronized === true
    },
  },
  { key: 'groupName', title: '班级' },
  {
    key: 'synchronized',
    title: '是否与校级考点配置同步',
    render(row: SchoolLearningTaskGroup) {
      return row.synchronized ? '是' : <span style={{ color: 'red' }}>否</span>
    },
  },
]

const syncSchoolGroupPlans = () => {
  if (isLocked.value || checkedRowKeysRef.value.length === 0) return
  buryPoint('click_Pc_eeV2_schoolPoint_sync', {}, 'homework')
  dialog.info({
    showIcon: false,
    title: '提示',
    content: `将要把校级考点配置同步给${checkedRowKeysRef.value.length}个班级，是否确认`,
    onPositiveClick: () => {
      isLocked.value = true
      syncSchoolGroupPlansApi({
        schoolLearningTaskId: route.query.id as string,
        planIds: checkedRowKeysRef.value,
      })
        .then(() => {
          groupShow.value = false
          draggable.value = false
          startLockStateLoop()
          getTaskDetail()
          getPoints(difficult.value)
          message.success('同步成功')
        })
        .catch((err) => {
          checkedRowKeysRef.value = []
          isLocked.value = false
          message.error(err.response.data.message)
        })
    },
    positiveText: '现在同步',
    negativeText: '取消',
  })
}
const pointsData = computed(() => {
  if (draggable.value) {
    return points.value
  } else {
    return points.value.filter((i) => !i.isClose)
  }
})

const getPoints = async (val: Difficult) => {
  if (draggable.value) {
    const data = {
      schoolLearningTaskId: route.query.id as string,
      items: points.value,
    }
    await updateSchoolPointsApi(data)
    getTaskDetail()
  }

  difficult.value = val
  getSchoolPointsApi({
    schoolLearningTaskId: route.query.id as string,
    difficult: val,
  }).then((res) => {
    getPointCases(res.items, true)
  })
}

const toPointPdf = (pointId: string) => {
  router.push({
    name: 'eeV2SchoolTaskDetailPdf',
    query: {
      id: route.query.id,
      pointId,
      difficult: difficult.value,
      tab: 'pdf',
    },
  })
}
const toPointVideo = (pointId: string) => {
  router.push({
    name: 'eeV2SchoolTaskDetailPdf',
    query: {
      id: route.query.id,
      pointId,
      difficult: difficult.value,
      tab: 'video',
    },
  })
}

const startLockStateLoop = () => {
  getSyncLockStateApi(route.query.id as string).then((res) => {
    isLocked.value = res.isLocked
    if (res.isLocked) {
      isLockedTimer.value = setInterval(() => {
        getSyncLockStateApi(route.query.id as string).then((res) => {
          isLocked.value = res.isLocked
          if (!res.isLocked) {
            getTaskDetail()
            clearInterval(isLockedTimer.value)
          }
        })
      }, 1000)
    }
  })
}

onMounted(() => {
  getTaskDetail()
  getPoints('PROBLEM_DIFFICULTY_HARD')
  getGroups()
  startLockStateLoop()
})

const syncDisabled = ref(false)
const listSync = () => {
  if (syncDisabled.value || isLocked.value) return
  dialog.info({
    showIcon: false,
    title: '提示',
    content:
      '该操作会将区域考点的调整同步至校级考点配置（不会调整校级考点顺序），是否确认操作？',
    onPositiveClick: () => {
      syncDisabled.value = true
      getSchoolDetailSyncApi(route.query.id as string).then((res) => {
        syncDisabled.value = false
        if (res.updateCount === 0) {
          message.info('已是最新配置')
        } else {
          message.success(`${res.updateCount}个配置发生变更`)
          getTaskDetail()
          getPoints(difficult.value)
        }
      })
    },
    positiveText: '确认',
    negativeText: '取消',
  })
}

const cancelDraggable = () => {
  unLearnTopic.value = true
  draggable.value = false
  getPoints(difficult.value)
}

const downloading = ref(false)
const download = (difficult: string) => {
  if (downloading.value) return
  buryPoint(
    'click_Pc_eeV2_schoolPoint_download',
    {
      button:
        difficult === 'PROBLEM_DIFFICULTY_HARD'
          ? '1'
          : difficult === 'PROBLEM_DIFFICULTY_MIDDLE'
            ? '2'
            : '3',
    },
    'homework',
  )
  downloadSchoolPlanApi({
    schoolLearningTaskId: route.query.id as string,
    difficult,
  }).then((res) => {
    message.info('系统正在请求下载，请勿关闭页面，耐心等待哦')
    downloading.value = true
    timer.value = setInterval(() => {
      pollingTime.value = pollingTime.value + 1
      // 超过最大次数，结束轮询
      if (pollingTime.value > maxPollingTime) {
        clearInterval(timer.value)
        downloading.value = false
      }
      getSchoolPlanUrlApi({ id: res.id })
        .then((res) => {
          if (res.url) {
            window.open(res.url, '_blank')
            clearInterval(timer.value)
            downloading.value = false
          }
        })
        .catch((err) => {
          message.error('下载失败，请重试', err.response.data.message)
          clearInterval(timer.value)
          downloading.value = false
        })
    }, 1000)
  })
}

const handleEdit = () => {
  if (!draggable.value) {
    buryPoint('click_Pc_eeV2_schoolPoint_adjust', {}, 'homework')
  }
  if (draggable.value) {
    updatePlanTopic(true)
  }
  draggable.value = !draggable.value
}

const updatePlanTopic = (isConfirm: boolean) => {
  if (isConfirm) {
    schoolUpdatePlanTopic(isConfirm)
  } else {
    getPointCases(points.value, false)
  }
}

const schoolUpdatePlanTopic = (isConfirm: boolean) => {
  if (isConfirm) {
    unLearnTopic.value = true
    points.value = points.value.map((i) => {
      return { ...i, isSubItemShow: false }
    })
  }
  const data = {
    schoolLearningTaskId: route.query.id as string,
    items: points.value,
    isConfirm,
  }
  updateSchoolPointsApi(data)
    .then((res) => {
      if (res) {
        getSchoolLearningTaskDetailApi({
          schoolLearningTaskId: route.query.id as string,
        }).then((res) => {
          planInfo.value = res
        })
        if (planInfo.value && isConfirm) {
          if (planInfo.value.unSyncClassCount > 0) {
            dialog.info({
              showIcon: false,
              title: `校级考点调整成功！${planInfo.value.unSyncClassCount}个班级未同步`,
              content:
                '校级考点调整后，需要选择同步到班级，才会在对应班级中生效。',
              onPositiveClick: () => {
                buryPoint(
                  'click_Pc_eeV2_schoolPoint_adjust_pop',
                  { button: '1' },
                  'homework',
                )
                groupShow.value = true
              },
              onNegativeClick: () => {
                buryPoint(
                  'click_Pc_eeV2_schoolPoint_adjust_pop',
                  { button: '2' },
                  'homework',
                )
              },
              positiveText: '现在同步',
              negativeText: '稍后同步',
            })
          } else {
            message.success('考点调整已生效')
          }
          draggable.value = false
        }
      }
    })
    .catch((err) => {
      message.error(err.response.data.message)
      getSchoolPointsApi({
        schoolLearningTaskId: route.query.id as string,
        difficult: difficult.value,
      }).then((res) => {
        getPointCases(res.items, false)
      })
    })
}

const getPointCases = (items: any, type: boolean) => {
  postSchoolLearningTaskPointCases({
    schoolLearningTaskId: route.query.id as string,
    items,
    difficult: difficult.value,
  }).then((res) => {
    if (type) {
      points.value = items.map((i: any) => {
        const cases = res.items.find((item) => item.pointId === i.pointId)
        if (cases) {
          return {
            ...i,
            learnCases: cases.learnCases,
            unLearnCases: cases.unLearnCases,
            isSubItemShow: false,
          }
        }
        return { ...i, isSubItemShow: false }
      })
      allItemShow.value = false
      loading.value = false
      if (points.value.length > 0) {
        points.value.forEach(async (pointItem) => {
          await getPointsInfo(pointItem)
        })
      }
    } else {
      points.value = items.map((i: any) => {
        const cases = res.items.find((item) => item.pointId === i.pointId)
        if (cases) {
          return {
            ...i,
            learnCases: cases.learnCases,
            unLearnCases: cases.unLearnCases,
          }
        }
        return { ...i }
      })
    }
  })
}

const allSubItemShow = () => {
  allItemShow.value = !allItemShow.value
  if (!allItemShow.value) {
    points.value.forEach((i) => {
      i.isSubItemShow = false
    })
  } else {
    points.value.forEach((i) => {
      i.isSubItemShow = true
    })
  }
}

const dragOverItem = (targetItem: TeacherPoint, draggedItem: TeacherPoint) => {
  const draggedIndex = points.value.findIndex(
    (item) => item.pointId === draggedItem.pointId,
  )
  const targetIndex = points.value.findIndex(
    (item) => item.pointId === targetItem.pointId,
  )

  if (
    draggedIndex !== -1 &&
    targetIndex !== -1 &&
    draggedIndex !== targetIndex
  ) {
    points.value.splice(draggedIndex, 1)
    points.value.splice(targetIndex, 0, draggedItem)
  }
}

watch(groupShow, (val) => {
  if (!val) {
    checkedRowKeysRef.value = []
    groups.value = []
  } else {
    getGroups()
  }
})
const learnTopicChange = (val: boolean) => {
  unLearnTopic.value = val
}
</script>

<style lang="scss" scoped>
.page-content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  padding: 30px calc((100vw - 1200px) / 2);
  overflow-y: scroll;
  background: #f5f7fe;

  &::-webkit-scrollbar {
    width: 0 !important;
  }

  .button-secondary {
    border-radius: 12px;
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    justify-content: center;
    height: 70vh;

    img {
      width: 288px;
      height: 168px;
    }

    .em-h1 {
      margin-top: 12px;
      margin-top: 24px;
      font-family: 'PingFang SC';
      font-size: 19px;
      font-weight: 600;
      color: #393548;
    }

    .refresh-btn {
      margin-top: 24px;
      border-radius: 80px;
    }
  }

  .plan-name {
    font-size: 28px;
    font-weight: 600;
    color: #393548;
  }

  .plan-name2 {
    display: inline-block;
    padding-left: 8px;
    margin-left: 8px;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    color: #8a869e;
    border-left: 1px solid #8a869e;
  }

  .get-sync-btn {
    position: absolute;
    right: 24px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;

    /* b5 标准色 辅助按钮default */
    color: #5e80ff;
    cursor: pointer;

    ::v-deep(.n-spin-body) {
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }

    &:hover {
      text-decoration: underline;
    }

    &.disabled {
      color: #8a869e;
      cursor: not-allowed;
    }
  }

  .main-content {
    width: 1200px;
    padding: 24px;
    margin-top: 24px;
    margin-bottom: 70px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
    opacity: 1;

    .content-tip {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 1152px;
      padding: 22px 24px;
      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      color: #393548;

      /* b1浅色块底色 focus色 */
      background: #f4f6ff;

      /* 按钮圆角 */
      border-radius: 12px;
      opacity: 1;

      .syncClassCount {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;

        /* n8大标题文本色 */
        color: #393548;
        letter-spacing: 0px;

        &::before {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/32-叹号@2x-58b381fc7c9d2243274fbb22c81ed06e.png')
            no-repeat;
          background-size: contain;
        }
      }

      .unSyncClassCount {
        display: flex;
        align-items: center;
        margin-left: 24px;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;

        /* n8大标题文本色 */
        color: #8a869e;
        letter-spacing: 0px;

        &::before {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/32-正确@2x-d794863ed94b9126c69655c831841f13.png')
            no-repeat;
          background-size: contain;
        }
      }

      .lastSyncAt {
        display: flex;
        align-items: center;
        margin-left: 16px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;

        /* n8大标题文本色 */
        color: #8a869e;
        letter-spacing: 0px;
      }

      // &::before {
      //   content: '';
      //   display: inline-block;
      //   width: 24px;
      //   height: 24px;
      //   position: absolute;
      //   background: url('https://fp.yangcong345.com/onion-extension/全局提示@2x-399699f384e5e9573503c20c7b56c5c1.png') no-repeat;
      //   background-size: contain;
      //   top: 16px;
      //   left: 24px;
      // }
    }

    .table-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .download-btn {
        img {
          transition: 0.3s ease-in-out;
        }

        &:hover {
          img {
            transform: rotate(180deg);
          }
        }
      }

      .downloading {
        &:hover {
          color: unset;
          background: unset;
          border: 1px solid #d5d2dd;
        }
      }

      ::v-deep(.download-select) {
        box-sizing: border-box;
        padding: 16px;

        /* n1 纯白 */
        background: #ffffff;

        /* n3 选项卡底色 */
        border: 1px solid #e4e9f7;
        border-radius: 16px;

        box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
        opacity: 1;

        .select-item {
          width: 148px;
          height: 48px;
          font-size: 18px;
          line-height: 48px;
          color: #393548;
          text-align: center;
          cursor: pointer;
          border-radius: 12px;

          &:hover {
            font-weight: 500;
            background: #f4f6ff;
          }
        }
      }
    }

    .lastUpdatedAt {
      flex: 1;
      margin-left: 16px;
      font-size: 14px;

      font-variation-settings: 'opsz' auto;
      font-weight: normal;
      line-height: 16px;

      /* n6 输入框文本、注解文本色 */
      color: #8a869e;
      letter-spacing: 0px;
    }

    .table-tab-box {
      display: flex;
      flex-direction: row;
      width: fit-content;
      padding: 4px;
      border: 1px solid #e4e9f7;
      border-radius: 314px;

      .table-tab-item {
        padding: 8px 24px;
        font-size: 16px;
        font-weight: normal;
        line-height: 16px;
        color: #8a869e;
        cursor: pointer;
        border-radius: 109px;

        &.active {
          color: #5e80ff;
          background: #f4f6ff;
        }
      }
    }

    ul {
      list-style: none;
    }

    li {
      position: relative;
      min-height: 61px;
      margin-bottom: 8px;
      font-family: 'PingFang SC';
      // border-bottom: 1px solid #DCD8E7;
      font-size: 17px;
      font-weight: 500;
      line-height: 61px;
      color: #393548;
      border-radius: 12px;

      &::after {
        position: absolute;
        bottom: -4px;
        display: inline-block;
        width: 100%;
        height: 1px;
        content: '';
        background: #dcd8e7;
      }

      .item-main {
        display: flex;
        align-items: center;

        &:hover {
          background: #f4f6ff;
        }

        .hint {
          box-sizing: border-box;
          display: inline-block;
          width: 48px;
          height: 23px;
          padding: 0 15px;
          margin-right: 15px;
          margin-left: 15px;
          background: url('https://fp.yangcong345.com/onion-extension/icon 拖动@2x-f3d612fa6dcbc5374ef686d7396e8ce3.png')
            no-repeat center;
          background-size: contain;
        }

        .item-kaodian {
          display: flex;
          // padding-left: 24px;
          flex-shrink: 0;
          align-items: center;
          width: 544px;

          &.draggable {
            width: 1000px;
          }

          .pointName {
            &.grey {
              color: #9792ac;
            }
          }
        }

        .item-topic {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          width: 260px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            display: inline-block;
            max-width: 170px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .kaodian-hint {
            display: inline-block;
            width: 21px;
            height: 21px;
            margin-left: 9px;
            background: url('https://fp.yangcong345.com/onion-extension/切图 2@2x (1)-cfb1cd141f0ed118846fbe2b1ebc586c.png')
              no-repeat;
            background-size: contain;
          }
        }

        .item-operate {
          display: flex;
          justify-content: space-between;
          width: 260px;
          padding-right: 20px;
          padding-left: 20px;

          .operate-btn {
            font-size: 16px;
            font-weight: 600;
            line-height: 22px;
            color: #5e80ff;
            background: transparent;
          }
        }
      }

      .sub-topic-list {
        line-height: 50px;

        .sub-topic-item {
          display: flex;
          align-items: center;

          .topic-name {
            position: relative;
            width: 1000px;
            padding-left: 136px;
            color: #393548;

            &::before {
              position: absolute;
              top: 21px;
              left: 120px;
              width: 8px;
              height: 8px;
              content: '';
              background: #393548;
              border-radius: 50%;
            }

            &.grey {
              color: #9792ac;

              &::before {
                background: #9792ac;
              }
            }
          }
        }
      }
    }

    .dragging {
      cursor: grab;
      background-color: #f5f5f6;

      opacity: 0.8;
    }

    .order-box {
      flex: 1;

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 360px;
          margin: 36px;
        }

        div {
          margin-bottom: 48px;
          font-size: 20px;
          font-weight: 600;
          color: #57526c;
        }
      }

      .oi-footer-btns {
        position: fixed;
        bottom: 20px;
        left: calc((100vw - 1200px) / 2 + 24px);
        width: 1153px;
      }

      .table-header {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 64px;
        padding: 0 24px;
        margin-bottom: 4px;
        font-size: 18px;
        font-weight: normal;
        line-height: 24px;

        /* n8大标题文本色 */
        color: #393548;
        text-align: left;
        letter-spacing: 0px;

        /* 灰（标签色）表头色 */
        background: #f7f7f9;
        border-radius: 12px;
        opacity: 1;

        div {
          flex-shrink: 0;
        }
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;

        /* Safari/Chrome/Webkit */
      }

      &::-moz-scrollbar-track {
        background-color: transparent;

        /* Firefox */
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcd8e7;

        /* Safari/Chrome/Webkit */
        border-radius: 10px;
      }

      &::-moz-scrollbar-thumb {
        background-color: #dcd8e7;

        /* Firefox */
        border-radius: 10px;
      }

      &::-webkit-scrollbar-corner {
        background-color: transparent;

        /* Safari/Chrome/Webkit */
      }

      &::-webkit-scrollbar {
        width: 8px;

        /* Safari/Chrome/Webkit */
      }

      &::-moz-scrollbar {
        width: 8px;

        /* Firefox */
      }
    }
  }
}

.popover-tip {
  padding: 10px 12px;
  background: #504b64;
  border-radius: 12px;
  opacity: 1;

  .popover-tip-title {
    margin-bottom: 12px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    color: #d4d1dd;
  }

  .popover-tip-content {
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #ffffff;

    span {
      margin-right: 20px;
      white-space: nowrap;
    }
  }
}

.drawer-content {
  padding-bottom: 80px;

  ::v-deep(.n-drawer-body-content-wrapper) {
    position: relative;
  }

  .groups-footer {
    position: fixed;

    /* left: 16px; */
    right: 16px;
    bottom: 16px;
    width: 547px;

    .disable {
      background: #c5c1d4;
    }
  }
}
</style>
