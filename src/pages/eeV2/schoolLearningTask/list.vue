<template>
  <section class="page-content teacher-v2">
    <Breadcrumb
      :data="[
        {
          name: '个性化学习',
          href: '/teacher-workbench/eeV2/list',
        },
        {
          name: `校级考点预览`,
        },
      ]"
    />
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else>
      <div v-if="list.length !== 0" class="main-content">
        <div class="sync" @click="listSync">获取区域配置</div>

        <n-data-table :columns="columns" :data="list" :bordered="false" />
      </div>

      <div v-else class="empty">
        <img
          src="https://fp.yangcong345.com/onion-extension/组 4463-92bc61c213c2b060c5682f999504e3c6.png"
          alt=""
        />
        <div class="em-h1">未获取到区域考点配置</div>
        <n-button class="refresh-btn" round @click="refresh"> 刷新 </n-button>
      </div>
    </div>
  </section>
</template>

<script setup lang="tsx">
import Breadcrumb from '../components/Breadcrumb/index.vue'
import type { DataTableColumns } from 'naive-ui'
import type { SchoolLearningTaskList } from '../service'
import {
  refreshSchoolLearningTaskApi,
  getSchoolLearningTaskListApi,
  SubjectEnum,
  GradeEnum,
  StageEnum,
  getSchoolLearningTaskSyncApi,
} from '../service'
import { OIWLoading, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import { buryPoint } from '@/utils/buryPoint'

const router = useRouter()
const loading = ref(true)

const message = useOIWMessage()

const list = ref<SchoolLearningTaskList[]>([])
getSchoolLearningTaskListApi()
  .then((res) => {
    loading.value = false
    list.value = res.items
  })
  .finally(() => {
    loading.value = false
  })

const refresh = () => {
  refreshSchoolLearningTaskApi().then(() => {
    getSchoolLearningTaskListApi().then((res) => {
      list.value = res.items
    })
  })
}

const syncDisabled = ref(false)
const listSync = () => {
  if (syncDisabled.value) return
  syncDisabled.value = true
  getSchoolLearningTaskSyncApi().then((res) => {
    syncDisabled.value = false
    if (res.updatedCount === 0) {
      message.info('已是最新配置')
    } else {
      message.success(`${res.updatedCount}个配置发生变更`)
      getSchoolLearningTaskListApi().then((res) => {
        list.value = res.items
      })
    }
  })
}
const toDetail = (id: string) => {
  router.push({
    name: 'eeV2SchoolTaskDetail',
    query: {
      id,
    },
  })
}

const columns: DataTableColumns<SchoolLearningTaskList> = [
  {
    title: '学段',
    key: 'stageId',
    render: (row) => {
      return <div>{StageEnum[row.stageId as keyof typeof StageEnum]}</div>
    },
  },
  {
    title: '学科',
    key: 'subjectId',
    render: (row) => {
      return <div>{SubjectEnum[row.subjectId as keyof typeof SubjectEnum]}</div>
    },
  },
  {
    title: '年级',
    key: 'grade',
    render: (row) => {
      return <div>{GradeEnum[row.grade as keyof typeof GradeEnum]}</div>
    },
  },
  {
    title: '学期',
    key: 'term',
    render: (row) => {
      return <div>{row.term === 'last' ? '上学期' : '下学期'}</div>
    },
  },
  {
    title: '场景',
    key: 'sceneName',
  },
  {
    title: '操作',
    key: 'opr',
    render: (row) => {
      return (
        <div class="detail" onClick={() => toDetail(row.id)}>
          查看
        </div>
      )
    },
  },
]

onMounted(() => {
  buryPoint('enterSchoolLevelExamPointPreviewPage', {}, 'course')
})
</script>

<style lang="scss" scoped>
.page-content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  padding: 30px calc((100vw - 1200px) / 2);
  overflow-y: scroll;
  background: #f5f7fe;

  &::-webkit-scrollbar {
    width: 0 !important;
  }

  .button-secondary {
    border-radius: 12px;
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    justify-content: center;
    height: 70vh;

    img {
      width: 288px;
      height: 168px;
    }

    .em-h1 {
      margin-top: 12px;
      margin-top: 24px;
      font-family: 'PingFang SC';
      font-size: 19px;
      font-weight: 600;
      color: #393548;
    }

    .refresh-btn {
      margin-top: 24px;
      border-radius: 80px;
    }
  }

  .main-content {
    position: relative;
    width: 1200px;
    padding: 24px;
    margin-top: 24px;
    margin-bottom: 70px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
    opacity: 1;

    ::v-deep(.detail) {
      font-variation-settings: 'opsz' auto;
      // font-size: 14px;
      font-weight: 600;
      line-height: 14px;

      /* b5 标准色 辅助按钮default */
      color: #5e80ff;
      letter-spacing: 0px;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .sync {
      position: absolute;
      top: -40px;
      right: 24px;
      font-size: 14px;

      font-variation-settings: 'opsz' auto;
      font-weight: 600;
      line-height: 14px;

      /* b5 标准色 辅助按钮default */
      color: #5e80ff;
      letter-spacing: 0px;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.popover-tip {
  padding: 10px 12px;
  background: #504b64;
  border-radius: 12px;
  opacity: 1;

  .popover-tip-title {
    margin-bottom: 12px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    color: #d4d1dd;
  }

  .popover-tip-content {
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #ffffff;

    span {
      margin-right: 20px;
      white-space: nowrap;
    }
  }
}
</style>
