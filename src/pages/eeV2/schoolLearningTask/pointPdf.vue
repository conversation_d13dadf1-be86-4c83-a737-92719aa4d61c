<template>
  <section class="page-content teacher-v2">
    <Breadcrumb
      :data="[
        {
          name: '个性化学习',
          href: '/teacher-workbench/eeV2/list',
        },
        {
          name: `校级考点预览`,
          href: '/teacher-workbench/eeV2/schoolLearningTask/list',
        },
        {
          name: `详情`,
          href: `/teacher-workbench/eeV2/schoolLearningTask/detail?id=${route.query.id}`,
        },
        {
          name: '学案',
        },
      ]"
    />
    <div class="page-content-inner">
      <div class="page-left">
        <n-select
          v-model:value="difficult"
          class="oi-select w-100% mb-16px"
          :options="difficultSelect"
          :consistent-menu-width="false"
          :to="false"
          @update:value="getPoints"
        />
        <div class="points">
          <div v-for="item in points" :key="item.pointId" class="point-item">
            <div :id="item.pointId" class="point-name" :title="item.pointName">
              {{ item.pointName }}
            </div>
            <div
              v-for="topic in item.topics"
              :key="topic.topicId"
              class="topic"
              :class="{ active: topic.topicId === topicId }"
              :title="topic.topicName"
              @click="changeTopic(topic)"
            >
              {{ topic.topicName
              }}{{ topic.kind === 'optional' ? '（选学）' : '（必学）' }}
            </div>
          </div>
        </div>
      </div>
      <div class="page-right">
        <div class="tabs-list">
          <div
            :class="{ 'tab-item': true, active: tab === 'pdf' }"
            @click="switchTab('pdf')"
          >
            学案
          </div>
          <div
            :class="{ 'tab-item': true, active: tab === 'video' }"
            @click="switchTab('video')"
          >
            微课
          </div>
        </div>
        <div v-if="tab === 'pdf' && topicId" ref="pdfViewerRef" class="pdf-tab">
          <div v-if="pdfSrc === ''" class="empty">
            <img
              src="https://fp.yangcong345.com/onion-extension/组 4463@2x (1)-0bf71936a68c6a4953ea65278ce89a56.png"
              alt=""
            />
            <div>该知识点暂无学案</div>
          </div>
          <canvas
            v-for="page in numOfPages"
            :id="'preview-pdf' + page"
            :key="page"
          />
        </div>
        <div v-if="tab === 'video' && topicId" class="video-tab">
          <div v-if="!videoId" class="empty">
            <img
              src="https://fp.yangcong345.com/onion-extension/组 4463@2x (1)-0bf71936a68c6a4953ea65278ce89a56.png"
              alt=""
            />
            <div>该知识点暂无微课</div>
          </div>
          <div v-if="videoId" class="video-box">
            <SimpleVideo :topic-id="topicId" :base-video="baseVideo" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import Breadcrumb from '../components/Breadcrumb/index.vue'
import SimpleVideo from '@/components/VideoPlayer/simpleVideo.vue'
import * as pdfjsLib from 'pdfjs-dist'
import type { TeacherPoint, Difficult } from '../service'
import {
  getSchoolPointsApi,
  getTopicInfoApi,
  getPlanTopicPdfApi,
  getAddressList,
  getVideoAddressApi,
} from '../service'
const route = useRoute()
const message = useOIWMessage()
const pdfViewerRef = ref<HTMLElement | null>(null)
const pdfSrc = ref<string>('')
const topicId = ref('')
const videoId = ref('')
const numOfPages = ref(0)
let pdfDoc: any = ''
const pdfScale = ref<number>(1)
const points = ref<TeacherPoint[]>([])
const tab = ref<string>((route.query.tab as string) || 'pdf')

const difficult = ref<Difficult>(route.query.difficult as Difficult)
const difficultSelect = ref([
  {
    label: '基础难度',
    value: 'PROBLEM_DIFFICULTY_BASIC',
  },
  {
    label: '中等难度',
    value: 'PROBLEM_DIFFICULTY_MIDDLE',
  },
  {
    label: '压轴难度',
    value: 'PROBLEM_DIFFICULTY_HARD',
  },
])
const baseVideo = computed(() => ({
  autoplay: false,
}))
const getPoints = async (val: Difficult): Promise<void> => {
  difficult.value = val
  await getSchoolPointsApi({
    schoolLearningTaskId: route.query.id as string,
    difficult: val,
  }).then((res) => {
    points.value = res.items
    mergeTopicDetailsToPoints(points.value).then(() => {
      // console.log('处理完成后的 points 数组:', points.value)
    })
  })
}

const changeTopic = async (topic: {
  topicId?: string
  topicName?: string
  videoId?: string
}) => {
  topicId.value = topic.topicId || ''
  videoId.value = ''
  getPlanTopicPdf(topicId.value)
  nextTick(() => {
    videoId.value = topic.videoId || ''
    getPlanTopicVideo(videoId.value, topicId.value)
  })
}

const getPlanTopicPdf = (_topicId: string) => {
  topicId.value = _topicId
  getPlanTopicPdfApi({ topicId: _topicId }).then((res) => {
    pdfSrc.value = res.pdfUrl
    nextTick(() => {
      initPdf()
    })
  })
}
const getPlanTopicVideo = async (videoId: string, topicId: string) => {
  const address: any = await getVideoAddressApi(videoId, topicId)
  if (address) {
    const videoAddresss = getAddressList(address.videoList[0].address)
    if (!videoAddresss.length) {
      message.error('该知识点为付费知识点，请前往教师端APP开通权益后重试')
    }
  }
}

onMounted(async () => {
  // fecth()
  await getPoints(route.query.difficult as Difficult)
})

// const pdfPreview = () => {
//   try {
//     numOfPages.value = 0
//     @ts-ignore
//     const loadingTask = createLoadingTask(pdfSrc.value)
//     loadingTask.promise.then((pdf) => {
//       numOfPages.value = pdf.numPages
//     })
//   } catch (error) { }
// }

const initPdf = async () => {
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    'https://fp.yangcong345.com/middle/1.0.0/pdf.worker.min.js'
  const loadTask = pdfjsLib.getDocument(pdfSrc.value)
  const pdf = await loadTask.promise
  pdfDoc = pdf
  numOfPages.value = pdf.numPages
  nextTick(() => {
    pdfRenderAll()
  })
}

const pdfRenderAll = async () => {
  for (let i = 0; i < numOfPages.value; i++) {
    const page = await pdfDoc.getPage(i + 1)
    const viewport = page.getViewport({ scale: pdfScale.value })
    const canvas = document.getElementById(
      'preview-pdf' + (i + 1),
    ) as HTMLCanvasElement
    const ctx = canvas.getContext('2d')
    const ratio =
      (pdfViewerRef.value?.clientWidth
        ? pdfViewerRef.value.clientWidth / viewport.width
        : window.devicePixelRatio) || 1
    canvas.width = viewport.width * ratio
    canvas.height = viewport.height * ratio
    canvas.style.width = viewport.width * ratio + 'px'
    canvas.style.height = viewport.height * ratio + 'px'
    ctx?.setTransform(ratio, 0, 0, ratio, 0, 0)
    const renderContext = {
      canvasContext: ctx,
      viewport,
    }
    page.render(renderContext)
  }
}

const mergeTopicDetailsToPoints = async (
  Points: TeacherPoint[],
): Promise<void> => {
  for (const point of Points) {
    try {
      const topicIds = point.topics.map((item) => item.id)
      const topicDetails = await getTopicInfoApi(topicIds as string[])
      topicDetails.items.forEach((item) => {
        point.topics.forEach((a) => {
          if (item.topicId === a.id) {
            item.kind = a.kind
          }
        })
      })
      point.topics = topicDetails.items
    } catch (error) {}
  }
  const curruntPoint = Points.filter(
    (item) => item.pointId === route.query.pointId,
  )[0]
  if (curruntPoint) {
    topicId.value = curruntPoint.topics[0].topicId || ''
    videoId.value = ''
    getPlanTopicPdf(topicId.value)
    nextTick(() => {
      videoId.value = curruntPoint.topics[0].videoId || ''
      getPlanTopicVideo(videoId.value, topicId.value)
    })
    document.getElementById(curruntPoint.pointId)?.scrollIntoView()
  }
}

const switchTab = (val: string) => {
  if (val !== tab.value) {
    tab.value = val
    getPlanTopicPdf(topicId.value)
    nextTick(() => {
      getPlanTopicVideo(videoId.value, topicId.value)
    })
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  padding: 30px calc((100vw - 1200px) / 2);
  overflow-y: scroll;
  background: #f5f7fe;

  &::-webkit-scrollbar {
    width: 0 !important;
  }

  .page-content-inner {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: calc(100vh - 90px);
    min-height: 300px;
    margin-top: 24px;

    .page-left {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      width: 242px;
      height: 100%;
      padding: 16px;
      padding-bottom: 0;

      /* n1 纯白 */
      background: #ffffff;
      border-radius: 16px;
      opacity: 1;

      .points {
        flex: 1;
        overflow-y: scroll;

        &::-webkit-scrollbar {
          width: 0 !important;
        }

        .point-item {
          margin-bottom: 16px;

          .point-name {
            position: relative;
            width: 100%;
            padding: 0 16px;
            margin-bottom: 4px;
            overflow: hidden;
            font-size: 14px;
            line-height: 22px;
            color: #c5c1d4;
            // margin-top: 16px;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: default;

            &::before {
              position: absolute;
              top: 8px;
              left: 4px;
              display: inline-block;
              width: 6px;
              height: 6px;
              content: '';
              background: #c5c1d4;
              border-radius: 50%;
            }
          }

          .topic {
            padding: 12px 16px;
            margin-bottom: 4px;
            font-size: 18px;
            line-height: 24px;
            cursor: pointer;

            &:hover {
              background: #f4f6ff;
              border-radius: 12px;
            }

            &.active {
              font-weight: 600;
              color: #5e80ff;
              background: #f4f6ff;
              border-radius: 12px;
            }
          }
        }
      }
    }

    .page-right {
      width: calc(100% - 242px - 24px);
      height: 100%;
      background: #fff;

      .tabs-list {
        display: flex;
        align-items: center;
        width: 136px;
        height: 48px;
        padding: 6px;
        margin: 16px;
        background: #f4f6ff;
        border-radius: 24px;

        .tab-item {
          width: 60px;
          height: 36px;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          color: #9792ac;
          cursor: pointer;
          border-radius: 18px;

          &.active {
            color: #5e80ff;
            background: #fff;
            box-shadow: 0px 3.2px 3.2px 0px rgba(80, 75, 100, 0.08);
          }
        }
      }

      .pdf-tab {
        height: calc(100% - 80px);
        overflow: scroll;
      }

      .video-box {
        padding-right: 15px;
        padding-left: 15px;
      }

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #fff;
        border-radius: 16px;

        img {
          width: 360px;
          margin: 36px;
        }

        div {
          margin-bottom: 48px;
          font-size: 20px;
          font-weight: 600;
          color: #57526c;
        }
      }

      ::v-deep(.vue-pdf-main) {
        margin-bottom: 16px;
        overflow: hidden;
        border-radius: 16px;

        &:last-child {
          margin: 0;
        }
      }
    }
  }
}
</style>
