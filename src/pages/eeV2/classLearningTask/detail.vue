<template>
  <section class="page-content">
    <div class="page">
      <Breadcrumb
        v-if="planInfo"
        :data="[
          {
            name: '个性化学习',
            href: '/teacher-workbench/eeV2/list',
          },
          {
            name: `${planInfo.roomName}${planInfo.scene}`,
          },
        ]"
      />
      <div v-if="planInfo" class="overview teacher-v2">
        <div
          :class="['state', planInfo.state === 'PUBLISHED' ? 'state-ing' : '']"
        >
          {{ StateEnum[planInfo.state as keyof typeof StateEnum] }}
        </div>
        <div class="name">
          {{ planInfo.roomName }}（{{ planInfo.ref }}）{{ planInfo.scene }}（{{
            planInfo.taskCount
          }}课时）
        </div>
        <div class="sub-info">
          {{
            planInfo.term === 'last' ? '上学期' : '下学期'
          }}｜{{ SubjectEnum[planInfo.subjectId as keyof typeof SubjectEnum]


















          }}｜{{ GradeEnum[planInfo.grade] }}
        </div>
        <n-button class="topic-order" secondary round @click="toPlanInfo">
          考点预览
        </n-button>
      </div>
      <Overview
        :class-info="planClassInfo || {}"
        :group-topic-list="groupFocusTopicList"
        @group-focus-topic="groupFocusTopicShow"
      />
      <div class="zhike-tab-table">
        <div class="zhike-tab-box">
          <div
            class="zhike-tab-item"
            :class="{ 'zhike-active': tab === 1 }"
            @click="handleClickTab(1)"
          >
            课时安排
            <span class="zhike-active-icon" />
          </div>
          <div
            class="zhike-tab-item"
            :class="{ 'zhike-active': tab === 2 }"
            @click="handleClickTab(2)"
          >
            学生分析
            <span class="zhike-active-icon" />
          </div>
          <div
            class="zhike-tab-item"
            :class="{ 'zhike-active': tab === 3 }"
            @click="handleClickTab(3)"
          >
            知识点分析
            <span class="zhike-active-icon" />
          </div>
        </div>
        <div class="zhike-table-body">
          <div v-if="tab === 1">
            <Table
              :data-list="taskDataList"
              :table-list="taskTableList"
              @send-click="sendClickPage"
              @test-paper="clickTestPaper"
            />
          </div>
          <div v-if="tab === 2" class="teacher-v2">
            <div class="select-box">
              <div class="select-item">
                <div class="select-label">学习方案：</div>
                <n-select
                  v-model:value="stuLevel"
                  class="w-175px"
                  :options="stuLevelSelect"
                  :consistent-menu-width="false"
                  :to="false"
                  @update:value="getPlanStudents"
                />
              </div>
              <div class="select-item">
                <div class="select-label">状态：</div>
                <n-select
                  v-model:value="stuState"
                  class="w-175px"
                  :options="stuStateSelect"
                  :consistent-menu-width="false"
                  :to="false"
                  @update:value="getPlanStudents"
                />
              </div>
            </div>
            <n-data-table
              :columns="columns"
              :data="planStudents"
              :bordered="false"
            />
          </div>
          <div v-if="tab === 3">
            <div class="zhike-btn-list-group">
              <n-radio-group
                v-model:value="btnGroupActive"
                name="radioButtonGroup"
                @update-value="changeValue"
              >
                <n-radio-button
                  v-for="(item, index) in btnList"
                  :key="index"
                  :value="item.value"
                  :round="!index || index === btnList.length - 1"
                  :label="item.name"
                />
              </n-radio-group>
            </div>
            <Table
              :data-list="topicDataList"
              :table-list="topicTableList"
              @topic-info="handleTopicClick"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <FocusTopicDrawer
    v-model:visible="focusTopicVisible"
    :group-topic-list="groupFocusTopicList"
    :plan-id="route.query.id as string || ''"
    :group-id="groupId"
    @close="focusTopicVisible = false"
  />
  <TopicInfoModal
    v-model:visible="topicInfoVisible"
    :plan-id="route.query.id as string || ''"
    :group-id="groupId"
    :topic-id="topicInfoId"
    @close="topicInfoVisible = false"
  />
</template>

<script setup lang="tsx">
import { ref, computed, nextTick } from 'vue'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import Breadcrumb from '../components/Breadcrumb/index.vue'
import StateSelectComponents from '../components/Select.vue'
import type { DataTableColumns } from 'naive-ui'
import MasTip from '../components/masTip.vue'
import Overview from '../components/overview.vue'
import Table from '../components/tableZhike.vue'
import FocusTopicDrawer from '../components/focusTopicDrawer.vue'
import TopicInfoModal from '../components/topicInfoModal.vue'
import { nextFEDomain } from '@/utils/apiUrl'
import { buryPoint } from '@/utils/buryPoint'
import type {
  PlanInfo,
  PlanTasks,
  PlanStudents,
  GetDataPlanRes,
  GroupFocusTopicsRes,
  GetPlanTopicsRes,
} from '../service'
import {
  getPlanInfoApi,
  SubjectEnum,
  GradeEnum,
  StateEnum,
  getPlanStudentsApi,
  StuStateEnum,
  updateStudentLevelApi,
  getDataPlan,
  stateFilter,
  sendFilter,
  getGroupFocusTopics,
  getDataPlanTopics,
} from '../service'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')
const route = useRoute()
const router = useRouter()
const message = useOIWMessage()
const focusTopicVisible = ref(false)
const tab = ref(1)
const stuLevel = ref('LALL')
const stuLevelSelect = ref([
  {
    label: '全部',
    value: 'LALL',
  },
  {
    label: '冲刺方案',
    value: 'A',
  },
  {
    label: '提升方案',
    value: 'B',
  },
  {
    label: '基础方案',
    value: 'C',
  },
])
const stuState = ref('SALL')
const stuStateSelect = ref([
  {
    label: '全部',
    value: 'SALL',
  },
  {
    label: '生效中',
    value: 'ON',
  },
  {
    label: '失效',
    value: 'OFF',
  },
])
const taskTableList = ref<
  { title: string; key: string; [k: string]: unknown }[]
>([
  { title: '考试/课时', key: 'task' },
  { title: '日期', key: 'dateTime', notSorter: true },
  { title: '时间', key: 'time', notSorter: true },
  { title: '状态', key: 'stateName' },
  { title: '操作', key: 'send', width: 200 },
])
const topicTableList = ref<
  { title: string; key: string; [k: string]: unknown }[]
>([
  { title: '课时', key: 'taskName', notSorter: true, width: 110 },
  {
    title: '考点',
    key: 'pointName',
    notSorter: true,
    identify: 'ellipsis',
    width: 260,
  },
  { title: '知识点', key: 'sendTopicId' },
  { title: '学习人数', key: 'studyNum', width: 150 },
  { title: '知识点掌握度', key: 'masterRate', identify: '%', width: 150 },
  {
    title: '平均学习时长',
    key: 'studyTimes',
    identify: '分钟',
    width: 150,
    align: 'center',
  },
])
const btnList = [
  { value: 'A', name: '冲刺' },
  { value: 'B', name: '巩固' },
  { value: 'C', name: '基础' },
]
const btnGroupActive = ref('A')
const topicDataList = ref<GetPlanTopicsRes[]>([])
const planInfo = ref<PlanInfo>()
const groupId = ref('')
const planClassInfo = ref<GetDataPlanRes | null>(null)
const planStudents = ref<PlanStudents[]>()
const groupFocusTopicList = ref<GroupFocusTopicsRes[]>([])
const topicInfoId = ref('')
const topicInfoVisible = ref(false)

const columns: DataTableColumns<PlanStudents> = [
  {
    title: '真实姓名',
    key: 'realName',
  },
  {
    title: '用户名',
    key: 'userName',
  },
  {
    title: '学习方案',
    key: 'state',
    render: (row) => {
      return (
        <StateSelectComponents
          stu={row}
          onUpdateLevel={updateLevel}
          onFocus={selectFocus}
        />
      )
    },
  },
  {
    title: '已完成知识点',
    key: 'finishedTopicCount',
    width: 170,
    align: 'center',
    sorter: (row1, row2) => row1.finishedTopicCount - row2.finishedTopicCount,
    render: (row) => {
      return (
        <div>
          <span>{`必学${row.finishedRequiredTopicCount}个`}</span>
          <span
            style={{ color: '#8A869E' }}
          >{`｜选学${row.finishedOptionalTopicCount}个`}</span>
        </div>
      )
    },
  },
  {
    title() {
      return <MasTip></MasTip>
    },
    key: 'masterRate',
    align: 'center',
    width: 130,

    sorter: (row1, row2) => row1.masterRate - row2.masterRate,
    render: (row) => {
      if (row.masterRate < 0) return '-'
      return row.masterRate + '%'
    },
  },
  {
    title: '平均知识点学习时长',
    key: 'studyTimes',
    width: 140,
    align: 'center',
    sorter: (row1, row2) => row1.studyTimes - row2.studyTimes,
    render: (row) => {
      if (row.studyTimes < 0) return '-'
      return row.studyTimes + '分钟'
    },
  },
  {
    title: '课时出勤率',
    key: 'attendanceRate',
    width: 120,
    align: 'center',
    sorter: (row1, row2) => row1.attendanceRate - row2.attendanceRate,
    render: (row) => {
      if (row.attendanceRate < 0) return '-'
      return row.attendanceRate + '%'
    },
  },
  {
    title: '平均学习异常次数',
    key: 'avgNewsCount',
    width: 120,
    align: 'center',
    sorter: (row1, row2) => row1.avgNewsCount - row2.avgNewsCount,
    render: (row) => {
      if (row.avgNewsCount < 0) return '-'
      return row.avgNewsCount
    },
  },
  {
    title: '状态',
    width: 110,
    key: 'finishedTopicCount',
    render: (row) => {
      return (
        <div class={row.state === 'ON' ? 'zhike-state-on' : 'zhike-state-off'}>
          {StuStateEnum[row.state as keyof typeof StuStateEnum]}
        </div>
      )
    },
  },
]

const taskDataList = computed(() => {
  return planClassInfo.value?.tasks?.map((item) => {
    const startTime = dayjs(item.startTime)
    const endTime = dayjs(item.endTime)
    const weekdaysCN = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return {
      task: item.name,
      dateTime: `${startTime.format('YYYY-MM-DD')}（${
        weekdaysCN[startTime.day()]
      }）`,
      time: `${startTime.format('HH:mm')}-${endTime.format(
        'HH:mm',
      )} ${Math.floor(Math.abs(endTime.diff(startTime)) / (1000 * 60))}分钟`,
      stateName: `${stateFilter(item.state || '')}`,
      state: item.state,
      type: item.type,
      send: `${sendFilter(item.type)}`,
      taskId: item.taskId,
      testPaperId: item.testPaperId || '',
    }
  })
})

const classPlanInfo = async () => {
  const data = await getDataPlan(route.query.id as string)
  if (data) {
    planClassInfo.value = data
  }
}

const groupFocusTopic = async () => {
  const res = await getGroupFocusTopics({
    groupId: groupId.value,
    planId: route.query.id as string,
  })
  if (res) {
    groupFocusTopicList.value = res.data
  }
}

const getPlanStudents = () => {
  getPlanStudentsApi({
    id: route.query.id as string,
    level: stuLevel.value,
    state: stuState.value,
  }).then((res) => {
    planStudents.value = res.data
  })
}

const getDataPlanTopicList = async () => {
  const { data } = await getDataPlanTopics({
    planId: route.query.id as string,
    level: btnGroupActive.value,
    groupId: groupId.value,
  })
  if (data) {
    topicDataList.value = data
  }
}

const fetch = () => {
  getPlanInfoApi({ id: route.query.id as string }).then((res) => {
    planInfo.value = res.data
    groupId.value = res.data.groupId
    nextTick(() => {
      groupFocusTopic()
      getDataPlanTopicList()
    })
  })
  classPlanInfo()
  getPlanStudents()
}

fetch()

const updateLevel = (id: string, level: string) => {
  buryPoint(
    'click_Pc_eeV2_PlanPage_studentTab_List',
    { button: level === 'A' ? '1' : level === 'B' ? '2' : '3' },
    'homework',
  )
  updateStudentLevelApi({ id, level })
    .then((res) => {
      if (res.data) {
        fetch()
      }
    })
    .catch((err) => {
      message.error(err.response.data.message)
      fetch()
    })
}

const toPlanInfo = () => {
  buryPoint('click_Pc_eeV2_PlanPage_roomPoint', {}, 'homework')
  router.push({
    name: 'eeV2PlanInfo',
    query: {
      id: route.query.id,
    },
  })
}

const dialog = useDialog()

const showdDialog = ref(true)

const selectFocus = (id: string, level: string) => {
  if (level === 'NO') {
    if (showdDialog.value) {
      dialog.info({
        showIcon: false,
        title: '提示',
        content: '调整方案后，学生会跳过摸底考试直接开始学习，是否继续操作？',
        positiveText: '确认',
        negativeText: '取消',
      })
      showdDialog.value = false
    }
  }
}

const sendClickPage = (row: any) => {
  if (row.type === 'START_EXAM') {
    clickExamReport()
  }
  if (row.type === 'TASK') {
    clickTaskReport(row)
  }
}
const clickTestPaper = (item: PlanTasks) => {
  buryPoint('click_Pc_eeV2_PlanPage_Button', { button: 'exam' }, 'homework')
  if (planInfo.value) {
    router.push({
      name: 'eeV2TestPaper',
      query: {
        id: route.query.id,
        testPaperId: item.testPaperId,
        planName: `${planInfo.value.roomName}${planInfo.value.scene}`,
      },
    })
  }
}
const clickExamReport = () => {
  buryPoint('click_Pc_eeV2_PlanPage_Button', { button: 'point' }, 'homework')
  if (planInfo.value) {
    window.open(
      `${nextFEDomain}/eeV2data/examReport?id=${route.query.id}`,
      '_blank',
    )
  }
}
const clickTaskReport = (item: PlanTasks) => {
  buryPoint('click_Pc_eeV2_PlanPage_Button', { button: 'taskline' }, 'homework')
  if (planInfo.value) {
    window.open(
      `${nextFEDomain}/eeV2data/taskReport?taskId=${item.taskId}&groupId=${planInfo.value.groupId}`,
      '_blank',
    )
  }
}
const handleClickTab = (val: number) => {
  buryPoint(
    'click_Pc_eeV2_PlanPage_studentTab',
    { button: `${val}` },
    'homework',
  )
  tab.value = val
}

const groupFocusTopicShow = () => {
  buryPoint('click_Pc_eeV2_PlanPage_studentButton', {}, 'homework')
  focusTopicVisible.value = true
}

const changeValue = (value: string) => {
  btnGroupActive.value = value
  getDataPlanTopicList()
}

const handleTopicClick = (row: any) => {
  buryPoint('click_Pc_eeV2_PlanPage_topicList', {}, 'homework')
  topicInfoId.value = row.topicId
  topicInfoVisible.value = true
}

onMounted(() => {
  buryPoint('enterSpecificClassAnalysisPage', {}, 'course')
})
</script>

<style lang="scss" scoped>
.page-content {
  background: #f5f7fe;

  &::-webkit-scrollbar {
    width: 0 !important;
  }

  .page {
    width: 1200px;
    min-height: 100vh;
    padding: 30px 0;
    margin: 0 auto;

    .overview {
      position: relative;
      box-sizing: border-box;
      width: 1200px;
      height: 199px;
      padding: 30px;
      margin-top: 23px;
      margin-bottom: 30px;
      background: #ffffff;
      border-radius: 15px 15px 15px 15px;
      box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);

      .state {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 81px;
        height: 31px;
        font-size: 13px;
        font-weight: 500;
        color: #393548;
        background: #f5f7fe;
        border-radius: 15px;

        &::before {
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-right: 4px;
          content: '';
          background: #b8b4c7;
          border-radius: 50%;
          opacity: 1;
        }
      }

      .state-ing {
        &::before {
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-right: 4px;
          content: '';
          background: #4ecc5e;
          border-radius: 50%;
          opacity: 1;
        }
      }

      .name {
        margin-top: 15px;
        font-family: 'PingFang SC';
        font-size: 23px;
        font-weight: 600;
        line-height: 31px;
        color: #393548;
      }

      .sub-info {
        margin-top: 8px;
        font-family: 'PingFang SC';
        font-size: 17px;
        font-weight: 400;
        line-height: 23px;
        color: #504b64;

        span:last-child {
          .line {
            display: none;
          }
        }
      }

      .topic-order {
        position: absolute;
        top: 77px;
        right: 54px;
        border-radius: 12px;
      }
    }
  }

  ::v-deep(.zhike-state-on) {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 15px;
      height: 15px;
      margin-right: 8px;
      content: '';
      background: #4ecc5e;
      border-radius: 50%;
    }
  }

  ::v-deep(.zhike-state-off) {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 15px;
      height: 15px;
      margin-right: 8px;
      content: '';
      background: #d4d1dd;
      border-radius: 50%;
    }
  }

  ::v-deep(.zhike-state-warn) {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 15px;
      height: 15px;
      margin-right: 8px;
      content: '';
      background: #fa5a65;
      border-radius: 50%;
    }
  }

  .zhike-tab-table {
    display: flex;
    flex-direction: column;

    .zhike-tab-box {
      order: -1;
      margin-bottom: -17px;

      .zhike-tab-item {
        position: relative;
        display: inline-block;
        width: 119px;
        min-height: 57px;
        padding-top: 17px;
        // box-sizing: border-box;
        padding-bottom: 17px;
        text-align: center;
        cursor: pointer;
        background: #e7ecf3;
        opacity: 1;
      }

      .zhike-active-icon {
        display: flex;
        width: 38px;
        height: 6px;
        margin-top: 16px;
        margin-right: auto;
        margin-left: auto;
        background: transparent;
        border-radius: 3px;
      }

      .zhike-active {
        background: #fff;

        .zhike-active-icon {
          background: #5e80fe;
        }
      }

      .zhike-tab-item:nth-child(1) {
        text-indent: 20px;
        border-top-left-radius: 15px;

        .zhike-active-icon {
          margin-left: 50px !important;
        }

        &::after {
          position: absolute;
          top: 0;
          right: -32px;
          z-index: 1;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2759@2x (3)-7d1d9621b4a6d77793a9e835b573ba6e.png');
          background-size: 100% 100%;
        }

        &.zhike-active::after {
          position: absolute;
          top: 0;
          right: -32px;
          z-index: 2;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2759@2x (2)-0da80ff9d073b96fe830fd6ccef83251.png');
          background-size: 100% 100%;
        }
      }

      .zhike-tab-item:nth-child(2),
      .zhike-tab-item:nth-child(3) {
        margin-left: 42px;

        &::before {
          position: absolute;
          top: 0;
          left: -32px;
          z-index: 1;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2757@2x (1)-b8c05be6015baf847cd18e0c9ab948ba.png');
          background-size: 100% 100%;
        }

        &::after {
          position: absolute;
          top: 0;
          right: -32px;
          z-index: 1;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2759@2x (3)-7d1d9621b4a6d77793a9e835b573ba6e.png');
          background-size: 100% 100%;
        }

        &.zhike-active::before {
          position: absolute;
          top: 0;
          left: -32px;
          z-index: 2;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2757@2x (2)-73459b4b38915ba6c1d5c3024fd711e8.png');
          background-size: 100% 100%;
        }

        &.zhike-active::after {
          position: absolute;
          top: 0;
          right: -32px;
          z-index: 2;
          display: inline-block;
          width: 32px;
          height: 63px;
          content: '';
          background: url('https://fp.yangcong345.com/onion-extension/矩形 2759@2x (2)-0da80ff9d073b96fe830fd6ccef83251.png');
          background-size: 100% 100%;
        }
      }
    }

    .zhike-table-body {
      position: relative;
      padding: 23px;
      padding-bottom: 8px;

      background: #fff;
      border-radius: 15px;
      box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);

      table {
        overflow: visible;
      }
    }
  }

  .select-box {
    display: flex;
    align-items: center;
    margin-bottom: 23px;

    .select-item {
      display: flex;
      align-items: center;
      margin-right: 30px;

      .select-label {
        margin-right: 8px;
        font-family: 'PingFang SC';
        font-size: 17px;
        font-weight: 400;
        color: #393548;
      }
    }
  }

  .zhike-btn-list-group {
    display: flex;
    align-items: center;

    &::v-deep(.n-radio-group .n-radio-button.n-radio-button--checked) {
      min-width: 60px;
      height: 32px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      line-height: 32px;
      color: #5e80ff;
      text-align: center;
      background: #ffffff;
      border-radius: 16px;
      box-shadow: 0px 3.2px 3.2px 0px rgba(80, 75, 100, 0.08);
    }

    &::v-deep(.n-radio-group .n-radio-button) {
      min-width: 60px;
      height: 32px;
      line-height: 32px;
      color: #9792ac;
      background: #f5f7fe;
      border: none;
      border-radius: 16px;
    }

    &::v-deep(.n-radio-group .n-radio-button__state-border) {
      display: none;
    }

    &::v-deep(.n-radio-group--button-group) {
      height: 40px;
      padding: 4px;
      line-height: 32px;
      background: #f5f7fe;
      border-radius: 20px;
    }

    &::v-deep(.n-radio-group__splitor) {
      display: none;
    }
  }
}
</style>
