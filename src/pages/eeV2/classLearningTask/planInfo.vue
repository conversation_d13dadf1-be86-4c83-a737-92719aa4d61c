<template>
  <section class="page-content teacher-v2">
    <Breadcrumb
      v-if="planInfo"
      :data="[
        {
          name: '个性化学习',
          href: '/teacher-workbench/eeV2/list',
        },
        {
          name: `${planInfo.roomName}${planInfo.scene}`,
          href: `/teacher-workbench/eeV2/detail?id=${route.query.id}`,
        },
        {
          name: `考点预览`,
        },
      ]"
    />
    <div class="main-content">
      <div class="content-tip">
        说明：系统将按照如下顺序给学生推荐个性化的学习内容，您可以根据实际教学计划调整考点顺序或者关闭考点，点击“调整考点”按钮进行操作。考点调整不影响已经结束的课时。
      </div>
      <div class="table-top mb-24px mt-24px">
        <div class="table-tab-box">
          <div
            class="table-tab-item"
            :class="difficult === 'PROBLEM_DIFFICULTY_HARD' ? 'active' : ''"
            @click="getPoints('PROBLEM_DIFFICULTY_HARD')"
          >
            压轴
          </div>
          <div
            class="table-tab-item"
            :class="difficult === 'PROBLEM_DIFFICULTY_MIDDLE' ? 'active' : ''"
            @click="getPoints('PROBLEM_DIFFICULTY_MIDDLE')"
          >
            中等
          </div>
          <div
            class="table-tab-item"
            :class="difficult === 'PROBLEM_DIFFICULTY_BASIC' ? 'active' : ''"
            @click="getPoints('PROBLEM_DIFFICULTY_BASIC')"
          >
            基础
          </div>
        </div>
        <div class="flex align-center">
          <NButton
            class="button-secondary mr-16px"
            secondary
            @click="handleEdit"
          >
            {{ draggable ? '完成' : '调整考点' }}
          </NButton>
          <n-popover
            v-if="!downloading"
            to=".table-top"
            trigger="hover"
            placement="bottom-end"
            :show-arrow="false"
            class="download-select"
          >
            <template #trigger>
              <NButton
                class="button-secondary download-btn"
                secondary
                style="padding: 0 22px"
                :disabled="downloading"
                :loading="downloading"
              >
                下载学案
                <img
                  class="ml-4px"
                  width="16"
                  src="https://fp.yangcong345.com/onion-extension/切图 2@1x (1)-750e8feb616045e0918299771b74f8ae.png"
                  alt=""
                />
              </NButton>
            </template>
            <div>
              <div
                class="select-item"
                @click="download('PROBLEM_DIFFICULTY_HARD')"
              >
                压轴考点学案
              </div>
              <div
                class="select-item"
                @click="download('PROBLEM_DIFFICULTY_MIDDLE')"
              >
                中等考点学案
              </div>
              <div
                class="select-item"
                @click="download('PROBLEM_DIFFICULTY_BASIC')"
              >
                基础考点学案
              </div>
            </div>
          </n-popover>
          <NButton
            v-else
            class="button-secondary download-btn downloading"
            secondary
            style="padding: 0 22px"
            :disabled="downloading"
            :loading="downloading"
          >
            下载中
          </NButton>
        </div>
      </div>
      <PreviewsTable
        v-if="!draggable"
        type="class"
        :points-data="pointsData"
        @point-video="toPointVideo"
        @point-pdf="toPointPdf"
      />
      <PreviewsDraggable
        v-else
        type="class"
        :points-data="pointsData"
        :un-learn-topic="unLearnTopic"
        @update-plan-topic="updatePlanTopic"
        @cancel-draggable="cancelDraggable"
        @all-sub-item-show="allSubItemShow"
        @drag-over-item="dragOverItem"
        @learn-topic-change="learnTopicChange"
      />
    </div>
  </section>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import Breadcrumb from '../components/Breadcrumb/index.vue'
import type { PlanInfo, TeacherPoint, Difficult } from '../service'
import {
  getPlanInfoApi,
  getTeacherPointsApi,
  getTopicInfoApi,
  updateTeacherPointsApi,
  downloadTeachingPlanApi,
  getTeachingPlanUrlApi,
} from '../service'
import { buryPoint } from '@/utils/buryPoint'
import PreviewsTable from '../components/previewsTable.vue'
import PreviewsDraggable from '../components/previewsDraggable.vue'

const route = useRoute()
const router = useRouter()
const message = useOIWMessage()

const maxPollingTime = 1800 // 最大轮询次数1800次，每秒一次，最大30min
const planInfo = ref<PlanInfo>()
const points = ref<TeacherPoint[]>([])
const difficult = ref<Difficult>('PROBLEM_DIFFICULTY_HARD')
const draggable = ref(false)
const timer = ref()
const pollingTime = ref(0)
const allItemShow = ref(false)
const unLearnTopic = ref(true)

const getPointsInfo = async (pointItem: TeacherPoint) => {
  const res = await getTopicInfoApi(
    pointItem.topics.map((i) => i.id) as string[],
  )
  if (res) {
    res.items.forEach((item) => {
      pointItem.topics.forEach((a) => {
        if (item.topicId === a.id) {
          a.topicName = item.topicName
        }
      })
    })
  }
}

const fecth = () => {
  getPlanInfoApi({ id: route.query.id as string }).then((res) => {
    planInfo.value = res.data
  })
}

const pointsData = computed(() => {
  if (draggable.value) {
    return points.value
  } else {
    return points.value.filter((i) => !i.isClose)
  }
})

const getPoints = async (val: Difficult, state?: boolean) => {
  difficult.value = val
  getTeacherPointsApi({
    planId: route.query.id as string,
    difficult: val,
  }).then((res) => {
    if (state) {
      const isSubItemShowTemp = points.value.map((i) => i.isSubItemShow)
      points.value = res.items.map((i, index) => {
        return { ...i, isSubItemShow: isSubItemShowTemp[index] || false }
      })
    } else {
      points.value = res.items.map((i) => {
        return { ...i, isSubItemShow: false }
      })
      allItemShow.value = false
    }
    if (points.value.length > 0) {
      points.value.forEach(async (pointItem) => {
        await getPointsInfo(pointItem)
      })
    }
  })
}
const toPointPdf = (pointId: string) => {
  router.push({
    name: 'eeV2PointPdf',
    query: {
      id: route.query.id,
      pointId,
      difficult: difficult.value,
      tab: 'pdf',
    },
  })
}

const toPointVideo = (pointId: string) => {
  router.push({
    name: 'eeV2PointPdf',
    query: {
      id: route.query.id,
      pointId,
      difficult: difficult.value,
      tab: 'video',
    },
  })
}

onMounted(() => {
  fecth()
  getPoints('PROBLEM_DIFFICULTY_HARD')
})

const cancelDraggable = () => {
  unLearnTopic.value = true
  draggable.value = false
  getPoints(difficult.value)
}

const downloading = ref(false)

const download = (difficult: string) => {
  if (downloading.value) return
  buryPoint(
    'click_Pc_eeV2_PlanPage_RPdownload',
    {
      button:
        difficult === 'PROBLEM_DIFFICULTY_HARD'
          ? '1'
          : difficult === 'PROBLEM_DIFFICULTY_MIDDLE'
            ? '2'
            : '3',
    },
    'homework',
  )
  downloadTeachingPlanApi({ planId: route.query.id as string, difficult }).then(
    (res) => {
      message.info('系统正在请求下载，请勿关闭页面，耐心等待哦')
      downloading.value = true
      timer.value = setInterval(() => {
        pollingTime.value = pollingTime.value + 1
        // 超过最大次数，结束轮询
        if (pollingTime.value > maxPollingTime) {
          clearInterval(timer.value)
          downloading.value = false
        }
        getTeachingPlanUrlApi({ id: res.id })
          .then((res) => {
            if (res.url) {
              window.open(res.url, '_blank')
              clearInterval(timer.value)
              downloading.value = false
            }
          })
          .catch((err) => {
            message.error('下载失败，请重试', err.response.data.message)
            clearInterval(timer.value)
            downloading.value = false
          })
      }, 1000)
    },
  )
}

const handleEdit = () => {
  if (!draggable.value) {
    buryPoint('click_Pc_eeV2_PlanPage_RPadjust', {}, 'homework')
  }
  if (draggable.value) {
    updatePlanTopic(true)
  }
  draggable.value = !draggable.value
}

const updatePlanTopic = (isConfirm: boolean) => {
  if (isConfirm) {
    unLearnTopic.value = true
    points.value = points.value.map((i) => {
      return { ...i, isSubItemShow: false }
    })
  }
  const data = {
    planId: route.query.id as string,
    items: points.value,
    isConfirm,
  }
  updateTeacherPointsApi(data)
    .then((res) => {
      if (res && isConfirm) {
        message.success('考点调整已生效')
        draggable.value = false
      }
      getPoints(difficult.value, true)
    })
    .catch((err) => {
      message.error(err.response.data.message)
      getTeacherPointsApi({
        planId: route.query.id as string,
        difficult: difficult.value,
      }).then((res) => {
        points.value = res.items
      })
    })
}

const allSubItemShow = () => {
  allItemShow.value = !allItemShow.value
  if (!allItemShow.value) {
    points.value.forEach((i) => {
      i.isSubItemShow = false
    })
  } else {
    points.value.forEach((i) => {
      i.isSubItemShow = true
    })
  }
}

const dragOverItem = (targetItem: TeacherPoint, draggedItem: TeacherPoint) => {
  const draggedIndex = points.value.findIndex(
    (item) => item.pointId === draggedItem.pointId,
  )
  const targetIndex = points.value.findIndex(
    (item) => item.pointId === targetItem.pointId,
  )

  if (
    draggedIndex !== -1 &&
    targetIndex !== -1 &&
    draggedIndex !== targetIndex
  ) {
    points.value.splice(draggedIndex, 1)
    points.value.splice(targetIndex, 0, draggedItem)
  }
}
const learnTopicChange = (val: boolean) => {
  unLearnTopic.value = val
}
</script>

<style lang="scss" scoped>
.page-content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  padding: 30px calc((100vw - 1200px) / 2);
  overflow-y: scroll;
  background: #f5f7fe;

  &::-webkit-scrollbar {
    width: 0 !important;
  }

  .button-secondary {
    border-radius: 12px;
  }

  .main-content {
    width: 1200px;
    padding: 24px;
    margin-top: 24px;
    margin-bottom: 70px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
    opacity: 1;

    .content-tip {
      position: relative;
      width: 1152px;
      padding: 14px 24px 14px 58px;
      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      color: #393548;

      /* b1浅色块底色 focus色 */
      background: #f4f6ff;

      /* 按钮圆角 */
      border-radius: 12px;
      opacity: 1;

      &::before {
        position: absolute;
        top: 16px;
        left: 24px;
        display: inline-block;
        width: 24px;
        height: 24px;
        content: '';
        background: url('https://fp.yangcong345.com/onion-extension/全局提示@2x-399699f384e5e9573503c20c7b56c5c1.png')
          no-repeat;
        background-size: contain;
      }
    }

    .table-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .download-btn {
        img {
          transition: 0.3s ease-in-out;
        }

        &:hover {
          img {
            transform: rotate(180deg);
          }
        }
      }

      .downloading {
        &:hover {
          color: unset;
          background: unset;
          border: 1px solid #d5d2dd;
        }
      }

      ::v-deep(.download-select) {
        box-sizing: border-box;
        padding: 16px;

        /* n1 纯白 */
        background: #ffffff;

        /* n3 选项卡底色 */
        border: 1px solid #e4e9f7;
        border-radius: 16px;

        box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
        opacity: 1;

        .select-item {
          width: 148px;
          height: 48px;
          font-size: 18px;
          line-height: 48px;
          color: #393548;
          text-align: center;
          cursor: pointer;
          border-radius: 12px;

          &:hover {
            font-weight: 500;
            background: #f4f6ff;
          }
        }
      }
    }

    .table-tab-box {
      display: flex;
      flex-direction: row;
      width: fit-content;
      padding: 4px;
      border: 1px solid #e4e9f7;
      border-radius: 314px;

      .table-tab-item {
        padding: 8px 24px;
        font-size: 16px;
        font-weight: normal;
        line-height: 16px;
        color: #8a869e;
        cursor: pointer;
        border-radius: 109px;

        &.active {
          color: #5e80ff;
          background: #f4f6ff;
        }
      }
    }

    ul {
      list-style: none;
    }

    li {
      position: relative;
      min-height: 61px;
      margin-bottom: 8px;
      font-family: 'PingFang SC';
      // border-bottom: 1px solid #DCD8E7;
      font-size: 17px;
      font-weight: 500;
      line-height: 61px;
      color: #393548;
      border-radius: 12px;

      &::after {
        position: absolute;
        bottom: -4px;
        display: inline-block;
        width: 100%;
        height: 1px;
        content: '';
        background: #dcd8e7;
      }

      .item-main {
        display: flex;
        align-items: center;

        &:hover {
          background: #f4f6ff;
        }

        .hint {
          box-sizing: border-box;
          display: inline-block;
          width: 48px;
          height: 23px;
          padding: 0 15px;
          margin-right: 15px;
          margin-left: 15px;
          background: url('https://fp.yangcong345.com/onion-extension/icon 拖动@2x-f3d612fa6dcbc5374ef686d7396e8ce3.png')
            no-repeat center;
          background-size: contain;
        }

        .item-kaodian {
          display: flex;
          // padding-left: 24px;
          flex-shrink: 0;
          align-items: center;
          width: 544px;

          &.draggable {
            width: 1000px;
          }

          .pointName {
            &.grey {
              color: #9792ac;
            }
          }
        }

        .item-topic {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          width: 260px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            display: inline-block;
            max-width: 170px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .kaodian-hint {
            display: inline-block;
            width: 21px;
            height: 21px;
            margin-left: 9px;
            background: url('https://fp.yangcong345.com/onion-extension/切图 2@2x (1)-cfb1cd141f0ed118846fbe2b1ebc586c.png')
              no-repeat;
            background-size: contain;
          }
        }

        .item-operate {
          display: flex;
          justify-content: space-between;
          width: 260px;
          padding-right: 20px;
          padding-left: 20px;

          .operate-btn {
            font-size: 16px;
            font-weight: 600;
            line-height: 22px;
            color: #5e80ff;
            background: transparent;
          }
        }
      }

      .sub-topic-list {
        line-height: 50px;

        .sub-topic-item {
          display: flex;
          align-items: center;

          .topic-name {
            position: relative;
            width: 1000px;
            padding-left: 136px;
            color: #393548;

            &::before {
              position: absolute;
              top: 21px;
              left: 120px;
              width: 8px;
              height: 8px;
              content: '';
              background: #393548;
              border-radius: 50%;
            }

            &.grey {
              color: #9792ac;

              &::before {
                background: #9792ac;
              }
            }
          }
        }
      }
    }

    .dragging {
      cursor: grab;
      background-color: #f5f5f6;

      opacity: 0.8;
    }

    .order-box {
      flex: 1;

      .empty {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 360px;
          margin: 36px;
        }

        div {
          margin-bottom: 48px;
          font-size: 20px;
          font-weight: 600;
          color: #57526c;
        }
      }

      .oi-footer-btns {
        position: fixed;
        bottom: 20px;
        left: calc((100vw - 1200px) / 2 + 24px);
        width: 1153px;
      }

      .table-header {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 64px;
        padding: 0 24px;
        margin-bottom: 4px;
        font-size: 18px;
        font-weight: normal;
        line-height: 24px;

        /* n8大标题文本色 */
        color: #393548;
        text-align: left;
        letter-spacing: 0px;

        /* 灰（标签色）表头色 */
        background: #f7f7f9;
        border-radius: 12px;
        opacity: 1;

        div {
          flex-shrink: 0;
        }
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;

        /* Safari/Chrome/Webkit */
      }

      &::-moz-scrollbar-track {
        background-color: transparent;

        /* Firefox */
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcd8e7;

        /* Safari/Chrome/Webkit */
        border-radius: 10px;
      }

      &::-moz-scrollbar-thumb {
        background-color: #dcd8e7;

        /* Firefox */
        border-radius: 10px;
      }

      &::-webkit-scrollbar-corner {
        background-color: transparent;

        /* Safari/Chrome/Webkit */
      }

      &::-webkit-scrollbar {
        width: 8px;

        /* Safari/Chrome/Webkit */
      }

      &::-moz-scrollbar {
        width: 8px;

        /* Firefox */
      }
    }
  }
}

.popover-tip {
  padding: 10px 12px;
  background: #504b64;
  border-radius: 12px;
  opacity: 1;

  .popover-tip-title {
    margin-bottom: 12px;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    color: #d4d1dd;
  }

  .popover-tip-content {
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #ffffff;

    span {
      margin-right: 20px;
      white-space: nowrap;
    }
  }
}
</style>
