<template>
  <section class="page-content teacher-v2">
    <div class="page">
      <Breadcrumb
        :data="[
          {
            name: '个性化学习',
            href: '/teacher-workbench/eeV2/list',
          },
          {
            name: `${route.query.planName}`,
            href: `/teacher-workbench/eeV2/detail?id=${route.query.id}`,
          },
          {
            name: `查看试卷`,
          },
        ]"
      />
      <div v-if="testPaper" class="paper-box">
        <div class="paper-left">
          <div class="le-title">
            <div class="paper-h1">
              {{ testPaper.name }}
            </div>
            <n-tooltip
              :disabled="!testPaper.hasStudentAnswer"
              arrow-point-to-center
              :arrow-style="{ background: '#504B64' }"
              trigger="hover"
              raw
            >
              <template #trigger>
                <div
                  v-if="!showChangeProblem"
                  class="oi-confirm-btn"
                  :class="{ disable: testPaper.hasStudentAnswer }"
                  @click="handleShowChangeProblem"
                >
                  换题
                </div>
              </template>
              <div
                :style="{
                  background: '#504B64',
                  borderRadius: '12px 12px 12px 12px',
                  padding: '10px 12px',
                  fontSize: '16px',
                  fontFamily: 'PingFang SC',
                  fontWeight: 500,
                  color: '#FFFFFF',
                  lineHeight: '20px',
                }"
              >
                已有学生作答，无法换题
              </div>
            </n-tooltip>
          </div>
          <div class="huanti-box" style="flex: 1; overflow: scroll">
            <div>
              <div
                v-for="(p, index) in sortProblems"
                :key="p.id"
                class="p-item-box"
                :class="
                  curChangeProblem && curChangeProblem.id === p.id
                    ? 'cur-select'
                    : ''
                "
              >
                <div class="topic-package-info">
                  <span :class="`difficult-${p.difficult}`">{{
                    p.difficultMsg
                  }}</span>
                </div>
                <ProblemRender
                  :difficulty-header-show="false"
                  :problem-no="index + 1"
                  :current-problem="p"
                />
                <div
                  v-if="showChangeProblem"
                  style="float: right; margin-top: -15px"
                  class="oi-confirm-btn"
                  @click="changeProblem(p)"
                >
                  换题
                </div>
              </div>
            </div>
          </div>
          <div v-if="showChangeProblem" class="oi-footer-btns">
            <div class="oi-cancel-btn" @click="updateTestPaperCancel">取消</div>
            <div class="oi-confirm-btn" @click="updateTestPaperDialog">
              确认
            </div>
          </div>
        </div>
        <div class="paper-right">
          <div class="paper-h2">试卷详情</div>
          <div class="p-card">
            <div class="item" style="border-right: 1px solid #d4d1dd">
              <div class="paper-h3">总分</div>
              <div class="paper-h1" style="margin-top: 8px">
                {{ testPaper.totalScore }}分
              </div>
            </div>
            <div class="item">
              <div class="paper-h3">题目</div>

              <div class="paper-h1" style="margin-top: 8px">
                {{ totalProblemItems }}题
              </div>
            </div>
          </div>
          <div class="paper-h3">难度</div>
          <div class="p-card-default">
            <div
              v-for="i in testPaper.difficultyItems"
              :key="i.difficultMsg"
              class="difficulty-item"
            >
              <div :class="`difficult-${i.difficult} paper-text`">
                {{ i.difficultMsg }}
              </div>
              <div class="paper-text">{{ i.nums }}题</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <n-drawer v-model:show="internalShow" :width="575" placement="right">
      <n-drawer-content title="换题" closable class="drawer-content">
        <div class="drawer-content-change-problems">
          <div
            v-for="(p, index) in changeProblems"
            :key="p.id"
            class="change-problems"
          >
            <div v-if="curChangeProblem" class="topic-package-info">
              <span :class="`difficult-${curChangeProblem.difficult}`">{{
                curChangeProblem.difficultMsg
              }}</span>
              <span>{{ curChangeProblem.topicPackageItemName }}</span>
              <span>{{ curChangeProblem.examPointName }}</span>
            </div>
            <ProblemRender
              :difficulty-header-show="false"
              :problem-no="index + 1"
              :current-problem="p"
            />
            <div class="oi-confirm-btn tpb" @click="updateTestPaper(p)">
              使用
            </div>
          </div>
        </div>
        <div class="oi-footer-btns">
          <div class="oi-confirm-btn" @click="changeOringinProblem">
            使用初始题目
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </section>
</template>

<script setup lang="ts">
import Breadcrumb from '../components/Breadcrumb/index.vue'
import { ProblemRender } from '@guanghe-pub/onion-problem-render'
import { ref } from 'vue'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import type { TestPaper, TestPaperProblem } from '../service'
import {
  getTestPaperApi,
  getProblemsByIdApi,
  getTestPaperProblemsApi,
  updateTestPaperApi,
  getInitPaperProblemsApi,
} from '../service'

const route = useRoute()
const testPaper = ref<TestPaper>()
const sortProblems = ref<TestPaperProblem[]>([])
const sortProblemsOriginal = ref<TestPaperProblem[]>([])
const showChangeProblem = ref(false)
const curChangeProblem = ref<TestPaperProblem | null>()
const changeProblems = ref<YcType.ProblemType[]>()
const internalShow = ref(false)
const message = useOIWMessage()

const deepClone = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item)) as T
  }

  const cloned = {} as T
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key])
    }
  }

  return cloned
}
const getTestPaper = () => {
  getTestPaperApi({
    testPaperId: route.query.testPaperId as string,
    planId: route.query.id as string,
  }).then((res) => {
    testPaper.value = res.data
    const resData = res.data
    const _problems: TestPaperProblem[] = []
    getProblemsByIdApi(resData.problems.map((i) => i.problemId)).then((res) => {
      resData.problems.forEach((p) => {
        const matchingItemB = res.find((item) => {
          return item.id === p.problemId
        })
        _problems.push({ ...p, ...matchingItemB, id: p.id })
      })
    })
    setTimeout(() => {
      sortProblems.value = _problems
      sortProblemsOriginal.value = deepClone(_problems)
    }, 1500)
  })
}
getTestPaper()
const handleShowChangeProblem = () => {
  if (!testPaper.value) return
  if (testPaper.value.hasStudentAnswer) return
  showChangeProblem.value = true
}
const totalProblemItems = computed(() => {
  return testPaper.value?.problems.length
})
const dialog = useDialog()
const updateTestPaper = (p: YcType.ProblemType) => {
  sortProblems.value.forEach((problem, index) => {
    if (problem.id === curChangeProblem.value?.id) {
      sortProblems.value[index] = {
        ...curChangeProblem.value,
        ...p,
        id: problem.id,
        problemId: p.id,
      }
    }
  })
  internalShow.value = false
}
const updateTestPaperCancel = () => {
  sortProblems.value = sortProblemsOriginal.value
  showChangeProblem.value = false
}
const updateTestPaperDialog = () => {
  dialog.info({
    showIcon: false,
    title: '提示',
    content:
      '调整题目后立即生效，学生将使用此试卷进行摸底考试，请仔细核对后保存。',
    onPositiveClick: () => {
      const data: { id: string; problemId: string }[] = []
      sortProblems.value.forEach((p) => {
        data.push({ id: p.id, problemId: p.problemId })
      })

      updateTestPaperApi(data).then((res) => {
        if (res.data) {
          internalShow.value = false
          showChangeProblem.value = false
          getTestPaper()
        }
      })
    },
    positiveText: '确认',
    negativeText: '取消',
  })
}
const changeOringinProblem = () => {
  if (curChangeProblem.value) {
    getInitPaperProblemsApi({
      id: curChangeProblem.value.id,
      planId: route.query.id as string,
      testPaperId: route.query.testPaperId as string,
    })
      .then((res) => {
        getProblemsByIdApi([res.data.problemId]).then((ress) => {
          changeProblems.value = ress
          updateTestPaper(ress[0])
          internalShow.value = false
        })
      })
      .catch((err) => {
        message.error(err.response.data.message)
      })
  }
}

const changeProblem = (p: TestPaperProblem) => {
  curChangeProblem.value = p
  internalShow.value = true
  getTestPaperProblemsApi({
    examType: p.examType,
    difficult: p.difficult as string,
    topicPackageItemId: p.topicPackageItemId,
    testPaperId: route.query.testPaperId as string,
    planId: route.query.id as string,
  }).then((res) => {
    getProblemsByIdApi(res.data).then((ress) => {
      changeProblems.value = ress
    })
  })
}

watch(
  internalShow,
  () => {
    if (!internalShow.value) {
      curChangeProblem.value = null
      internalShow.value = false
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
.page-content {
  background: #f5f7fe;
}

.page {
  display: flex;
  flex-direction: column;
  width: 1150px;
  height: 100vh;
  padding: 30px 0;
  margin: 0 auto;
  overflow: scroll;

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.paper-box {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 23px;

  .le-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 46px;
  }

  .paper-h1 {
    font-family: 'PingFang SC';
    font-size: 23px;
    font-weight: 600;
    line-height: 31px;
    color: #393548;
  }

  .paper-h2 {
    font-family: 'PingFang SC';
    font-size: 19px;
    font-weight: 500;
    color: #393548;
  }

  .paper-h3 {
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: 400;
    color: #848096;
  }

  .paper-text {
    font-family: 'PingFang SC';
    font-size: 17px;
    font-weight: 400;
    line-height: 23px;
    color: #393548;
  }

  .p-card {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    height: 92px;
    padding: 15px;
    margin-top: 15px;
    margin-bottom: 30px;
    background: #f5f7fe;
    border-radius: 15px 15px 15px 15px;
    opacity: 1;

    .item {
      flex: 1;
      text-align: center;
    }
  }

  .p-card-default {
    box-sizing: border-box;
    width: 100%;
    padding: 15px 23px;
    padding-bottom: 5px;
    margin-top: 8px;
    margin-bottom: 30px;
    background: #f5f7fe;
    border-radius: 15px 15px 15px 15px;
  }

  .difficulty-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .difficult-55 {
      display: flex;
      align-items: center;

      &::before {
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-right: 6px;
        content: '';
        background: #4ecc5e;
        border-radius: 50%;
      }
    }

    .difficult-75 {
      display: flex;
      align-items: center;

      &::before {
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-right: 6px;
        content: '';
        background: #fea345;
        border-radius: 50%;
      }
    }

    .difficult-95 {
      display: flex;
      align-items: center;

      &::before {
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-right: 6px;
        content: '';
        background: #fa5a65;
        border-radius: 50%;
      }
    }
  }

  .paper-left {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    width: 759px;
    height: 87vh;
    padding: 31px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
    opacity: 1;
  }

  .paper-right {
    box-sizing: border-box;
    width: 368px;
    padding: 31px 23px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0px 4px 4px 0px rgba(151, 151, 151, 0.1);
  }

  ::v-deep(.onion-problem-render__option) {
    display: flex;
    flex-wrap: wrap;
  }

  ::v-deep(.onion-problem-render__option--item) {
    width: 50%;
    border: none;
  }

  ::v-deep(.onion-problem-render__main) {
    font-size: 17px;
  }

  ::v-deep(.onion-problem-render__examBox-show) {
    display: none;
  }

  ::v-deep(.onion-problem-render__main img) {
    max-height: 200px;
  }

  ::v-deep(
      .onion-problem-render__examBox-show + .onion-problem-render__option
    ) {
    display: none;
  }

  .p-item-box {
    padding: 30px 8px;
    padding-bottom: 48px;
    border-bottom: 1px solid #efeef3;
  }

  .cur-select {
    background: #f5f7fe;
    border-radius: 15px;
  }
}

.topic-package-info {
  display: flex;
  align-items: center;

  span {
    padding: 10px 12px;
    margin-right: 8px;
    font-family: 'PingFang SC';
    font-size: 13px;
    font-weight: 500;
    line-height: 15px;
    color: #393548;
    background: #f5f7fe;
    border-radius: 8px 8px 8px 8px;
  }

  .difficult-55 {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 6px;
      content: '';
      background: #4ecc5e;
      border-radius: 50%;
    }
  }

  .difficult-75 {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 6px;
      content: '';
      background: #fea345;
      border-radius: 50%;
    }
  }

  .difficult-95 {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 6px;
      content: '';
      background: #fa5a65;
      border-radius: 50%;
    }
  }
}

.change-problems {
  padding-top: 30px;
  border-bottom: 1px solid #efeef3;

  &:first-child {
    padding: 0;
  }

  ::v-deep(.onion-problem-render__option--item) {
    // width: 50%;
    border: none;
  }

  ::v-deep(.onion-problem-render__main) {
    font-size: 17px;
  }

  ::v-deep(.onion-problem-render__examBox-show) {
    display: none;
  }

  ::v-deep(.onion-problem-render__main img) {
    max-height: 200px;
  }

  ::v-deep(
      .onion-problem-render__examBox-show + .onion-problem-render__option
    ) {
    display: none;
  }
}

.tpb {
  margin: 23px 0 30px 390px;
}

.drawer-content-change-problems {
  height: calc(100vh - 83px - 76px - 37px);
  overflow: scroll;

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.huanti-box {
  &::-webkit-scrollbar {
    width: 0 !important;
  }
}
</style>
