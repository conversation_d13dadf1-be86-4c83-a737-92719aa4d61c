<template>
  <div class="pb-60px">
    <div
      v-if="filterOptions"
      class="relative bg-#F7F7F9 rounded-16px text-size-14px font-500 p-16px"
    >
      <FilterItem
        v-if="filters.problemExamTypeOption"
        label-width="56"
        name="学科:"
        class="mb-12px"
      >
        <n-cascader
          v-model:value="stageAndSubject"
          class="oiw-cascader w-180px"
          label-field="name"
          value-field="treeId"
          placeholder="选择学科"
          expand-trigger="click"
          :options="csvOptions"
          check-strategy="child"
          filterable
          @update:value="onFilterChange('stageAndSubject', $event)"
        />
      </FilterItem>
      <FilterItem
        v-if="filters.problemExamTypeOption"
        label-width="56"
        name="题型:"
        class="mb-12px"
      >
        <FilterSelect
          :value="queryParams.problemExamType"
          :options="filters.problemExamTypeOption.items"
          @update:value="onFilterChange('problemExamType', $event)"
        />
      </FilterItem>
      <div v-if="isExpand">
        <FilterItem v-if="filters.gradeOption" label-width="56" name="年级:">
          <FilterSelect
            :value="queryParams.grade"
            :options="filters.gradeOption.items"
            @update:value="onFilterChange('grade', $event)"
          />
        </FilterItem>
        <FilterItem v-if="filters.termOption" label-width="56" name="学期:">
          <FilterSelect
            :value="queryParams.term"
            :options="filters.termOption.items"
            @update:value="onFilterChange('term', $event)"
          />
        </FilterItem>
        <FilterItem v-if="filters.yearOption" label-width="56" name="年份:">
          <FilterSelect
            :value="queryParams.year"
            :options="filters.yearOption.items"
            @update:value="onFilterChange('year', $event)"
          />
        </FilterItem>
        <FilterItem
          v-if="filters.paperTypeOption"
          label-width="56"
          name="类型:"
        >
          <FilterSelect
            :value="queryParams.paperType"
            :options="filters.paperTypeOption.items"
            @update:value="onFilterChange('paperType', $event)"
          />
        </FilterItem>
        <FilterItem
          v-if="filters.paperLevelOption"
          label-width="56"
          name="等级:"
        >
          <FilterSelect
            :value="queryParams.paperLevel"
            :options="filters.paperLevelOption.items"
            @update:value="onFilterChange('paperLevel', $event)"
          />
        </FilterItem>
        <div class="flex">
          <FilterItem
            v-if="filters.provinceCodeOption"
            label-width="56"
            labelline-height="32"
            name="地区:"
            class="mr-24px"
          >
            <OIWSelect
              :value="queryParams.provinceCode"
              class="medium-select"
              hight="30"
              style="width: 180px; height: 30px"
              :options="filters.provinceCodeOption.items"
              :consistent-menu-width="false"
              placeholder="选择地区"
              @update:value="onFilterChange('provinceCode', $event)"
            ></OIWSelect>
          </FilterItem>
          <FilterItem
            v-if="filters.publisherIdOption"
            label-width="76"
            labelline-height="32"
            name="教材版本:"
            class="mr-24px"
          >
            <OIWSelect
              :value="queryParams.publisherId"
              class="medium-select"
              hight="30"
              style="width: 180px; height: 30px"
              :options="filters.publisherIdOption"
              :consistent-menu-width="false"
              placeholder="选择教材版本"
              @update:value="onFilterChange('publisherId', $event)"
            ></OIWSelect>
          </FilterItem>
          <FilterItem
            v-if="tagsWithAll.length"
            label-width="66"
            labelline-height="32"
            name="知识点:"
            class="teachingTagSelect"
          >
            <n-select
              :value="queryParams.teachingTagIds"
              multiple
              clearable
              filterable
              virtual-scroll
              :style="
                queryParams.teachingTagIds.length > 1
                  ? { 'min-height': '30px', height: 'auto', width: '200px' }
                  : { height: '30px', width: '200px' }
              "
              :options="tagsWithAll"
              placeholder="选择知识点"
              :render-option="renderOption"
              @update:value="changeTeachingTagIds"
            ></n-select>
          </FilterItem>
        </div>
      </div>
      <div
        class="absolute right-16px flex items-center leading-20px color-#5E80FF text-14px font-600 cursor-pointer"
        :class="{
          'bottom-12px': !isExpand,
          'bottom-16px': isExpand,
        }"
        @click="isExpand = !isExpand"
      >
        <span class="mr-2px">{{ isExpand ? '收起' : '展开' }}</span>
        <ArrowIcon
          class="w-18px h-18px color-#5E80FF transition-all duration-300 ease-in-out"
          :class="{
            'rotate-0': isExpand,
            'rotate-180': !isExpand,
          }"
        />
      </div>
    </div>
    <div class="">
      <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
      <div v-else-if="problemList.length">
        <div class="flex mt-24px justify-between items-center">
          <div>筛选到题目总数：{{ total }}</div>
        </div>
        <ProblemItem
          v-for="(problem, index) in problemList"
          :key="problem.problemId"
          :problem="problem"
          :index="index + 1 + (page - 1) * 10"
          class="border-b border-gray-200 last:border-none"
          :is-favorite="isFavorite(problem.id)"
          :booksIds="booksIds"
          @on-favorite="handleFavoriteClick(problem.id)"
          @on-add-to-books="addToBooks(problem.id)"
        >
          <template #topTags>
            <span
              class="px-8px py-4px text-12px rounded-4px mx-8px font-bold color-#5CA8F8 cursor-pointer"
              style="background: rgba(92, 168, 248, 0.15)"
              @click="onPaperClick(problem.paperName)"
            >
              {{ problem.paperName }}
            </span>
          </template>
        </ProblemItem>
        <n-space v-if="total > 1" justify="end" style="margin-top: 20px">
          <n-pagination
            v-model:page="page"
            v-model:page-size="pageSize"
            class="custom-pagination"
            :item-count="total"
            @update:page="onPageChange"
          />
          <BackTop />
        </n-space>
      </div>
      <OIWStateBlock
        v-else
        class="mt-120px text-center"
        type="empty"
        title="暂无资源"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { VNode } from 'vue'
import type { FilterOptions, Option } from '../service'
import { getFilterOptionsApi } from '../service'
import BackTop from '@/components/BackTop.vue'
import { getPaperProblemPoolListApi } from '@/components/SideResource/service'
import type { GetPaperProblemPoolListParams } from '@/components/SideResource/service'
import FilterItem from '@/components/FilterItem/FilterItem.vue'
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'
import {
  OIWSelect,
  OIWLoading,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { useCvs, useCvsEnum } from '@/hooks/useCvs'
import { useLoading } from '@/hooks/useLoading'
import { useAuth } from '@/hooks/useAuth.ts'
import type { ProblemDetailType } from '@/pages/problem/utils'
import { postProblemDetail } from '@/pages/problem/service'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import ArrowIcon from '~icons/yc/arrowIcon'
import useBooksProblem from '@/store/booksProblem'
import type { CascaderOption, SelectOption } from 'naive-ui'
import { NTooltip } from 'naive-ui'
import { useAddFavorite } from '@/hooks/useFavorite'
import { getFavoriteIdsApi } from '@/service/common'
import { usePaperTeachingTags } from '@/hooks/usePaperTeachingTags.ts'
import { debounce } from 'lodash-es'

type ValueType = string | number | undefined | null
interface FilterSelectOptions {
  label: string
  value: ValueType
}

interface TransformedOption {
  key: string
  items: FilterSelectOptions
}

const message = useOIWMessage()
const booksProblemStore = useBooksProblem()
const { booksIds } = storeToRefs(booksProblemStore)
const { PublisherEnum } = useCvsEnum()
const csvTree = useCvs()
const { toggleLoading, loading } = useLoading()
const { subjectId, stageId } = useAuth()
const isExpand = ref(true)
const selectSubjectId = ref(subjectId.value)
const selectStageId = ref(stageId.value)
const stageAndSubject = computed(() => {
  console.log('selectStageId.value', selectStageId.value, selectSubjectId.value)
  return `${selectStageId.value}-${selectSubjectId.value}`
})

const csvOptions = computed(() => {
  const newOptions = (csvTree.value as unknown as CascaderOption[]).map((a) => {
    return {
      treeId: `${a.id}`,
      name: a.name,
      children: a.subjects?.map((b) => {
        return {
          treeId: `${a.id}-${b.id}`,
          name: b.name,
        }
      }),
    }
  })
  // console.log('newOptions', newOptions, csvTree.value)
  return newOptions
})
const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem',
    stage: selectStageId.value,
    subject: selectSubjectId.value,
  })
  console.log('res？？？？', res)
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()
const handleFavoriteClick = async (problemId: string) => {
  const updateProblemFavoriteCount = (id: string, add = true) => {
    const problem = problemList.value.find((el) => el.id === id)
    if (problem) {
      if (add) {
        const newCount = (problem.favoriteCount as number) + 1
        problem.favoriteCount = newCount
      } else {
        const newCount = (problem.favoriteCount as number) - 1
        problem.favoriteCount = newCount
      }
    }
  }
  if (!isFavorite(problemId)) {
    const problem = problemList.value.find((el) => el.id === problemId)
    await addFavorite({
      resourceId: problemId,
      resourceType: 'problem',
      stage: selectStageId.value,
      subject: selectSubjectId.value,
      extra: {
        examType: problem?.examType,
        type: problem?.type,
        region: problem?.region,
        difficulty: problem?.difficulty,
        accuracy: problem?.accuracy,
      },
    })
    updateProblemFavoriteCount(problemId, true)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    updateProblemFavoriteCount(problemId, false)
    favoriteIds.value.delete(problemId)
  }

  // fetch()
}
const addToBooks = async (id: string) => {
  if (booksIds.value.includes(id)) {
    await booksProblemStore.deleteBookProblems([id])
    message.success('已移出')
  } else {
    if (booksIds.value.length >= 100) {
      message.warning('无法加入，选题本最多支持100题')
      return
    }
    await booksProblemStore.addToBookProblems(
      id,
      'BOOK_PROBLEM_SOURCE_PROBLEM_POOL',
      'Problem',
    )
    message.success('已加入')
  }
}

const router = useRouter()
const onPaperClick = (paperName: string) => {
  const href = router.resolve({
    name: 'PaperList',
    query: {
      name: paperName,
      stageId: selectStageId.value,
      subjectId: selectSubjectId.value,
    },
  }).href
  window.open(href, '_blank')
}
const queryParams = ref<{
  problemExamType: ValueType
  grade: ValueType
  term: ValueType
  year: ValueType
  paperType: ValueType
  paperLevel: ValueType
  provinceCode: ValueType
  publisherId: ValueType
  // csvValue: string
  stageId: number
  subjectId: number
  teachingTagIds: string[] // 知识点标签数组
}>({
  problemExamType: '',
  grade: 0,
  term: '',
  year: 0,
  paperType: '',
  paperLevel: 0,
  provinceCode: 0,
  publisherId: 0,
  stageId: stageId.value || 0,
  subjectId: subjectId.value || 0,
  teachingTagIds: ['all'],
})

const filterOptions = ref<FilterOptions>()
const filters = computed(() => {
  const transformOption = <T,>(option: Option<T>): TransformedOption<T> => ({
    ...option,
    items: option.items.map((item) => ({
      id: item.id,
      value: item.value,
      label: item.name, // 将 name 转换为 label
    })),
  })

  if (!filterOptions.value) return {}

  return {
    problemExamTypeOption: transformOption(
      filterOptions.value.problemExamTypeOption,
    ),
    gradeOption: transformOption(filterOptions.value.gradeOption),
    termOption: transformOption(filterOptions.value.termOption),
    yearOption: transformOption(filterOptions.value.yearOption),
    paperTypeOption: transformOption(filterOptions.value.paperTypeOption),
    paperLevelOption: transformOption(filterOptions.value.paperLevelOption),
    provinceCodeOption: transformOption(filterOptions.value.provinceCodeOption),
    publisherIdOption: PublisherEnum.value.map((item, index) => ({
      id: index,
      label: item || '全部',
      value: index,
    })),
  }
})

const { tagsWithAll, getPaperTeachingTags } = usePaperTeachingTags()
// 渲染知识点标签Option
const renderOption = ({
  node,
  option,
}: {
  node: VNode
  option: SelectOption
}) =>
  h(NTooltip, null, {
    trigger: () => node,
    default: () => option.label,
  })

onMounted(async () => {
  const res = await getFilterOptionsApi()
  filterOptions.value = res
  fetchList()
  booksProblemStore.getBookProblems()
  await getPaperTeachingTags({
    stageId: selectStageId.value ? Number(selectStageId.value) : 2,
    subjectId: selectSubjectId.value ? Number(selectSubjectId.value) : 1,
    scene: 'course',
  })
})

const onPageChange = (val: number) => {
  page.value = val
  fetchList()
}

const onFilterChange = (
  type:
    | 'problemExamType'
    | 'grade'
    | 'term'
    | 'year'
    | 'paperType'
    | 'paperLevel'
    | 'provinceCode'
    | 'publisherId'
    | 'stageAndSubject',
  value: ValueType,
) => {
  if (type === 'stageAndSubject') {
    const [stageId, subjectId] = value?.toString().split('-') || []
    queryParams.value.stageId = parseInt(stageId)
    queryParams.value.subjectId = parseInt(subjectId)
    selectStageId.value = parseInt(stageId)
    selectSubjectId.value = parseInt(subjectId)
    // 重新获取知识点标签列表，并重置已选择的知识点标签
    queryParams.value.teachingTagIds = ['all']
    getPaperTeachingTags({
      stageId: parseInt(stageId),
      subjectId: parseInt(subjectId),
      scene: 'course',
    })
  } else {
    queryParams.value[type] = value
  }
  page.value = 1
  fetchList()
}

const debounceFetch = debounce(fetchList, 1000)
const changeTeachingTagIds = (ids: string[]) => {
  if (ids.length === 0 || ids[ids.length - 1] === 'all') {
    queryParams.value.teachingTagIds = ['all']
  } else {
    // 判断是否超过5个
    const delAllIds = ids.filter((item) => item !== 'all')
    if (delAllIds.length > 5) {
      message.warning('最多选择5个知识点')
      return
    }
    queryParams.value.teachingTagIds = delAllIds
  }
  page.value = 1
  debounceFetch()
}

const problemList = ref<ProblemDetailType[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)

async function fetchList() {
  try {
    toggleLoading()
    const realTeachingTagIds =
      queryParams.value.teachingTagIds.length === 1 &&
      queryParams.value.teachingTagIds[0] === 'all'
        ? []
        : queryParams.value.teachingTagIds
    const res = await getPaperProblemPoolListApi({
      ...queryParams.value,
      teachingTagIds: realTeachingTagIds,
      page: page.value,
      pageSize: pageSize.value,
    } as GetPaperProblemPoolListParams)
    if (res) {
      total.value = res.total
      const problem = await postProblemDetail({
        problemsId: res.paperProblems.map((item) => item.problemId),
      })
      problemList.value = res.paperProblems.map((item) => {
        const temp = problem.find((p) => p.id === item.problemId)
        return {
          ...item,
          ...temp,
          accuracy: undefined,
        }
      }) as ProblemDetailType[]
    }
    fetchFavorites()
  } catch (error) {
    console.error(error)
  } finally {
    toggleLoading()
    // disabled.value = false
  }
}
</script>

<style scoped>
::v-deep(.difficulty-text) {
  display: none;
}

::v-deep(.type-text) {
  margin-right: 0;
}

::v-deep(.filter-item) {
  margin-bottom: 12px;
}

.teachingTagSelect {
  ::v-deep(.n-base-selection-tag-wrapper) {
    display: block !important;
    width: 100%;
  }

  ::v-deep(.n-base-selection--selected) {
    border-radius: 12px;
  }

  ::v-deep(.n-base-selection__border) {
    border: 1px solid #c5c1d4;
  }
}
</style>
