<template>
  <section
    class="scroll-wrapper w-1200px mx-auto flex"
    :class="{ 'flex-col': checkValue === 'paper' }"
  >
    <div v-if="checkValue === 'practice'">
      <OIWRadioButtonGroup
        v-model:value="checkValue"
        class="mb-16px"
        :on-update-value="checkValueChanged"
      >
        <OIWRadioButton value="practice">同步习题</OIWRadioButton>
        <OIWRadioButton value="paper">试卷真题</OIWRadioButton>
      </OIWRadioButtonGroup>
      <CsvTreeSelect class="h-100% overflow-scroll" @change="onSelectChange" />
    </div>
    <div
      v-if="checkValue === 'practice'"
      class="h-100% pl-24px flex-1 overflow-scroll pb-60px scrollbar-none"
    >
      <div class="bg-#F7F7F9 rounded-16px text-size-14px font-500 p-16px">
        <FilterItem label-width="56" name="题型:">
          <FilterSelect
            :value="filterModel.examType"
            :options="examTypeFilters"
            @update:value="onExamTypeChange"
          />
        </FilterItem>
        <FilterItem label-width="56" name="正确率:" class="mt-12px">
          <FilterSelect
            :value="filterModel.accuracy"
            :options="accuracyOptions"
            @update:value="onAccuracyChange"
          />
        </FilterItem>
        <FilterItem label-width="56" name="难度:" class="mt-12px">
          <FilterSelect
            :value="filterModel.difficulty"
            :options="difficultyOptions"
            @update:value="onDifficultyChange"
          />
        </FilterItem>
        <FilterItem
          label-width="56"
          labelline-height="32"
          name="地区:"
          class="mt-12px"
        >
          <OIWSelect
            :value="filterModel.region"
            hight="30"
            class="medium-select"
            style="width: 180px; height: 30px"
            :options="regionFilters"
            :consistent-menu-width="false"
            placeholder="选择地区"
            @update:value="onRegionCodeChange"
          ></OIWSelect>
        </FilterItem>
      </div>
      <div class="flex mt-32px justify-between items-center">
        <div>筛选到题目总数：{{ total }}</div>
        <div class="button-group">
          <div
            v-for="i in sortByOptions"
            :key="i.value"
            class="button-item"
            :class="{ active: i.value === filterModel.sortBy }"
            @click="onSortByChange(i.value)"
          >
            {{ i.label }}
          </div>
        </div>
      </div>
      <OIWLoading :show="loading" width="200px" height="200px">
        <template v-if="problems.length">
          <div class="divide-y-[1px] divide-[#DCD8E7]">
            <ProblemItem
              v-for="(problem, index) of problems"
              :key="problem.id"
              :problem="problem"
              :index="index + 1"
              :is-favorite="isFavorite(problem.id)"
              :books-ids="booksIds"
              from="problemList"
              @on-favorite="favoriteItem(problem.id)"
              @on-add-to-books="addToBooks(problem.id)"
            />
          </div>
          <div class="mt-59px flex justify-end">
            <n-pagination
              class="custom-pagination"
              :page="page"
              :page-size="10"
              :item-count="total"
              @update:page="onPageChange"
            />
          </div>
          <BackTop />
        </template>
        <template v-else>
          <OIWStateBlock type="empty" title="暂无资源" />
        </template>
      </OIWLoading>
    </div>
    <div v-if="checkValue === 'paper'">
      <OIWRadioButtonGroup
        v-model:value="checkValue"
        class="mb-16px"
        :on-update-value="checkValueChanged"
      >
        <OIWRadioButton value="practice">同步习题</OIWRadioButton>
        <OIWRadioButton value="paper">试卷真题</OIWRadioButton>
      </OIWRadioButtonGroup>
    </div>
    <PaperList v-if="checkValue === 'paper'" />
  </section>
</template>

<script setup lang="ts">
import BackTop from '@/components/BackTop.vue'
import CsvTreeSelect from '../components/Tree.vue'
import {
  OIWLoading,
  OIWRadioButton,
  OIWRadioButtonGroup,
  useOIWMessage,
  OIWSelect,
  OIWStateBlock,
} from '@guanghe-pub/onion-ui-web'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import type { ProblemFilterItem, ProblemPoolSimple } from '../service'
import FilterItem from '@/components/FilterItem/FilterItem.vue'
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'

import PaperList from '../components/PaperList.vue'
import {
  getProblemPoolListApi,
  getProblemFilterApi,
  getProblemDetailsByIdApi,
} from '../service'
import { useLoading } from '@/hooks/useLoading'
import { DifficultyEnums } from '@guanghe-pub/onion-problem-render'
import { problemSort, type ProblemDetailType } from '../utils'
import { getFavoriteIdsApi } from '@/service/common'
import { useAddFavorite } from '@/hooks/useFavorite'
import { buryPoint } from '@/utils/buryPoint'
import useBooksProblem from '@/store/booksProblem'

const message = useOIWMessage()
const booksProblemStore = useBooksProblem()
const { booksIds } = storeToRefs(booksProblemStore)

const checkValue = ref<'practice' | 'paper'>('practice')
const checkValueChanged = (value: 'practice' | 'paper') => {
  checkValue.value = value
}

const filterModel = reactive<{
  examType?: string
  accuracy?: string
  difficulty?: string
  region?: string
  sortBy: string
}>({
  examType: undefined,
  accuracy: undefined,
  difficulty: undefined,
  region: '',
  sortBy: 'usageCount',
})

const onExamTypeChange = (value: any) => {
  filterModel.examType = value
  onFilterChange()
}
const onAccuracyChange = (value: any) => {
  filterModel.accuracy = value
  onFilterChange()
}
const onDifficultyChange = (value: any) => {
  filterModel.difficulty = value
  onFilterChange()
}
const onRegionCodeChange = (value: any) => {
  filterModel.region = value
  onFilterChange()
}
const onSortByChange = (value: any) => {
  filterModel.sortBy = value
  onFilterChange()
}

const resetFilterModel = () => {
  filterModel.examType = undefined
  filterModel.accuracy = undefined
  filterModel.difficulty = undefined
  filterModel.region = ''
  filterModel.sortBy = 'usageCount'
}

const accuracyOptions = [
  {
    label: '全部',
    value: undefined,
  },
  {
    label: '20%以下',
    value: '0-20',
  },
  {
    label: '20%-40%',
    value: '20-40',
  },
  {
    label: '40%-60%',
    value: '40-60',
  },
  {
    label: '60%-80%',
    value: '60-80',
  },
  {
    label: '80%以上',
    value: '80-101',
  },
]

const difficultyOptions = [
  {
    label: '全部',
    value: undefined,
  },
  ...DifficultyEnums.map((el) => {
    return {
      label: el.name,
      value: `${el.score}`,
    }
  }),
]

const sortByOptions = [
  {
    label: '使用数从高到低',
    value: 'usageCount',
  },
  {
    label: '收藏数从高到低',
    value: 'favoriteCount',
  },
]

interface ChangeEvent {
  id: string
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  resourceType?: 'new'
}
const currentCsv = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}>()

const isFirst = ref(true)
const onSelectChange = ({
  id,
  resourceType,
  subjectId,
  stageId,
  publisherId,
  semesterId,
}: ChangeEvent) => {
  currentCsv.value = {
    subjectId,
    stageId,
    publisherId,
    semesterId,
  }
  fetchFavorites()
  fetch(subjectId, stageId, id, resourceType)
  if (isFirst.value) {
    isFirst.value = false
    buryPoint(
      'enterTeacherWorkBenchResourceSecTabPage',
      {
        browserResolution: `${screen.width}x${screen.height}`,
        optionName: 'public',
        option: 'problemList',
        subjectId: subjectId ? Number(subjectId) : -1,
        stageId: stageId ? Number(stageId) : -1,
        publisherId: publisherId ? Number(publisherId) : -1,
        semesterId: semesterId ? Number(semesterId) : -1,
      },
      'course',
    )
  }
}

const filters = ref<ProblemFilterItem[]>([])
const regionFilters = computed(() => {
  const regions = (
    filters.value.find((el) => el.name === 'region')?.enums || []
  ).map((el) => {
    return {
      label: el.showName,
      value: el.value,
    }
  })
  return [
    {
      label: '全部',
      value: '',
    },
    ...regions,
  ]
})
const examTypeFilters = computed(() => {
  const examFilters = (filters.value.find((el) => el.name === 'examType')
    ?.enums || []) as {
    showName: string
    value: string
  }[]
  return [
    {
      label: '全部',
      value: undefined,
    },
    ...examFilters.map((el) => {
      return {
        label: el.showName,
        value: el.value,
      }
    }),
  ]
})

const allProblemPool = shallowRef<ProblemPoolSimple[]>([])
const filterProblemPool = shallowRef<ProblemPoolSimple[]>([])
const page = ref(1)
const currentPageProblems = computed(() => {
  return filterProblemPool.value.slice((page.value - 1) * 10, page.value * 10)
})
const total = computed(() => filterProblemPool.value.length)
const { loading, startLoading, endLoading } = useLoading(true)

const fetch = async (
  subjectId: YcType.CsvId,
  stageId: YcType.CsvId,
  subSectionId: string,
  resourceType?: 'new',
) => {
  startLoading()
  if (subSectionId === '') {
    endLoading()
    allProblemPool.value = []
    filterProblemPool.value = []
    problems.value = []
    return
  }
  const res = await getProblemPoolListApi({
    subSectionId,
    resourceType,
  })
  const filtersRes = await getProblemFilterApi({
    subjectId,
    stageId,
    subSectionId,
    resourceType,
  })
  filters.value = filtersRes.filters
  allProblemPool.value = res.data
  filterProblemPool.value = res.data
  resetFilterModel()
  await filterCurrentProblemPool()
  booksProblemStore.getBookProblems()
  endLoading()
}

const addToBooks = async (id: string) => {
  if (booksIds.value.includes(id)) {
    await booksProblemStore.deleteBookProblems([id])
    message.success('已移出')
  } else {
    buryPoint('click_resourceCenter_exam_add', { problemId: id }, 'course')
    if (booksIds.value.length >= 100) {
      message.warning('无法加入，选题本最多支持100题')
      return
    }
    await booksProblemStore.addToBookProblems(
      id,
      'BOOK_PROBLEM_SOURCE_PROBLEM_POOL',
      'Problem',
    )
    message.success('已加入')
  }
}

async function onFilterChange() {
  startLoading()
  await filterCurrentProblemPool()
  endLoading()
}

async function filterCurrentProblemPool() {
  let filterProblems: ProblemPoolSimple[] = []
  filterProblems = allProblemPool.value.filter((el) => {
    let regionFilter = true
    let examTypeFilter = true
    let difficultyFilter = true
    let accuracyFilter = true
    if (filterModel.region) {
      regionFilter = el.region === filterModel.region
    }
    if (filterModel.examType) {
      examTypeFilter =
        (el.examType && el.examType === filterModel.examType) ||
        el.type === filterModel.examType
    }
    if (filterModel.accuracy) {
      const [min, max] = filterModel.accuracy.split('-')
      accuracyFilter =
        Number(el.accuracy) >= Number(min) && Number(el.accuracy) < Number(max)
    }
    if (filterModel.difficulty) {
      difficultyFilter = el.difficulty === Number(filterModel.difficulty)
    }
    return regionFilter && examTypeFilter && difficultyFilter && accuracyFilter
  })
  if (filterModel.sortBy) {
    filterProblems = problemSort(
      filterProblems,
      filterModel.sortBy === 'usageCount'
        ? ['usageCount', 'favoriteCount']
        : ['favoriteCount', 'usageCount'],
    )
  }
  filterProblemPool.value = filterProblems
  page.value = 1
  if (filterProblemPool.value.length > 0) {
    await fetchProblemDetails()
  } else {
    problems.value = []
  }
}

const problems = ref<ProblemDetailType[]>([])

async function fetchProblemDetails() {
  const currentPageProblemIds = currentPageProblems.value.map(
    (el) => el.problemId,
  )
  if (currentPageProblemIds.length === 0) return
  const res = await getProblemDetailsByIdApi(currentPageProblemIds)
  problems.value = res.map((el) => {
    return {
      ...el,
      accuracy:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.accuracy || 0,
      favoriteCount:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.favoriteCount || '0',
      usageCount:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.usageCount || '0',
      region:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.region || '',
    }
  })
}
const onPageChange = (val: number) => {
  page.value = val
  fetchProblemDetails()
}

const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem',
    stage: currentCsv.value?.stageId,
    subject: currentCsv.value?.subjectId,
    semester: currentCsv.value?.semesterId,
    publisher: currentCsv.value?.publisherId,
  })
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()

const updateProblemFavoriteCount = (id: string, add = true) => {
  const simpleProblem = allProblemPool.value.find((el) => el.problemId === id)
  const problem = problems.value.find((el) => el.id === id)
  if (simpleProblem) {
    if (add) {
      const newCount = parseInt(simpleProblem.favoriteCount) + 1
      simpleProblem.favoriteCount = newCount.toString()
      if (problem) {
        problem.favoriteCount = newCount.toString()
      }
    } else {
      let newCount = parseInt(simpleProblem.favoriteCount) - 1
      newCount = newCount < 0 ? 0 : newCount
      simpleProblem.favoriteCount = newCount.toString()
      if (problem) {
        problem.favoriteCount = newCount.toString()
      }
    }
  }
}
const favoriteItem = async (problemId: string) => {
  if (!isFavorite(problemId)) {
    await addFavorite({
      resourceType: 'problem',
      resourceId: problemId,
      publisher: currentCsv.value?.publisherId,
      subject: currentCsv.value?.subjectId,
      stage: currentCsv.value?.stageId,
      semester: currentCsv.value?.semesterId,
      extra: {
        accuracy: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.accuracy,
        difficulty: allProblemPool.value.find(
          (el) => el.problemId === problemId,
        )?.difficulty,
        examType: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.examType,
        type: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.type,
        region: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.region,
      },
    })
    updateProblemFavoriteCount(problemId)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    favoriteIds.value.delete(problemId)
    updateProblemFavoriteCount(problemId, false)
  }
  buryPoint('click_prepareCenter_collect6', {
    subjectId: parseInt(currentCsv.value?.subjectId as string),
    stageId: parseInt(currentCsv.value?.stageId as string),
    publisherId: parseInt(currentCsv.value?.publisherId as string),
    semesterId: parseInt(currentCsv.value?.semesterId as string),
    pageName: '1',
    problemId,
  })
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;

  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.scroll-wrapper {
  height: calc(100vh - 64px - 72px);
}

.medium-select {
  width: 150px;
}
</style>
