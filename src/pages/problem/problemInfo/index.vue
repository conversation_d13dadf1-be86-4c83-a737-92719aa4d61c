<template>
  <div class="problem-info-main">
    <ClassProblem :problem="problem" />
  </div>
</template>

<script setup lang="ts">
import { postProblemDetail } from '../service'
import { getSchoolProblemDetailApi } from '@/pages/schoolResource/schoolProblem/service'
import { buryPoint } from '@/utils/buryPoint'
import ClassProblem from '@/components/ClassRoomMode/ClassProblem.vue'

const props = defineProps<{
  problemId: string
  courseId: string
  type?: string
}>()

const problem = ref<any>({})
const init = async () => {
  const res =
    props.type === 'SchoolProblem'
      ? (await getSchoolProblemDetailApi({ problemsId: [props.problemId] }))
          .data
      : await postProblemDetail({ problemsId: [props.problemId] })
  if (res) {
    problem.value = res[0]
  }
}
init()
buryPoint(
  'enter_TW_slideEdit_answerViewpage',
  {
    problemId: props.problemId,
    courseId: props.courseId,
  },
  'Teaching',
)
buryPoint('enter_teachingCenter', { fromPageName: 'other' }, 'course')
</script>

<style lang="scss" scoped>
.problem-info-main {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}
</style>
