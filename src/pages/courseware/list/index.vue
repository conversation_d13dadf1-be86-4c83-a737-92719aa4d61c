<template>
  <section class="scroll-wrapper w-1200px mx-auto flex relative">
    <div v-if="isAuth" class="movement-box">
      <n-checkbox
        v-model:checked="movementCheckbox"
        @update:checked="handleCheckAll"
        >全选</n-checkbox
      >
      <button
        :disabled="!movementList.length"
        class="movement-btn"
        @click="handleMovementClick"
      >
        移动
      </button>
    </div>
    <CsvTreeSelect
      v-show="!isSearch"
      class="h-100% overflow-y-scroll"
      :startLevelTree="1"
      :search-change-id="searchChangeId"
      @select="onSelectTree"
      @expand="onExpandTree"
    />
    <div class="scrollbar-none pl-24px flex-1 h-100% overflow-y-scroll pb-60px">
      <div class="flex justify-between">
        <div>
          <div v-show="!isSearch" class="button-group">
            <div
              v-for="i in options"
              :key="i.value"
              class="button-item"
              :class="{ active: i.value === filterModel.sort }"
              @click="onUpdateSort(i.value as CouserwareSort)"
            >
              {{ i.label }}
            </div>
          </div>
        </div>
        <OIWInput
          :value="filterModel.title"
          :maxlength="50"
          placeholder="输入课件名称搜索资源"
          clearable
          size="medium"
          class="right mb-16px"
          @update:value="onUpdateTitle"
        >
          <template #suffix>
            <SearchIcon />
          </template>
        </OIWInput>
      </div>
      <div v-if="isSearch && !loading" class="search-chapters-box">
        <div class="search-title-box">
          教材章节搜索结果<span class="grey-text"
            >｜搜索到如下教材章节，点击章节名称查看该章节所有课件</span
          >
        </div>
        <div v-if="searchList.length" class="search-chapters-content">
          <div
            v-for="item in searchList"
            :key="item.id"
            class="search-chapters-list"
          >
            <div class="chapters-name" @click="changeSearchId(item.id || '')">
              {{ item.name }}
            </div>
            <div class="chapters-count">
              （{{ item.coursewareCount }}个课件）
            </div>
          </div>
        </div>
        <div v-else class="search-chapters-content">
          <div class="search-chapters-list search-chapters-null">
            <div class="chapters-count">暂无数据</div>
          </div>
        </div>
      </div>
      <div
        class="py-21px"
        style="box-sizing: border-box; height: calc(100% - 40px)"
      >
        <div v-if="isSearch && !loading" class="search-title-box mb-24px">
          课件搜索结果
        </div>
        <CourSewareCard
          :loading="loading"
          :coursewares="coursewares"
          :total="total"
          :page="page"
          :page-size="pageSize"
          :isAuth="isAuth"
          :search="isSearch"
          @page-change="onPageChange"
          @edit-click="onEditClick"
          @preview-click="onPreviewClick"
          @create-cloud-disk-file="createCloudDiskFile"
          @share="onShare"
          @checkbox-click="onCheckboxClick"
        />
      </div>
    </div>
    <MovementModal
      v-model:visible="movementModalVisible"
      @movement-confirm="handleMovementConfirm"
    />
  </section>
</template>

<script setup lang="ts">
import CsvTreeSelect from '../components/Tree.vue'
import CourSewareCard from '../components/CourSewareCard.vue'
import { OIWInput, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import type {
  CouserwareSort,
  CourserwareItem,
  CouserwareSearchRes,
} from '../service'
import {
  createCloudDiskFileApi,
  getCouserwareDetailApi,
  getCouserwareListApi,
  postCouserwareSearch,
  postCouserwareBatchUpdate,
} from '../service'
import type { ChangeEvent } from '../utils'
import SearchIcon from '~icons/yc/search'
import { debounce } from '@guanghe-pub/onion-utils'
import { useLoading } from '@/hooks/useLoading'
import { couserwareFineAuth } from '@/hooks/usePermission'
import { buryPoint } from '@/utils/buryPoint'
import { useShare } from '@/components/ShareModal/useShare'
import { authByShowSouretypeApi } from '@/service/common'
import MovementModal from '../components/MovementModal.vue'

const message = useOIWMessage()
const dialog = useDialog()
const isAuth = ref(false)
authByShowSouretypeApi('PUBLIC_RESOURCE_EDIT').then((res) => {
  isAuth.value = res.isShow
})

const options = [
  {
    label: '默认',
    value: 'DEFAULT',
  },
  {
    label: '加入备课数从多到少',
    value: 'USE',
  },
  {
    label: '点赞数从多到少',
    value: 'STAR',
  },
]

const page = ref(1)
const pageSize = ref(9)
const total = ref(9)

const searchList = ref<CouserwareSearchRes[]>([])
const searchChangeId = ref<string>('')

const filterModel = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  subSectionId?: string
  sectionId?: string
  chapterId: string
  sort: CouserwareSort
  title?: string | null
  sectionIds?: string[]
  subSectionIds?: string[]
}>({
  publisherId: '',
  semesterId: '',
  subjectId: '',
  stageId: '',
  subSectionId: '',
  sectionId: '',
  sort: 'DEFAULT',
  chapterId: '',
  title: null,
  sectionIds: [],
  subSectionIds: [],
})

const isSearch = computed(() => {
  return !!filterModel.value.title
})

const onUpdateTitle = (val: string) => {
  filterModel.value.title = val
  if (val) {
    pageSize.value = 12
    debounceSearchListData()
  } else {
    pageSize.value = 9
  }
  page.value = 1
  debounceFetchData()
}
const onUpdateSort = (val: CouserwareSort) => {
  filterModel.value.sort = val
  page.value = 1
  fetch()
  buryPoint('click_resourceCenter_courseListRank', {
    button: val === 'STAR' ? '2' : '1',
  })
}
const { loading, endLoading, startLoading } = useLoading(true)

const onExpandTree = (event: ChangeEvent) => {
  searchChangeId.value = ''
  filterModel.value.title = ''
  onTreeChange(event)
}

const onSelectTree = (event: ChangeEvent) => {
  searchChangeId.value = ''
  filterModel.value.title = ''
  onTreeChange(event)
}
const isFrist = ref(true)

const onTreeChange = ({
  publisherId,
  semesterId,
  subjectId,
  stageId,
  node,
  sectionIds,
  subSectionIds,
}: ChangeEvent) => {
  startLoading()
  console.log('node>>>>>>>>>', node)
  let chapterId = ''
  let sectionId = ''
  let subSectionId = ''
  if (!node) {
    coursewares.value = []
    endLoading()
    // return
  } else {
    ;[chapterId, sectionId, subSectionId] = node.ids as string[]
  }
  filterModel.value = {
    publisherId,
    semesterId,
    subjectId,
    stageId,
    subSectionId,
    chapterId,
    sectionId,
    sectionIds,
    subSectionIds,
    sort: 'DEFAULT',
    title: null,
  }
  buryPoint('click_resourceCenter_courseList', {
    subjectId: parseInt(filterModel.value.subjectId as string),
    stageId: parseInt(filterModel.value.stageId as string),
    publisherId: parseInt(filterModel.value.publisherId as string),
    semesterId: parseInt(filterModel.value.semesterId as string),
  })
  page.value = 1
  fetch()
  if (isFrist.value) {
    isFrist.value = false
    buryPoint(
      'enterTeacherWorkBenchResourceSecTabPage',
      {
        browserResolution: `${screen.width}x${screen.height}`,
        optionName: 'public',
        option: 'courseware',
        subjectId: subjectId ? Number(subjectId) : -1,
        stageId: stageId ? Number(stageId) : -1,
        publisherId: publisherId ? Number(publisherId) : -1,
        semesterId: semesterId ? Number(semesterId) : -1,
      },
      'course',
    )
  }
}
const query = computed(() => {
  return {
    ...filterModel.value,
    page: page.value,
    pageSize: pageSize.value,
  }
})

const coursewares = ref<CourserwareItem[]>([])
const movementCheckbox = ref(false)
const movementList = ref<string[]>([])
const movementModalVisible = ref<boolean>(false)

const fetch = async () => {
  startLoading()
  if (query.value.chapterId === '') {
    endLoading()
    movementList.value = []
    movementCheckbox.value = false
    coursewares.value = []
    total.value = 0
    return
  }
  const res = await getCouserwareListApi(query.value).finally(() =>
    endLoading(),
  )
  movementList.value = []
  movementCheckbox.value = false
  coursewares.value = res.data
  total.value = res.total
}
const couserwareSearchList = async () => {
  const { data } = await postCouserwareSearch({
    subjectId: Number(filterModel.value.subjectId),
    stageId: Number(filterModel.value.stageId),
    publisherId: Number(filterModel.value.publisherId),
    semesterId: Number(filterModel.value.semesterId),
    keyword: filterModel.value.title || '',
  })
  if (data) {
    searchList.value = data
  }
}
const debounceFetchData = debounce(fetch, 300)
const debounceSearchListData = debounce(couserwareSearchList, 300)

const onPageChange = (val: number) => {
  page.value = val
  fetch()
}

const onPreviewClick = async (coursewareId: string, isFine: boolean) => {
  const auth = await couserwareFineAuth({
    subjectId: Number(filterModel.value.subjectId),
    stageId: Number(filterModel.value.stageId),
  })
  if (isFine && !auth) {
    dialog.info({
      showIcon: false,
      title: '提示',
      content: '该课件为精品课件，请联系洋葱工作人员开通使用权限',
      positiveText: '确认',
    })
    return
  }
  buryPoint('click_teacherWorkbench_preview_course', {
    subjectId: filterModel.value.subjectId,
    stageId: filterModel.value.stageId,
    publisherId: filterModel.value.publisherId,
    semesterId: filterModel.value.semesterId,
    courseId: coursewareId,
  })
  window.open(
    `/teacher-workbench/resourceCenter/commonResource/coursePreview?coursewareId=${coursewareId}`,
    '_blank',
  )
}

const router = useRouter()
const onEditClick = async (
  coursewareId: string,
  isFine: boolean,
  name: string,
) => {
  const res = await getCouserwareDetailApi(coursewareId)
  const href = router.resolve({
    name: 'SlideEdit',
    query: {
      fileId: res.fileId,
      platform: 'resourceCenter',
      fileName: name,
    },
  }).href
  window.open(href, '_blank')
}

const createState = ref(new Set<string>())
const createCloudDiskFile = async (id: string, isFine: boolean) => {
  const auth = await couserwareFineAuth({
    subjectId: Number(filterModel.value.subjectId),
    stageId: Number(filterModel.value.stageId),
  })
  if (isFine && !auth) {
    dialog.info({
      showIcon: false,
      title: '提示',
      content: '该课件为精品课件，请联系洋葱工作人员开通使用权限',
      positiveText: '确认',
    })
    return
  }
  buryPoint('click_prepareCenter_add', { pageName: '1', courseId: id })
  if (createState.value.has(id)) return
  createState.value.add(id)
  createCloudDiskFileApi(id).then(() => {
    createState.value.delete(id)
    message.success('添加成功')
  })
}
const { createShare } = useShare()
const onShare = (course: CourserwareItem) => {
  createShare({
    resourceId: course.coursewareId,
    resourceName: course.title,
    resourceType: 'courseware',
    from: 'courseList',
    extra: {
      subjectId: filterModel.value.subjectId,
      stageId: filterModel.value.stageId,
      publisherId: filterModel.value.publisherId,
      semesterId: filterModel.value.semesterId,
    },
  })
}

const changeSearchId = (id: string) => {
  searchChangeId.value = id
}
const handleCheckAll = () => {
  if (movementCheckbox.value) {
    movementList.value = []
    coursewares.value.forEach((item) => {
      item.cardCheckbox = true
      movementList.value.push(item.coursewareId)
    })
    movementCheckbox.value = true
  } else {
    coursewares.value.forEach((item) => {
      item.cardCheckbox = false
    })
    movementList.value = []
    movementCheckbox.value = false
  }
}
const onCheckboxClick = (id: string) => {
  coursewares.value.forEach((item) => {
    if (item.coursewareId === id) {
      item.cardCheckbox = !item.cardCheckbox
      if (item.cardCheckbox) {
        movementList.value.push(id)
      } else {
        movementList.value = movementList.value.filter((item) => item !== id)
      }
    }
  })
  if (movementList.value.length >= pageSize.value) {
    movementCheckbox.value = true
  }
}

const handleMovementClick = () => {
  movementModalVisible.value = true
}

const handleMovementConfirm = async (expandedKeys: string[]) => {
  try {
    await postCouserwareBatchUpdate({
      coursewareIds: movementList.value,
      chapterId: expandedKeys[0],
      sectionId: expandedKeys[1],
      subSectionId: expandedKeys[2],
    })
    message.success('迁移成功')
    movementList.value = []
    movementCheckbox.value = false
    page.value = 1
    fetch()
  } catch (e) {
    console.log(e)
    message.error('迁移失败')
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.oiw-state-block--box) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.button-group {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;
  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.scroll-wrapper {
  height: calc(100vh - 64px - 72px);
}

.left {
  width: 180px;
}

.right {
  width: 282px;
}

.search-chapters-box {
  margin-top: 20px;
  margin-bottom: 11px;
  box-shadow: inset 0px -1px 0px 0px #dcd8e7;

  .search-chapters-content {
    padding-bottom: 16px;

    .search-chapters-list {
      display: flex;
      align-items: center;
      padding: 16px;

      &:hover {
        background: #f4f6ff;
        border-radius: 12px;
      }

      &.search-chapters-null {
        padding-top: 20px;
        padding-bottom: 20px;

        &:hover {
          background: #fff;
          border-radius: 0;
        }
      }

      .chapters-name {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #5e80ff;
        cursor: pointer;
      }

      .chapters-count {
        margin-left: 4px;
        font-size: 16px;
        line-height: 24px;
        color: #393548;
      }
    }
  }
}

.search-title-box {
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: #393548;

  &.mb-24px {
    margin-bottom: 24px;
  }

  .grey-text {
    font-size: 14px;
    font-weight: 400;
    color: #9792ac;
  }
}

.movement-box {
  position: absolute;
  top: -60px;
  right: 0;
  display: flex;
  align-items: center;

  .movement-btn {
    width: 104px;
    height: 40px;
    color: #fff;
    background: #5e80ff;
    border-radius: 140px;

    &:disabled {
      cursor: not-allowed;
      background: #c5c1d4;
    }
  }
}
</style>
