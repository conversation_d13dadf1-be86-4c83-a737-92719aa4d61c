<template>
  <div class="question-editor">
    <div class="flex items-center">
      <div
        ref="editor"
        class="content-editor"
        contenteditable
        @input="handleEditorInput(emit)"
        @paste="(event) => handlePaste(event, emit)"
      ></div>

      <div class="flex items-center">
        <DeleteIcon
          class="w-20px h-20px cursor-pointer mx-8px"
          @click="emit('remove')"
        />
        <UploadImg
          class="upload-img-btn w-20px! h-20px! mr-8px"
          @update:value="(newValue) => handleImageUpload(newValue, emit)"
        >
          <template #uploadImg>
            <UploadImgIcon class="w-20px h-20px cursor-pointer mt-10px" />
          </template>
        </UploadImg>

        <button @click="openFormulaModal(null)">
          <FxIcon
            class="w-20px h-20px cursor-pointer"
            @click="openFormulaModal(null)"
          />
        </button>
      </div>
    </div>
    <FormulaModal
      v-if="showFormulaModal"
      v-model:show="showFormulaModal"
      :initialLatex="editingFormula"
      @confirm="(latex: string) => handleFormulaConfirm(latex, emit)"
      @close="showFormulaModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, nextTick } from 'vue'
import FormulaModal from './FormulaModal.vue'
import { OIWButton } from '@guanghe-pub/onion-ui-web'
import { useContentEditor } from '@/hooks/useContentEditor'
// import katex from 'katex'
import UploadImg from './UploadImg.vue'
import DeleteIcon from '~icons/yc/delete'
import UploadImgIcon from '~icons/yc/upload-img'
import FxIcon from '~icons/yc/fx'

interface Props {
  modelValue: string  
  id: string
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  id: '',
})
const emit = defineEmits(['update:modelValue', 'validate', 'remove'])
const {
  editor,

  handleEditorInput,
  handlePaste,
  // currentFormulaElement,
  // savedRange,
  showFormulaModal,
  editingFormula,
  // formattedContent,
  handleImageUpload,
  openFormulaModal,
  handleFormulaConfirm,
} = useContentEditor(props.modelValue)

watch(
  () => props.id,
  (newVal) => {
    if (editor.value) {
      editor.value.textContent = props.modelValue
    }
  },
)
onMounted(() => {
  console.log('props', props)
  if (props.modelValue) {
    // 修改这里：不直接设置innerHTML，而是设置textContent
    if (editor.value) {
      editor.value.textContent = props.modelValue
    }
  }
})

// onMounted(() => nextTick(() => editor.value?.focus()))
</script>

<style lang="scss" scoped>
.n-form-item-blank--error > * > .content-editor {
  border: 1px solid #ff5e5e !important;
}

.preview-panel {
  padding: 15px;
  margin-top: 20px;
  background: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.preview-title {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.preview-content {
  font-family: monospace;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

.question-editor {
  position: relative;
  max-width: 800px;

  /* padding: 20px; */

  /* margin: 20px auto; */
}

.content-editor {
  width: 400px;
  height: 40px;
  padding: 0 15px;
  overflow-x: scroll;
  overflow-y: hidden;
  font-size: 14px;
  line-height: 40px;
  // text-overflow: ellipsis;
  word-wrap: break-word; /* 允许长单词换行 */
  white-space: nowrap; /* 保持换行和空格 */
  border: 1px solid #ccc;
  border-radius: 8px;

  &::-webkit-scrollbar {
    display: none;
  }

  /* 隐藏滚动条（Firefox） */
  scrollbar-width: none;

  /* 隐藏滚动条（IE/Edge） */
  -ms-overflow-style: none;

  &:focus {
    border: 1px solid #5381fe;
    outline: none;
  }
}

.formula-btn {
  /* position: absolute; */
  top: 0;
  right: -100px;
  padding: 8px 15px;
  color: white;
  cursor: pointer;
  background: #409eff;
  border: none;
  border-radius: 4px;
}

// ::v-deep(.formula-wrapper) {
//   display: inline-block;
//   padding: 2px 5px;
//   margin: 0 4px;
//   vertical-align: middle;
//   cursor: pointer;
//   background: #f0f4ff;
//   border-radius: 3px;
//   transition: background 0.2s;

//   &:hover {
//     background: #d6e4ff;
//   }
// }

.formula-display {
  display: inline-block;
  font-size: 1em;
}

.katex-error {
  color: #ff4d4f;
}
</style>
