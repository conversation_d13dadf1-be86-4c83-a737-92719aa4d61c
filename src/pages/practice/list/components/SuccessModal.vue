<template>
  <OIWModal
    v-model:show="modalShow"
    preset="dialog"
    :close-on-esc="false"
    :mask-closable="false"
    class="practice-success-dialog"
    @close="onClose"
  >
    <div v-if="!isShowMoreRoom" class="successBox">
      <div class="ic" />
      <div class="ti">布置完成</div>
      <div class="item-info">
        <span class="item-info-title">练习名称：</span>
        <span class="item-info-content">{{ homeworkItemData.name }}</span>
      </div>
      <div class="item-info">
        <span class="item-info-title">截止时间：</span>
        <span class="item-info-content">{{
          dayjs(homeworkItemData.expiredDate).format('YYYY.MM.DD HH:mm')
        }}</span>
      </div>
      <div class="item-info">
        <span class="item-info-title">练习内容：</span>
        <span class="item-info-content">
          <span class="text-link">{{ homeworkItemData.topicsNumber }}</span
          >{{ homeworkType === 'practice' ? '题' : '个知识点'
          }}<span
            v-if="
              homeworkItemData.payTopicsNumber && homeworkType !== 'practice'
            "
            >，包含</span
          ><span
            v-if="
              homeworkItemData.payTopicsNumber && homeworkType !== 'practice'
            "
            class="text-link"
            >{{ homeworkItemData.payTopicsNumber }}</span
          ><span
            v-if="
              homeworkItemData.payTopicsNumber && homeworkType !== 'practice'
            "
            >个付费知识点</span
          >
        </span>
      </div>
      <div class="item-info">
        <span class="item-info-title">布置班级：</span>
        <span class="item-info-content">{{ roomsName }}</span>
      </div>
      <OIWButton round class="notificationButton" @click="handelNotification">
        通知学生或家长
      </OIWButton>
      <div class="tip">点击复制通知内容发送到班级群内</div>
    </div>
    <div v-if="isShowMoreRoom" class="successBox">
      <div class="ic1" />
      <div class="ti">请选择需要通知的班级</div>
      <div class="classes">
        <div v-for="item in mockData" :key="item.id" class="cl">
          <span>{{ item.roomName }}</span>
          <span @click="handleCopyMessage(item)">复制通知内容</span>
        </div>
      </div>
      <div class="tip">关闭后可以选择对应的练习再次进行通知</div>
    </div>
  </OIWModal>
</template>

<script lang="ts" setup>
import { OIWModal, OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import dayjs from 'dayjs'
import { buryPoint } from '@/utils/buryPoint'
import { getDynamicHost } from '@/utils/apiUrl'
import type { CreateHomeworkResType } from '@/pages/practice/utils'
import { getShortLinkApi } from '@/pages/practice/list/service'
import copy from 'copy-to-clipboard'
import useTeacherHomeworkStore from '@/pages/practice/store'
import { getQueryString } from '@guanghe-pub/onion-utils'
import { useCvsEnum } from '@/hooks/useCvs'

const props = defineProps<{
  show: boolean
}>()

const store = useTeacherHomeworkStore()
const { SubjectEnum } = useCvsEnum()
const isShowMoreRoom = ref(false)

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    store.toggleSuccessModal(val)
  },
})

const onClose = () => {
  if (isShowMoreRoom.value) {
    buryPoint(
      'clickNNHTHPCTPHIPNRClose',
      { homeworkType: homeworkType.value },
      'Homework',
    )
    message.success('关闭后可以选择对应的练习再次进行通知')
  } else {
    buryPoint(
      'clickNNHTHPCTPHIPClose',
      { homeworkType: homeworkType.value },
      'Homework',
    )
  }
  modalShow.value = false
  isShowMoreRoom.value = false
}

const mockData = computed(() => {
  return store.createHomeworkRes
})
// 后续需要换成实际数据 store.createHomeworkRes
const homeworkItemData = computed(() => {
  return mockData.value[0]
})
const roomsName = computed(() => {
  return mockData.value
    .map((it) => {
      return it.roomName
    })
    .join('、')
})
const homeworkType = computed(() => {
  return homeworkItemData.value.source === 'practice'
    ? 'practice'
    : homeworkItemData.value.source === 'preview'
      ? 'synvideo'
      : 'syncreview'
})
// 点击通知学生或家长，如果多个班级打开多个班级的选择弹窗
const handelNotification = () => {
  buryPoint(
    'clickNNHTHPCTPHIPNotify',
    { homeworkType: homeworkType.value },
    'Homework',
  )
  if (mockData.value.length > 1) {
    isShowMoreRoom.value = true
    buryPoint(
      'popupNNHTHPCTPHIPNotifyRoom',
      { homeworkType: homeworkType.value },
      'Homework',
    )
  } else {
    copyMessage(mockData.value[0])
  }
}

const handleCopyMessage = async (homeworkItem: CreateHomeworkResType) => {
  copyMessage(homeworkItem)
}

const message = useOIWMessage()
// 复制通知内容
const copyMessage = async (homeworkItem: CreateHomeworkResType) => {
  try {
    buryPoint(
      'clickNNHTHPCTPHIPNRCopy',
      { homeworkType: homeworkType.value },
      'Homework',
    )

    // 从 homeworkItem 中解构需要的数据,避免传递整个对象
    const { type, creatorName, expiredTime, subjectId, source, id, roomName } =
      homeworkItem

    const downloadChannel =
      source === 'practice' ? 'TeacherPCpractice' : 'TeacherPCvideo'
    const joinClassSource =
      source === 'practice'
        ? 'pc同步练习通知'
        : source === 'review'
          ? 'pc同步复习通知'
          : 'pc微课练习通知'

    const expiredTimeStr = dayjs(expiredTime).format('YYYY-MM-DD HH:mm')
    const subjectName = SubjectEnum.value[subjectId]

    // 构建分享链接
    const host = getDynamicHost()
    const shareUrl = `${host}/school/activityH5/homeworkShare/${id}/${type}`
    const queryParams = new URLSearchParams({
      isshare: 'true',
      downloadChannel,
      joinClassSource,
      sharePlatform: 'Copy',
      pageFrom: 'PCHomeworkSuccess',
    })
    const url = `${shareUrl}?${queryParams.toString()}`

    const shortRes = await getShortLinkApi(url)

    if (shortRes?.urls?.short) {
      const copyValue = `${creatorName}老师在洋葱学园布置了一份【${subjectName}】练习\n布置班级：${roomName}\n截止时间：${expiredTimeStr}\n${shortRes.urls.short}`
      copy(copyValue)
      message.success('复制成功，可将通知发送到班级群')
    }
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请重试')
  }
}

const isEEData = getQueryString('pageFrom') === 'eeData'
watch(
  () => props.show,
  (val: boolean) => {
    if (val) {
      buryPoint(
        'popupNNHTHPCTPHIPSuccess',
        {
          courseId: homeworkItemData.value?.specialCourseId || '',
          pageSource: homeworkItemData.value.source || '',
          scene: homeworkItemData.value.scene || 'none',
          fromPageName: isEEData ? '2' : '1',
          suitId: mockData.value.map((i: CreateHomeworkResType) => i.id) || [],
          from: 'normal',
        },
        'course',
      )
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
.successBox {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ic {
  width: 200px;
  height: 158px;
  background: url('https://fp.yangcong345.com/onion-extension/111-ee43d71667d8715954cd02ce73e82541.png')
    no-repeat;
  background-size: 100%;
}

.ic1 {
  width: 200px;
  height: 158px;
  background: url('https://fp.yangcong345.com/onion-extension/222-0c6adc4aa9a093a87a7d71d10c92887b.png')
    no-repeat;
  background-size: 100%;
}

.ti {
  margin: 24px 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: #57526c;
  user-select: none;
}

.item-info {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 16px;

  .item-info-title {
    width: 70px;
    font-size: 14px;
    line-height: 20px;
    color: #393548;
    user-select: none;
  }

  .item-info-content {
    width: 224px;
    overflow: hidden;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #57526c;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
  }
}

.notificationButton {
  margin-top: 24px;
}

.tip {
  margin-top: 16px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #9792ac;
  user-select: none;
}

.classes {
  width: 100%;
  margin-top: 8px;
  margin-bottom: 8px;

  .cl {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    padding: 14px 16px;
    border-radius: 12px;

    &:hover {
      background: #f4f6ff;
    }

    span:nth-child(1) {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      color: #393548;
      user-select: none;
    }

    span:nth-child(2) {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #5e80ff;
      cursor: pointer;
      user-select: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>

<style lang="scss">
.practice-success-dialog {
  .n-dialog__content {
    margin-top: 8px !important;
    margin-bottom: 8px !important;
  }
}
</style>
