<template>
  <div class="practice-report-table">
    <div class="report-table-header">
      <div class="report-table-header-list">
        <div class="report-table-header-item-name">姓名</div>
        <div class="report-table-header-item-study">微课学习</div>
        <div v-if="pool?.length" class="report-table-header-item-detail">
          <span> 练习详情 </span>
          <n-popover trigger="hover">
            <template #trigger>
              <img
                class="report-table-popover-icon"
                src="https://fp.yangcong345.com/onion-extension/111-59d80521931c4d1e56fd00043f1ba523.png"
                alt="提示"
              />
            </template>
            <div class="report-table-popover-content">
              <div class="report-table-popover-item">
                <img
                  src="https://fp.yangcong345.com/onion-extension/222-2657b6de7cb3a7978c641e7b06b4acbb.png"
                  alt=""
                />
                该层无错通过
              </div>
              <div class="report-table-popover-item">
                <img
                  src="https://fp.yangcong345.com/onion-extension/111-bdc78c36945506b6ccfdf7edef47beae.png"
                  alt=""
                />
                该层有错通过
              </div>
            </div>
          </n-popover>
        </div>
      </div>
    </div>
    <div v-if="members.length > 0" class="report-table-body">
      <div
        v-for="member in members"
        :key="member.id"
        class="report-table-body-item"
      >
        <div class="report-table-body-item-name">
          <span class="report-table-body-item-name-text">{{
            member.name
          }}</span>
          <span
            :class="['report-table-body-item-name-tag', nameTagClass(member)]"
            >{{ nameTag(member) }}</span
          >
        </div>
        <div class="report-table-body-item-study">
          <TableProcess
            v-if="member.type !== 'notStudy'"
            :process="member.videoRecord"
          />
          <span v-else>未完成</span>
        </div>
        <div
          v-if="member.type !== 'notStudy' && pool?.length"
          class="report-table-body-item-detail"
        >
          <ul v-if="member.levelCorrects" class="detail-list">
            <li v-for="(item, index) in pool" :key="index" class="detail-item">
              <span
                :class="[
                  'detail-item-icon',
                  'detail-item-icon-' + (index + 1),
                  'detail-item-icon-type-' + item,
                  'detail-item-icon-state-' +
                    (member.levelCorrects[index] || 1),
                ]"
              ></span>
              <span class="detail-item-arrow" />
            </li>
          </ul>
          <div
            class="body-item-detail-arrow"
            @click="handleClickDetailArrow(member.id, member.name)"
          />
        </div>
      </div>
    </div>
    <div v-if="members.length <= 0" class="report-table-no-data">
      <div class="report-table-no-data-placeholder">
        <img
          src="https://fp.yangcong345.com/onion-extension/111-d4422038b60b352ad5ae5eb8920590ad.png"
          alt=""
        />
        <span>暂无学生</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TableProcess from '@/pages/practice/report/components/TableProcess.vue'
import type { Member } from '../service'

const props = defineProps<{
  pool: string[] | null
  masterMember: Member[]
  studyMember: Member[]
  notStudyMember: Member[]
}>()
const emits =
  defineEmits<(e: 'userDetail', userId: string, name: string) => void>()
const members = computed<Member[]>(() => {
  return [
    ...props.masterMember.map((member) => ({
      ...member,
      type: 'master',
    })),
    ...props.studyMember.map((member) => ({
      ...member,
      type: 'study',
    })),
    ...props.notStudyMember.map((member) => ({
      ...member,
      type: 'notStudy',
    })),
  ]
})

const nameTag = (member: Member) => {
  return member.type === 'master'
    ? member.isOnTime
      ? '已掌握'
      : '已掌握-逾期'
    : member.type === 'study'
      ? '未掌握'
      : '未完成'
}

const nameTagClass = (member: Member) => {
  return member.type === 'master'
    ? 'master-tag'
    : member.type === 'study'
      ? 'study-tag'
      : 'not-study-tag'
}

const handleClickDetailArrow = (userId: string, name: string) => {
  emits('userDetail', userId, name)
}
</script>

<style lang="scss" scoped>
.practice-report-table {
  .report-table-header {
    height: 56px;
    background: #f7f7f9;
    border-radius: 8px;

    .report-table-header-list {
      display: flex;
      align-items: center;
      height: 100%;

      .report-table-header-item-name {
        width: 232px;
        padding-left: 24px;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #393548;
        user-select: none;
      }

      .report-table-header-item-study {
        width: 321px;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #393548;
        user-select: none;
      }

      .report-table-header-item-detail {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #393548;
        user-select: none;

        .report-table-popover-icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }
  }

  .report-table-body {
    .report-table-body-item {
      display: flex;
      align-items: center;
      height: 56px;
      box-shadow: inset 0px -1px 0px 0px #dcd8e7;

      &:last-child {
        box-shadow: none;
      }

      .report-table-body-item-name {
        display: flex;
        gap: 8px;
        align-items: center;
        width: 232px;
        padding-left: 24px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #393548;

        .report-table-body-item-name-text {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .report-table-body-item-name-tag {
          position: relative;
          padding: 8px 8px 8px 24px;
          font-size: 12px;
          font-weight: 600;
          line-height: 12px;
          color: #3d3d3d;
          background: #f7f7f9;
          border-radius: 8px;

          &.master-tag {
            &::before {
              position: absolute;
              top: 8px;
              left: 8px;
              width: 12px;
              height: 12px;
              content: '';
              background: #4ecc5e;
              border-radius: 50%;
            }
          }

          &.study-tag {
            &::before {
              position: absolute;
              top: 8px;
              left: 8px;
              width: 12px;
              height: 12px;
              content: '';
              background: #fa5a65;
              border-radius: 50%;
            }
          }

          &.not-study-tag {
            &::before {
              position: absolute;
              top: 8px;
              left: 8px;
              width: 12px;
              height: 12px;
              content: '';
              background: #c5c1d4;
              border-radius: 50%;
            }
          }
        }
      }

      .report-table-body-item-study {
        width: 326px;
        font-size: 14px;
        font-weight: 600;
        color: #9792ac;
      }

      .report-table-body-item-detail {
        display: flex;
        align-items: center;
        width: 321px;

        .detail-list {
          display: flex;
          align-items: center;

          .detail-item {
            display: inline-flex;
            align-items: center;

            .detail-item-icon {
              width: 32px;
              height: 32px;

              &.detail-item-icon-1 {
                background: url('https://fp.yangcong345.com/onion-extension/111-0a6ee13307b6bcffa1b18e03c6958d88.png')
                  no-repeat center center;
                background-size: 100% 100%;

                &.detail-item-icon-state-1 {
                  background: url('https://fp.yangcong345.com/onion-extension/111-ea035cf3c970728f10a9e55e2ba9c8f9.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }

                &.detail-item-icon-state-2 {
                  background: url('https://fp.yangcong345.com/onion-extension/111-fedd02f636fe4d15590ff5ecf73156b4.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }
              }

              &.detail-item-icon-2 {
                background: url('https://fp.yangcong345.com/onion-extension/222-04993694da23111fbbfa8eacfa1e7457.png')
                  no-repeat center center;
                background-size: 100% 100%;

                &.detail-item-icon-state-1 {
                  background: url('https://fp.yangcong345.com/onion-extension/222-ecc100e7f87976da844d123ce5dc163d.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }

                &.detail-item-icon-state-2 {
                  background: url('https://fp.yangcong345.com/onion-extension/222-27995d9b47ad36db0f864c78362ffcd4.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }
              }

              &.detail-item-icon-3 {
                background: url('https://fp.yangcong345.com/onion-extension/333-3fcb2afe0df5dc50db4b8ae4f29eeb27.png')
                  no-repeat center center;
                background-size: 100% 100%;

                &.detail-item-icon-state-1 {
                  background: url('https://fp.yangcong345.com/onion-extension/333-57f631ea143aefce1342ffd673663b07.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }

                &.detail-item-icon-state-2 {
                  background: url('https://fp.yangcong345.com/onion-extension/333-44f93ee7f786e2f70cb7c0b6e1e7f1b9.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }
              }

              &.detail-item-icon-4 {
                background: url('https://fp.yangcong345.com/onion-extension/444-ec8b1d58f2252f1d53b76d5b574b021a.png')
                  no-repeat center center;
                background-size: 100% 100%;

                &.detail-item-icon-state-1 {
                  background: url('https://fp.yangcong345.com/onion-extension/444-33bf71bad7acfa4b2f718b4028c3575f.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }

                &.detail-item-icon-state-2 {
                  background: url('https://fp.yangcong345.com/onion-extension/444-fc5839f9e04b86bf5c93e765b2fa7365.png')
                    no-repeat center center;
                  background-size: 100% 100%;
                }

                &.detail-item-icon-type-goal {
                  background: url('https://fp.yangcong345.com/onion-extension/goal-555-11bef3de954a62b1bd3655c5e3b3d491.png')
                    no-repeat center center;
                  background-size: 100% 100%;

                  &.detail-item-icon-state-1 {
                    background: url('https://fp.yangcong345.com/onion-extension/goal-444-11cfc133b181938d44b6d9aa63c9c788.png')
                      no-repeat center center;
                    background-size: 100% 100%;
                  }

                  &.detail-item-icon-state-2 {
                    background: url('https://fp.yangcong345.com/onion-extension/goal-444-63c0caa0463fd21be7b3dcad8ec3f1d7.png')
                      no-repeat center center;
                    background-size: 100% 100%;
                  }
                }
              }

              &.detail-item-icon-5 {
                &.detail-item-icon-type-goal {
                  background: url('https://fp.yangcong345.com/onion-extension/goal-444-55d17d15f96380e57b93322b4a7963da.png')
                    no-repeat center center;
                  background-size: 100% 100%;

                  &.detail-item-icon-state-1 {
                    background: url('https://fp.yangcong345.com/onion-extension/goal-555-0ced31447eebd90b01efbc4932dc5d6c.png')
                      no-repeat center center;
                    background-size: 100% 100%;
                  }

                  &.detail-item-icon-state-2 {
                    background: url('https://fp.yangcong345.com/onion-extension/goal-555-26b0556902fd2cc9b7485476ddd7262f.png')
                      no-repeat center center;
                    background-size: 100% 100%;
                  }
                }
              }
            }

            .detail-item-arrow {
              width: 6px;
              height: 8px;
              margin-right: 12px;
              margin-left: 12px;
              background: url('https://fp.yangcong345.com/onion-extension/333-7eec986174f4b491b84d737ac6e61ebe.png')
                no-repeat center center;
              background-size: 100% 100%;
            }

            &:last-child {
              .detail-item-arrow {
                display: none;
              }
            }
          }
        }

        .body-item-detail-arrow {
          width: 16px;
          height: 16px;
          margin-left: 24px;
          cursor: pointer;
          background: url('https://fp.yangcong345.com/onion-extension/111-2b88c7bbca75217bea0f39441df40421.png')
            no-repeat center center;
          background-size: 100% 100%;

          &:hover {
            background: url('https://fp.yangcong345.com/onion-extension/222-c8f1ed86bc4cdf9aabaa130f20a3c81e.png')
              no-repeat center center;
            background-size: 100% 100%;
          }
        }
      }
    }
  }

  .report-table-no-data {
    text-align: center;

    .report-table-no-data-placeholder {
      padding: 48px 0;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      color: rgba(194, 194, 194, 1);
      text-align: center;
      user-select: none;

      img {
        display: block;
        width: 40px;
        height: 40px;
        margin: 0 auto;
        margin-bottom: 8px;
      }
    }
  }
}

.report-table-popover-content {
  padding-right: 12px;

  .report-table-popover-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #393548;

    img {
      width: 32px;
      height: 32px;
    }
  }
}
</style>
