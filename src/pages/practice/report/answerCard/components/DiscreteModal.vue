<template>
  <OIWModal
    v-model:show="modalShow"
    class="rounded-16px p-32px"
    closable
    :mask-closable="false"
    style="width: 752px; min-height: 324px"
  >
    <div class="modal-box">
      <div class="close-icon" @click="modalShow = false" />
      <div class="text-16px leading-24px font-600">
        题目的离散度是评估班级学生对同一题目理解差异性的关键指标
      </div>
      <div class="mt-12px">
        <OIWTable :columns="columns" :data="data" :bordered="false" />
      </div>
    </div>
  </OIWModal>
</template>

<script setup lang="ts">
import { OIWModal, OIWTable } from '@guanghe-pub/onion-ui-web'

const props = defineProps<{
  show: boolean
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

const columns = [
  {
    title: '离散度区间',
    key: 'index',
  },
  {
    title: '教学含义',
    key: 'desc',
  },
  {
    title: '干预建议',
    key: 'suggestion',
  },
]

const data = [
  {
    index: '低 <0.15',
    desc: '学生掌握均匀（优等生/薄弱生都会或都不会）',
    suggestion: '全班统一讲评',
  },
  {
    index: '中 0.15~0.25',
    desc: '中等差异（部分学生存在理解漏洞）',
    suggestion: '分组辅导',
  },
  {
    index: '高 >0.25',
    desc: '严重两极分化（教学或题目设计问题）',
    suggestion: '优先进行错因访谈+调整教学',
  },
]
</script>

<style lang="scss" scoped>
.modal-box {
  background-color: #ffffff;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/333-0bf9ccbdb2613616d5e53b93d4625262.png')
      no-repeat center;
    background-size: cover;
  }
}
</style>
