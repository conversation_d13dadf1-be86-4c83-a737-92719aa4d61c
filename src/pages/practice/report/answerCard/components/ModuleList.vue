<template>
  <div class="exam-type-list-box">
    <OIWTable :columns="columns" :data="examTypeStatistics" :bordered="false" />
  </div>
</template>

<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui'
import { OIWTable } from '@guanghe-pub/onion-ui-web'
import type { GetReportExamTypeRes } from '../service'
import { convertToChineseNumber } from '@/utils/formatNumber.ts'
import { NTooltip } from 'naive-ui'
import TipIcon from '~icons/yc/tip'
import { useStayTime } from '@/hooks/useStayTime'

defineProps<{
  examTypeStatistics: GetReportExamTypeRes[]
}>()

const columns: DataTableColumns<GetReportExamTypeRes> = [
  {
    title: '序号',
    key: 'orderNum',
    render: (row) => {
      return <span>{convertToChineseNumber(String(row.orderNum + 1))}</span>
    },
  },
  {
    title: '模块名称',
    key: 'name',
  },
  {
    title: '模块满分',
    key: 'score',
  },
  {
    title: () => {
      return (
        <div class="flex items-center">
          <NTooltip
            arrow-class="paper-preferences-arrow-popover"
            content-class="paper-preferences-content-popover"
            class="paper-preferences-wrapper-popover"
          >
            {{
              trigger: () => <TipIcon class="w-16px h-16px mr-4px" />,
              default: () => (
                <div class="text-14px font-600 color-#FFFFFF">
                  <div class="flex items-center justify-start">
                    <span class="w-12px h-12px rounded-50% mr-4px bg-#FA5A65 border-solid border-1px border-#ffffff" />
                    <span>[0% - 60%)</span>, <span>红色</span>
                  </div>
                  <div class="flex items-center justify-start">
                    <span class="w-12px h-12px rounded-50% mr-4px bg-#FEA345 border-solid border-1px border-#ffffff" />
                    <span>[60% - 80%)</span>, <span>橙色</span>
                  </div>
                  <div class="flex items-center justify-start">
                    <span class="w-12px h-12px rounded-50% mr-4px bg-#4ECC5E border-solid border-1px border-#ffffff" />
                    <span>[80% - 100%]</span>, <span>绿色</span>
                  </div>
                </div>
              ),
            }}
          </NTooltip>
          模块得分率
        </div>
      )
    },
    key: 'scoreRate',
    sorter: (row1, row2) => row1.scoreRate - row2.scoreRate,
    render: (row) => {
      return row.scoreRate !== undefined && row.scoreRate >= 0 ? (
        <div class="flex items-center justify-center bg-#F7F7F9 w-66px h-28px rounded-28px leading-28px text-12px  color-#393548">
          {row.scoreRate >= 0 && row.scoreRate < 60 && (
            <span class="w-12px h-12px rounded-50% mr-4px bg-#FA5A65" />
          )}
          {row.scoreRate >= 60 && row.scoreRate < 80 && (
            <span class="w-12px h-12px rounded-50% mr-4px bg-#FEA345" />
          )}
          {row.scoreRate >= 80 && (
            <span class="w-12px h-12px rounded-50% mr-4px bg-#4ECC5E" />
          )}
          {row.scoreRate}%
        </div>
      ) : (
        <div class="flex items-center justify-center  w-66px h-28px rounded-28px leading-28px text-12px  color-#393548">
          -
        </div>
      )
    },
  },
  {
    title: () => {
      return (
        <div class="flex items-center">
          <NTooltip
            arrow-class="paper-preferences-arrow-popover"
            content-class="paper-preferences-content-popover"
            class="paper-preferences-wrapper-popover"
          >
            {{
              trigger: () => <TipIcon class="w-16px h-16px mr-4px" />,
              default: () => (
                <div class="text-14px font-600 color-#FFFFFF">
                  {
                    '选取该模块中{(得分率 < 100) & (得分率 >= 0) & 最低的题目s}，'
                  }
                  <br />
                  {'无法挑选出高频错题时，显示「-」'}
                </div>
              ),
            }}
          </NTooltip>
          高频错题得分率
        </div>
      )
    },
    key: 'highFrequencyWrongProblem',
    sorter: (row1, row2) =>
      row1.highFrequencyWrongProblemScoreRate -
      row2.highFrequencyWrongProblemScoreRate,
  },
]
useStayTime('getAIClassASTaskReportTabStayDuration', 'course', {
  pageName: 'module',
})
</script>

<style lang="scss" scoped>
.exam-type-list-box {
  width: 100%;
  margin-top: 16px;
}
</style>
