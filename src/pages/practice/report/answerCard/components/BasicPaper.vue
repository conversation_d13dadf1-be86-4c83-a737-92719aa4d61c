<template>
  <div class="basic-paper-main">
    <div class="basic-paper-header">
      <span class="header-index">{{
        convertToChineseNumber(`${examIndex + 1}`) + '、'
      }}</span>
      <span class="header-title">{{ exam.name }}</span>
    </div>
    <div class="basic-paper-content">
      <div v-for="problem in exam.problems" :key="problem.id">
        <div class="basic-paper-content-item">
          <div class="content-item-index">
            {{ (problem.orderNum || 0) + 1 }}
          </div>
          <div class="content-item-status">
            {{
              problem.answer || problem.answers.length
                ? problem.isCorrect
                  ? '答对'
                  : '答错'
                : '未作答'
            }}
          </div>
          <div class="content-item-answer">
            <div
              v-if="problem.choiceCount && exam.examType === 'single_choice'"
              class="choice-box-answer"
            >
              <span
                v-for="item in problem.choiceCount"
                :key="item"
                class="choice-box-answer-item"
                :class="{
                  correct:
                    problem.correctAnswer?.toUpperCase() ===
                    numberToLetter(item).toUpperCase(),
                  isCorrect:
                    problem.answer?.toUpperCase() ===
                    numberToLetter(item).toUpperCase(),
                  isError:
                    problem.answer?.toUpperCase() ===
                      numberToLetter(item).toUpperCase() && !problem.isCorrect,
                }"
              >
                {{ numberToLetter(item) }}
              </span>
            </div>
            <div
              v-else-if="exam.examType === 'multi_choice'"
              class="choice-box-answer"
            >
              <span
                v-for="item in problem.choiceCount"
                :key="item"
                class="choice-box-answer-item"
                :class="{
                  correct: problem.correctAnswers?.some(
                    (answer) =>
                      answer.toUpperCase() ===
                      numberToLetter(item).toUpperCase(),
                  ),
                  isCorrect: problem.answers?.some(
                    (answer) =>
                      answer.toUpperCase() ===
                      numberToLetter(item).toUpperCase(),
                  ),
                  isError:
                    problem.answers?.some(
                      (answer) =>
                        answer.toUpperCase() ===
                        numberToLetter(item).toUpperCase(),
                    ) &&
                    !problem.correctAnswers?.some(
                      (correctAnswer) =>
                        correctAnswer.toUpperCase() ===
                        numberToLetter(item).toUpperCase(),
                    ),
                }"
              >
                {{ numberToLetter(item) }}
              </span>
            </div>
            <div
              v-else
              class="blank-box-answer"
              :class="[
                problem.answer ? (problem.isCorrect ? 'correct' : 'error') : '',
              ]"
            >
              <KatexRender :content="problem.answer || ''" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GetAnswerCardStudentExamTypes } from '../service'
import { convertToChineseNumber, numberToLetter } from '@/utils/formatNumber.ts'
import { KatexRender } from '@guanghe-pub/onion-problem-render'

defineProps<{
  exam: GetAnswerCardStudentExamTypes
  examIndex: number
}>()
</script>

<style lang="scss" scoped>
.basic-paper-main {
  .basic-paper-header {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    color: #393548;
  }

  .basic-paper-content {
    padding-top: 4px;

    .basic-paper-content-item {
      display: flex;
      align-items: center;
      margin-top: 16px;

      .content-item-index {
        width: 32px;
        font-family: MiSansHeavy;
        font-size: 14px;
        font-weight: 900;
        line-height: 20px;
        color: #393548;
      }

      .content-item-status {
        width: 58px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #393548;
      }

      .content-item-answer {
        width: calc(100% - 32px - 58px);

        .choice-box-answer {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          align-items: center;

          &-item {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            font-size: 20px;
            font-weight: 600;
            line-height: 24px;
            color: #393548;
            text-align: center;
            background: #f7f7f9;
            border: 1px solid #c5c1d4;
            border-radius: 50px;

            &.correct {
              color: #4ecc5e;
              border: 1px solid #4ecc5e;
            }

            &.isCorrect {
              color: #ffffff;
              background: #4ecc5e;
              border: 1px solid #4ecc5e;

              &::before {
                position: absolute;
                top: -2px;
                right: -2px;
                width: 16px;
                height: 16px;
                content: '';
                background: url('https://fp.yangcong345.com/onion-extension/111-f281507ba44f4ab4d0d1a61518238a6c.png')
                  no-repeat center center / 100% 100%;
              }
            }

            &.isError {
              color: #ffffff;
              background: #fa5a65;
              border: 1px solid #fa5a65;

              &::before {
                position: absolute;
                top: -2px;
                right: -2px;
                width: 16px;
                height: 16px;
                content: '';
                background: url('https://fp.yangcong345.com/onion-extension/222-73ee00b869c0934ccc8c3dbc09a2440c.png')
                  no-repeat center center / 100% 100%;
              }
            }
          }
        }

        .blank-box-answer {
          width: 100%;
          min-height: 40px;
          padding: 10px 16px;
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          color: #393548;
          background: #f7f7f9;
          border: 1px solid #c5c1d4;
          border-radius: 12px;

          &.correct {
            position: relative;
            background: #ffffff;
            border: 1px solid #4ecc5e;

            &::before {
              position: absolute;
              top: -5px;
              right: -5px;
              width: 16px;
              height: 16px;
              content: '';
              background: url('https://fp.yangcong345.com/onion-extension/111-f281507ba44f4ab4d0d1a61518238a6c.png')
                no-repeat center center;
              background-size: 100% 100%;
            }
          }

          &.error {
            position: relative;
            background: #ffffff;
            border: 1px solid #fa5a65;

            &::before {
              position: absolute;
              top: -5px;
              right: -5px;
              width: 16px;
              height: 16px;
              content: '';
              background: url('https://fp.yangcong345.com/onion-extension/222-73ee00b869c0934ccc8c3dbc09a2440c.png')
                no-repeat center center;
              background-size: 100% 100%;
            }
          }
        }
      }
    }
  }
}
</style>
