<template>
  <OIWModal
    v-model:show="modalShow"
    class="rounded-16px p-32px"
    closable
    :mask-closable="false"
    style="width: 490px; min-height: 412px"
  >
    <div class="modal-box">
      <div class="close-icon" @click="modalShow = false" />
      <n-scrollbar style="max-height: 348px">
        <ul v-if="examTypeStatistics?.length" class="overview-item-examType">
          <li
            v-for="(item, index) in examTypeStatistics"
            :key="index"
            class="overview-item-examType-item"
          >
            {{
              `【${convertToChineseNumber(`${item.orderNum + 1}`)}、${
                item.name
              }】${item.scoreRate}%`
            }}
          </li>
        </ul>
      </n-scrollbar>
    </div>
  </OIWModal>
</template>

<script setup lang="ts">
import { OIWModal } from '@guanghe-pub/onion-ui-web'
import type { GetReportExamTypeRes } from '../service'
import { convertToChineseNumber } from '@/utils/formatNumber.ts'

const props = defineProps<{
  show: boolean
  examTypeStatistics?: GetReportExamTypeRes[]
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})
</script>

<style lang="scss" scoped>
.modal-box {
  background-color: #ffffff;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/333-0bf9ccbdb2613616d5e53b93d4625262.png')
      no-repeat center;
    background-size: cover;
  }
}

.overview-item-examType {
  .overview-item-examType-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #57526c;
  }
}
</style>
