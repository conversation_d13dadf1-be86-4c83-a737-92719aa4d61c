<template>
  <NPopover
    v-model:show="userPopover"
    class="home-nav-item-title"
    trigger="click"
    :width="160"
    :show-arrow="false"
    placement="bottom-end"
    raw
    :to="false"
  >
    <template #trigger>
      <div class="user-drop">
        <NAvatar
          round
          :size="35"
          :src="avatar"
          fallback-src="https://cosplay.yangcong345.com/default_avatars/student_default_male.png"
        />
        <span class="user-name">
          {{ nickName }}
        </span>
        <div class="dropdown-arrow" />
      </div>
    </template>
    <div class="nav-sub-list">
      <div class="nav-sub" @click="onSelect('user-account')">个人中心</div>
      <div v-if="canUse" class="nav-sub" @click="onSelect('admin-backend')">
        管理员后台
      </div>
      <div class="nav-sub" @click="onSelect('download')">下载</div>
      <div class="nav-sub" @click="onSelect('contact-us')">联系我们</div>
      <div
        v-if="me?.type !== 'diandianyunxiao'"
        class="nav-sub logout"
        @click="onSelect('log-out')"
      >
        退出
      </div>
    </div>
  </NPopover>
</template>

<script lang="ts" setup>
import { useAuth } from '@/hooks/useAuth'
import { isDesktop } from '@/utils/wdesk'
import { bettwebAuthApi } from '@/service/auth'
import useAuthStore from '@/store/auth'
import { buryPoint } from '@/utils/buryPoint'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

const { avatar, nickName, me, userAuth, school } = useAuth()
const message = useOIWMessage()
const store = useAuthStore()
const router = useRouter()
const isRenMin = computed(() => {
  return window.location.href.indexOf('renminzhike') > 1
})
// 是否是确认教师或管理员
const canUse = computed(() => {
  return (
    (userAuth.value?.roles.includes('admin') ||
      userAuth.value?.roles.includes('tenantTeacher')) &&
    !isRenMin.value
  )
})
const userPopover = ref<boolean>(false)
const onSelect = async (key: string) => {
  userPopover.value = false
  if (key === 'user-account') {
    buryPoint('clickNavUIUserCenter', {}, 'course')
    router.push({
      name: 'UserCenterInfo',
    })
  }
  if (key === 'admin-backend') {
    const schoolId = school.value?._id
    if (!schoolId) {
      message.warning('未获取到学校信息, 不支持跳转')
      return
    }
    const res = await bettwebAuthApi(schoolId)
    if (!res) {
      message.warning('跳转获取参数失败，请检查网络重试')
      return
    }
    const { appId, code, webHost, ssoLoginPath } = res
    const redirectUrl = encodeURI(`${webHost}/#/`)
    const jumpURL = `${ssoLoginPath}?appId=${appId}&code=${code}&redirectUrl=${redirectUrl}`
    if (isDesktop) {
      window.open(jumpURL, '', 'width=1100, height=1000')
    } else {
      window.open(jumpURL, '_blank')
    }
  }
  if (key === 'download') {
    window.open('https://yangcongxueyuan.com/applications/', '_blank')
  }
  if (key === 'contact-us') {
    router.push({
      name: 'UserCenterContactUs',
    })
  }
  if (key === 'log-out') {
    store.loginOut()
  }
}
</script>

<style lang="scss" scoped>
.user-drop {
  display: flex;
  align-items: center;
  cursor: pointer;

  .user-name {
    margin-left: 15px;
    font-size: 16px;
    color: #8a869e;
  }

  .dropdown-arrow {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background: url('https://fp.yangcong345.com/onion-extension/dsd-a7cc3cbf076fc2dce0199efe294dc01b.png')
      no-repeat;
    background-size: contain;
    transition: all 0.3s ease;
  }
}
</style>
