<template>
  <n-modal
    v-model:show="modalShow"
    closable
    :mask-closable="false"
    :auto-focus="false"
    block-scroll
    :close-on-esc="false"
    class="password-change-modal"
  >
    <div class="password-change-content">
      <div class="close-icon" @click="closeModal" />
      <VerifyCellPhone
        v-if="steps === 'verify'"
        :recordPhone="recordPhone"
        :recordCountryCode="recordCountryCode"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
      <BindCellPhone
        v-if="steps === 'bind'"
        :passCaptchaRandom="passCaptchaRandom"
        :type="bindCellType"
        :oldPhone="recordPhone"
        :desc="desc"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
      <VerifyPassword
        v-if="steps === 'password'"
        :recordPhone="recordPhone"
        :passCaptchaRandom="passCaptchaRandom"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import { computed } from 'vue'
import VerifyCellPhone from './VerifyCellPhone.vue'
import BindCellPhone from './BindCellPhone.vue'
import VerifyPassword from './VerifyPassword.vue'
import useAuthStore from '@/store/auth'

const props = defineProps<{
  visible: boolean
  recordPhone: string | undefined
  recordCountryCode: string | undefined
}>()
const emits = defineEmits<{
  (e: 'update:visible', val: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

const message = useOIWMessage()
const authStore = useAuthStore()
const modalShow = computed({
  get: () => props.visible,
  set: (val) => emits('update:visible', val),
})
const bindCellType = ref<'first' | 'change'>('first')
const passCaptchaRandom = ref('')
const desc = ref('')
const steps = ref<'verify' | 'bind' | 'password'>('bind')

watch(
  () => props.visible,
  (val) => {
    if (val && props.recordPhone) {
      steps.value = 'verify'
      bindCellType.value = 'change'
      desc.value = ''
    } else {
      steps.value = 'bind'
      bindCellType.value = 'first'
      desc.value =
        '您还未绑定手机号，请先绑定后继续设置密码，绑定手机号之后我们能更好的帮助您保护账号安全'
    }
  },
)

function closeModal() {
  modalShow.value = false
  emits('cancel')
}

const handleCancel = () => {
  modalShow.value = false
  emits('cancel')
}
const handleConfirm = async (val?: string) => {
  if (steps.value === 'password') {
    message.success('设置密码成功')
    setTimeout(() => {
      emits('confirm')
    }, 2000)
  }
  if (steps.value === 'bind') {
    passCaptchaRandom.value = val || ''
    await authStore.getMe()
    steps.value = 'password'
  }
  if (steps.value === 'verify') {
    steps.value = 'password'
    passCaptchaRandom.value = val || ''
  }
}
</script>

<style lang="scss" scoped>
.password-change-modal {
  width: 490px;
  min-height: 272px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0px 3.2px 3.2px 0px rgba(80, 75, 100, 0.08);
}

.password-change-content {
  position: relative;
  padding: 32px;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/333-0bf9ccbdb2613616d5e53b93d4625262.png')
      no-repeat center;
    background-size: cover;
  }
}
</style>
