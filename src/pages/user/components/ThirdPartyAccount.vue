<template>
  <div class="third-party-container">
    <div class="third-party-header">账号绑定</div>
    <div class="third-party-content">
      <div
        v-for="(item, index) in thirdPartyList"
        :key="index"
        class="third-party-item"
      >
        <div class="content-icon">
          <img :src="item.icon" alt="icon" />
        </div>
        <div class="item-info">
          <div class="item-title">
            {{ item.title }}
            <span :class="{ 'item-type': true, 'bind-state': item.bind }">{{
              item.bind ? '已绑定' : '未绑定'
            }}</span>
          </div>
          <p class="item-desc">
            {{ item.desc }}
          </p>
        </div>
        <OIWButton
          :type="item.bind ? 'error' : 'info'"
          ghost
          size="small"
          @click="handleClickBtn(item)"
        >
          {{ item.bind ? '解除绑定' : '立即绑定' }}
        </OIWButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { QQUserInfoType } from '../service'
import {
  getQQBindPathApi,
  unbindQQApi,
  bindQQApi,
  isBindSeeWo,
  unbindSeeWo,
  bindSeeWoApi,
} from '../service'
import { OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import { nextFEDomain } from '@/utils/apiUrl'
import { useAuth } from '@/hooks/useAuth'
type StepsType = 'particulars' | 'thirdParty'
const emits = defineEmits<(e: 'changeTab', val: StepsType) => void>()

const { me } = useAuth()
const dialog = useDialog()
const message = useOIWMessage()
const route = useRoute()

const scene = route.query.scene

const thirdPartyList = ref<
  { icon: string; title: string; desc: string; bind: boolean; type: string }[]
>([
  {
    icon: 'https://fp.yangcong345.com/onion-extension/<EMAIL>',
    title: 'QQ账号',
    desc: '绑定后可以同时使用QQ账号登录',
    bind: me.value?.thirdPartyOauths.some((el) => el.type === 'qq') || false,
    type: 'qq',
  },
  {
    icon: 'https://fp.yangcong345.com/onion-extension/<EMAIL>',
    title: '希沃白板账号',
    desc: '绑定后可将备课中心课件同步至希沃云空间',
    bind: false,
    type: 'seewo',
  },
])

watch(
  () => window.localStorage.getItem('qqUserInfo'),
  (val) => {
    if (!val) {
      return
    }
    const qqUserInfo = JSON.parse(val) as QQUserInfoType
    const { unionId, openId, userInfo } = qqUserInfo
    const nickname = userInfo.nickname || ''
    if (!unionId || !openId) {
      return
    }
    bindQQApi(openId, unionId, nickname)
      .then(() => {
        thirdPartyList.value[0].bind = true
      })
      .catch((err: any) => {
        const msg = JSON.parse(
          err.response?.data?.msg ?? { message: '绑定失败' },
        ).message
        dialog.error({
          title: msg,
        })
      })
      .finally(() => {
        window.localStorage.removeItem('qqUserInfo')
      })
  },
  {
    immediate: true,
  },
)
watch(
  () => window.localStorage.getItem('seewoLoginCode'),
  (val) => {
    if (!val) {
      return
    }
    const qqUserInfo = JSON.parse(val) as {
      code: string
      state: string
      from: string
    }
    const { code, from } = qqUserInfo
    if (!code || !from) {
      return
    }
    dialog.info({
      title: '确认绑定',
      content: '是否确定绑定希沃账号？',
      negativeText: '取消',
      positiveText: '确认',
      positiveButtonProps: {
        type: 'primary',
      },
      onPositiveClick: () => {
        bindSeeWoApi(code, from)
          .then(() => {
            thirdPartyList.value[1].bind = true
            message.success('绑定成功！')
            if (scene === 'cloud-disk-bind-seewo') {
              window.close()
            }
          })
          .catch((err) => {
            const msg = JSON.parse(
              err.response?.data?.msg ?? { message: '绑定失败' },
            ).message
            message.success(msg)
          })
          .finally(() => {
            window.localStorage.removeItem('seewoLoginCode')
          })
      },
      onNegativeClick: () => {
        window.localStorage.removeItem('seewoLoginCode')
      },
    })
  },
  {
    immediate: true,
  },
)

const searchSeeWoBind = async () => {
  const res = await isBindSeeWo()
  if (res) {
    thirdPartyList.value[1].bind = res.isBind
  }
}
searchSeeWoBind()

const handleClickBtn = (item: {
  icon: string
  title: string
  desc: string
  bind: boolean
  type: string
}) => {
  if (item.type === 'qq') {
    if (item.bind) {
      onUnbindQQ()
    } else {
      routeBindQQ()
    }
  }
  if (item.type === 'seewo') {
    if (item.bind) {
      onUnbindSeeWo()
    } else {
      onBindSeeWo()
    }
  }
}

const routeBindQQ = () => {
  getQQBindPathApi().then((res) => {
    const newUrl = new URL(res)
    window.location.href = newUrl.href
  })
}

const onUnbindQQ = () => {
  dialog.info({
    title: '确认解绑',
    content: '确认解绑QQ吗？',
    negativeText: '取消',
    positiveText: '确认',
    positiveButtonProps: {
      type: 'primary',
    },
    onPositiveClick: () => {
      unbindQQApi()
        .then(() => {
          thirdPartyList.value[0].bind = false
        })
        .catch((err: any) => {
          const msg = JSON.parse(
            err.response?.data?.msg ?? { message: '解绑失败' },
          ).message
          dialog.info({
            title: '解绑失败',
            content: msg,
            positiveText: '我知道了',
            positiveButtonProps: {
              type: 'primary',
            },
          })
        })
    },
  })
}

const onBindSeeWo = () => {
  if (!me.value?.phone) {
    dialog.info({
      title: '绑定手机',
      content: '需要绑定手机后才能进行同步操作',
      negativeText: '取消',
      positiveText: '去绑定',
      positiveButtonProps: {
        type: 'primary',
      },
      onPositiveClick: () => {
        emits('changeTab', 'particulars')
      },
    })
  } else {
    window.location.href = `https://id.seewo.com/oauth2/login?app_id=2f4ddf31d0e44bce8352245cdd5cb78a&redirect_uri=${nextFEDomain}/seewoLoginCode&scope=user_base&state=SEEWO_USER_CENTER`
  }
}
const onUnbindSeeWo = () => {
  dialog.info({
    title: '确认解绑',
    content: '解除绑定后您将无法再同步课件至希沃，是否确认?',
    negativeText: '取消',
    positiveText: '确认',
    positiveButtonProps: {
      type: 'primary',
    },
    onPositiveClick: async () => {
      await unbindSeeWo()
      thirdPartyList.value[1].bind = false
    },
  })
}
</script>

<style lang="scss" scoped>
.third-party-container {
  width: 100%;
  padding-left: 24px;

  .third-party-header {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    color: #3d3d3d;
  }

  .third-party-content {
    margin-top: 16px;

    .third-party-item {
      display: flex;
      align-items: center;
      padding: 16px 0;

      &:not(:last-child) {
        box-shadow: inset 0px -1px 0px 0px #dcd8e7;
      }

      .content-icon {
        width: 40px;
        height: 40px;
        margin-right: 16px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .item-info {
        flex: 1;

        .item-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          color: #393548;

          .item-type {
            box-sizing: border-box;
            display: inline-block;
            padding: 3px 5px;
            margin-left: 8px;
            font-size: 12px;
            font-weight: 600;
            line-height: 12px;
            color: #fa5a65;
            border: 1px solid rgba(250, 90, 101, 0.6);
            border-radius: 4px;

            &.bind-state {
              color: #4ecc5e;
              border: 1px solid rgba(78, 204, 94, 0.6);
            }
          }
        }

        .item-desc {
          margin-top: 8px;
          font-size: 14px;
          font-weight: normal;
          line-height: 20px;
          color: #9792ac;
        }
      }

      .item-btn {
        box-sizing: border-box;
        padding: 5px 16px;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        color: #393548;
        background: transparent;
        border: 1px solid #c5c1d4;
        border-radius: 8px;

        &.btn-bind {
          color: #ffffff;
          background: #fa5a65;
          border: none;
        }
      }
    }
  }
}
</style>
