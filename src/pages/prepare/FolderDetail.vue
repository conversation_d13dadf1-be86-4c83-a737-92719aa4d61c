<template>
  <div class="prepare-folder-detail">
    <OIWLoading :show="prepareStore.loading" width="200px" />
    <div v-if="!prepareStore.loading">
      <div class="header-box">
        <FolderBreadcrumb :breadcrumbs="prepareStore.breadcrumbs" />
        <div
          v-if="!isEditState && folderId !== 'school-resources'"
          class="btn-group"
        >
          <OIWButton
            v-if="isShowTemplateManage"
            type="info"
            ghost
            class="mr-16px"
            @click="handleToTemplate"
            >模版管理</OIWButton
          >
          <OIWButton type="info" ghost class="mr-16px" @click="createFolder"
            >新建文件夹</OIWButton
          >
          <n-upload
            v-if="!uploadLoading"
            accept=".ppt, .pptx, .docx, .doc"
            :show-file-list="false"
            @change="handleUploadFinish"
          >
            <OIWButton type="info" class="mr-16px" :loading="uploadLoading"
              >上传文件</OIWButton
            >
          </n-upload>
          <OIWButton v-else type="info" class="mr-16px" :loading="uploadLoading"
            >上传文件</OIWButton
          >
          <NPopover
            trigger="hover"
            class="home-nav-item-title rounded-12px mt-8px"
            :delay="300"
            placement="bottom-start"
            raw
            :show-arrow="false"
          >
            <template #trigger>
              <OIWButton type="info" class="new-file" style="padding-left: 8px">
                新建文件
                <div class="dropdown-arrow" />
              </OIWButton>
            </template>
            <div class="nav-sub-list">
              <div class="nav-sub">
                <div class="flex items-center" @click="createCourseware('1')">
                  <PPTXIcon class="flex-none w-22px h-22px mr-2px" />
                  新建PPTX
                </div>
              </div>
              <div class="nav-sub">
                <div class="flex items-center" @click="createWord">
                  <WordIcon class="flex-none w-22px h-22px mr-2px" />
                  新建Word
                </div>
              </div>
            </div>
          </NPopover>
          <OIWButton
            v-if="isShowAIEntry"
            class="ml-16px"
            style="
              width: 125px;
              background: linear-gradient(
                138deg,
                #9582f4 0%,
                #6482ff 68%,
                #68c0f9 107%
              );
            "
            type="info"
            @click="handleClickAIEntry"
          >
            AI生成课件
            <template #icon>
              <AIIcon />
            </template>
          </OIWButton>
        </div>
        <div
          v-else-if="folderId !== 'school-resources'"
          class="btn-group-small"
        >
          <OIWButton ghost type="info" class="mr-16" @click="cancelEdit"
            >取消</OIWButton
          >
          <OIWButton ghost type="info" class="mr-16" @click="moveFiles"
            >移动</OIWButton
          >
          <OIWButton ghost type="info" class="mr-16" @click="syncSeeWo"
            >同步希沃</OIWButton
          >
          <OIWButton type="error" @click="deleteFiles">删除</OIWButton>
        </div>
      </div>
      <div
        v-if="folderId !== 'school-resources'"
        class="flex justify-between items-center mt-22px"
      >
        <n-popover
          :show="showSerchResult"
          placement="bottom"
          trigger="manual"
          :show-arrow="false"
          style="width: 282px"
          class="file-serch-result"
        >
          <template #trigger>
            <OIWInput
              :value="filterName"
              :maxlength="50"
              placeholder="输入文件名称搜索"
              clearable
              size="medium"
              class="serch-input"
              @update:value="onUpdateName"
              @focus="handleFocus"
              @blur="handleBlur"
            >
              <template #suffix>
                <SearchIcon />
              </template>
            </OIWInput>
          </template>
          <div>
            <div v-if="findFiles.length">
              <div
                v-for="(item, index) in findFiles"
                :key="index + 'findFiles'"
                class="pl-12px flex flex-row mb-12px items-center h-38pxh-38px hover:bg-#F4F6FF rounded-8px"
                @click="openFolder(item)"
              >
                <PPTXIcon v-if="item.type === 'PPT'" class="flex-none w-28px" />
                <WordIcon
                  v-if="item.type === 'DOCX'"
                  class="flex-none w-28px"
                />
                <FolderIcon
                  v-if="item.type === 'FOLDER'"
                  class="flex-none w-28px"
                />
                <div class="ml-8px">{{ item.name }}</div>
              </div>
            </div>
            <div
              v-else
              class="h-132px flex items-center mb-5 justify-center"
              style="flex-direction: column"
            >
              <img
                class="w-64px h-64px"
                src="https://fp.yangcong345.com/onion-extension/无数据-c5e1bda3d38128bdf56f5e6beaa0e043.png"
                alt=""
              />
              <div class="font-size-10px color-#DCD8E7">暂无资源</div>
            </div>
          </div>
        </n-popover>
        <div v-if="folderId !== 'school-resources'" class="button-group">
          <div
            v-for="i in sortByOptions"
            :key="i.value"
            class="button-item"
            :class="{ active: i.value === sortBy }"
            @click="onSortByChange(i.value)"
          >
            {{ i.label }}
          </div>
        </div>
      </div>
      <div v-else class="mt-22px">
        <!-- OIWinput无法正确导出ref，导致无法实现focus方法，所以这里使用了n-input -->
        <n-input
          ref="schoolResourceSearchInputRef"
          :value="searchValue"
          class="w-265px!"
          :maxlength="50"
          clearable
          size="medium"
          placeholder="输入搜索关键词搜索资源"
          @update:value="searchValueChange"
        >
          <template #suffix>
            <SearchIcon theme="outline" size="16" fill="#C5C1D4" />
          </template>
        </n-input>
        <div class="flex items-center mt-12px">
          <TipIcon class="w-24px h-24px mr-6px" />
          <div class="font-size-14px color-#393548 font-bold">
            注意：您在此处修改文件，会同步修改校本资源库的课件哦～
          </div>
        </div>
      </div>
      <div
        v-if="
          prepareStore.listData.length > 0 ||
          prepareStore.breadcrumbs.length === 0
        "
        class="list-content"
      >
        <div v-if="isSearch" class="pl-20px">
          <div class="search-title-box mb-24px">课件搜索结果</div>
        </div>
        <div class="flex items-center">
          <n-checkbox
            v-if="
              prepareStore.listData.length > 0 &&
              folderId !== 'school-resources'
            "
            size="large"
            :checked="isChecked"
            :indeterminate="isIndeterminate"
            :on-update:checked="handleChangeCheckAll"
            style="padding-left: 24px"
          >
            全选
          </n-checkbox>
          <NPopover
            trigger="click"
            class="home-nav-item-title rounded-12px mt-8px"
            :delay="300"
            placement="bottom-start"
            raw
            :show-arrow="false"
          >
            <template #trigger>
              <div
                class="flex-1 p-4px rounded-8px flex items-center text-14px color-#57526C ml-32px hover:bg-#F4F6FF cursor-pointer"
                :class="
                  sortType === 'NAME_ASC' || sortType === 'NAME_DESC'
                    ? 'color-#5E80FF'
                    : ''
                "
              >
                文件名
                <DropdownIcon
                  v-if="sortType !== 'NAME_ASC' && sortType !== 'NAME_DESC'"
                  class="ml-4px w-6px h-6px"
                  color="#57526C"
                />
                <ArrowIcon
                  v-if="sortType === 'NAME_ASC'"
                  class="flex-none w-16px h-16px mr-2px"
                  color="#5E80FF"
                />
                <ArrowIcon
                  v-if="sortType === 'NAME_DESC'"
                  class="flex-none w-16px h-16px mr-2px rotate-180"
                  color="#5E80FF"
                />
              </div>
            </template>
            <div class="nav-sub-list">
              <div class="nav-sub">
                <div
                  class="flex items-center"
                  @click="onSortTypeChange('NAME_ASC')"
                >
                  <ArrowIcon
                    class="flex-none w-16px h-16px mr-2px"
                    color="#393548"
                  />
                  文件名升序
                </div>
              </div>
              <div class="nav-sub">
                <div
                  class="flex items-center"
                  @click="onSortTypeChange('NAME_DESC')"
                >
                  <ArrowIcon
                    class="flex-none w-16px h-16px mr-2px rotate-180"
                    color="#393548"
                  />
                  文件名倒序
                </div>
              </div>
            </div>
          </NPopover>
          <NPopover
            trigger="click"
            class="home-nav-item-title rounded-12px mt-8px"
            :delay="300"
            placement="bottom-start"
            raw
            :show-arrow="false"
          >
            <template #trigger>
              <div
                class="mr-345px p-4px rounded-8px flex items-center text-14px color-#57526C hover:bg-#F4F6FF cursor-pointer"
                :class="
                  sortType === 'CREATED_AT_ASC' ||
                  sortType === 'CREATED_AT_DESC'
                    ? 'color-#5E80FF'
                    : ''
                "
              >
                创建时间
                <DropdownIcon
                  v-if="
                    sortType !== 'CREATED_AT_ASC' &&
                    sortType !== 'CREATED_AT_DESC'
                  "
                  class="ml-4px w-6px h-6px"
                  color="#57526C"
                />
                <ArrowIcon
                  v-if="sortType === 'CREATED_AT_ASC'"
                  class="flex-none w-16px h-16px mr-2px"
                  color="#5E80FF"
                />
                <ArrowIcon
                  v-if="sortType === 'CREATED_AT_DESC'"
                  class="flex-none w-16px h-16px mr-2px rotate-180"
                  color="#5E80FF"
                />
              </div>
            </template>
            <div class="nav-sub-list">
              <div class="nav-sub">
                <div
                  class="flex items-center"
                  @click="onSortTypeChange('CREATED_AT_ASC')"
                >
                  <ArrowIcon
                    class="flex-none w-16px h-16px mr-2px"
                    color="#393548"
                  />
                  创建时间升序
                </div>
              </div>
              <div class="nav-sub">
                <div
                  class="flex items-center"
                  @click="onSortTypeChange('CREATED_AT_DESC')"
                >
                  <ArrowIcon
                    class="flex-none w-16px h-16px mr-2px rotate-180"
                    color="#393548"
                  />
                  创建时间倒序
                </div>
              </div>
            </div>
          </NPopover>
        </div>

        <n-scrollbar style="height: calc(100vh - 280px)">
          <div
            v-if="
              prepareStore.breadcrumbs.length === 0 && isSchoolResourcesAuth
            "
            class="item-box"
            @click="
              openFolder({
                id: 'school-resources',
                name: '校本资源我上传的课件',
                type: 'FOLDER',
                fileId: 'school-resources',
                isChecked: false,
                isNameEditState: false,
                childFolderCount: 0,
                updatedAt: '',
                createdAt: '',
                seewo: {
                  isShow: false,
                  lastSyncTime: '',
                },
              })
            "
          >
            <div class="item-info">
              <div class="w-24px"></div>
              <div class="name-box">
                <FolderIcon class="flex-none w-32px" />
                <span>校本资源我上传的课件</span>
              </div>
            </div>
            <div class="flex-1 text-align-right pr-35px">
              <span
                class="font-size-14px color-#9792AC"
                style="white-space: nowrap"
                >--</span
              >
            </div>
            <div class="btn-box min-w-310px"></div>
          </div>

          <div
            v-for="(item, index) in prepareStore.listData"
            :key="index + '文件详情'"
            class="item-box"
            @drop="drop(item)"
            @dragover.prevent
          >
            <div class="item-info">
              <div
                v-if="folderId !== 'school-resources'"
                :class="isEditState ? 'item-check-show' : 'item-check'"
              >
                <n-checkbox
                  v-model:checked="item.isChecked"
                  size="large"
                  :disabled="item.isNameEditState"
                />
              </div>
              <div
                v-if="!item.isNameEditState"
                class="name-box"
                :draggable="folderId !== 'school-resources'"
                @click="openFolder(item)"
                @dragstart="dragstart(item)"
              >
                <PPTXIcon v-if="item.type === 'PPT'" class="flex-none w-32px" />
                <WordIcon
                  v-if="item.type === 'DOCX'"
                  class="flex-none w-32px"
                />
                <FolderIcon
                  v-if="item.type === 'FOLDER'"
                  class="flex-none w-32px"
                />
                <span v-if="item.type === 'FOLDER'">{{ item.name }}</span>
                <span v-if="item.type !== 'FOLDER'">{{
                  item.nameString || item.name
                }}</span>
                <n-popover
                  v-if="
                    item.seewo?.isShow &&
                    dayjs(item.updatedAt) < dayjs(item.seewo?.lastSyncTime)
                  "
                  trigger="hover"
                >
                  <template #trigger>
                    <SeeWoIcon class="ml-6px" style="flex-shrink: 0" />
                  </template>
                  <div>
                    <div
                      class="mt-4px mb-4px font-size-14px font-bold color-#393548"
                    >
                      已同步至希沃云空间
                    </div>
                    <div class="mt-4px mb-4px color-#57526C">
                      最后操作时间：{{
                        dayjs(item.seewo?.lastSyncTime).format('YYYY-MM-DD')
                      }}
                    </div>
                  </div>
                </n-popover>
              </div>
              <div v-else class="name-box">
                <PPTXIcon v-if="item.type === 'PPT'" class="flex-none w-32px" />
                <WordIcon
                  v-if="item.type === 'DOCX'"
                  class="flex-none w-32px"
                />
                <FolderIcon
                  v-if="item.type === 'FOLDER'"
                  class="flex-none w-32px"
                />
                <OIWInput
                  v-model:value="item.newName"
                  maxlength="50"
                  class="folder-name"
                  autofocus
                  @blur="handleRename(item)"
                />
              </div>
            </div>
            <div class="flex-1 text-align-right">
              <span
                class="font-size-14px color-#9792AC"
                style="white-space: nowrap"
                >{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm') }}</span
              >
            </div>
            <div class="btn-box min-w-310px">
              <OIWButton
                v-if="
                  !item.isSchoolResources &&
                  (item.type === 'PPT' || item.type === 'DOCX')
                "
                text
                type="info"
                @click="onShare(item)"
              >
                分享
              </OIWButton>
              <OIWButton
                v-if="item.isSchoolResources && item.type === 'PPT'"
                text
                type="info"
                @click="handleAddToMyPrepare(item.id)"
              >
                <span class="ml-2px">加入备课</span>
              </OIWButton>
              <OIWButton
                v-if="item.type === 'PPT'"
                text
                type="info"
                @click="playPPT(item)"
              >
                <PPTPlayIcon />
                <span class="ml-2px">授课</span>
              </OIWButton>
              <OIWButton
                v-if="item.type === 'PPT' || item.type === 'DOCX'"
                text
                type="info"
                @click="downloadPPT(item)"
                >下载</OIWButton
              >
              <OIWButton
                v-if="!item.isSchoolResources"
                text
                type="info"
                @click="moveSingleFile(item)"
                >移动</OIWButton
              >
              <FileActionDrop
                :file="item"
                @rename="changeNameEditState"
                @delete-file="deleteSingleFile"
                @copy-file="copyFile"
                @get-shot="getShot"
                @sync-seewo="syncSeeWoSingle"
                @sync-to-school-resource="syncToSchoolResource"
              />
            </div>
          </div>
          <div class="pagination-box-bottom">
            <div>
              <div
                v-if="isSeeWo"
                class="sync-seewo-btn"
                @click="syncProgressShow"
              >
                课件同步进度
              </div>
            </div>
            <div v-if="prepareStore.totalPage > 1" class="pagination-box">
              <n-pagination
                v-model:page="prepareStore.curPage"
                class="custom-pagination"
                :page-count="prepareStore.totalPage"
                :on-update:page="
                  (page) => prepareStore.changePage(page, folderId)
                "
              />
            </div>
          </div>
        </n-scrollbar>
      </div>
      <EmptyFolder
        v-else
        :is-school-resources="folderId === 'school-resources'"
        @create-courseware="createCourseware('2')"
        @create-word="createWord"
      />
      <MoveModal
        v-model:show="isShowMove"
        type="move"
        :files="needMoveFiles"
        :folderId="folderId"
      />
      <MoveModal
        v-model:show="isShowCopy"
        type="copy"
        :files="needCopyFiles"
        :folderId="folderId"
      />
      <SeeWoFilesModal
        v-model:show="isShowSeeWoFile"
        type="copy"
        :files="needSyncFiles"
        :folderId="folderId"
      />
      <ScreenShotModal
        :id="shotId"
        v-model:show="isShowShot"
        :name="shotFileName"
      />
      <CreatePPTModal
        v-model:show="isShowCreatePPT"
        :folderId="folderId"
        from="personalResource"
      />
      <CreateWordModal
        v-model:show="isShowCreateWord"
        :folderId="folderId"
        from="new"
      />
      <SyncProgressModal
        v-model:show="isShowSyncProgress"
        :fileIds="syncFileIds"
      />
      <SyncToSchoolResourceModal
        v-model:show="isShowSyncToSchoolResource"
        :resourceId="resourceId"
        modalTitle="同步课件至校本资源"
        subtitle="同步至校本资源的课件若需修改,请在文件夹「校本资源我上传的课件」内修改"
        @on-confirm="handleConfirmSyncToSchoolResource"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { debounce } from '@guanghe-pub/onion-utils'
import EmptyFolder from '@/pages/prepare/components/EmptyFolder.vue'
import FolderBreadcrumb from '@/pages/prepare/components/FolderBreadcrumb.vue'
import FileActionDrop from '@/pages/prepare/components/FileActionDrop.vue'
import MoveModal from '@/pages/prepare/components/MoveModal.vue'
import SyncToSchoolResourceModal from '@/pages/schoolResource/components/MoveModal.vue'
import ScreenShotModal from '@/pages/prepare/components/ScreenShotModal.vue'
import CreatePPTModal from '@/pages/prepare/components/CreatePPTModal.vue'
import CreateWordModal from '@/pages/prepare/components/CreateWordModal.vue'
import SyncProgressModal from '@/pages/prepare/components/SyncProgressModal.vue'
import SeeWoFilesModal from '@/pages/prepare/components/SeeWoFilesModal.vue'
import {
  OIWButton,
  OIWLoading,
  OIWInput,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import type {
  FindFiles,
  FileType,
  PrepareTreeItemTypeWithIsChecked,
  SortType,
} from './service'
import { getFullFileName } from '@/pages/prepare/utils'
import useAuthStore from '@/store/auth'
import {
  createFileApi,
  renameFileApi,
  deleteFilesApi,
  exportPPTApi,
  getFindFilesApi,
  uploadFilesApi,
  isBindSeeWo,
  syncToSchoolResourceApi,
} from './service'
import {
  downloadFileApi,
  renameSchoolResourceFileApi,
  deleteSchoolResourceFileApi,
  syncToMyFileApi,
} from '@/pages/schoolResource/schoolCourseware/service'
import { buryPoint } from '@/utils/buryPoint'
import { uploadToOss } from '@/utils/uploadUtils'
import usePrepareStore from '@/pages/prepare/prepareStore'
import PPTXIcon from '~icons/yc/PPTXIcon'
import WordIcon from '~icons/yc/WordIcon'
import AIIcon from '~icons/yc/ai'
import SeeWoIcon from '~icons/yc/seewo-logo'
import FolderIcon from '~icons/yc/folderIcon'
import TipIcon from '~icons/yc/tip-blue'
import PPTPlayIcon from '~icons/yc/pptPlayIcon'
import DropdownIcon from '~icons/yc/dropdown'
import ArrowIcon from '~icons/yc/arrow'
import SearchIcon from '~icons/yc/search'
import { useShare } from '@/components/ShareModal/useShare'
import type { UploadFileInfo } from 'naive-ui'
import { PCDomain } from '@/utils/apiUrl'
import dayjs from 'dayjs'
import type { ResourceParams } from '@/pages/schoolResource/schoolCourseware/types.ts'
import { downloadFile } from '@/utils/download'

const props = defineProps<{
  folderId: string | undefined
}>()

const filterName = ref('')
const findFiles = ref<FindFiles[]>([])
const sortBy = ref<'PPT' | 'DOCX' | 'ALL'>('ALL')
const sortType = ref<SortType>(
  (window.localStorage.getItem('sortType') as SortType) || 'DEFAULT',
)
const showSerchResult = ref(false)
const uploadLoading = ref(false)
const isShowSeeWoFile = ref(false)
const isShowSyncToSchoolResource = ref(false)
const resourceId = ref('')
const prepareStore = usePrepareStore()
const authStore = useAuthStore()
const { isShowAIEntry, isShowTemplateManage, isSchoolResourcesAuth } =
  storeToRefs(authStore)

watch(
  () => props.folderId,
  (val) => {
    prepareStore.getFolderDetail(true, val)
    sortBy.value = 'ALL'
  },
)
authStore.getTemplateManage()

const router = useRouter()
const dialog = useOIWDialog()
const message = useOIWMessage()
// 创建课件
const isShowCreatePPT = ref(false)
const isShowCreateWord = ref(false)
const searchValue = ref<string>('')
const schoolResourceSearchInputRef = ref<InstanceType<typeof OIWInput>>()

const isSearch = computed(() => {
  return !!searchValue.value
})

const handleConfirmSyncToSchoolResource = (data: ResourceParams) => {
  syncToSchoolResourceApi({
    id: resourceId.value,
    publisherId: data.publisherId,
    semesterId: data.semesterId,
    subjectId: data.subjectId,
    stageId: data.stageId,
    chapterId: data.chapterId,
    sectionId: data.sectionId,
    subsectionId: data.subSectionId,
  }).then(() => {
    message.success('课件同步成功')
    isShowSyncToSchoolResource.value = false
  })
}
const syncToSchoolResource = (file: PrepareTreeItemTypeWithIsChecked) => {
  resourceId.value = file.id as string
  isShowSyncToSchoolResource.value = true
}

const searchValueChange = async (value: string) => {
  searchValue.value = value
  await prepareStore.getFolderDetail(
    true,
    props.folderId,
    sortBy.value,
    searchValue.value,
  )
  schoolResourceSearchInputRef.value?.focus()
}

const sortByOptions = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '课件',
    value: 'PPT',
  },
  {
    label: '文档',
    value: 'DOCX',
  },
]

const isShowSyncProgress = ref(false)
const isSeeWo = ref(false)
const syncFileIds = ref<string[]>([])

const createCourseware = (button: '1' | '2') => {
  buryPoint('click_prepareCenter_courseware', { button })
  isShowCreatePPT.value = true
  buryPoint('click_prepareCenter', { button: 'courseware' })
}

const onSortByChange = (value: any) => {
  sortBy.value = value
  prepareStore.getFolderDetail(true, props.folderId, sortBy.value)
}

const onSortTypeChange = (value: SortType) => {
  sortType.value = value
  window.localStorage.setItem('sortType', sortType.value)
  prepareStore.getFolderDetail(true, props.folderId, sortBy.value)
}

const createWord = () => {
  isShowCreateWord.value = true
}

const fileUploadError = () => {
  message.error('Sorry，文件上传失败了，请重试')
}
const handleToTemplate = () => {
  router.push({
    name: 'TemplateManage',
  })
}
// 上传成功的回调方法
const fileUploadEnd = async (
  res: { data: { url: string } },
  file: { originFileName: string; url: string; type: string },
) => {
  const { originFileName, url, type } = file
  let fileType: FileType = 'UNKNOWN'
  const pptMimeType = 'application/vnd.ms-powerpoint'
  const pptxMimeType =
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  const docMimeType = 'application/msword'
  const docxMimeType =
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

  if (type === pptxMimeType || type === pptMimeType) {
    fileType = 'PPT'
  } else if (type === docxMimeType || type === docMimeType) {
    fileType = 'DOCX'
  } else {
    fileType = 'UNKNOWN'
  }
  buryPoint('click_courseUpload', { typeName: fileType === 'PPT' ? '1' : '2' })

  uploadFilesApi({
    fileName: originFileName,
    url,
    folderId: props.folderId as string,
    fileType,
  })
    .then(() => {
      prepareStore.initData(true, props.folderId, sortType.value)
      uploadLoading.value = false
      message.success('上传成功')
    })
    .catch(() => {
      message.success('上传失败，请重试')
      uploadLoading.value = false
    })
}

const handleUploadFinish = async (options: {
  file: UploadFileInfo
  fileList: UploadFileInfo[]
  event?: Event
}) => {
  if (uploadLoading.value) return
  const { file, fileList } = options
  const fileSize = file.file!.size
  console.log('fileSize', fileSize)
  if (fileSize / 1024 / 1024 > 100) {
    message.error('文件大小不能超过100M')
    return
  }
  uploadLoading.value = true
  message.info('课件正在上传中，请勿关闭当前页面')
  await uploadToOss([file.file as File], fileUploadEnd, fileUploadError)
  console.log('Upload finished', file, fileList)
}
// 授课PPT
const playPPT = (item: PrepareTreeItemTypeWithIsChecked) => {
  buryPoint('click_prepareCenter_teaching_play', { courseId: item.fileId })
  window.open(
    `/teacher-workbench/slide/edit?fileId=${item.fileId}&id=${
      item.id
    }&isPlay=true&fileName=${item.nameString}&fromPage=personal${
      item.isSchoolResources ? '&platform=schoolResource' : ''
    }`,
    '_blank',
  )
  buryPoint('click_prepareCenter_CW', {
    button: 'teaching',
    courseId: item.fileId,
  })
}
const searchFetch = async () => {
  if (filterName.value === '') {
    findFiles.value = []
    return
  }
  const params = {
    fileName: filterName.value,
    page: 1,
    pageSize: 50,
  }
  const res = await getFindFilesApi(params)
  findFiles.value = res.items
}
const debounceFetchData = debounce(searchFetch, 300)

const onUpdateName = (val: string) => {
  filterName.value = val
  if (val === '') {
    showSerchResult.value = false
    findFiles.value = []
    return
  }
  if (!showSerchResult.value) {
    showSerchResult.value = true
  }
  debounceFetchData()
}

const handleFocus = () => {
  if (filterName.value === '') return
  showSerchResult.value = true
}

const handleBlur = () => {
  showSerchResult.value = false
}

// 导出PPT
const downloadPPT = async (item: PrepareTreeItemTypeWithIsChecked) => {
  buryPoint('click_prepareCenter_export', { courseId: item.fileId })
  buryPoint('click_prepareCenter_CW', {
    button: 'export',
    courseId: item.fileId,
  })
  let fileName = item.nameString || item.name
  if (
    !fileName.endsWith('.pptx') &&
    !fileName.endsWith('.ppt') &&
    !fileName.endsWith('.docx') &&
    !fileName.endsWith('.doc')
  ) {
    fileName += item.type === 'PPT' ? '.pptx' : '.docx'
  }
  if (item.isSchoolResources) {
    downloadFileApi({ id: item.id })
      .then((res) => {
        if (res && res.url) {
          // 使用公共下载方法
          downloadFile(res.url, fileName)
        }
      })
      .catch(() => {
        message.error('下载失败')
      })
  } else {
    const res = await exportPPTApi({ fileId: item.id })
    if (res && res.url) {
      // 使用公共下载方法
      downloadFile(res.url, fileName)
    }
  }
}
// 创建文件夹, 且进入名字编辑状态
const createFolder = async () => {
  const body = {
    fileName: '新建文件夹',
    fileType: 'FOLDER',
    folderId: props.folderId,
  }
  await createFileApi(body)
  await prepareStore.initData(true, props.folderId)
  changeNameEditState(prepareStore.listData[0])
  buryPoint('click_prepareCenter', { button: 'folder' })
}
// 打开文件夹/课件
const openFolder = (item: PrepareTreeItemTypeWithIsChecked) => {
  onUpdateName('') // 重置搜索
  if (item.type === 'FOLDER') {
    router.replace({
      name: 'prepareCenterList',
      query: {
        folderId: item.id as string,
      },
    })
    // 触发左侧文件夹树的变化
    prepareStore.updateLeftTreeData(item.id as string)
  }
  if (item.type === 'PPT') {
    window.open(
      `/teacher-workbench/slide/edit?fileId=${item.fileId}&id=${
        item.id
      }&fileName=${item.nameString || item.name}${
        item.isSchoolResources ? '&platform=schoolResource' : ''
      }`,
      '_blank',
    )
  }
  if (item.type === 'DOCX') {
    window.open(
      `/teacher-workbench/word/edit?fileId=${item.fileId}&id=${
        item.id
      }&fileName=${item.nameString || item.name}`,
      '_blank',
    )
  }
}

// 计算当前选中的列表
const checkedList = computed(() => {
  return prepareStore.listData.filter((i) => {
    return i.isChecked
  })
})

// 是否进入批量操作的编辑模式
const isEditState = computed(() => {
  return checkedList.value.length > 0
})

// 校验当前是否在批量编辑模式，如果在批量编辑模式下，则不允许进行文件的单项操作
const checkEditState = (item: PrepareTreeItemTypeWithIsChecked) => {
  if (item.isChecked) {
    message.warning(
      '当前正处于批量操作模式，如果需要操作单个文件请先取消选中状态',
    )
    return false
  }
  return true
}
// 全选按钮逻辑
const handleChangeCheckAll = (val: boolean) => {
  prepareStore.listData.forEach((i) => {
    i.isChecked = val
  })
}
const isChecked = computed(() => {
  return checkedList.value.length === prepareStore.listData?.length
})

// 部分全选逻辑
const isIndeterminate = computed(() => {
  return (
    checkedList.value.length > 0 &&
    checkedList.value.length < prepareStore.listData?.length
  )
})

const handleClickAIEntry = () => {
  buryPoint('click_prepareCenter', { button: 'ai' })
  const fullPath = router.resolve({
    name: 'createAIPPT',
  })
  window.open(fullPath.href, '_blank')
}

// 批量操作--取消
const cancelEdit = () => {
  prepareStore.listData.forEach((i) => {
    i.isChecked = false
  })
}

const isShowCopy = ref(false)
const needCopyFiles = ref<PrepareTreeItemTypeWithIsChecked[]>([])

// 复制-单个文件
const copyFile = (item: PrepareTreeItemTypeWithIsChecked) => {
  const checkRes = checkEditState(item)
  if (!checkRes) {
    return
  }
  if (item) {
    needCopyFiles.value = [item]
    isShowCopy.value = true
  } else {
    message.warning('未获取到需要复制的资源')
  }
}

const isShowShot = ref(false)
const shotId = ref('')
const shotFileName = ref('')
const getShot = (item: PrepareTreeItemTypeWithIsChecked) => {
  shotId.value = item.id
  shotFileName.value = item.nameString || item.name
  isShowShot.value = true
}

const isShowMove = ref(false)
const needMoveFiles = ref<PrepareTreeItemTypeWithIsChecked[]>([])
// 移动-单个文件
const moveSingleFile = (item: PrepareTreeItemTypeWithIsChecked) => {
  const checkRes = checkEditState(item)
  if (!checkRes) {
    return
  }
  if (item) {
    needMoveFiles.value = [item]
    isShowMove.value = true
  } else {
    message.warning('未获取到需要移动的资源')
  }
  buryPoint('click_prepareCenter_CW', { button: 'move', courseId: item.fileId })
  buryPoint('click_prepareCenter_CW_move_pop', { courseId: item.id })
}
// 移动-批量操作
const moveFiles = () => {
  if (!checkedList.value.length) {
    message.warning('未获取到需要移动的资源')
    return
  }
  needMoveFiles.value = checkedList.value
  isShowMove.value = true
}
// 批量删除
const deleteFiles = () => {
  if (!checkedList.value.length) {
    return
  }
  const ids = checkedList.value.map((i) => i.id)
  deleteFilesAction(ids)
}

const needSyncFiles = ref<PrepareTreeItemTypeWithIsChecked[]>([])

const checkeBindAndContinue = (pptList: PrepareTreeItemTypeWithIsChecked[]) => {
  isBindSeeWo().then((res) => {
    if (!res.isBind) {
      dialog.create({
        showIcon: false,
        title: `提示`,
        content: `绑定希沃账号后即可将课件同步至希沃云空间`,
        positiveText: '去绑定',
        negativeText: '取消',
        onPositiveClick: () => {
          window.open(
            `https://id.seewo.com/oauth2/login?app_id=2f4ddf31d0e44bce8352245cdd5cb78a&redirect_uri=${PCDomain}/seewoLoginCode&scope=user_base&state=SEEWO_CLOUD_DISK`,
            '_blank',
          )
        },
      })
      return
    }
    needSyncFiles.value = pptList
    isShowSeeWoFile.value = true
  })
}

// 同步希沃
const syncSeeWo = () => {
  const unsupportedFile = checkedList.value.find((i) => i.type !== 'PPT')
  const withoutPPT =
    checkedList.value.filter((i) => i.type === 'PPT').length === 0
  buryPoint('click_xiwoSync_button3')

  if (unsupportedFile) {
    if (withoutPPT) {
      dialog.create({
        showIcon: false,
        title: `提示`,
        content: `仅支持将课件同步至希沃云空间，此次操作将不会同步文件夹中的资源及非课件的资源`,
        positiveText: '知道了',
        positiveButtonProps: {
          type: 'error',
        },
      })
      return
    }
    dialog.create({
      showIcon: false,
      title: `提示`,
      content: `仅支持将课件同步至希沃云空间，此次操作将不会同步文件夹中的资源及非课件的资源`,
      negativeText: '知道了',
      positiveText: '继续',
      onPositiveClick: () => {
        checkeBindAndContinue(checkedList.value.filter((i) => i.type === 'PPT'))
      },
    })
    return
  }
  checkeBindAndContinue(checkedList.value)
}

// 同步希沃 - 单个
const syncSeeWoSingle = (item: PrepareTreeItemTypeWithIsChecked) => {
  buryPoint('click_xiwoSync_button2')
  checkeBindAndContinue([item])
}

// 单个文件删除
const deleteSingleFile = (item: PrepareTreeItemTypeWithIsChecked) => {
  const checkRes = checkEditState(item)
  if (!checkRes) {
    return
  }
  const ids = [item.id]
  deleteFilesAction(ids, item)
}
// 删除方法
const deleteFilesAction = (
  ids: string[],
  item?: PrepareTreeItemTypeWithIsChecked,
) => {
  let titleText = '文件夹/资源'
  if (item) {
    titleText = item.type === 'FOLDER' ? '文件夹' : '资源'
    buryPoint('click_prepareCenter_CW_more_pop', {
      button: 'delete',
      courseId: item.id,
    })
  }
  dialog.create({
    showIcon: false,
    title: `确认删除所选${titleText}？`,
    content: `将要删除所选${titleText}，不可恢复`,
    positiveButtonProps: {
      type: 'error',
    },
    onPositiveClick: () => {
      if (item?.isSchoolResources) {
        deleteSchoolResourceFileApi({ ids })
          .then(async () => {
            message.success('删除成功！')
            await prepareStore.initData(true, props.folderId)
          })
          .catch(() => {
            message.error('删除失败！')
          })
      } else {
        deleteFilesApi({ fileIds: ids })
          .then(async () => {
            message.success('删除成功！')
            await prepareStore.initData(true, props.folderId)
          })
          .catch(() => {
            message.error('删除失败！')
          })
      }
    },
    onClose: () => {
      message.info('取消操作')
    },
    positiveText: '删除',
    negativeText: '取消',
  })
}
// 重命名逻辑
const changeNameEditState = (item: PrepareTreeItemTypeWithIsChecked) => {
  buryPoint('click_prepareCenter_CW_more_pop', {
    button: 'rename',
    courseId: item.id,
  })
  const checkRes = checkEditState(item)
  if (!checkRes) {
    return
  }
  item.isNameEditState = true
  item.newName = item.type === 'FOLDER' ? item.name : item?.nameString
  nextTick(() => {
    setTimeout(() => {
      const wrapperElement = document.querySelector('.folder-name')
      if (wrapperElement) {
        const inputElement = wrapperElement.querySelector('input')
        if (inputElement) {
          inputElement.focus()
        }
      }
    }, 0)
  })
}
// input失焦事件触发更新名称
const handleRename = (item: PrepareTreeItemTypeWithIsChecked) => {
  const oldName = item.type === 'FOLDER' ? item.name : item.nameString
  if (!item.newName || item.newName === oldName) {
    item.isNameEditState = false
    return
  }
  const newNameString =
    item.type === 'FOLDER'
      ? item.newName
      : getFullFileName(item.newName, item.typeString)
  if (!newNameString) {
    return
  }
  if (item.isSchoolResources) {
    renameSchoolResourceFileApi({ id: item.id, name: newNameString })
      .then(async () => {
        message.success('名称更新成功！')
        await prepareStore.initData(true, props.folderId)
      })
      .catch(() => {
        message.error('名称更新失败！')
      })
  } else {
    renameFileApi({ fileId: item.id, name: newNameString })
      .then(async () => {
        message.success('名称更新成功！')
        await prepareStore.initData(true, props.folderId)
      })
      .catch(() => {
        message.error('名称更新失败！')
      })
  }
}
// 拖拽
const dragstart = (item: PrepareTreeItemTypeWithIsChecked) => {
  const newDragContent = [
    {
      id: item.id,
      name: item.name,
      type: item.type,
      childFolderCount: item.childFolderCount,
      fileId: item.fileId,
    },
  ]
  // console.log('拖拽开始：', newDragContent)
  prepareStore.$patch((state) => {
    state.dragContent = newDragContent
  })
}
// 拖拽移入，需要校验
const drop = (item: PrepareTreeItemTypeWithIsChecked) => {
  if (!item) {
    message.warning('未获取到拖拽的目标地址')
    return
  }
  if (item.type !== 'FOLDER') {
    message.warning('当前资源类型不支持其他文件夹/资源进行移入操作')
    return
  }
  const firstData = prepareStore.dragContent[0]
  // 从左树移入右侧详情的文件夹 校验目标元素是否是当前拖拽元素的子集：通过当前详情文件夹的面包屑ID数组校验
  if (firstData.type === 'FOLDER') {
    const breadcrumbsIds = prepareStore.breadcrumbs.map((i) => i.id)
    if (breadcrumbsIds.includes(firstData.id)) {
      message.warning('不能将文件夹移入到自己的子级文件夹内！')
      return
    }
    if (firstData.id === item.id) {
      message.warning('不能将当前文件夹移入到自己！')
      return
    }
  }
  const newDragContent = [
    firstData,
    {
      id: item.id,
      name: item.name,
      type: item.type,
      childFolderCount: item.childFolderCount,
      fileId: item.fileId,
    },
  ]
  // console.log('拖拽结束：', newDragContent)
  prepareStore.$patch((state) => {
    state.dragContent = newDragContent
    state.isShowDragConfirm = true
  })
}

const { createShare } = useShare()
const onShare = (item: PrepareTreeItemTypeWithIsChecked) => {
  let resourceType: 'ppt' | 'word' = 'ppt'
  switch (item.type) {
    case 'PPT':
      resourceType = 'ppt'
      break
    case 'PPTX':
      resourceType = 'ppt'
      break
    case 'DOCX':
      resourceType = 'word'
      break
    case 'DOC':
      resourceType = 'word'
      break
  }
  createShare({
    resourceName: item.name,
    resourceType,
    resourceId: item.fileId,
    from: 'prepareCenterList',
  })
  buryPoint('click_prepareCenter_CW', {
    button: 'share',
    courseId: item.fileId,
  })
}
const syncProgressShow = () => {
  isShowSyncProgress.value = true
}

const getBindSeeWo = async () => {
  const res = await isBindSeeWo()
  if (res) {
    isSeeWo.value = res.isBind
  }
}
getBindSeeWo()

// 校本资源的课件点击加入备课
const handleAddToMyPrepare = debounce((id: string) => {
  syncToMyFileApi(id)
    .then(() => {
      message.success('添加成功')
    })
    .catch(() => {
      message.error('添加失败')
    })
}, 1000)
</script>

<style>
.file-serch-result {
  min-height: 132px;
  max-height: 428px;
  overflow-y: scroll;
  border: 1px solid #c5c1d4;
  border-radius: 10px !important;
  box-shadow: 0px 3.2px 3.2px 0px rgba(151, 151, 151, 0.1);
}
</style>

<style lang="scss" scoped>
::v-deep(.n-input) {
  font-size: 14px;
  border-radius: 12px 12px 12px 12px;
  box-shadow: none;

  .n-input-wrapper {
    padding-right: 16px;
    padding-left: 16px;
  }

  .n-input__placeholder {
    color: #c5c1d4;
  }

  .n-input-word-count {
    font-size: 14px;
    color: #c5c1d4;
  }

  .n-input__input-el {
    height: 40px;
  }

  .n-input__border {
    border: 1px solid #d4d1dd;
    border-radius: 12px 12px 12px 12px;
  }
}

.button-group {
  display: flex;

  align-items: center;
  height: 40px;
  padding: 3px 4px;

  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.new-file {
  .dropdown-arrow {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background: url('https://fp.yangcong345.com/onion-extension/xsz-170083cfa129f8c1924ad3ad644b8b86.png')
      no-repeat;
    background-size: contain;
    transition: all 0.3s ease;
  }

  &:hover {
    .dropdown-arrow {
      width: 16px;
      height: 16px;
      background: url('https://fp.yangcong345.com/onion-extension/xsz-170083cfa129f8c1924ad3ad644b8b86.png')
        no-repeat;
      background-size: contain;
      transition: all 0.3s ease;
      transform: rotate(180deg);
    }
  }
}

.prepare-folder-detail {
  position: relative;
  width: 100%;
  height: calc(100vh - 88px);
}

.serch-input {
  width: 282px;
  // margin-top: 22px;
}

.loading-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.header-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.btn-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

  ::v-deep(.oiw-button--box.n-button--medium-type) {
    width: 104px;
    min-width: 96px;
    height: 40px;
    padding: 0;
    font-size: 14px;
  }
}

.btn-group-small {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

  ::v-deep(.oiw-button--box.n-button--medium-type) {
    width: 96px;
    min-width: 96px;
    height: 40px;
    padding: 0;
  }
}

.list-content {
  margin-top: 32px;

  .search-title-box {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    color: #393548;

    &.mb-24px {
      margin-bottom: 24px;
    }

    .grey-text {
      font-size: 14px;
      font-weight: 400;
      color: #9792ac;
    }
  }

  ::v-deep(.n-checkbox-box-wrapper) {
    // width: 24px;
    margin-right: 2px;
    // font-size: 18px;

    .n-checkbox-box {
      width: 16px;
      height: 16px;
      border-radius: 5px;
    }
  }

  ::v-deep(.n-checkbox__label) {
    height: 24px;
    padding: 0 6px;
    font-size: 14px;
    line-height: 24px;
  }
}

.pagination-box-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;

  .pagination-box {
    float: right;
    padding: 12px 0;
  }

  .sync-seewo-btn {
    position: relative;
    display: flex;
    padding-left: 20px;
    margin: 18px 0 18px 10px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #57526c;
    cursor: pointer;

    &::after {
      position: absolute;
      top: 1px;
      left: 0;
      width: 16px;
      height: 16px;
      content: '';
      background: url('https://fp.yangcong345.com/onion-extension/222-83be7872791f208cbb54383b92d574df.png')
        no-repeat;
      background-size: cover;
    }

    &:hover {
      color: #5e80ff;

      &::after {
        position: absolute;
        top: 1px;
        left: 0;
        width: 16px;
        height: 16px;
        content: '';
        background: url('https://fp.yangcong345.com/onion-extension/111-7010b1bf075e01f0c4f85e75a374278f.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
}

.item-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64px;
  padding: 0 32px 0 20px;
  border-radius: 16px;

  .item-check {
    margin-left: 4px;
    visibility: hidden;
  }

  .item-check-show {
    margin-left: 4px;
    visibility: visible;
  }

  &:hover {
    background: #f4f6ff;

    .item-check {
      visibility: visible;
    }
  }

  .item-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }

  .name-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 0 5px;
    cursor: pointer;

    span {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #3d3d3d;
    }
  }

  .folder-name {
    width: 188px;
    height: 28px;
    margin-left: 20px;
    font-size: 14px;
    line-height: 28px;

    ::v-deep(.n-input__input-el) {
      height: 28px;
    }

    ::v-deep(.n-input__border) {
      border-radius: 8px;
    }

    ::v-deep(.n-input__state-border) {
      border-radius: 8px;
    }
  }

  .btn-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;

    ::v-deep(.oiw-button--box.n-button--medium-type) {
      width: 70px;
      min-width: 70px;
      height: 40px;
      padding: 0;
      font-size: 14px;
    }
  }
}
</style>
