<template>
  <div>
    <n-modal
      v-model:show="modalShow"
      preset="card"
      closable
      :mask-closable="false"
      style="width: 960px; height: 660px"
      class="move-modal"
    >
      <template #header>
        <div class="flex items-baseline">
          <h2 class="font-t3 font-600 mr-4px">查看上课截屏</h2>
          <span v-if="shotList" class="font-size-14px color-#9792AC"
            >共{{ shotLength }}张截屏</span
          >
        </div>
      </template>
      <n-divider style="margin-top: 0" />
      <div
        v-if="shotList && shotList.length > 0"
        class="h-456px overflow-scroll scrollbar-none"
      >
        <div
          v-for="shot in shotList"
          :key="shot.time"
          class="shot-list mt-10px"
        >
          <div class="font-size-16px color-#57526C font-bold mb-12px">
            {{ shot.time }}
          </div>
          <div class="flex flex-wrap">
            <div
              v-for="img in shot.data"
              :key="img.id"
              class="shot-item mr-16 mb-16px"
            >
              <div
                class="shot-item-header group relative"
                :style="{
                  backgroundImage: `url(${img.domain + '/' + img.key})`,
                }"
              >
                <div
                  class="hidden group-hover:block left-0 absolute top-0 right-0 bottom-0 rounded-12px"
                >
                  <div
                    class="rounded-12px h-100% w-100% *:cursor-pointer *:inline-flex *:w-48px *:h-48px *:rounded-50% *:bg-#c7c7c7 *:justify-center *:items-center bg-#12254d/60 flex justify-center pt-20px backdrop-blur-4px"
                  >
                    <div class="hover:bg-#FFD633">
                      <LookOverIcon
                        class="w-32px h-32px"
                        color="#393548"
                        @click="
                          () => {
                            showImgDetail = true
                            imgDetail = img.domain + '/' + img.key
                          }
                        "
                      />
                    </div>

                    <div class="ml-38px hover:bg-#FFD633">
                      <DownloadIcon
                        class="w-32px h-32px"
                        color="#393548"
                        @click="
                          download(img.domain + '/' + img.key, img.createdAt)
                        "
                      />
                    </div>
                  </div>
                  <div
                    class="absolute left-32px top-77px color-#EFEEF3 font-size-14px"
                  >
                    查看大图
                  </div>
                  <div class="absolute left-118px top-77px color-#EFEEF3">
                    下载截屏
                  </div>
                </div>
              </div>
              <div class="m-8px">
                截屏时间：{{ dayjs(img.createdAt).format('HH:mm:ss') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="h-456px flex items-center justify-center">
        <OIWStateBlock type="empty" title="暂无内容" />
      </div>

      <n-space
        v-if="shotList && shotList.length > 0"
        justify="end"
        class="mt-30px"
      >
        <OIWButton :loading="downloadLoading" @click="downloadAll"
          >下载全部截屏</OIWButton
        >
      </n-space>
    </n-modal>
    <div
      v-if="modalShow && showImgDetail"
      class="n-modal-mask fixed left-0 top-0 z-999999 w-100vw h-100vh flex items-center justify-center"
    >
      <div
        class="w-936px h-510px rounded-16px relative"
        :style="{
          backgroundImage: `url(${imgDetail})`,
          backgroundSize: 'cover',
        }"
      >
        <div
          class="w-32px h-32px flex items-center justify-center bg-#F7F7F9 rounded-50% absolute top-[-36px] right-[-36px] cursor-pointer"
          @click="
            () => {
              showImgDetail = false
              imgDetail = ''
            }
          "
        >
          <CloseIcon class="w-32px h-32px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  OIWButton,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import CloseIcon from '~icons/yc/close'
import DownloadIcon from '~icons/yc/download'
import LookOverIcon from '~icons/yc/look-over'
import { downloadPicture } from '../utils.ts'
import { getShotApi, packFileZipApi } from '@/pages/prepare/service'
import type { Shot } from '@/pages/prepare/service'
import dayjs from 'dayjs'

const props = defineProps<{
  show: boolean
  id: string
  name: string
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()
const message = useOIWMessage()
const downloadLoading = ref(false)
const shotList = ref<Shot[]>()
const showImgDetail = ref(false)
const imgDetail = ref(
  'https://documents.yangcong345.com/b/teacherWorkbench/image-d774d7be45334ad81ae4387230ed24ac.jpg',
)

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

const shotLength = computed(() => {
  if (!shotList.value) return 0
  let len = 0
  shotList.value.forEach((e: any) => {
    len += e.data.length
  })
  return len
})

const download = async (imageUrl: string, createdAt: string) => {
  downloadPicture(
    imageUrl,
    props.name +
      '课件截图' +
      dayjs(createdAt).format('YYYY年MM月DD日HH时mm分ss秒') +
      '.png',
  )
}

const getShotList = () => {
  getShotApi(props.id).then((res) => {
    shotList.value = res.data
  })
}

const downloadAll = async () => {
  if (!shotList.value) return
  if (downloadLoading.value) return
  downloadLoading.value = true
  const files: { url: string; name: string }[] = []
  shotList.value.forEach((shot) => {
    shot.data.forEach((data) => {
      files.push({
        url: data.domain + '/' + data.key,
        name:
          props.name +
          '课件截图' +
          dayjs(data.createdAt).format('YYYY年MM月DD日HH时mm分ss秒') +
          '.png',
      })
    })
  })
  const res = await packFileZipApi({
    files,
    zipName: props.name + '上课截图',
  })
  if (res && res.url) {
    const a = document.createElement('a')
    a.href = res.url
    a.click()
    message.success('下载成功')
    setTimeout(() => {
      a && a.remove()
    }, 1000)
  }
  downloadLoading.value = false
}

watch(
  () => props.show,
  (val) => {
    if (val) {
      getShotList()
    }
  },
)
</script>

<style scoped lang="scss">
.shot-item {
  width: 206px;
  height: 148px;
  border-radius: 16px;

  &:hover {
    background: #f4f6ff;
  }

  .shot-item-header {
    width: 206px;
    height: 112px;
    background-size: cover;

    /* 卡片圆角 */
    border-radius: 16px;
  }
}
</style>
