<template>
  <n-modal
    v-model:show="prepareStore.isShowDragConfirm"
    preset="card"
    closable
    :mask-closable="false"
    style="width: 660px"
    class="move-modal"
  >
    <template #header>
      <h2 class="font-t3 font-600">确认移动吗？</h2>
    </template>
    <n-divider style="margin-top: 0" />
    <div>
      <div class="modalTextStyle">{{ modalText }}</div>
    </div>
    <n-space justify="end" class="mt-30px">
      <OIWButton type="info" ghost class="mr-16" @click="onCancel"
        >取消</OIWButton
      >
      <OIWButton @click="handleMove">确认</OIWButton>
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import { OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import usePrepareStore from '@/pages/prepare/prepareStore'
import { moveFilesApi } from '@/pages/prepare/service'
import { getQueryString } from '@guanghe-pub/onion-utils'

const prepareStore = usePrepareStore()
const message = useOIWMessage()

const modalText = computed(() => {
  if (prepareStore.dragContent.length !== 2) {
    return '资源定位异常，请重新选择'
  }
  const dragFile = prepareStore.dragContent[0] // 拖拽的文件/文件夹
  const dropFile = prepareStore.dragContent[1] // 拖拽到的文件夹
  const dragFileType = dragFile.type === 'FOLDER' ? '文件夹' : '资源'
  return `确认将【${dragFileType}】${dragFile.name} 移动到【文件夹】${dropFile.name} 下吗？`
})

// 确认移动
const handleMove = () => {
  const dragFileId = prepareStore.dragContent[0].id
  const dragFolderId = prepareStore.dragContent[1].id
  const curFolderId = getQueryString('folderId')
  moveFilesApi({
    fileIds: [dragFileId],
    folderId: dragFolderId,
  }).then(async () => {
    message.success('操作成功！')
    await prepareStore.initData(true, curFolderId)
    onCancel()
  })
}

const onCancel = () => {
  prepareStore.$patch((state) => {
    state.isShowDragConfirm = false
    state.dragContent = []
  })
}
</script>

<style scoped lang="scss">
.modalTextStyle {
  font-size: 16px;
}
</style>
