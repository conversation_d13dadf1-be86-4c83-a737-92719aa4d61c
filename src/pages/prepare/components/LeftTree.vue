<template>
  <div>
    <div
      class="tree-root"
      :class="{ 'bg-#f4f6ff': !isShowTree }"
      @click="handleShowTree"
      @drop="dropRoot"
      @dragover.prevent
    >
      <span @click.stop="handleToRootFolder">我的备课</span>
      <ArrowIcon
        :class="isShowTree ? 'icon-style' : 'icon-style icon-style-close'"
      />
    </div>
    <n-space v-if="isShowTree" vertical>
      <n-tree
        class="custom-tree"
        draggable
        :allow-drop="() => true"
        show-line
        block-line
        virtual-scroll
        selectable
        :data="prepareStore.leftTreeData"
        :on-load="handleLoad"
        style="height: 80vh"
        :node-props="nodeProps"
        :expanded-keys="prepareStore.expandedKeysRef"
        :selected-Keys="prepareStore.selectedKeysRef"
        :render-label="renderLabel"
        @update:expanded-keys="handleExpandedKeysChange"
        @update:selected-keys="handleSelectedKeysChange"
      >
        <template #empty>
          <n-empty description="暂无内容" class="empty-tree-box" />
        </template>
      </n-tree>
    </n-space>
  </div>
</template>

<script lang="tsx" setup>
import { useRouter } from 'vue-router'
import type { TreeOption } from 'naive-ui'
import type {
  prepareTreeApiParams,
  PrepareTreeItemType,
} from '@/pages/prepare/service'
import { getFolderDetailApi } from '@/pages/prepare/service'
import ArrowIcon from '~icons/yc/arrow-up'
import FloderIcon from '~icons/yc/floder-default'
import FloderBlueIcon from '~icons/yc/floder-blue'
import usePrepareStore from '@/pages/prepare/prepareStore'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

const prepareStore = usePrepareStore()
const router = useRouter()
const message = useOIWMessage()

const handleExpandedKeysChange = (_keys: string[]) => {
  prepareStore.$patch((state) => {
    state.expandedKeysRef = _keys
  })
}
const handleSelectedKeysChange = (_keys: string[]) => {
  prepareStore.$patch((state) => {
    state.selectedKeysRef = _keys
  })
}
const renderLabel = ({ option }: { option: TreeOption }) => {
  return (
    <div class="flex items-center">
      {option.id === prepareStore.selectedKeysRef[0] ? (
        <FloderBlueIcon class="w-16px mr-4px" />
      ) : (
        <FloderIcon class="w-16px mr-4px" />
      )}
      {option.name}
    </div>
  )
}
const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      if (option.id) {
        router.replace({
          name: 'prepareCenterList',
          query: {
            folderId: option.id as string,
          },
        })
      }
    },
    // 拖拽开始
    ondragstart() {
      if (!option) {
        return
      }
      const newDragContent = [
        {
          id: option.id,
          name: option.name,
          type: option.type,
          childFolderCount: option.childFolderCount,
          fileId: option.fileId,
        },
      ]
      // console.log('拖拽开始：', newDragContent)
      prepareStore.$patch((state) => {
        state.dragContent = newDragContent as PrepareTreeItemType[]
      })
    },
    // 拖拽结束
    async ondrop() {
      // console.log('ondrop', option)
      if (!option) {
        return
      }
      const firstData = prepareStore.dragContent[0]
      // 从右侧详情的文件夹移入左侧的树， 校验目标元素是否是当前拖拽元素的子集
      if (firstData.type === 'FOLDER') {
        if (firstData.id === option.id) {
          message.warning('不能将当前文件夹移入到自己！')
          return
        }
        // 请求option.id的详情接口，获取目标元素的父级数组（面包屑）
        const body = {
          folderId: option?.id,
          fileType: ['FOLDER'],
          page: 1,
          pageSize: 30,
        }
        const res = await getFolderDetailApi(body as prepareTreeApiParams)
        if (res.breadcrumbs) {
          const breadcrumbsIds = res.breadcrumbs.map((i) => i.id)
          if (breadcrumbsIds.includes(firstData.id)) {
            message.warning('不能将文件夹移入到自己的子级文件夹内！')
            return
          }
        }
      }
      const newDragContent = [
        firstData,
        {
          id: option.id,
          name: option.name,
          type: option.type,
          childFolderCount: option.childFolderCount,
          fileId: option.fileId,
        },
      ]
      prepareStore.$patch((state) => {
        state.dragContent = newDragContent as PrepareTreeItemType[]
        state.isShowDragConfirm = true
      })
    },
  }
}

// 根目录跳转
const handleToRootFolder = () => {
  router.replace({
    name: 'prepareCenterList',
    query: {},
  })
}
// 根文件夹的展开收起
const isShowTree = ref(true)
const handleShowTree = () => {
  isShowTree.value = !isShowTree.value
  prepareStore.getLeftTreeData()
}
// 拖拽至根目录
const dropRoot = () => {
  const firstData = prepareStore.dragContent[0]
  const newDragContent = [
    firstData,
    {
      id: '',
      name: '我的备课',
      type: 'FOLDER',
      childFolderCount: 0,
      fileId: '',
    },
  ]
  prepareStore.$patch((state) => {
    state.dragContent = newDragContent as PrepareTreeItemType[]
    state.isShowDragConfirm = true
  })
}
// 获取文件夹下的子文件树，根文件夹为空字符串
const handleLoad = async (node?: TreeOption) => {
  const folderId = node ? (node.key as string) : ''
  const res = await getFolderDetailApi({
    folderId: folderId || '',
    fileTypes: ['FOLDER'],
  })
  if (res && res.items) {
    const newData = res.items.map((i) => {
      return {
        ...i,
        key: i.id,
        label: i.name,
        isLeaf: i.childFolderCount === 0,
      }
    })
    if (node) {
      node.children = newData
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-root {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  cursor: pointer;
  // background-color: #f4f6ff;
  border-radius: 8px;

  span {
    font-size: 14px;
    font-weight: 600;
  }

  .icon-style {
    width: 10px;
    transition: transform 0.2s;
    transform: rotate(180deg);
  }

  .icon-style-close {
    transition: transform 0.2s;
    transform: rotate(0deg);
  }
}

.empty-tree-box {
  margin-top: 200px;
}
</style>
