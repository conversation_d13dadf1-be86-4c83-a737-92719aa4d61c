<template>
  <div>
    <n-modal
      v-model:show="modalShow"
      preset="card"
      closable
      :mask-closable="false"
      style="width: 660px"
      class="move-modal"
    >
      <template #header>
        <h2 class="font-t3 font-600">
          {{ type === 'copy' ? '将课件复制' : typeName }}到
        </h2>
      </template>
      <n-divider style="margin-top: 0" />
      <div class="flex flex-wrap">
        <div class="tree-root" @click="selectRoot">我的备课</div>
        <n-tree
          class="custom-tree"
          show-line
          block-line
          virtual-scroll
          selectable
          :data="moveTreeData"
          :on-load="handleLoad"
          style="height: 400px"
          :node-props="nodeProps"
        >
          <template #empty>
            <n-empty description="暂无内容" class="empty-tree-box" />
          </template>
        </n-tree>
      </div>
      <n-space justify="end" class="mt-30px">
        <OIWButton type="info" ghost class="mr-16" @click="onCancel"
          >取消</OIWButton
        >
        <OIWButton @click="handleMove">{{ typeName }}</OIWButton>
      </n-space>
    </n-modal>
    <div v-if="copyToast" class="my-toast flex items-center">
      <SuccessIcon color="#12C874" class="w-20px h-20px mr-4px" />
      <div class="n-message__content">
        复制成功，<span
          class="color-#5e80ff font-bold cursor-pointer"
          @click="toFloder"
          >前往查看</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import usePrepareStore from '@/pages/prepare/prepareStore'
import SuccessIcon from '~icons/yc/success'

import type { TreeOption } from 'naive-ui'
import {
  getFolderDetailApi,
  moveFilesApi,
  copyFilesApi,
} from '@/pages/prepare/service'
import type {
  PrepareTreeItemTypeWithIsChecked,
  PrepareTreeItemType,
} from '@/pages/prepare/service'

const props = defineProps<{
  type: 'move' | 'copy'
  show: boolean
  files: PrepareTreeItemTypeWithIsChecked[]
  folderId: string | undefined
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

const typeName = computed(() => {
  return props.type === 'move' ? '移动' : '复制'
})

const prepareStore = usePrepareStore()
const message = useOIWMessage()
const curSelectFolderId = ref('')
const curSelectFolderName = ref('')
const copyToast = ref(false)
const moveTreeData = ref<PrepareTreeItemType[]>([])

watch(
  () => props.show,
  (val) => {
    if (val) {
      getFolderList()
    }
  },
)

// 需要重新拉取文件夹目录(否则添加的禁止操作的文件夹会污染到左侧的树结构)
const getFolderList = async () => {
  const params = {
    folderId: '',
    fileTypes: ['FOLDER'],
  }
  const res = await getFolderDetailApi(params)
  if (res && res.items) {
    moveTreeData.value = res.items.map((i) => {
      return {
        ...i,
        key: i.id,
        label: i.name,
        isLeaf: i.childFolderCount === 0,
        disabled: getDisabled(i),
      }
    })
  }
}
const getDisabled = (item: PrepareTreeItemType) => {
  return props.files.some((i) => i.id === item.id && i.type === 'FOLDER')
}
// 选中根目录的处理
const selectRoot = () => {
  curSelectFolderId.value = ''
  curSelectFolderName.value = '我的备课'
}
// 成功的toast文案
const successMsg = computed(() => {
  let preText = '文件夹/资源'
  if (props.files.length === 1) {
    preText = props.files[0].type === 'FOLDER' ? '文件夹' : '资源'
  }
  return `操作成功，${preText}被${typeName.value}至${curSelectFolderName.value}`
})
const handleMove = () => {
  if (!curSelectFolderName.value) {
    message.error(`请选择${typeName.value}到的文件夹`)
    return
  }
  if (!props.files.length) {
    message.error(`请选择需要${typeName.value}的文件`)
    return
  }
  if (props.type === 'move') {
    const ids = props.files.map((i) => i.id)
    moveFilesApi({
      fileIds: ids,
      folderId: curSelectFolderId.value,
    }).then(async () => {
      message.success(successMsg.value)
      modalShow.value = false
      curSelectFolderId.value = ''
      curSelectFolderName.value = ''
      await prepareStore.initData(true, props.folderId)
    })
  }
  // console.log('props.files', props.files)
  // console.log('curSelectFolderId.value', curSelectFolderId.value, props.folderId)
  if (props.type === 'copy') {
    copyFilesApi({
      fileId: props.files[0].id,
      folderId: curSelectFolderId.value,
    }).then(async () => {
      copyToast.value = true
      setTimeout(() => {
        copyToast.value = false
      }, 5000)
      modalShow.value = false
      // curSelectFolderId.value = ''
      curSelectFolderName.value = ''
      if (curSelectFolderId.value === props.folderId) {
        await prepareStore.initData(true, props.folderId)
        message.success('复制成功')
      }
    })
  }
}

const router = useRouter()
const toFloder = () => {
  router.replace({
    name: 'prepareCenterList',
    query: {
      folderId: curSelectFolderId.value,
    },
  })
  // 触发左侧文件夹树的变化
  prepareStore.updateLeftTreeData(curSelectFolderId.value)
}

const onCancel = () => {
  modalShow.value = false
  curSelectFolderId.value = ''
  curSelectFolderName.value = ''
  moveTreeData.value = []
}
// 处理树的节点
const nodeProps = ({ option }: { option: TreeOption }) => {
  if (option.disabled) {
    return {}
  }
  return {
    onClick() {
      if (option.id) {
        curSelectFolderId.value = option.id as string
        curSelectFolderName.value = option.name as string
      }
    },
  }
}
const handleLoad = async (node?: TreeOption) => {
  if (node && node.disabled) {
    return false
  }
  const folderId = node ? (node.key as string) : ''
  const res = await getFolderDetailApi({
    folderId: folderId || '',
    fileTypes: ['FOLDER'],
  })
  if (res && res.items) {
    const newData = res.items.map((i) => {
      return {
        ...i,
        key: i.id,
        label: i.name,
        isLeaf: i.childFolderCount === 0,
        disabled: getDisabled(i),
      }
    })
    if (node) {
      node.children = newData
    }
  }
}
</script>

<style scoped lang="scss">
.my-toast {
  position: fixed;
  top: 14px;
  left: 50%;
  z-index: 9999999;
  padding: 14px 24px;
  margin-left: -117px;
  font-size: 18px;
  font-weight: 500;
  color: #393548;
  background: #fff;
  border-radius: 12px;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.tree-root {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  background-color: #f4f6ff;
  border-radius: 8px;
}

.custom-tree {
  font-size: 14px;
  font-weight: 500;

  ::v-deep(.n-tree-node-wrapper) {
    min-height: 36px;

    .n-tree-node-switcher {
      height: 100%;
      min-height: 36px;
    }

    .n-tree-node-content {
      min-height: 36px;
    }

    .n-tree-node {
      border-radius: 8px;
    }
  }

  ::v-deep(.n-tree-node--selected) {
    .n-tree-node-content {
      color: #5e80ff;
    }
  }
}
</style>
