<template>
  <OIWModal
    v-model:show="modalShow"
    preset="card"
    closable
    :mask-closable="false"
    style="width: 759px; height: 665px"
    class="ppt-modal"
    title="选择AI模板"
  >
    <div class="button-group">
      <div
        v-for="i in options"
        :key="i.value"
        class="button-item"
        :class="{ active: i.value === templateStyle }"
        @click="onStyleChange(i.value as TemplateStyle)"
      >
        {{ i.label }}
      </div>
    </div>
    <div
      v-if="list"
      class="tep-box max-h-555px overflow-y-scroll flex flex-wrap justify-between"
    >
      <div
        v-for="item in list"
        :key="item.id"
        class="group relative mt-16px w-221px h-176px rounded-12px b-1px b-transparent p-8px pb-0 hover:b-1px cursor-pointer hover:b-#5E80FF hover-bg-#F4F6FF"
      >
        <OIWButton
          class="use-btn hidden group-hover:flex absolute top-88px left-66px"
          size="small"
          round
          @click="createFile(item)"
          >使用</OIWButton
        >
        <div
          class="rounded-8px overflow-hidden img-box"
          style="box-shadow: 0px 4px 4px 0px rgba(80, 75, 100, 0.08)"
        >
          <img :src="item.coverURL" class="w-100% h-128px" alt="" />
        </div>
        <div class="flex items-center mt-8px">
          <PPTIcon class="w-24px h-24px flex-shrink-0" />
          <div class="text-ellipsis text-nowrap overflow-hidden">
            {{ item.name }}
          </div>
        </div>
      </div>
      <i></i><i></i>
    </div>
    <div id="ai-problem-item-insert">
      <InsertProblem :problem="insertProblem" :type="1" />
    </div>
    <div v-if="aiGenerating" class="ai-generating">
      <div class="ai-generating-box gradient-border">
        <!-- <div  /> -->
        <Vue3Lottie
          class="ai-loading"
          mode="normal"
          animation-link="https://fp.yangcong345.com/middle/2.0.1/data.json"
        />
        <div class="font-size-16px ml-8px color-#3D3D3D flex-1">
          课件生成中 … 请勿退出
        </div>
        <div
          class="font-size-16px ml-8px color-#8A869E cursor-pointer"
          @click="cancelAiGenerating"
        >
          取消
        </div>
      </div>
    </div>
  </OIWModal>
</template>

<script setup lang="ts">
import { OIWButton, OIWModal, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import PPTIcon from '~icons/yc/PPTIcon'
import type {
  TemplateStyle,
  TemplateFile,
} from '@/pages/templateManage/service.ts'
import {
  TemplateStyleEnum,
  getListTemplateV2Api,
} from '@/pages/templateManage/service.ts'
import type { AISlidePages } from '@/pages/createAIPPT/service.ts'
import {
  aiGenerateApi,
  createAiPPTApi,
  createAiPPTForSchoolResourceApi,
} from '@/pages/createAIPPT/service.ts'
import { uploadToOss, base64ToFile } from '@/utils/uploadUtils'
import InsertProblem from '@/pages/presentation/components/insertProblem.vue'
import html2canvas from 'html2canvas'
import { Vue3Lottie } from 'vue3-lottie'
const props = defineProps<{
  show: boolean
  randomInt?: number | undefined
  isFromSchoolResource?: boolean
  schoolResourceIds?: {
    stageId: YcType.CsvId
    subjectId: YcType.CsvId
    publisherId: YcType.CsvId
    semesterId: YcType.CsvId
    chapterId: YcType.CsvId
    sectionId: YcType.CsvId
    subsectionId: YcType.CsvId
  }
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const message = useOIWMessage()
const options = [
  { label: '全部', value: 'TEMPLATE_FILE_STYLE_ALL' },
  ...TemplateStyleEnum,
]
const insertProblem = ref()
const list = ref<TemplateFile[]>()
const templateStyle = ref<TemplateStyle>('TEMPLATE_FILE_STYLE_ALL')

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

const onStyleChange = (val: TemplateStyle) => {
  templateStyle.value = val
  getList()
}

watch(
  () => props.show,
  (val) => {
    if (val) {
      getList()
    }
  },
)

const getList = async () => {
  const res = await getListTemplateV2Api({
    templateStyle: templateStyle.value,
    templateType: 'TEMPLATE_TYPE_CREATE_AI_PPT_V2',
    state: 'TEMPLATE_STATE_PUBLISHED',
    page: 1,
    pageSize: 30,
  })
  list.value = res.templateFiles
}

const fileUploadEnd = async (
  res: { data: { url: string } },
  file: { originFileName: string; url: string; type: string },
  slidePage: AISlidePages,
) => {
  const { url } = file
  slidePage.contents.forEach((content) => {
    if (content.placeholderText === 'questpic') {
      content.contents = [url]
    }
  })
  return url
}
const fileUploadError = () => {
  message.error('Sorry，文件上传失败了，请重试')
}

const cancelAiGenerating = () => {
  modalShow.value = false
  aiGenerating.value = false
  window.localStorage.removeItem(`ai-generate-data-${props.randomInt}`)
}

const aiGenerating = ref(false)
const sessionId = ref<string>('')
const createFile = async (item: TemplateFile) => {
  aiGenerating.value = true
  sessionId.value = `${new Date().getTime()}`
  const aiGenerateData = {
    ...JSON.parse(
      window.localStorage.getItem(
        `ai-generate-data-${props.randomInt}`,
      ) as string,
    ),
    sessionId: sessionId.value,
  }
  const genContent = await aiGenerateApi(aiGenerateData)

  if (genContent.sessionId !== sessionId.value) return

  await problemToPNG(genContent)

  if (aiGenerating.value) {
    try {
      const res = props.isFromSchoolResource
        ? await createAiPPTForSchoolResourceApi({
            templateId: item.id,
            genContent,
            ...props.schoolResourceIds!,
          })
        : await createAiPPTApi({ templateId: item.id, genContent })
      window.open(
        `/teacher-workbench/slide/edit?fileId=${res.fileId}&id=${res.id}${
          props.isFromSchoolResource ? '&platform=schoolResource' : ''
        }`,
        '_blank',
      )
    } catch (error) {
      modalShow.value = false
      aiGenerating.value = false
      message.error('课件生成失败，请重试')
    }
  }
  modalShow.value = false
  aiGenerating.value = false
}
const problemToPNG = async (genContent: {
  slidePages: AISlidePages[]
  id: string
}) => {
  for (const slidePage of genContent.slidePages) {
    if (
      slidePage.type === 'TOPIC_KEY_EXPLANATION_PROBLEM' ||
      slidePage.type === 'CLASS_WORK'
    ) {
      insertProblem.value = slidePage.extras.problem
      await new Promise((resolve) =>
        // eslint-disable-next-line no-promise-executor-return
        setTimeout(resolve, 100),
      )
      try {
        // 获取元素
        const content = document.getElementById(
          'ai-problem-item-insert',
        ) as HTMLElement
        if (!content) {
          console.error('Element with id "ai-problem-item-insert" not found.')
          continue
        }

        // 生成 Canvas
        const canvas = await html2canvas(content, {
          useCORS: true,
          allowTaint: true,
          scale: 1,
        })

        // 将 Base64 转换为 File 对象
        const file = await base64ToFile(canvas.toDataURL('image/png'))

        // 上传到 OSS
        await new Promise((resolve, reject) => {
          uploadToOss(
            [file],
            async (res, file) => {
              try {
                const url = await fileUploadEnd(res, file, slidePage) // 调用 fileUploadEnd，获取 URL
                resolve(url)
              } catch (error) {
                reject(error)
              }
            },
            fileUploadError,
          )
        })
      } catch (error) {
        console.error('Error during processing slidePage:', error)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tep-box {
  i {
    display: inline-block;
    width: 221px;
    height: 1px;
  }
}

.img-box {
  border: 1px solid #c5c1d4;
  box-shadow: 0px 4px 4px 0px rgba(80, 75, 100, 0.08);
}

#ai-problem-item-insert {
  position: fixed;
  top: -100vw;
  right: -100vw;
  z-index: -1 !important;
  width: fit-content;
  width: 1145px !important;
  padding: 0 16px;
  background-color: #fff;

  ::v-deep(.onion-problem-render__main) {
    font-size: 27px !important;
  }

  ::v-deep(.multi-line) {
    margin-bottom: -10px;
  }
}

.gradient-border {
  position: relative;
  padding: 16px; /* Adjust padding to match your design */
  background: white; /* Background color for the inner content */
  border-radius: 16px;

  &::before {
    position: absolute;
    top: -2px;
    right: -2px;
    bottom: -2px;
    left: -2px;
    z-index: -1; /* Position behind the content */
    content: '';
    background: linear-gradient(95deg, #c474e7 0%, #6482ff 44%, #68c0f9 95%);
    border-radius: 16px; /* Same border-radius as the container */
  }
}

.ai-generating {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgba(18, 37, 77, 0.6);

  backdrop-filter: blur(3.2px);

  .ai-generating-box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 678px;
    height: 56px;
    background: #ffffff;

    /* 卡片圆角 */
    border-radius: 16px;

    /* 大投影 */
    box-shadow: 0px 6px 32px -4px rgba(97, 113, 136, 0.2);
    opacity: 1;

    .ai-loading {
      width: 32px;
      height: 32px;
    }
  }
}
</style>
