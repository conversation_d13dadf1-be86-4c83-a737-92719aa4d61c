<template>
  <section>
    <div class="flex items-center justify-end pb-11px pt-11px pr-32px">
      <button
        v-if="paperType === 'CB'"
        class="default-btn"
        @click="onCreateShare"
      >
        分享
      </button>
      <button
        v-if="examPaper || otherPaperName"
        :disabled="disabledBtn"
        class="default-btn"
        @click="onLessonPreparation"
      >
        加入备课
      </button>
      <button
        v-if="examPaper || otherPaperName"
        :disabled="disabledBtn"
        class="download-btn"
        @click="createPdfTask"
      >
        下载
      </button>
    </div>
    <div class="w-800px mx-auto mt-24px">
      <div
        class="color-#393548 text-24px font-600 flex items-center justify-center"
      >
        <img
          v-if="
            paperType === 'CB' && examPaper?.level === 'EXAM_PAPER_LEVEL_BEST'
          "
          class="w-32px h-20px mr-8px"
          src="https://fp.yangcong345.com/onion-extension/%<EMAIL>"
          alt=""
        />
        <n-ellipsis style="max-width: 700px">
          {{ paperType === 'CB' ? examPaper?.name : otherPaperName }}
        </n-ellipsis>
      </div>
      <div
        v-if="paperType === 'CB'"
        class="flex items-center justify-center mt-16px text-14px color-#9792AC"
      >
        <span>下载量：{{ examPaper?.downloadCount }}</span>
        <span>｜</span>
        <span>收藏量：{{ examPaper?.favoriteCount }}</span>
      </div>
      <div class="mb-40px text-14px">
        <div class="divide-y-[1px] divide-[#DCD8E7]">
          <ProblemSingle
            v-for="(problem, index) of problems"
            :key="problem.id"
            class="pb-32px problem-render-government"
            :index="index + 1"
            :problem="problem"
            isHideFavorite
            :booksIds="booksIds"
            from="paperPreview"
            :tagConfig="{
              isHideBaseTags: true,
              isHideUsageCount: true,
              isHideArrangementNum: true,
              isHideAnswerNum: true,
              isHideAccuracy: true,
              isHideCorrectRate: true,
              isHideIsUsed: true,
            }"
            :showSchoolAiExplainButton="isAIExplainAuth"
            :problemTypeConfig="{
              isSchool:
                paperType !== 'CB' && problem?.sourceType === 'SchoolProblem',
            }"
            @on-add-to-books="addToBooks(problem)"
          />
        </div>
        <BackTop />
      </div>
    </div>
    <PaperChangeModal
      v-model:show="paperChangeShow"
      :type="paperChangeType"
      @cancel="handleCancelPaperChange"
      @confirm="handleConfirmPaperChange"
    />
  </section>
</template>

<script setup lang="ts">
import type { ExamPaperItem } from '../service'
import {
  getPaperDetailApi,
  postExamPaperDownload,
  postExamPaperSaveInCloudDisk,
} from '../service'
import ProblemSingle from '@/components/ProblemSingle/index.vue'
import type { ProblemPoolSimple } from '@/pages/problem/service'
import { getProblemDetailsByIdApi } from '@/pages/problem/service'
import { useShare } from '@/components/ShareModal/useShare'
import { buryPoint } from '@/utils/buryPoint'
import { examBestAuth } from '@/hooks/usePermission'
import useBooksProblem from '@/store/booksProblem'
import PaperChangeModal from '@/components/PaperChangeModal.vue'
import { getQueryString } from '@guanghe-pub/onion-utils'
import { downloadFile } from '@/utils/download.ts'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'
import { getTestPaper } from '@/pages/combinationPaper/service.ts'
import { useProblem } from '@/hooks/useProblem.ts'
import { usePdf } from '@/hooks/usePdf.ts'
import { useWord } from '@/hooks/useWord.ts'
import type {
  createPdfType,
  CreatePdfDataType,
  PostWordGenExamWordReq,
} from '@/service/pdf.ts'
import type {
  ProblemDetailType,
  ProblemSimpleType,
} from '@/pages/problem/utils.ts'
import { getPDFCreateBody } from '@/utils/pdfHelper.ts'
import { useAuth } from '@/hooks/useAuth.ts'

interface ModalData {
  with: 'WITH_NONE' | 'WITH_ANSWER' | 'WITH_ANSWER_AND_EXPLAIN'
  size: 'A4' | 'A3' | 'A5'
  fileFormat: 'FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF'
}

const props = defineProps<{
  id: string
  paperType: string // 试卷类型：CB代表洋葱试卷
}>()
const message = useOIWMessage()
const { isAIExplainAuth } = useAuth()
const booksProblemStore = useBooksProblem()
const { booksIds } = storeToRefs(booksProblemStore)
const dialog = useDialog()
const problemPool = shallowRef<ProblemPoolSimple[]>([])
const problems = shallowRef<YcType.ProblemType[]>([])
const examPaper = ref<ExamPaperItem>()
const paperChangeShow = ref(false)
const paperChangeType = ref<'download' | 'preparation'>('download')
const disabledBtn = ref(false)

// 获取CB试卷详情
async function fetchCB() {
  const res = await getPaperDetailApi(props.id)
  problemPool.value = res.problems
  examPaper.value = res.examPaper
  const problemDetailsRes = await getProblemDetailsByIdApi(
    problemPool.value.map((el) => el.problemId),
  )
  problems.value = problemDetailsRes
}

// 非CB来源试卷相关
const groups = ref<
  {
    problems?: {
      problemId?: string
      design?: Record<string, unknown>
      totalIndex?: number
      [k: string]: unknown
    }[]
    id?: string
    name?: string
    examType?: string
  }[]
>([])
const otherPaperName = ref('')
const { fetchDetails } = useProblem()
const { createPdf, pdfLink, pdfName, isPdfError } = usePdf()
const { createWord, wordLink, wordName, isWordError } = useWord()

// 获取非CB来源试卷(用户自己组卷的详情,不含上传的校本试卷）
const fetchOtherPaperDetail = async () => {
  const res = await getTestPaper({ id: props.id })
  if (res) {
    otherPaperName.value = res.name || ''
    let simpleProblem: ProblemSimpleType[] = []
    if (res.groups && res.groups.length) {
      const ids: string[] = []
      res.groups.forEach((item) => {
        item.problems?.forEach((item) => {
          ids.push(item.problemId as string)
          simpleProblem.push({
            problemId: item.problemId as string,
            sourceType: item.sourceType || '',
          })
        })
      })
      const detail = await fetchDetails(simpleProblem)
      problems.value = detail
      // 为非CB来源试卷准备groups数据
      let totalIndex = 0
      res.groups?.forEach((item) => {
        item.problems = item.problems?.map((problem) => {
          const detailItem = detail.find(
            (item) => item.id === problem.problemId,
          )
          if (detailItem && detailItem.type === 'exam' && problem.design) {
            problem.design.br = 3
          }
          totalIndex += 1
          return {
            totalIndex,
            ...problem,
            ...detailItem,
          }
        })
      })
      groups.value = res.groups
    }
  }
}

const fetchPaperDetail = async () => {
  if (props.paperType === 'CB') {
    await fetchCB()
  } else {
    await fetchOtherPaperDetail()
  }
  await booksProblemStore.getBookProblems()
}

const addToBooks = async (problem: ProblemDetailType) => {
  if (booksIds.value.includes(problem.id)) {
    await booksProblemStore.deleteBookProblems([problem.id])
    message.success('已移出')
  } else {
    buryPoint(
      'click_resourceCenter_exam_add',
      { problemId: problem.id },
      'course',
    )
    if (booksIds.value.length >= 100) {
      message.warning('无法加入，选题本最多支持100题')
      return
    }
    await booksProblemStore.addToBookProblems(
      problem.id,
      'BOOK_PROBLEM_SOURCE_EXAM_PAPER',
      problem.sourceType === 'SchoolProblem' ? 'SchoolProblem' : 'Problem',
    )
    message.success('已加入')
  }
}

async function createPdfTask() {
  // 只有CB来源的试卷需要权限验证
  if (props.paperType === 'CB') {
    let auth = true
    if (examPaper.value?.level === 'EXAM_PAPER_LEVEL_BEST') {
      auth = await examBestAuth(
        examPaper.value?.pkgIds,
        examPaper.value?.stageId,
        examPaper.value?.subjectId,
      )
    }
    if (!auth) {
      dialog.info({
        showIcon: false,
        title: '提示',
        content:
          '该试卷为精品试卷，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  }

  buryPoint('click_resourceCenter_paperList', { examId: props.id })
  paperChangeType.value = 'download'
  paperChangeShow.value = true
}

async function onLessonPreparation() {
  buryPoint(
    'click_resourceCenter_paperPreview_add',
    { examId: props.id },
    'course',
  )

  // 只有CB来源的试卷需要权限验证
  if (props.paperType === 'CB') {
    let auth = true
    if (examPaper.value?.level === 'EXAM_PAPER_LEVEL_BEST') {
      auth = await examBestAuth(
        examPaper.value?.pkgIds,
        examPaper.value?.stageId,
        examPaper.value?.subjectId,
      )
    }
    if (!auth) {
      dialog.info({
        showIcon: false,
        title: '提示',
        content:
          '该试卷为精品试卷，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  }

  paperChangeType.value = 'preparation'
  paperChangeShow.value = true
}

const handleCancelPaperChange = () => {
  paperChangeShow.value = false
  paperChangeType.value = 'preparation'
}

const handleConfirmPaperChange = async (modal: ModalData) => {
  try {
    disabledBtn.value = true
    message.warning('处理中...')

    if (props.paperType === 'CB') {
      // CB来源试卷的处理逻辑
      if (paperChangeType.value === 'preparation') {
        const data = await postExamPaperSaveInCloudDisk({
          id: examPaper.value?.id || '',
          with: modal.with,
          fileFormat: modal.fileFormat as 'FILE_FORMAT_WORD',
        })
        if (data) {
          message.success('添加成功，可在备课中心查看')
        }
      }

      if (paperChangeType.value === 'download') {
        const data = await postExamPaperDownload({
          id: examPaper.value?.id || '',
          fileFormat: modal.fileFormat,
          with: modal.with,
        })
        if (data) {
          const name = `${examPaper.value?.name || '试卷'}${
            modal.fileFormat === 'FILE_FORMAT_WORD' ? '.docx' : '.pdf'
          }`
          downloadFile(data.url || '', name, false)
        }
      }

      // CB来源试卷处理完成后重置状态
      paperChangeShow.value = false
      paperChangeType.value = 'preparation'
      disabledBtn.value = false
    } else {
      // 非CB来源试卷的处理逻辑
      const tempModal = JSON.parse(JSON.stringify(modal))
      if (modal.fileFormat === 'FILE_FORMAT_PDF') {
        await pdfPaperChange(tempModal)
      } else {
        await wordPaperChange(tempModal)
      }

      // 非CB来源试卷的状态重置在监听器中处理
      paperChangeShow.value = false
    }
  } catch (e) {
    message.error('操作失败')
    paperChangeShow.value = false
    paperChangeType.value = 'preparation'
    disabledBtn.value = false
  }
}

// 非CB来源试卷的Word处理
const wordPaperChange = async (modal: ModalData) => {
  let simpleProblem: ProblemSimpleType[] = []
  if (groups.value && groups.value.length) {
    groups.value.forEach((item) => {
      item.problems?.forEach((item) => {
        simpleProblem.push({
          problemId: item.problemId as string,
          sourceType: item.sourceType || '',
        })
      })
    })
  }
  const data: PostWordGenExamWordReq = {
    isCloudDisk: paperChangeType.value === 'preparation',
    fileName: otherPaperName.value,
    problems: simpleProblem,
    style: {
      answerOpt:
        modal.with === 'WITH_ANSWER' || modal.with === 'WITH_ANSWER_AND_EXPLAIN'
          ? 'AnswerOptEnd'
          : 'AnswerOptHide',
      isExplain: modal.with === 'WITH_ANSWER_AND_EXPLAIN',
      textSize: 28,
      isScore: false,
    },
  }
  await createWord(data)
}

// 非CB来源试卷的PDF处理
const pdfPaperChange = async (modal: ModalData) => {
  const data: CreatePdfDataType = {
    pdfName: otherPaperName.value,
    isShowExplain: modal.with === 'WITH_ANSWER_AND_EXPLAIN',
    isShowAnswer: modal.with === 'WITH_ANSWER',
    pdfType: 'paper',
    groups: groups.value.map((item) => {
      return {
        examType: item.examType,
        name: item.name,
        id: item.id,
        problems: item.problems?.map((problem) => {
          return {
            ...problem,
            index: problem.totalIndex,
          }
        }) as ProblemDetailType[] | undefined,
      }
    }),
  }
  const createPdfData: createPdfType = getPDFCreateBody(
    data,
    otherPaperName.value,
  )
  await createPdf(createPdfData)
}

const { createShare } = useShare()
const onCreateShare = () => {
  if (props.paperType === 'CB') {
    // CB来源试卷的分享
    createShare({
      from: '',
      resourceType: 'exam_paper',
      resourceId: examPaper.value!.id,
      resourceName: examPaper.value!.name,
    })
  }
}

// 监听PDF下载链接
watch(
  () => pdfLink.value,
  (val) => {
    if (val) {
      downloadFile(val, pdfName.value + '.pdf' || '', false)
      disabledBtn.value = false
    }
  },
)

// 监听PDF下载失败
watch(
  () => isPdfError.value,
  (val) => {
    if (val) {
      message.error('下载失败，请重试')
      disabledBtn.value = false
    }
  },
)

// 监听word下载链接
watch(
  () => wordLink.value,
  (val) => {
    if (val) {
      if (paperChangeType.value === 'preparation') {
        message.success('添加成功，可在备课中心查看')
      } else {
        downloadFile(val, wordName.value + '.docx' || '', false)
      }
      paperChangeType.value = 'preparation'
      disabledBtn.value = false
    }
  },
)

// 监听word下载失败
watch(
  () => isWordError.value,
  (val) => {
    if (val) {
      message.error(
        paperChangeType.value === 'preparation'
          ? '添加失败，请重试'
          : '下载失败，请重试',
      )
      disabledBtn.value = false
      paperChangeType.value = 'preparation'
    }
  },
)

onBeforeMount(async () => {
  if (props.id) {
    await fetchPaperDetail()
  }
})

onMounted(() => {
  const fromPage = getQueryString('fromPage')
  buryPoint(
    'enterTestPaperPreviewPage',
    {
      fromPageName: fromPage === 'resource' ? '1' : '2',
    },
    'course',
  )
})
</script>

<style lang="scss" scoped>
.problem-render-government {
  font-size: 14px !important;
}

.default-btn {
  min-width: 104px;
  padding: 9px 24px;
  margin-left: 16px;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  color: #393548;
  background: transparent;
  border: 1px solid #c5c1d4;
  border-radius: 12px;

  &:disabled {
    color: #c5c1d4;
    cursor: not-allowed;
    border: 1px solid #c5c1d4;
  }
}

.download-btn {
  min-width: 104px;
  padding: 9px 24px;
  margin-left: 16px;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  color: #393548;
  background: #ffd633;
  border: none;
  border-radius: 12px;

  &:disabled {
    color: #ffffff;
    cursor: not-allowed;
    background: #c5c1d4;
  }
}

::v-deep(.onion-problem-render__option) {
  display: flex;
  flex-wrap: wrap;
}

::v-deep(.onion-problem-render__option--item) {
  width: 50%;
  border: none !important;
}

::v-deep(.onion-problem-render__main) {
  font-size: 17px;
}

::v-deep(.onion-problem-render__examBox-show) {
  display: none;
}

::v-deep(.onion-problem-render__main img) {
  max-height: 200px;
}

::v-deep(.onion-problem-render__examBox-show + .onion-problem-render__option) {
  display: none;
}

::v-deep(.onion-problem-render__difficulty) {
  display: flex;
  align-items: center;
}
</style>
