<template>
  <div
    :class="paper.state === 'unpublished' ? 'opacity-50' : ''"
    class="flex py-16px border-#E4E9F7 border-b-solid border-b-1px leading-24px relative"
  >
    <PaperIcon class="mt-16px" />
    <div v-if="paper.level === 'EXAM_PAPER_LEVEL_BEST'" class="best-tag" />

    <div class="ml-12px flex flex-col h-90px">
      <div class="color-#57526C inline-flex items-center">
        <n-ellipsis style="max-width: 600px">
          {{ paper.name }}
        </n-ellipsis>
        <StarIcon
          v-if="paper.isFavorite"
          class="ml-16px cursor-pointer"
          width="20px"
          height="20px"
          color="#FFD633"
          @click="emits('favoriteItem', paper)"
        />
        <StarBlankIcon
          v-else
          class="ml-16px cursor-pointer"
          width="20px"
          height="20px"
          color="#FFD633"
          @click="emits('favoriteItem', paper)"
        />
        <ShareIcon
          width="20"
          class="ml-8px cursor-pointer"
          height="20"
          @click="onCreateShare"
        />
      </div>
      <div
        class="mt-12px mb-12px leading-12px flex [&>span+span]:ml-12px color-#393548"
      >
        <span
          v-for="item of paperItemTags"
          :key="item"
          class="text-12px bg-#F7F7F9 rounded-8px px-8px py-4px"
        >
          {{ item }}
        </span>
      </div>
      <div class="color-#8A869E leading-22px flex text-14px mt-auto">
        <span>下载量：{{ paper.downloadCount }}</span>
        <span class="ml-24px">收藏量：{{ paper.favoriteCount }}</span>
      </div>
    </div>
    <div
      v-if="paper.state === 'unpublished'"
      class="ml-25px inline-flex items-center"
    >
      <ExpriedIcon />
    </div>
    <div class="ml-auto self-center">
      <div
        class="w-76px h-40px line-height-40px cursor-pointer text-center color-#393548 font-size-14px rounded-12px"
        style="font-weight: 600; border: 1px solid #c5c1d4"
        @click="toPreview(paper)"
      >
        预览
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ExamPaperItem } from '../service'
import StarBlankIcon from '~icons/yc/star-blank'
import StarIcon from '~icons/yc/star'
import PaperIcon from '~icons/yc/paper'
import ExpriedIcon from '~icons/yc/expried'
import { getPaperItemTags } from '../utils'
import { examBestAuth } from '@/hooks/usePermission'
import ShareIcon from '~icons/yc/share'
import { useShare } from '@/components/ShareModal/useShare'
import { buryPoint } from '@/utils/buryPoint'

const props = defineProps<{
  paper: ExamPaperItem
  from: '1' | '2' // [1:资源中心公共资源试卷-2:资源中心我的收藏试卷]
}>()
const emits = defineEmits<(e: 'favoriteItem', val: ExamPaperItem) => void>()
const dialog = useDialog()
const paperItemTags = computed(() => {
  return getPaperItemTags(props.paper)
})

const router = useRouter()
const toPreview = async (paper: ExamPaperItem) => {
  buryPoint('click_teacherWorkbench_preview_paperList', {
    pageName: props.from,
    examId: paper.id,
  })
  let auth = true
  if (paper.level === 'EXAM_PAPER_LEVEL_BEST') {
    auth = await examBestAuth(paper.pkgIds, paper.stageId, paper.subjectId)
    console.log('是否有权限', auth)
  }
  if (auth) {
    window.open(
      router.resolve({
        name: 'PaperPreview',
        query: {
          id: paper.id,
          fromPage: props.from === '1' ? 'resource' : 'personal',
          paperType: 'CB'
        },
      }).href,
    )
  } else {
    dialog.info({
      showIcon: false,
      title: '提示',
      content:
        '该试卷为精品试卷，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      positiveText: '确认',
    })
  }
}

const { createShare } = useShare()
const onCreateShare = () => {
  createShare({
    resourceType: 'exam_paper',
    resourceId: props.paper.id,
    resourceName: props.paper.name,
    from: props.from === '1' ? 'paperList' : 'paperCollection',
  })
}
</script>

<style scoped lang="scss">
.best-tag {
  position: absolute;
  top: 26px;
  left: 27px;
  display: inline-block;
  width: 33px;
  height: 26px;
  background: url('https://fp.yangcong345.com/onion-extension/切图 <EMAIL>')
    no-repeat;
  background-size: 100% 100%;
}
</style>
