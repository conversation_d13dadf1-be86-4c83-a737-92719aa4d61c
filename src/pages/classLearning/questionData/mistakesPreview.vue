<template>
  <div class="pb-40px h-90vh overflow-y-scroll box-border">
    <div class="flex items-center justify-between px-32px py-11px mb-24px">
      <OIWBreadcrumb>
        <OIWBreadcrumbItem @click="goClassManage">班级管理</OIWBreadcrumbItem>
        <OIWBreadcrumbItem @click="back">班级学情</OIWBreadcrumbItem>
        <OIWBreadcrumbItem> 导出或布置错题 </OIWBreadcrumbItem>
      </OIWBreadcrumb>
      <div class="flex justify-end items-center">
        <OIWButton
          ghost
          type="info"
          class="mr-16px"
          :loading="exportLoading"
          @click="exportPdf"
        >
          导出
        </OIWButton>
        <OIWButton @click="createHomework"> 布置 </OIWButton>
      </div>
    </div>
    <div v-if="loading">
      <OIWLoading :show="loading" width="200px" />
    </div>
    <div
      v-if="!loading && problems.length === 0"
      class="flex flex-col items-center justify-start mt-100px mb-32px"
    >
      <OIWStateBlock type="empty" title="暂无资源" />
    </div>
    <div v-if="!loading && problems.length > 0" class="w-800px mx-auto">
      <div v-for="(problem, index) of problems" :key="problem.id">
        <ProblemRender
          :currentProblem="problem"
          :problemNo="index + 1"
          :difficultyHeaderShow="false"
          showIndex
          class="problem-detail"
        >
          <template #footer>
            <div class="problem-footer flex justify-end mb-18px">
              <OIWButton
                round
                ghost
                type="error"
                size="small"
                @click="onDelete(problem.id)"
              >
                <DeteleIcon class="w-16px h-16px mr-4px" color="#FA5A65" />
                移除题目
              </OIWButton>
            </div>
          </template>
        </ProblemRender>
      </div>
      <BackTop />
    </div>
    <TeacherHomeWorkModal
      v-model:show="createHWModal"
      isPractice
      homework-type="practice"
      homework-scene="xueqingWrong"
      homework-name="错题本"
      :topics-number="chooseNum"
      :pay-topics-number="0"
      @success="onCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import {
  OIWBreadcrumb,
  OIWBreadcrumbItem,
  OIWButton,
  OIWLoading,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { ProblemRender } from '@guanghe-pub/onion-problem-render'
import TeacherHomeWorkModal from '@/components/TeacherHomeWorkModal/index.vue'
import BackTop from '@/components/BackTop.vue'
import DeteleIcon from '~icons/yc/minus'
import type { CorrectionNoteProblemSimpleType } from '@/pages/classLearning/service.ts'
import type { TeacherRoom } from '@/pages/classManage/service'
import type { ProblemDetailType } from '@/pages/problem/utils.ts'
import { getProblemDetailsByIdApi } from '@/pages/problem/service.ts'
import {
  exportRoomReportPdfApi,
  getCorrectionNoteProblemsApi,
} from '@/pages/classLearning/service.ts'
import { createPracticeHomeworkApi } from '@/pages/practice/configure/practice/service.ts'
import { getTeacherRoomListApi } from '@/pages/classManage/service'
import useHomeworkStore from '@/pages/practice/store.ts'
import { useLoading } from '@/hooks/useLoading.ts'
import { useAuth } from '@/hooks/useAuth.ts'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  groupId: string
  startAt: string
  endAt: string
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  publisherId?: YcType.CsvId
  semesterId?: YcType.CsvId
  chapterId?: string
  sectionId?: string
  source?: string
  answerNum?: string
  accuracy?: string
}>()

const router = useRouter()

const goClassManage = () => {
  router.replace({
    name: 'ClassManage',
  })
}
const back = () => {
  router.back()
}

const problems = ref<ProblemDetailType[]>([]) // 当前页面题目列表(包含题目详情)
const allProblemPool = ref<CorrectionNoteProblemSimpleType[]>([])
const filterProblemPool = ref<CorrectionNoteProblemSimpleType[]>([])
const filterCBProblemPool = ref<CorrectionNoteProblemSimpleType[]>([]) // 除去校本题目的数组

const { toggleLoading, loading } = useLoading()
// 根据 班级，时间，教材，来源去拉取全量题目（简单结构）
const fetch = async () => {
  // 校验
  if (!props.groupId) {
    message.error('请返回上一个页面选择班级')
    return
  }
  if (!props.subjectId || !props.stageId) {
    message.error('请返回上一个页面请选择教材')
    return
  }
  if (!props.startAt || !props.endAt) {
    message.error('请返回上一个页面请选择时间范围')
    return
  }
  const params = {
    groupId: props.groupId,
    startAt: props.startAt,
    endAt: props.endAt,
    subjectId: Number(props.subjectId),
    stageId: Number(props.stageId),
    publisherId: props.publisherId ? Number(props.publisherId) : undefined,
    semesterId: props.semesterId ? Number(props.semesterId) : undefined,
    chapterId: props.chapterId,
    sectionId: props.sectionId,
    source: props.source,
  }
  toggleLoading()
  const res = await getCorrectionNoteProblemsApi(params)
  allProblemPool.value = res.wrongProblems
  filterProblemPool.value = res.wrongProblems
  await filterCurrentProblemPool()
  toggleLoading()
}

fetch()

// 前端处理筛选逻辑：作答人次，正确率; 过滤校本题目; 最大100道题
async function filterCurrentProblemPool() {
  let filterProblems: CorrectionNoteProblemSimpleType[] = []
  filterProblems = allProblemPool.value.filter((el) => {
    let answerNumFilter = true
    let accuracyFilter = true
    if (props.answerNum && props.answerNum !== 'all') {
      answerNumFilter = el.answerNum >= Number(props.answerNum)
    }
    if (props.accuracy && props.accuracy !== 'all') {
      accuracyFilter = el.accuracy <= Number(props.accuracy)
    }
    return answerNumFilter && accuracyFilter
  })
  // 按正确率升序
  filterProblems.sort((a, b) => {
    return a.accuracy - b.accuracy
  })
  filterProblemPool.value = filterProblems
  // 过滤校本题目类型
  filterCBProblemPool.value = filterProblems.filter((el) => {
    return el.problemType === 'cb_problem'
  })
  // 计算过滤掉的校本题目数量，并给出toast提示
  if (filterProblems.length > filterCBProblemPool.value.length) {
    message.warning(
      `有${
        filterProblems.length - filterCBProblemPool.value.length
      }道题来自校本题库，暂无法导出或者布置这些题目`,
    )
  }
  // filterCBProblemPool 题目最大数量为100，超100的剔除
  if (filterCBProblemPool.value.length > 100) {
    filterCBProblemPool.value = filterCBProblemPool.value.slice(0, 100)
    message.warning('最多支持100题，已剔除超出的题目')
  }
  if (filterCBProblemPool.value.length > 0) {
    await fetchProblemDetails()
  } else {
    problems.value = []
  }
}
// 根据题目ID获取题目详情（此页面仅需要请求cb题目）
async function fetchProblemDetails() {
  const ids = filterCBProblemPool.value.map((el) => el.problemId)
  if (ids.length > 0) {
    problems.value = await getProblemDetailsByIdApi(ids)
  }
}

const dialog = useDialog()
const message = useOIWMessage()
// 点击移除题目
const onDelete = (id: string) => {
  dialog.warning({
    title: '提示',
    content: '确定移除该题目吗?',
    negativeText: '取消',
    negativeButtonProps: {
      type: 'primary',
      ghost: false,
    },
    positiveText: '确认',
    positiveButtonProps: {
      type: 'default',
    },
    onPositiveClick: () => {
      const index = problems.value.findIndex((el) => el.id === id)
      if (index !== -1) {
        problems.value.splice(index, 1)
        message.info('移除成功')
      }
    },
  })
}

// 获取班级列表
const { userId } = useAuth()
const list = ref<TeacherRoom[]>([])
const fetchRoomList = async () => {
  list.value = await getTeacherRoomListApi(userId.value as string)
}
fetchRoomList()
const curClassRoomInfo = computed(() => {
  return list.value.find((el) => el.groupId === props.groupId)
})
// 校验
const check = () => {
  if (loading.value) {
    message.warning('数据加载中，请稍后操作')
    return false
  }
  if (problems.value.length === 0) {
    message.warning('暂无题目')
    return false
  }
  if (problems.value.length > 100) {
    message.info(
      `最多支持布置100道题目，目前已选择${problems.value.length}道，请移除部分题目后重新布置`,
    )
    return false
  }
  if (!props.groupId) {
    message.error('请返回上一个页面选择班级')
    return false
  }
  if (!props.subjectId || !props.stageId) {
    message.error('请返回上一个页面请选择教材')
    return false
  }
  return true
}

// 布置
const createHWModal = ref(false)
const chooseNum = ref(0)
const createHomework = () => {
  const checkRes = check()
  if (!checkRes) {
    return
  }
  chooseNum.value = problems.value.length
  createHWModal.value = true
}

const HomeworkStore = useHomeworkStore()
const onCreateSuccess = (data: any) => {
  const problemsData = problems.value.map((p, index) => {
    return {
      no: index + 1,
      goalId: p.goalId,
      problemId: p.id,
    }
  })
  const { name, units, leaveMessage, expiredDate, startTime } = data
  const body = {
    homeworkType: 'practice',
    name,
    units,
    leaveMessage,
    expiredDate,
    startTime,
    courseName: '',
    problems: problemsData,
    publisherId: props.publisherId ? Number(props.publisherId) : undefined,
    semesterId: props.semesterId ? Number(props.semesterId) : undefined,
    subjectId: Number(props.subjectId),
    stageId: Number(props.stageId),
    isHaveExam: 'NO',
    source: 'xueqingWrong',
    scene: 'xueqingWrong',
  }
  createPracticeHomeworkApi(body).then((res) => {
    buryPoint(
      'clickAIClassWrongBookExportPageButton',
      { button: 'assign' },
      'course',
    )
    HomeworkStore.setCreateHomeworkRes(
      res.map((item) => {
        return { ...item, topicsNumber: chooseNum.value, scene: 'xueqingWrong' }
      }),
    )
    HomeworkStore.toggleSuccessModal(true)
    router.replace({
      name: 'PracticeList',
    })
  })
}

// 导出
const exportLoading = ref(false)
const exportPdf = () => {
  const checkRes = check()
  if (!checkRes) {
    return
  }
  if (exportLoading.value) {
    return
  }
  exportLoading.value = true
  const problemIds = problems.value.map((p) => p.id)
  message.success('后台正在处理中，处理完成后将会为您下载到本地')
  buryPoint(
    'clickAIClassWrongBookExportPageButton',
    { button: 'export' },
    'course',
  )
  const pdfName = curClassRoomInfo.value?.name
    ? `${curClassRoomInfo.value?.name}错题本`
    : '错题本'
  exportRoomReportPdfApi({
    pdfName,
    problemIds,
    isShowAnswer: false,
  })
    .then((res) => {
      if (res) {
        window.open(res.url, '_blank')
      }
    })
    .catch(() => {
      message.error('导出失败')
    })
    .finally(() => {
      exportLoading.value = false
    })
}
</script>

<style lang="scss" scoped>
.mistakes {
  height: calc(100vh - 80px - 64px);
  overflow-y: scroll;
  scrollbar-width: none;
}

.problem-detail {
  padding-top: 16px;
  font-size: 14px !important;

  ::v-deep(.onion-problem-render__choice) {
    margin-top: 0;
  }

  ::v-deep(.onion-problem-render__blank) {
    margin-top: 0;
  }

  ::v-deep(.multi-line) {
    .multi {
      display: none;
    }
  }

  ::v-deep(.onion-problem-render__option) {
    display: flex;
    flex-wrap: wrap;
    font-weight: bold;
  }

  ::v-deep(.onion-problem-render__option--item) {
    width: 50%;
    border: none !important;
  }

  ::v-deep(.onion-problem-render__main) {
    font-size: 14px;
  }

  ::v-deep(.onion-problem-render__examBox-show) {
    display: none;
  }

  ::v-deep(.onion-problem-render__main img) {
    max-height: 200px;
  }

  ::v-deep(
      .onion-problem-render__examBox-show + .onion-problem-render__option
    ) {
    display: none;
  }

  ::v-deep(.onion-problem-render__answer) {
    padding: 0 !important;
    margin: 0;
    font-weight: bold !important;
    border: none !important;
    box-shadow: none !important;

    .onion-problem-render__answer--content {
      padding-top: 2px;
      font-weight: bold !important;
    }

    .onion-problem-render__answer--header {
      font-weight: bold !important;

      &::before {
        display: none;
      }
    }
  }

  ::v-deep(.onion-problem-render__explain) {
    display: none !important;
  }
}
</style>
