<template>
  <div class="w-1200px mx-auto">
    <div class="text-14px font-semibold my-24px">
      <router-link v-slot="{ href, navigate }" to="/classManage/list" custom>
        <a :href="href" class="color-#9792AC" @click="navigate"> 全部班级 </a>
      </router-link>
      <span class="color-#9792AC"> > </span>
      <span class="color-#393548">{{ name }}</span>
    </div>
    <div v-if="detail" class="p-24px bg-#F7F7F9 rounded-16px">
      <div class="flex justify-between items-center">
        <div class="text-24px font-semibold color-#3D3D3D mr-8px">
          {{ name }}
        </div>
        <span
          v-if="detail.type === 'admin'"
          class="text-12px color-#FEA345 border-1px border-#FEA345 rounded-8px px-8px py-2px"
          >行政班</span
        >
        <span class="flex-1"></span>
        <NPopover
          class="home-nav-item-title"
          trigger="hover"
          :delay="300"
          placement="bottom-start"
          raw
          :to="false"
          :show-arrow="false"
        >
          <template #trigger>
            <OIWButton
              class="nav-item mr-16px"
              type="info"
              ghost
              icon-placement="right"
            >
              更多操作
              <div class="dropdown-arrow" />
            </OIWButton>
          </template>
          <div class="nav-sub-list">
            <div class="nav-sub" @click="toAccountList">打印账号列表</div>
          </div>
        </NPopover>
        <OIWButton type="info" @click="handleRollCall">
          课堂随机点名
        </OIWButton>
      </div>
      <div class="flex items-center text-16px color-#9792AC mt-10px">
        <span class="flex items-center pr-2px">
          班级编号：
          <span class="color-#393548">{{ detail.ref }}</span>
          <span
            class="flex items-center text-14px color-#5E80FF ml-8px cursor-pointer"
            @click="onCopyText"
            ><CopyIcon class="w-16px h-16px" />复制</span
          >
          ｜
        </span>
        <span class="flex items-center pr-2px">
          学生数量：
          <span class="color-#393548">{{ detail.memberCount }}</span>
          <span
            class="flex items-center text-14px color-#5E80FF ml-8px cursor-pointer"
            @click="onAddStudentShow"
            ><AddIcon class="w-16px h-16px" />录入</span
          >
          ｜
        </span>
        <span class="flex items-center pr-2px">
          家长数量：
          <span class="color-#393548">{{ detail.parentCount }}</span>
          <span
            class="flex items-center text-14px color-#5E80FF ml-8px cursor-pointer"
            @click="onShareToParent"
            ><ShareIcon class="w-16px h-16px" />邀请</span
          >
        </span>
        ｜
        <span v-if="type === 'admin'" class="flex items-center pr-2px">
          教师数量：
          <span class="color-#393548">{{ detail.owners.length }}</span>
          <span
            class="flex items-center text-14px color-#5E80FF ml-8px cursor-pointer"
            @click="onOwnerList"
            >查看</span
          >
        </span>
      </div>
      <div v-if="type === 'admin'" class="flex items-center mt-16px">
        <span class="flex items-center text-16px color-#9792AC pr-2px">
          当前状态：
          <span class="room-state" :class="detail.state">
            {{ roomStateFormatter(detail.state) }}
          </span>
        </span>
      </div>
    </div>

    <n-space v-if="loading" vertical :size="30" class="pt-100">
      <n-skeleton height="25px" />
      <n-skeleton height="25px" />
      <n-skeleton height="25px" />
      <n-skeleton height="25px" />
    </n-space>
    <template v-else>
      <div v-if="detail && detail.memberCount > 0" class="mt-24px">
        <div class="flex items-center mb-16px">
          <OIWRadioButtonGroup
            v-model:value="tableType"
            @update:value="onUpdateTableType"
          >
            <OIWRadioButton :value="0">全部学生</OIWRadioButton>
            <OIWRadioButton :value="1">小组</OIWRadioButton>
          </OIWRadioButtonGroup>
          <!-- <div class="color-#161b22 text-18px font-500">学生名单</div> -->
          <n-space v-if="tableType === 0" class="ml-auto">
            <OIWButton type="info" ghost @click="handleAddStudentGuideShow">
              学生入班指南
            </OIWButton>
            <OIWButton type="info" ghost @click="exportExcel">
              导出学生
            </OIWButton>
            <OIWButton type="info" @click="onAddStudentShow">
              录入学生
            </OIWButton>
          </n-space>
        </div>

        <OIWTable
          v-if="tableType === 0"
          :data="detail.members"
          :columns="columns"
          :bordered="false"
          @update:filters="onFilterChange"
        />
        <GroupManage
          v-if="tableType === 1"
          :room-id="detail.id"
          :members="detail.members.filter((item) => item.state !== 'unJoin')"
          @on-click-event="onClickEvent"
        />
      </div>
      <Empty v-else @add-class="onAddStudentShow" />
    </template>

    <CreateOrEditClass
      v-model:show="createModalShow"
      create
      @done="fetchData"
    />
    <AddStudentModal
      v-if="detail"
      v-model:show="addStudentModalShow"
      :room-id="detail.id"
      @done="fetchData"
    />
    <TeacherListModal
      v-if="detail"
      v-model:show="ownerListModalShow"
      :owners="detail.owners"
    />
    <EditStudentRealNameModal
      v-if="detail"
      v-model:show="editNameModalShow"
      :room-id="detail.id"
      :member="currentMember"
      @done="fetchData"
    />
    <AddStudentGuide
      v-if="detail"
      v-model:show="addStudentGuideShow"
      :room-ref="detail.ref"
    />
    <RoomCallDrawer v-model:show="roomCallDrawer" :room-id="activeRoomId" />
  </div>
</template>

<script setup lang="tsx">
import {
  OIWButton,
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWTable,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import type { TeacherRoom, RoomMember } from './service'
import type { DataTableBaseColumn, DataTableFilterState } from 'naive-ui'
import GroupManage from '@/pages/classManage/components/Group/GroupManage.vue'
import {
  getAdminClassDetailApi,
  resetStudentPasswordApi,
  exportAdminRoomExcelApi,
  validateStudentCount,
} from './service'
import CreateOrEditClass from './components/CreateOrEditClass.vue'
import RoomCallDrawer from '@/pages/presentation/components/RoomCallDrawer.vue'
import AddStudentGuide from './components/addStudentGuide.vue'
import AddStudentModal from './components/addAdminStudentModal.vue'
import TeacherListModal from './components/TeacherListModal.vue'
import EditStudentRealNameModal from './components/EditStudentRealNameModal.vue'
import CopyIcon from '~icons/yc/copy-blue'
import AddIcon from '~icons/yc/add-blue-new'
import ShareIcon from '~icons/yc/share-blue'
import copy from 'copy-to-clipboard'
import { useLoading } from '@/hooks/useLoading'
import {
  tableColumns,
  resetDialogContent,
  confrimResetDialogContent,
} from './utils'
import { getDynamicHost } from '@/utils/apiUrl'
import { buryPoint } from '@/utils/buryPoint'

const props = defineProps<{
  name: string
  roomId: string
  type: 'admin' | 'teaching'
}>()

const { loading, toggleLoading } = useLoading()
const router = useRouter()

const detail = ref<TeacherRoom>()
const currentMember = ref<RoomMember>({} as RoomMember)
const editNameModalShow = ref(false)
const roomCallDrawer = ref(false)
const ownerListModalShow = ref(false)
const addStudentGuideShow = ref(false)
const tableType = ref(0) // 0 全部学生 1 小组
const activeRoomId = ref('')
const message = useOIWMessage()

// 点击事件埋点方法
const onClickEvent = (button: string) => {
  if (!detail.value?.ref) {
    return
  }
  buryPoint(
    'clickClasRoomManagementDetailPageButton',
    {
      roomRef: detail.value?.ref,
      type: 'admin',
      button,
    },
    'course',
  )
}

const onUpdateTableType = (val: number) => {
  const button = val === 0 ? '全部学生tab' : '小组tab'
  onClickEvent(button)
}

const toAccountList = () => {
  onClickEvent('打印账号列表')
  router.push({
    name: 'ClassAccountList',
    query: {
      type: detail.value?.type,
      roomRid:
        detail.value?.type === 'admin' ? detail.value?.id : detail.value?.ref,
    },
  })
}

const addStudentModalShow = ref(false)
const onAddStudentShow = () => {
  if (detail.value?.ref) {
    validateStudentCount(detail.value.ref).then(
      (res: { canJoin: boolean; studentLimit: number }) => {
        if (res.canJoin) {
          addStudentModalShow.value = true
        } else {
          message.info(`班级人数已达${res.studentLimit}人，无法录入更多学生`)
        }
      },
    )
    onClickEvent('录入学生')
  }
}

const onShareToParent = () => {
  const host = getDynamicHost()
  const url = `${host}/school/activityH5/joinClass/parent`
  copy(url)
  message.success('成功复制邀请链接，可发到班级群邀请家长加入')
  onClickEvent('邀请')
}

const fetchData = async (isFirst = false) => {
  toggleLoading()
  getAdminClassDetailApi(props.roomId)
    .then((res) => {
      detail.value = res
      console.log('member', detail.value.members)
      if (isFirst) {
        buryPoint(
          'enterClasRoomManagementDetailPage',
          { roomRef: detail.value?.ref, type: 'admin' },
          'course',
        )
      }
    })
    .finally(() => {
      toggleLoading()
    })
}
const onResetPass = (row: RoomMember) => {
  onClickEvent('重置密码')
  dialog.create({
    showIcon: false,
    title: '确认重置密码',
    content: resetDialogContent(row.realName),
    positiveButtonProps: {
      type: 'error',
    },
    onPositiveClick: () => {
      if (detail && detail.value && detail.value.id) {
        resetStudentPasswordApi(
          detail.value.id,
          detail.value.type,
          row.id,
        ).then((res) => {
          dialog.create({
            showIcon: false,
            title: '确认重置密码',
            content: confrimResetDialogContent(
              res.defaultPassword || 'a123456',
            ),
            positiveText: '我知道了',
            positiveButtonProps: {
              type: 'primary',
            },
            negativeText: undefined,
          })
        })
      }
    },
    positiveText: '确认重置',
    negativeText: '取消',
  })
}
const onEditName = (row: RoomMember) => {
  currentMember.value = row
  editNameModalShow.value = true
  onClickEvent('编辑真实姓名')
}
const onFilterChange = (
  filters: DataTableFilterState,
  sourceColumn: DataTableBaseColumn<any>,
) => {
  if (filters[sourceColumn.key]) {
    sourceColumn.filterOptionValue = filters[sourceColumn.key] as string
  } else {
    sourceColumn.filterOptionValue = 'all'
  }
}
const { columns } = tableColumns(onResetPass, onEditName)

const exportExcel = async () => {
  onClickEvent('导出学生')
  exportAdminRoomExcelApi(props.roomId).then((res) => {
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res)
    link.download = `${detail.value?.name}学生名单.xlsx`
    link.click()
    window.URL.revokeObjectURL(link.href)
  })
}

const roomStateFormatter = (state: TeacherRoom['state']) => {
  switch (state) {
    case 'alive':
      return '生效中'
    case 'graduate':
      return '已毕业'
    case 'placeOnFile':
      return '已归档'
  }
}

onMounted(() => {
  fetchData(true)
})

const onCopyText = () => {
  copy(detail.value?.ref as string)
  message.success('已复制到粘贴板')
  onClickEvent('复制')
}
const dialog = useOIWDialog()

const createModalShow = ref(false)

const handleRollCall = () => {
  roomCallDrawer.value = true
  activeRoomId.value = detail.value?.id as string
  onClickEvent('课堂随机点名')
}

const onOwnerList = () => {
  ownerListModalShow.value = true
  onClickEvent('查看')
}

const handleAddStudentGuideShow = () => {
  addStudentGuideShow.value = true
  onClickEvent('学生入班指南')
}
</script>

<style scoped lang="scss">
.room-state {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  font-size: 12px;
  font-weight: 600;
  line-height: 12px;
  color: #393548;
  background: #ffffff;
  border-radius: 241px;

  &.alive {
    &::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 4px;
      content: '';
      background: #67c23a;
      border-radius: 50%;
    }
  }

  &.graduate {
    &::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 4px;
      content: '';
      background: #909399;
      border-radius: 50%;
    }
  }

  &.placeOnFile {
    &::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 4px;
      content: '';
      background: #f7ba2a;
      border-radius: 50%;
    }
  }
}
</style>
