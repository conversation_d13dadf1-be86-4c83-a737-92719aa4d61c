<template>
  <OIWTable
    :columns="columns"
    :data="tableList"
    :pagination="false"
    :bordered="false"
    :row-key="getRowKey"
    class="class-table-box"
    max-height="calc(100vh - 150px)"
  />
</template>

<script setup lang="tsx">
import { OIWTable } from '@guanghe-pub/onion-ui-web'
import type { DataTableColumns } from 'naive-ui'
import useSchoolReport from '../hook'
import type { SchoolReportClassTableItem } from '../service'
import useCvsStore from '@/store/csv'
import router from '@/routers'
import { useAuth } from '@/hooks/useAuth'
import dayjs from 'dayjs'
import { buryPoint } from '@/utils/buryPoint.ts'

const { me } = useAuth()
const cvsStore = useCvsStore()
const { tableList, startTime } = useSchoolReport()
const getRowKey = (rowData: SchoolReportClassTableItem) => {
  return rowData.tableKey || ''
}
const columns: DataTableColumns<SchoolReportClassTableItem> = [
  {
    type: 'expand',
    expandable: (row) => !!row.expandData && row.expandData?.length > 0,
    renderExpand: (row: any) => {
      return (
        <div class="expand-level-list">
          {row.expandData.map((item: any) => {
            return (
              <div
                style={
                  'height: 54px;display:flex;align-items: center;position: relative;'
                }
              >
                <span
                  style={
                    'width: 323px;background: #FFFFFF;height: 54px; z-index: 2;position: sticky;top:0;left: 0;'
                  }
                />
                <span
                  style={
                    'width: 114px;display:inline-block;padding-left: 24px; box-sizing: border-box;'
                  }
                >
                  {subjectName(item.subjectId)}
                </span>
                <span
                  style={
                    'width: 114px;display:inline-block;padding-left: 24px;box-sizing: border-box;'
                  }
                >
                  {item.measuringCount}
                </span>
                <span
                  style={`width: 114px;display:inline-block;padding-left: 24px;box-sizing: border-box; ${
                    item.attendRate !== undefined &&
                    item.attendRate >= 0 &&
                    item.attendRate < 80
                      ? 'color: #fa5a65'
                      : ''
                  }`}
                >
                  {item.attendRate !== undefined && item.attendRate >= 0
                    ? item.attendRate + '%'
                    : '-'}
                </span>
                <span
                  style={
                    'width: 114px;display:inline-block;padding-left: 24px;box-sizing: border-box;'
                  }
                >
                  {item.videoCount}
                </span>
                <span
                  style={
                    'width: 114px;display:inline-block;padding-left:24px;box-sizing: border-box;'
                  }
                >
                  {item.problemCount}
                </span>
                <span
                  style={`width: 114px;display:inline-block;padding-left:24px;box-sizing: border-box; ${
                    item.problemAccuracy !== undefined &&
                    item.problemAccuracy >= 0 &&
                    item.problemAccuracy < 80
                      ? 'color: #fa5a65'
                      : ''
                  }`}
                >
                  {item.problemAccuracy !== undefined &&
                  item.problemAccuracy >= 0
                    ? item.problemAccuracy + '%'
                    : '-'}
                </span>
                <span
                  style={
                    'width: 114px;display:inline-block;padding-left:24px;box-sizing: border-box;'
                  }
                >
                  {item.aiQACount}
                </span>
                <span
                  style={
                    'width: 77px;display:inline-block;box-sizing: border-box;'
                  }
                >
                  <div class="flex items-center justify-center">
                    <span
                      class="view-check-btn"
                      onClick={() => viewCheck(row, item)}
                    >
                      详情
                    </span>
                  </div>
                </span>
              </div>
            )
          })}
        </div>
      )
    },
  },
  {
    title: '班级',
    width: 170,
    key: 'groupName',
    render: (row) => {
      return (
        <div>
          <span>{row.groupName}</span>
          {row.grade ? (
            <span class="px-5px py-3px border-1px border-solid border-#C5C1D4 rounded-4px ml-4px text-12px leading-12px color-#57526C inline-block">
              {cvsStore.GradeEnum[row.grade || 0]}
            </span>
          ) : null}
        </div>
      )
    },
  },
  {
    title: '学生总数',
    key: 'studentCount',
  },
  {
    title: '学科',
    key: 'subjectName',
    render: (row) => {
      return (
        <span>
          {row.subjectId ? cvsStore.SubjectEnum[row.subjectId] : '全部'}
        </span>
      )
    },
  },
  {
    title: '课时数',
    key: 'measuringCount',
  },
  {
    title: '平均出勤率',
    key: 'attendRate',
    sorter: (row1, row2) => (row1.attendRate || 0) - (row2.attendRate || 0),
    render: (row) => {
      return (
        <span
          class={`${
            row.attendRate !== undefined &&
            row.attendRate >= 0 &&
            row.attendRate < 80
              ? 'color-#fa5a65'
              : ''
          }`}
        >
          {row.attendRate !== undefined && row.attendRate >= 0
            ? row.attendRate + '%'
            : '-'}
        </span>
      )
    },
  },
  {
    title: '微课数',
    key: 'videoCount',
    sorter: (row1, row2) => (row1.videoCount || 0) - (row2.videoCount || 0),
  },
  {
    title: '题目数',
    key: 'problemCount',
    sorter: (row1, row2) => (row1.problemCount || 0) - (row2.problemCount || 0),
  },
  {
    title: '题目正确率',
    key: 'problemAccuracy',
    sorter: (row1, row2) =>
      (row1.problemAccuracy || 0) - (row2.problemAccuracy || 0),
    render: (row) => {
      return (
        <span
          class={`${
            row.problemAccuracy !== undefined &&
            row.problemAccuracy >= 0 &&
            row.problemAccuracy < 80
              ? 'color-#fa5a65'
              : ''
          }`}
        >
          {row.problemAccuracy !== undefined && row.problemAccuracy >= 0
            ? row.problemAccuracy + '%'
            : '-'}
        </span>
      )
    },
  },
  {
    title: 'AI互动次数',
    key: 'aiQACount',
    sorter: (row1, row2) => (row1.aiQACount || 0) - (row2.aiQACount || 0),
  },
  {
    title: '操作',
    key: 'actions',
    width: 77,
    render: (row) => (
      <div class="flex items-center justify-center">
        <span class="view-check-btn" onClick={() => viewCheck(row)}>
          详情
        </span>
      </div>
    ),
  },
]

const subjectName = (num: number) => {
  return cvsStore.SubjectEnum[num] || '全部'
}

const viewCheck = (row: SchoolReportClassTableItem, item?: any) => {
  window.localStorage.setItem(
    'AIClassListQuery',
    JSON.stringify({
      groupIds: [row.groupId],
      subjectId: item?.subjectId || 'all',
      stageId: me.value?.school.stageId || '2',
      startAt: dayjs(startTime.value).startOf('week').toISOString(),
      endAt: dayjs(startTime.value).endOf('week').toISOString(),
      timestamp: dayjs().valueOf(),
    }),
  )
  buryPoint(
    'clickPrincipalReportPageButton',
    {
      button: 'detail',
    },
    'course',
  )
  const { href } = router.resolve({
    name: 'LessonEntry',
  })
  window.open(href, '_blank')
}
</script>

<style lang="scss" scoped>
.class-table-box {
  width: 100%;

  &::v-deep(.view-check-btn) {
    color: #5e80ff;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  &::v-deep(.n-data-table-td--last-col) {
    padding: 1px;
  }
}
</style>
