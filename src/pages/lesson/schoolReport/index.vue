<template>
  <div class="learning-status-school-report">
    <ReportTop />
    <Panoramic />
    <Teacher />
    <Class />
  </div>
</template>

<script setup lang="ts">
import ReportTop from './components/ReportTop.vue'
import Panoramic from './components/Panoramic.vue'
import Teacher from './components/Teacher.vue'
import Class from './components/Class.vue'
import useSchoolReport from './hook'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useStayTime } from '@/hooks/useStayTime'

const { initDateTime, startTime, endTime, initialized, fetchDataDebounced } =
  useSchoolReport()

onMounted(() => {
  buryPoint(
    'enterTeacherWorkBenchPrincipalReportPage',
    {
      browserResolution: `${screen.width}x${screen.height}`,
    },
    'course',
  )
  initDateTime()
})

watch(
  () => [startTime.value, endTime.value],
  (newVal, oldVal) => {
    if (
      !initialized.value ||
      !startTime.value ||
      !endTime.value ||
      JSON.stringify(newVal) === JSON.stringify(oldVal)
    ) {
      return
    }

    fetchDataDebounced()
  },
  { deep: false },
)

useStayTime('getPrincipalReportPageStayDuration', 'course', {})
</script>

<style lang="scss" scoped>
.learning-status-school-report {
  width: 1200px;
  padding-bottom: 30px;
  margin: 0 auto;
}
</style>
