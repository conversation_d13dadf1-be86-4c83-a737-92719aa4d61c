<template>
  <div class="lesson-analyze-container">
    <TopOverview
      v-if="overview"
      :overview="overview"
      :monitor-news="monitorNews"
      @monitoring-show-all="monitoringShowAll"
    />
    <OIWCardTabs
      v-model:value="checkValue"
      :on-update-value="groupChanged"
      size="large"
      class="mt-40px"
    >
      <OIWCardTab value="classDetail">课时详情</OIWCardTab>
      <OIWCardTab value="learningAnalysis">学情分析</OIWCardTab>
    </OIWCardTabs>

    <div v-if="checkValue === 'classDetail'">
      <ClassDetail
        :list="classDetailList"
        :groupId="groupId"
        :task-list="taskList"
        @view-check="viewCheck"
      />
    </div>
    <div v-if="checkValue === 'learningAnalysis'">
      <LearningAnalysis :id="id" />
    </div>
  </div>
  <MonitoringPanel
    :id="id"
    v-model:show="monitoringPanelShow"
    :news-list="monitorNewsList"
  />
  <UserTaskDrawer
    v-model:show="userTaskDrawerShow"
    :measuring-id="id"
    :user-id="userTaskDrawerUserId"
    :user-list="userTaskDrawerUserList"
    @user-change="userTaskChange"
  />
</template>

<script setup lang="ts">
import { OIWCardTabs, OIWCardTab } from '@guanghe-pub/onion-ui-web'
import TopOverview from './components/TopOverview.vue'
import MonitoringPanel from './components/MonitoringPanel.vue'
import ClassDetail from './components/ClassDetail.vue'
import UserTaskDrawer from './components/UserTaskDrawer/index.vue'
import LearningAnalysis from '@/pages/lesson/analyze/components/LearningAnalysis/index.vue'
import type {
  GetTeacherAiClassTeacherDashboardOverviewRes,
  GetTeacherAiClassTeacherDashboardEventListRes,
  AiClassDashboardDetail,
} from './service'
import {
  getTeacherAiClassTeacherDashboardOverview,
  getTeacherAiClassTeacherDashboardEventList,
  getTeacherAiClassTeacherDashboardDetail,
} from './service'
import type { TaskList } from '@/pages/lesson/analyze/components/LearningAnalysis/service.ts'
import { getTaskList } from '@/pages/lesson/analyze/components/LearningAnalysis/service.ts'
import { useServerTime } from '@/hooks/useServerTime'
import usePageVisibility from '@/hooks/usePageVisibility'
import dayjs from 'dayjs'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  id: string
  groupId: string
}>()
const { isPageActive } = usePageVisibility()
const { fetchServerTime } = useServerTime()
const checkValue = ref<'classDetail' | 'learningAnalysis'>('classDetail')
const overview = ref<GetTeacherAiClassTeacherDashboardOverviewRes | undefined>()
const timer = ref<any | null>(null)
const monitoringPanelShow = ref(false)
const monitorNewsList = ref<GetTeacherAiClassTeacherDashboardEventListRes[]>([])
const monitorNews = ref<GetTeacherAiClassTeacherDashboardEventListRes[]>([])
const classDetailList = ref<AiClassDashboardDetail[]>([])
const userTaskDrawerShow = ref(false)
const userTaskDrawerUserId = ref('')
const userTaskDrawerUserList = computed(() => {
  return classDetailList.value.map((item) => item.userId || '')
})

buryPoint('enterCourseDetailPage', {}, 'course')

const taskList = ref<TaskList[]>([])

onMounted(async () => {
  timer.value = setInterval(() => {
    console.log('isPageActive.value>>>>>>>>>', isPageActive.value)
    if (isPageActive.value) {
      fetch()
    }
  }, 10000)
  fetch()
  const data = await getTaskList({ measuringId: props.id })
  taskList.value = data.rows
  console.log('taskList.value', taskList.value)
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})

function fetch() {
  getOverview()
  getMonitorNews()
  fetchServerTime()
  if (checkValue.value === 'classDetail') {
    getClassDetail()
  }
}

async function getOverview() {
  const res = await getTeacherAiClassTeacherDashboardOverview({
    id: props.id,
  })
  overview.value = res
}

async function getMonitorNews() {
  const res = await getTeacherAiClassTeacherDashboardEventList({
    id: props.id,
  })
  monitorNewsList.value = res.rows.map((item) => {
    return {
      ...item,
      message: item.message?.replace('任务', '环节'),
      time: dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss'),
    }
  })
  monitorNews.value = monitorNewsList.value.slice(0, 2)
}

async function getClassDetail() {
  const res = await getTeacherAiClassTeacherDashboardDetail({
    id: props.id,
  })
  if (res) {
    classDetailList.value = res.rows.map((item) => {
      return {
        ...item,
        progressRate:
          (item.totalVideoCount || 0) + (item.totalProblemCount || 0) > 0
            ? Math.round(
                (((item.finishedVideoCount || 0) +
                  (item.finishedProblemCount || 0)) /
                  ((item.totalVideoCount || 0) +
                    (item.totalProblemCount || 0))) *
                  100,
              )
            : -1,
        learningExceptionMessage: item.eventStatList?.length
          ? item.eventStatList[0].message
          : '-',
      }
    })
  }
}

const groupChanged = (value: 'classDetail' | 'learningAnalysis') => {
  checkValue.value = value
  buryPoint(
    'clickCourseDetailTab',
    {
      option: value === 'classDetail' ? 'detail' : 'report',
    },
    'course',
  )
}

const monitoringShowAll = () => {
  monitoringPanelShow.value = true
}

const viewCheck = (row: AiClassDashboardDetail) => {
  userTaskDrawerShow.value = true
  userTaskDrawerUserId.value = row.userId || ''

  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'detail',
      button: 'view',
      moduleId: props.id,
      taskId: '',
      contentId: '',
    },
    'course',
  )
}

const userTaskChange = (userId: string) => {
  userTaskDrawerUserId.value = userId
}
</script>

<style lang="scss" scoped>
.lesson-analyze-container {
  width: 1200px;
  min-height: 100vh;
  // padding-bottom: 100px;
  margin: 0 auto;
}
</style>
