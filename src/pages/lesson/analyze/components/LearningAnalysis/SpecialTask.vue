<template>
  <div v-if="specialInfo" class="special-task">
    <div class="flex items-center text-14px color-#57526C">
      <span class="text-20px font-600 color-#57526C mr-16px">{{
        specialInfo.name
      }}</span>
      已完成{{ specialInfo.finishedUserCount }}人
      <n-popover
        trigger="hover"
        arrow-class="paper-preferences-arrow-popover"
        content-class="paper-preferences-content-popover"
        class="paper-preferences-wrapper-popover"
        placement="bottom"
        :to="false"
        raw
      >
        <template #trigger>
          <QuestionIcon class="ml-4px" />
        </template>
        <div class="max-w-300px">
          {{
            specialInfo.finishedUsers.length > 0
              ? specialInfo.finishedUsers.map((user) => user.name).join('、')
              : '暂无'
          }}
        </div>
      </n-popover>
      ｜未完成{{ specialInfo.unfinishedUserCount }}人
      <n-popover
        trigger="hover"
        arrow-class="paper-preferences-arrow-popover"
        content-class="paper-preferences-content-popover"
        class="paper-preferences-wrapper-popover"
        placement="bottom"
        :to="false"
        raw
      >
        <template #trigger>
          <QuestionIcon class="ml-4px" />
        </template>
        <div class="max-w-300px">
          {{
            specialInfo.unfinishedUsers.length > 0
              ? specialInfo.unfinishedUsers.map((user) => user.name).join('、')
              : '暂无'
          }}
        </div>
      </n-popover>
    </div>
    <div>
      <div
        class="w-800px px-18px h-56px bg-#F7F7F9 rounded-8px flex items-center mt-16px"
      >
        <span class="w-130px text-14px font-semibold color-#57526C"
          >知识点</span
        >
        <span class="w-80px text-14px font-semibold color-#57526C text-center"
          >已学习</span
        >
        <span class="w-110px text-14px font-semibold color-#57526C text-center"
          >班级平均熟练度</span
        >
        <span class="w-56px text-center text-14px font-semibold color-#57526C"
          >操作</span
        >
        <div class="w-1px h-100% bg-[#DCD8E7]"></div>
        <span class="ml-24px w-130px text-14px font-semibold color-#57526C"
          >知识点</span
        >
        <span class="w-80px text-14px font-semibold color-#57526C text-center"
          >已学习</span
        >
        <span class="w-110px text-14px font-semibold color-#57526C text-center"
          >班级平均熟练度</span
        >
        <span class="w-50px text-center text-14px font-semibold color-#57526C"
          >操作</span
        >
      </div>
      <div
        class="w-800px user-video-item-box"
        :class="[
          specialInfo.specialPoints.length % 2 === 0
            ? 'is-even-numbers'
            : 'is-odd-numbers',
        ]"
      >
        <div
          v-for="(point, index) in specialInfo.specialPoints"
          :key="point.id"
          class="user-video-item inline-flex items-center text-14px font-semibold color-#57526C h-56px border-b-[1px] border-[#DCD8E7]"
          :class="{
            'border-r-[1px] border-r-[#DCD8E7]': index % 2 === 0,
          }"
        >
          <n-tooltip>
            <template #trigger>
              <span
                class="w-148px text-ellipsis pl-18px cursor-default"
                :class="{
                  'pl-24px! w-160px! ': index % 2 !== 0,
                }"
              >
                {{ point.name }}</span
              >
            </template>
            <div class="max-w-300px">
              {{ point.name }}
            </div>
          </n-tooltip>
          <span class="w-80px text-center"> {{ point.studyUserCount }}人</span>
          <span class="w-110px text-center"> {{ point.proficiency }}%</span>
          <span
            class="w-52px text-center cursor-pointer color-#5E80FF"
            @click="onDetailClick(point.id)"
          >
            详情</span
          >
        </div>
      </div>
    </div>
    <n-drawer
      v-model:show="detailDrawer"
      :default-width="689"
      :close-on-esc="false"
      :block-scroll="false"
      :auto-focus="false"
      placement="right"
      class="task-detail-drawer"
    >
      <n-drawer-content
        v-if="specialTopicContent"
        class="detail-drawer-content"
      >
        <div>
          <div class="flex items-center justify-between">
            <div class="text-24px font-600 color-#393548">
              {{ specialTopicContent.name }}
            </div>
            <n-popover
              trigger="hover"
              arrow-class="paper-preferences-arrow-popover"
              content-class="paper-preferences-content-popover"
              class="paper-preferences-wrapper-popover"
              placement="bottom"
              :to="false"
              raw
            >
              <template #trigger>
                <div class="cursor-pointer flex items-center">
                  <span class="text-16px font-600 color-#57526C mr-4px"
                    >数据说明</span
                  >
                  <TipIcon class="w-22px h-22px cursor-pointer" />
                </div>
              </template>
              <div class="popover-overview-tip" style="width: 268px">
                <div class="popover-overview-tip-title">学习数据统计规则：</div>
                <div
                  class="popover-overview-tip-content pb-12px border-b-[1px] border-[#9792AC]"
                >
                  平板中「快背系列」与「AI课堂快背环节」的学习数据合并计算
                </div>
                <div class="popover-overview-tip-title pt-12px">
                  知识点熟练度反映掌握程度：
                </div>
                <div class="popover-overview-tip-content">
                  ·已掌握：100%熟练度
                </div>
                <div class="popover-overview-tip-content">
                  ·待巩固：0-80%熟练度
                </div>
                <div class="popover-overview-tip-content">
                  ·待学习：未学习该知识点题目
                </div>
              </div>
            </n-popover>
          </div>
          <div>
            <ParseText
              class="text-14px! color-#393548!"
              :text="specialTopicContent.content"
            />
          </div>
          <div class="flex mt-16px">
            <n-image
              v-if="specialTopicContent.explainImages.length > 0"
              :src="specialTopicContent.explainImages[0]"
              width="180px"
              class="explain-image mr-16px"
              alt=""
            />
            <div>
              <!-- <div> -->
              <div
                v-for="(item, index) in specialTopicContent.statusInfo"
                :key="index"
              >
                <div
                  v-if="item.status === 'HASGRASP'"
                  class="flex text-14px mb-16px"
                >
                  <span class="color-#9792AC mr-8px flex-shrink-0"
                    >已掌握（{{ item.users.length }}人）:</span
                  >
                  <div>
                    <span
                      v-for="(user, userIndex) in item.users"
                      :key="user.id"
                    >
                      {{ user.name }}
                      {{ userIndex < item.users.length - 1 ? '、' : '' }}
                    </span>
                  </div>
                </div>
                <div
                  v-if="item.status === 'NEEDREVIEW'"
                  class="flex text-14px mb-16px"
                >
                  <span class="color-#9792AC mr-8px flex-shrink-0"
                    >待巩固（{{ item.users.length }}人）:</span
                  >
                  <div>
                    <span
                      v-for="(user, userIndex) in item.users"
                      :key="user.id"
                    >
                      {{ user.name }}
                      {{ userIndex < item.users.length - 1 ? '、' : '' }}
                    </span>
                  </div>
                </div>
                <div
                  v-if="item.status === 'NEEDSTUDY'"
                  class="flex text-14px mb-16px"
                >
                  <span class="color-#9792AC mr-8px flex-shrink-0"
                    >待学习（{{ item.users.length }}人）:</span
                  >
                  <div>
                    <span
                      v-for="(user, userIndex) in item.users"
                      :key="user.id"
                    >
                      {{ user.name }}
                      {{ userIndex < item.users.length - 1 ? '、' : '' }}
                    </span>
                  </div>
                </div>
              </div>
              <!-- </div> -->
            </div>
          </div>
          <div>
            <ProblemItem
              v-for="(problem, problemIndex) in problemDetails"
              :key="problem.id"
              style="box-shadow: inset 0px 1px 0px 0px #dcd8e7"
              :problem="problem"
              :index="problemIndex + 1"
              :accuracy="problem.problemAccuracy"
              :correct-user-count="String(problem.correctUserCount)"
              :wrong-user-count="String(problem.wrongUserCount)"
              :answer-count="problem.answerUserCount"
              :task-problem-report="problem.taskProblemReport"
            />
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import type { SpecialInfo, SpecialTopicContent } from './service'
import {
  getMeasuringTaskItemSpecialPointApi,
  getTaskItemProblemReportApi,
} from './service'
import QuestionIcon from '~icons/yc/question'
import { getProblemDetailsByIdApi } from '@/pages/problem/service'
import ProblemItem from './ProblemItem.vue'
import ParseText from '@/components/ParseText.vue'
import {
  // getVideoRecordDuration,
  processChoiceOptions,
  processAnswerReport,
} from './utils'
import TipIcon from '~icons/yc/tip'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  specialInfo: SpecialInfo | undefined
  measuringId: string
  measuringTaskId: string
  measuringTaskItemId: string
}>()
const detailDrawer = ref(false)
const specialTopicContent = ref<SpecialTopicContent>()
const problemDetails = ref<YcType.ProblemType[]>()

const onDetailClick = (specialPointId: string) => {
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'detail',
      moduleId: props.measuringId,
      taskId: props.measuringTaskId,
      contentId: props.measuringTaskItemId,
    },
    'course',
  )
  getMeasuringTaskItemSpecialPointApi({
    measuringId: props.measuringId,
    measuringTaskItemId: props.measuringTaskItemId,
    stageId: props.specialInfo?.stageId.toString() || '',
    subjectId: props.specialInfo?.subjectId.toString() || '',
    moduleId: props.specialInfo?.moduleId || '',
    specialId: props.specialInfo?.specialId || '',
    pointId: specialPointId,
  }).then((res) => {
    console.log(res)
    specialTopicContent.value = res
    detailDrawer.value = true
    getProblemDetailsByIdApi(
      res.problems.map((problem) => problem.problemId),
    ).then((problems) => {
      console.log(problems)
      let arr: YcType.ProblemType[] = []
      specialTopicContent.value?.problems.forEach((problem) => {
        const problemDetail = problems.find(
          (item) => item.id === problem.problemId,
        )
        console.log(problemDetail)
        if (problemDetail) {
          arr.push({
            ...problemDetail,
            ...problem,
          })
        }
      })

      console.log('problemDetails.value', problemDetails.value)
      problemDetails.value = arr
      if (problemDetails.value.length > 0) {
        problemDetails.value.forEach(async (problem) => {
          console.log('item', problem)
          problem.taskProblemReport = []
          problem.taskProblemReport = await getTaskProblemReport(problem)
          problem.problemAccuracy =
            problem.answerUserCount === 0
              ? 0
              : Math.round(
                  (problem.correctUserCount / problem.answerUserCount) * 100,
                )
          console.log('problem.taskProblemReport', problem.taskProblemReport)
        })
      }
    })
  })
}
const getTaskProblemReport = async (problemBody: YcType.ProblemType) => {
  let taskProblemReport = (
    await getTaskItemProblemReportApi({
      measuringId: props.measuringId,
      measuringTaskItemId: props.measuringTaskItemId,
      problemId: problemBody.id,
    })
  ).rows
  console.log('taskProblemReport.value', taskProblemReport)

  if (problemBody.type === 'single_choice') {
    taskProblemReport = processChoiceOptions(problemBody, taskProblemReport)
  } else {
    taskProblemReport = processAnswerReport(problemBody, taskProblemReport)
  }
  console.log('整合用户答案后的选项', taskProblemReport)
  return taskProblemReport
}
</script>

<style scoped lang="scss">
.explain-image {
  min-width: 180px;
  height: fit-content;
}

.user-video-item-box .user-video-item:nth-child(odd) {
  border-right: 1px solid #dcd8e7;
}

.is-odd-numbers {
  border-bottom: 1px solid #dcd8e7;

  .user-video-item:last-child {
    border-bottom: none;
  }
}

.special-task {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
</style>
