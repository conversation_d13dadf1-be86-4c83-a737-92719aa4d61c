<template>
  <OIWLoading
    v-if="pdfLoading"
    :show="pdfLoading"
    width="200px"
    height="200px"
    class="h-400px!"
  />
  <div
    v-if="pptCoursewareItem"
    :style="{
      opacity: pdfLoading ? 0 : 1,
      height: pdfLoading ? '0px' : 'auto',
    }"
  >
    <div
      class="flex items-center pb-12px"
      style="box-shadow: inset 0px -1px 0px 0px #dcd8e7"
    >
      <PPTIcon class="w-32px h-32px" />
      <div class="text-16px font-600 color-#393548 flex-1 ml-8px">
        {{ pptCoursewareItem.name }}
      </div>
      <OIWButton
        type="info"
        round
        @click="handleOpenPPT(pptCoursewareItem.sourceUserId)"
        >打开课件</OIWButton
      >
    </div>
    <div class="mt-16px">
      <div class="text-20px font-600 color-#393548 flex-1 mb-16px">
        有疑问页面
      </div>
      <div
        v-if="pptCoursewareItem.markPages.length > 0"
        class="flex items-center gap-12px flex-wrap"
      >
        <div v-for="page in pptCoursewareItem.markPages" :key="page.pageNumber">
          <div v-if="page.pageNumber <= pdfPages" class="mark-page-item">
            <canvas
              :id="`pdf-canvas-${page.pageNumber}`"
              class="canvas-pdf-item"
            />
            <div class="mask">
              <div
                class="w-32px h-32px flex items-center justify-center bg-#FFD633 rounded-full"
                @click="onPreviewClick(page.pageNumber)"
              >
                <EyeIcon class="w-22px h-22px" color="#393548" />
              </div>
              <span class="text-14px font-500 color-#FFFFFF mt-4px">预览</span>
            </div>
          </div>
          <div
            v-if="page.pageNumber <= pdfPages"
            class="px-12px mt-9px flex items-center"
          >
            <div class="text-14px font-600 color-#393548">
              第{{ page.pageNumber }}页
            </div>
            <div
              class="text-12px ml-8px font-bold color-#FA5A65 px-3px py-2px bg-#FFECEE rounded-4px"
            >
              {{ page.users.length }}人提问
            </div>
          </div>
          <div class="text-16px color-#999999"></div>
        </div>
      </div>
      <div v-else class="text-16px text-center color-#999999 mt-20px">
        暂无数据
      </div>
      <OIWModal
        v-model:show="showModal"
        :closable="false"
        preset="card"
        size="large"
        :title="`第${currentPage?.pageNumber}页`"
        width="800px"
        height="600px"
        class="ppt-task-modal"
      >
        <div v-if="currentPage" class="w-688px h-full">
          <canvas id="current-pdf" class="canvas-pdf-item" />
          <div class="text-16px font-600 color-#393548 mt-12px">
            有疑问（{{ currentPage.users.length }}人）
          </div>
          <div class="text-16px font-600 color-#393548 mt-12px">
            {{ currentPage.users.map((user) => user.name).join('、') }}
          </div>
        </div>
        <CloseIcon class="close-round-icon" @click="showModal = false" />
      </OIWModal>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TaskPPTCoursewareItem } from './service'
import { getFilePDFUrlByFileIDApi } from './service'
import PPTIcon from '~icons/yc/PPTIcon'
import EyeIcon from '~icons/yc/eye'
import CloseIcon from '~icons/yc/close-round'
import { OIWButton, OIWModal, OIWLoading } from '@guanghe-pub/onion-ui-web'
import { useAuth } from '@/hooks/useAuth'
import * as pdfjsLib from 'pdfjs-dist'
import type { PDFDocumentProxy } from 'pdfjs-dist/types/src/display/api'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  pptCoursewareItem: TaskPPTCoursewareItem
  extraData?: Record<string, any>
  measuringTaskItemId: string
}>()

let pdfDoc: PDFDocumentProxy
const pdfLoading = ref(true)
const pdfPages = ref<number>(0)
const pdfScale = ref<number>(1)
const pdfUrl = ref<string>('')
const showModal = ref(false)
const currentPage = ref<TaskPPTCoursewareItem['markPages'][0]>()
const { userId } = useAuth()

watch(
  () => props.pptCoursewareItem.sourceId,
  () => {
    pdfLoading.value = true

    getFilePDFUrlByFileIDApi(props.pptCoursewareItem.sourceId).then((res) => {
      pdfUrl.value = res.url
      initPdf()
    })
  },
  {
    immediate: true,
  },
)

const initPdf = async () => {
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    'https://fp.yangcong345.com/middle/1.0.0/pdf.worker.min.js'
  pdfLoading.value = true
  const loadTask = pdfjsLib.getDocument(pdfUrl.value)
  const pdf = await loadTask.promise
  pdfDoc = pdf
  pdfPages.value = pdf.numPages
  nextTick(() => {
    pdfRenderAll().then(() => {
      pdfLoading.value = false
    })
  })
}
const pdfRenderAll = async () => {
  const renderPromises = []
  for (const markPage of props.pptCoursewareItem.markPages) {
    const renderPromise = (async () => {
      // 假如一份课件有10页，学生在第10页点击有疑问，老师后续把课件第10页删掉，那么在学情处的第10页图片将无法获取到了，这种情况不展示第10页
      if (markPage.pageNumber > pdfPages.value) {
        return
      }
      const page = await pdfDoc.getPage(markPage.pageNumber)
      const viewport = page.getViewport({ scale: pdfScale.value })
      const canvas = document.getElementById(
        `pdf-canvas-${markPage.pageNumber}`,
      ) as HTMLCanvasElement
      if (!canvas) {
        return
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return
      }
      const ratio = 0.2
      canvas.width = viewport.width * ratio
      canvas.height = viewport.height * ratio
      ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
      const renderContext = {
        canvasContext: ctx as CanvasRenderingContext2D,
        viewport: viewport,
      }
      await page.render(renderContext).promise
    })()
    renderPromises.push(renderPromise)
  }
  return Promise.all(renderPromises)
}

const pdfRenderSingle = async (num: number) => {
  const page = await pdfDoc.getPage(num)
  const viewport = page.getViewport({ scale: pdfScale.value })
  const canvas = document.getElementById(`current-pdf`) as HTMLCanvasElement
  const ctx = canvas.getContext('2d')
  const ratio = 1
  canvas.width = viewport.width * ratio
  canvas.height = viewport.height * ratio
  if (!ctx) {
    return
  }
  ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
  const renderContext = {
    canvasContext: ctx,
    viewport: viewport,
  }
  page.render(renderContext)
}

const handleOpenPPT = (sourceUserId: string) => {
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'open',
      moduleId: props.extraData?.measuringId,
      taskId: props.extraData?.measuringTaskId,
      contentId: props.extraData?.measuringTaskItemId,
    },
    'course',
  )
  if (sourceUserId === userId.value) {
    window.open(
      `/teacher-workbench/slide/edit?fileId=${props.pptCoursewareItem.id}&id=${props.pptCoursewareItem.id}&fileName=${props.pptCoursewareItem.name}&sourceId=${props.measuringTaskItemId}&platform=prepare`,
      '_blank',
    )
  } else {
    window.open(
      `/teacher-workbench/slide/preview?fileId=${props.pptCoursewareItem.id}&id=${props.pptCoursewareItem.id}&fileName=${props.pptCoursewareItem.name}&sourceId=${props.measuringTaskItemId}&platform=prepare`,
      '_blank',
    )
  }
}

const onPreviewClick = (pageNumber: number) => {
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'questionView',
      moduleId: props.extraData?.measuringId,
      taskId: props.extraData?.measuringTaskId,
      contentId: props.extraData?.measuringTaskItemId,
    },
    'course',
  )
  showModal.value = true

  pdfRenderSingle(pageNumber)
  currentPage.value = props.pptCoursewareItem.markPages.find(
    (page) => page.pageNumber === pageNumber,
  )
}
</script>

<style scoped lang="scss">
.ppt-task-modal {
  position: relative;

  .close-round-icon {
    position: absolute;
    right: -70px;
    bottom: 0px;
  }
}

.canvas-pdf-item {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mark-page-item {
  position: relative;
  width: 189px;
  height: 105px;
  overflow: hidden;
  border: 1px solid #dcd8e7;
  border-radius: 16px;

  &:hover {
    .mask {
      display: flex;
    }
  }

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgba(18, 37, 77, 0.6);
    backdrop-filter: blur(3.2px);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
