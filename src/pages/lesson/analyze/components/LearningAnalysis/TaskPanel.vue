<template>
  <div class="my-20px">
    <div class="task-header">
      <ExpandIcon
        class="cursor-pointer"
        :class="!openPanel ? 'rotate-180' : ''"
        @click="openPanel = !openPanel"
      />
      <span
        v-if="task.tag === 1"
        class="ml-16px w-40px h-24px lh-24px text-center text-12px font-600 color-#393548 border-1px border-#C5C1D4 rounded-8px"
        >自学</span
      >
      <span
        v-if="task.tag === 2"
        class="ml-16px w-40px h-24px lh-24px text-center text-12px font-600 color-#393548 border-1px border-#C5C1D4 rounded-8px"
        >快背</span
      >
      <span
        v-if="task.tag === 3"
        class="ml-16px w-40px h-24px lh-24px text-center text-12px font-600 color-#393548 border-1px border-#C5C1D4 rounded-8px"
        >讲授</span
      >
      <div class="text-20px font-600 ml-8px color-#393548">
        环节{{ task.rankIdx + 1 }}
      </div>
      <div class="text-20px font-600 ml-16px color-#393548 mx-24px">
        {{ task.name }}
      </div>
      <div class="text-14px font-400 color-#393548 flex-1">
        <span v-if="task.tag === 2">专题{{ task.itemCount }}</span>
        <span v-if="task.videoCount !== 0">微课{{ task.videoCount }}</span>
        <span v-if="task.tag === 3">课件{{ task.itemCount }}</span>
        <n-divider
          v-if="task.problemCount !== 0 && task.videoCount !== 0"
          vertical
          class="h-16px! mx-4px! bg-#393548!"
        />
        <span v-if="task.problemCount !== 0">题目{{ task.problemCount }}</span>
      </div>
      <n-popover
        v-if="task.subGroupUserCounts.length > 0"
        trigger="hover"
        arrow-class="paper-preferences-arrow-popover"
        content-class="paper-preferences-content-popover"
        class="paper-preferences-wrapper-popover"
        placement="bottom"
        :to="false"
        raw
      >
        <template #trigger>
          <div
            class="text-16x font-400 color-#393548 mr-24px lh-36px flex items-center cursor-pointer"
          >
            {{ task.subGroupUserCounts.length }} 个小组，{{
              getSubGroupUserCounts()
            }}人<InfoIcon class="w-16px h-16px ml-4px" />
          </div>
        </template>
        <div v-for="item in task.subGroupUserCounts" :key="item.subGroupId">
          {{ item.subGroupName }}-{{ item.userCount }}人
        </div>
      </n-popover>

      <div
        v-if="task.tag !== 3"
        class="text-16x font-400 color-#393548 mr-24px lh-39px"
      >
        已完成
        <span class="num">{{ task.finishedUserCount }}</span>
        人
      </div>
      <div
        v-if="task.tag !== 3"
        class="text-16x font-400 color-#393548 lh-39px"
      >
        未完成
        <span class="num">{{ task.unfinishedUserCount }}</span>
        人
      </div>
      <n-popover
        v-if="task.tag !== 3"
        trigger="hover"
        arrow-class="paper-preferences-arrow-popover"
        content-class="paper-preferences-content-popover"
        class="paper-preferences-wrapper-popover"
        placement="bottom"
        :to="false"
        raw
      >
        <template #trigger>
          <TipIcon class="w-22px h-22px ml-4px cursor-pointer" />
        </template>
        <div class="popover-overview-tip" style="width: 382px">
          <div class="popover-overview-tip-title">未完成:</div>
          <div class="popover-overview-tip-content">
            <span v-for="i in task.unfinishedUsers" :key="i.id">
              {{ i.name }}
            </span>
          </div>
        </div>
      </n-popover>
    </div>
    <div v-if="openPanel" class="flex mt-24px">
      <div class="task-item-list max-h-540px overflow-y-auto onion-scroll">
        <div
          v-for="item in taskItems"
          :key="item.id"
          class="task-item"
          :class="{ active: taskItem?.id === item.id }"
          @click="clickTaskItem(item, true, true)"
        >
          <span class="rank-idx">{{ item.rankIdx + 1 }}</span>
          <span class="icon">
            <VideoIcon
              v-if="['Video', 'Clip', 'SchoolVideo'].includes(item.sourceType)"
            />
            <ProblemIcon
              v-if="['Problem', 'SchoolProblem'].includes(item.sourceType)"
            />
            <SpecialTopicIcon
              v-if="item.sourceType === 'SpecialTopic'"
              class="w-32px h-32px"
            />
            <PPTIcon
              v-if="item.sourceType === 'PPTCourseware'"
              class="w-32px h-32px"
            />
          </span>
          <div class="flex-1 flex flex-col justify-center">
            <span class="text-ellipsis item-name" :title="item.name">
              {{ item.name }}
            </span>
            <div class="flex justify-between text-14px color-#57526C">
              <span
                v-if="item.sourceType !== 'PPTCourseware'"
                :class="finishingRate(item) < 80 ? 'color-#E7414D' : ''"
              >
                完成率{{ finishingRate(item) }}%
              </span>
              <span
                v-if="
                  item.sourceType === 'PPTCourseware' &&
                  item.pptMarkUserCount > 0
                "
                >{{ String(item.pptMarkUserCount) }}人有疑问</span
              >

              <span
                v-if="
                  ['Video', 'Clip', 'SchoolVideo'].includes(item.sourceType) &&
                  item.markUserCount > 0
                "
                class="ml-8px"
                :class="item.markUserCount >= 5 ? 'color-#E7414D' : ''"
                >{{ item.markUserCount }}个标记</span
              >

              <span
                v-if="['Problem', 'SchoolProblem'].includes(item.sourceType)"
                :class="correctRate(item) < 60 ? 'color-#E7414D' : ''"
              >
                正确率{{ correctRate(item) }}%
              </span>
            </div>
          </div>
        </div>
      </div>
      <n-divider vertical class="h-inherit! mx-24px!" />
      <div v-if="taskItem" class="flex-1 overflow-y-auto onion-scroll">
        <div
          v-if="
            ['Problem', 'SchoolProblem'].includes(taskItem.sourceType) &&
            taskProblemItem &&
            taskProblemBody
          "
          class="divide-y-[1px] divide-[#DCD8E7]"
        >
          <ProblemItem
            v-if="taskProblemBody"
            :key="taskProblemItem.problemId"
            :accuracy="
              Math.floor(
                (taskItem.correctUserCount / taskItem.finishedUserCount) * 100,
              )
            "
            :problem="taskProblemBody"
            :index="1"
            :correct-users="taskProblemItem.correctUsers"
            :wrong-users="taskProblemItem.wrongUsers"
            :answer-count="taskProblemItem.answerCount"
            :unfinished-user-count="taskProblemItem.unfinishedUserCount"
            :corrects-picture-count="taskProblemItem.correctsPictureCount"
            from="report"
            :task-problem-report="taskProblemReport!"
            :task-ai-explain="taskAiExplain"
            :task-ai-qa-stat="taskProblemAiQaStat"
            :measuring-id="measuringId"
            :measuring-task-id="task.id"
            :measuring-task-item-id="taskItem.id"
            @modify-student-ai-explain="modifyShowAiExplain"
            @update:show-answer-detail="showAnswerDetail = $event"
            @update:task-ai-explain="regenerateAiExplain"
            @update:show-ai-explain="handleShowAiExplain"
          >
            <template #insert> </template>
          </ProblemItem>
        </div>
        <div
          v-if="
            ['Video', 'Clip', 'SchoolVideo'].includes(taskItem.sourceType) &&
            taskVideoItem
          "
          class="min-w-799px"
        >
          <div class="flex items-center justify-between">
            <VideoIcon class="w-24px h-24px mr-12px" />
            <span class="flex-1 text-16px color-#57526C font-semibold">{{
              taskVideoItem.itemDetail.name
            }}</span>
            <div
              v-if="['Video', 'Clip'].includes(taskItem.sourceType)"
              class="flex items-center"
            >
              <n-switch
                :value="
                  taskVideoItem.itemDetail.aiTeacherSwitch === 'SwitchStateOpen'
                "
                @update:value="handleAiTeacherSwitch"
              />
              <span
                class="cursor-pointer color-#5E80FF mx-6px font-semibold text-16px"
                @click="
                  handleAiTeacherSwitch(
                    !taskVideoItem.itemDetail.aiTeacherSwitch,
                  )
                "
                >AI私教</span
              >
              <n-tooltip class="paper-preferences-wrapper-popover">
                <template #trigger>
                  <TipIcon class="w-20px h-20px" />
                </template>
                <span>仅洋葱学园视频提供AI私教答疑</span>
              </n-tooltip>
            </div>
            <OIWButton
              v-model:value="taskVideoCheckValue"
              size="small"
              class="ml-30px"
              type="info"
              round
              @click="onFullVideoClick"
            >
              播放
            </OIWButton>
          </div>
          <OIWRadioButtonGroup
            v-model:value="taskVideoCheckValue"
            size="small"
            class="mt-24px"
            @update:value="onTaskVideoCheckValueChange"
          >
            <OIWRadioButton value="1"
              >{{ taskItem.finishedUserCount }}人已完成</OIWRadioButton
            >
            <OIWRadioButton v-if="showInteractionTab" value="3"
              >交互题</OIWRadioButton
            >
            <OIWRadioButton value="2"
              >{{ taskItem.markUserCount }}个标记「没看懂」</OIWRadioButton
            >
          </OIWRadioButtonGroup>
          <div v-if="taskVideoCheckValue === '1'">
            <div
              v-if="taskVideoItem.userVideoList.length === 0"
              class="w-100% text-center"
            >
              <OIWStateBlock
                type="empty"
                title="暂无记录"
                style="margin-top: 24px"
              />
            </div>
            <div
              v-else
              class="w-100% px-14px h-56px bg-#F7F7F9 rounded-8px flex items-center mt-16px"
            >
              <span class="w-78px text-14px font-semibold color-#57526C"
                >学生</span
              >
              <span class="w-164px text-14px font-semibold color-#57526C"
                >学习进度</span
              >
              <span class="w-64px text-14px font-semibold color-#57526C"
                >学习时长</span
              >
              <span class="w-73px text-14px font-semibold color-#57526C"></span>
              <div class="w-1px h-100% bg-[#DCD8E7]"></div>
              <span class="ml-24px w-70px text-14px font-semibold color-#57526C"
                >学生</span
              >
              <span class="w-164px text-14px font-semibold color-#57526C"
                >学习进度</span
              >
              <span class="w-64px text-14px font-semibold color-#57526C"
                >学习时长</span
              >
              <span class="w-30px text-14px font-semibold color-#57526C"></span>
            </div>
            <div
              class="w-100% user-video-item-box"
              :class="[
                taskVideoItem.userVideoList.length % 2 === 0
                  ? 'is-even-numbers'
                  : 'is-odd-numbers',
              ]"
            >
              <div
                v-for="(user, index) in showUserVideoAll
                  ? taskVideoItem.userVideoList
                  : taskVideoItem.userVideoList.slice(0, 40)"
                :key="user.id"
                class="user-video-item inline-flex items-center text-14px font-semibold color-#57526C px-14px h-56px border-b-[1px] border-[#DCD8E7]"
                :class="{
                  'border-r-[1px] border-r-[#DCD8E7]': index % 2 === 0,
                }"
              >
                <span
                  class="w-70px mr-10px text-ellipsis"
                  :title="user.userName"
                  >{{ user.userName }}</span
                >
                <VideoRecordsProcess
                  class="w-153px"
                  :process="user.videoRecords"
                />
                <span class="ml-10px inline-block w-56px whitespace-nowrap">{{
                  formatDuration(user.duration)
                }}</span>
                <span
                  class="ml-10px w-56px text-14px font-semibold color-#FEA345"
                  >{{
                    user.stayDuration < 0.67 * user.duration &&
                    user.stayDuration > 0
                      ? '倍速播放'
                      : ''
                  }}</span
                >
              </div>
            </div>
            <div v-if="taskVideoItem.userVideoList.length > 40" class="mt-18px">
              <span
                class="cursor-pointer text-14px font-semibold color-#5E80FF mr-30px px-24px"
                @click="showUserVideoAll = !showUserVideoAll"
                >{{ showUserVideoAll ? '收起' : '展开全部' }}</span
              >
              <span
                v-if="!showUserVideoAll"
                class="text-14px font-semibold color-#57526C"
                >{{ taskVideoItem.userVideoList.length - 40 }}人被折叠</span
              >
            </div>
          </div>
          <div v-else-if="taskVideoCheckValue === '2'">
            <div
              v-if="taskVideoItem.marks.length === 0"
              class="w-100% text-center"
            >
              <OIWStateBlock
                type="empty"
                title="暂无记录"
                style="margin-top: 24px"
              />
            </div>
            <div v-else class="flex flex-wrap justify-between">
              <div
                v-for="mark in showMarkVideoAll
                  ? taskVideoItem.marks
                  : taskVideoItem.marks.slice(0, 30)"
                :key="mark.id"
                class="w-240px h-56px flex items-center px-24px font-14px"
                style="box-shadow: inset 0px -0.8px 0px 0px #dcd8e7"
                @click="onMarkPreviewClick(mark.timePoint)"
              >
                <PlayIcon class="mr-4px" />
                <span class="underline color-#5E80FF cursor-pointer mr-8px">{{
                  convertSecondsToTime(
                    mark.timePoint -
                      (taskVideoItem.itemDetail.videoClipExtra
                        ?.playRangeStart ?? 0),
                  )
                }}</span>
                {{ mark.userName }}
              </div>
              <i class="w-240px h-1px"></i><i class="w-240px h-1px"></i>
            </div>
            <div v-if="taskVideoItem.marks.length > 30" class="mt-38px">
              <span
                class="cursor-pointer text-14px font-semibold color-#5E80FF mr-30px"
                @click="showMarkVideoAll = !showMarkVideoAll"
                >{{ showMarkVideoAll ? '收起' : '展开全部' }}</span
              >
              <span
                v-if="!showMarkVideoAll"
                class="text-14px font-semibold color-#57526C"
                >{{ taskVideoItem.marks.length - 30 }}人被折叠</span
              >
            </div>
          </div>
          <div
            v-else-if="taskVideoCheckValue === '3' && videoInteraction"
            class="flex flex-wrap"
          >
            <div
              v-for="item in videoInteraction.statistics"
              :key="item.interactionId"
              class="mr-10px mt-16px relative video-interaction-item"
            >
              <img
                :src="item.imageUrl"
                alt=""
                class="w-190px h-104px rounded-8px"
              />
              <span class="video-interaction-time cursor-pointer mr-8px">
                {{
                  convertSecondsToTime(
                    item.time -
                      (taskVideoItem.itemDetail.videoClipExtra
                        ?.playRangeStart ?? 0),
                  )
                }}</span
              >
              <div class="video-interaction w-190px h-104px rounded-8px">
                <div
                  class="video-interaction-play"
                  @click="onMarkPreviewClick(item.time)"
                >
                  <PlayIcon class="play-icon w-16px h-16px mr-4px" />
                  播放视频
                </div>
                <div
                  class="video-interaction-play"
                  @click="
                    onUserVideoInteractionClick(
                      item.interactionId,
                      item.imageUrl,
                      convertSecondsToTime(
                        item.time -
                          (taskVideoItem.itemDetail.videoClipExtra
                            ?.playRangeStart ?? 0),
                      ),
                    )
                  "
                >
                  <ListIcon class="play-icon w-16px h-16px mr-4px" />
                  作答详情
                </div>
              </div>

              <div class="flex items-center mt-8px">
                <span class="text-14px color-#393548 font-bold mr-10px"
                  >{{ item.answerCount }}人作答</span
                >
                <span
                  v-if="item.accuracy > -1"
                  class="text-12px lh-12px px-6px py-4px rounded-4px font-bold"
                  :class="[
                    item.accuracy > 80
                      ? 'color-#4ECC5E bg-#4ECC5E/15'
                      : item.accuracy > 40
                        ? 'color-#FEA345 bg-#FEA345/15'
                        : 'bg-#fa5a65/15 color-#FA5A65',
                  ]"
                >
                  正确率{{ item.accuracy }}%
                </span>
              </div>
            </div>
          </div>
        </div>
        <SpecialTask
          v-if="taskItem.sourceType === 'SpecialTopic'"
          :special-info="taskSpecialTopicItem"
          :measuring-id="measuringId"
          :measuring-task-id="task.id"
          :measuring-task-item-id="taskItem.id || ''"
        />
        <PPTTask
          v-if="
            taskItem.sourceType === 'PPTCourseware' && taskPPTCoursewareItem
          "
          :ppt-courseware-item="taskPPTCoursewareItem"
          :extra-data="{
            measuringId,
            measuringTaskId: task.id,
            measuringTaskItemId: taskItem.id,
          }"
          :measuring-task-item-id="taskItem.id"
        />
        <VideoModel
          v-model:show="showVideoModal"
          :clip-id="videoPlayInfo.clipId"
          :special-course-id="videoPlayInfo.specialCourseId"
          :topic-id="videoPlayInfo.topicId"
          :baseVideo="{
            startTime,
          }"
          :video-clip="
            videoPlayInfo.isSelfClip
              ? {
                  start: videoPlayInfo.videoClip?.start,
                  end: videoPlayInfo.videoClip?.end,
                }
              : undefined
          "
        />
        <VideoModalMp4
          v-model:show="showMP4Modal"
          :video-id="mp4VideoId"
          :baseVideo="{
            startTime,
          }"
          :video-clip="
            videoPlayInfo.isSelfClip
              ? {
                  start: videoPlayInfo.videoClip?.start,
                  end: videoPlayInfo.videoClip?.end,
                }
              : undefined
          "
        />
      </div>
    </div>
    <UserVideoInteraction
      v-if="userVideoInteraction"
      v-model:show="showUserVideoInteraction"
      :user-video-interaction="userVideoInteraction"
      :user-video-interaction-image-url="userVideoInteractionImageUrl"
      :user-video-interaction-time="userVideoInteractionTime"
    />
  </div>
</template>

<script setup lang="tsx">
import type {
  TaskItem,
  TaskList,
  TaskProblemItem,
  TaskVideoItem,
  TaskProblemReport,
  TaskItemAiExplain,
  VideoInteractionStatistics,
  SpecialInfo,
  TaskProblemAiQaStat,
  TaskPPTCoursewareItem,
  TaskProblemReportRes,
} from './service'
import {
  formatDuration,
  getTaskItemsApi,
  getTaskProblemItemApi,
  getTaskVideoItemApi,
  getTaskItemProblemReportApi,
  getTaskItemAiExplainApi,
  getTaskProblemAiQaStatApi,
  putAiExplainApi,
  putShowAiExplainApi,
  getVideoInteractionStatisticsApi,
  getUserVideoInteractionApi,
  getMeasuringTaskItemSpecialTopicApi,
  getTaskItemPPTCoursewareApi,
  putAiTeacherSwitchApi,
} from './service'
import { getProblemDetailsByIdApi } from '@/pages/problem/service'
import {
  getVideoRecordDuration,
  processChoiceOptions,
  processAnswerReport,
} from './utils'
import ProblemItem from './ProblemItem.vue'
import VideoModel from '@/pages/presentation/components/VideoModel.vue'
import VideoRecordsProcess from '@/pages/lesson/analyze/components/LearningAnalysis/VideoRecordsProcess.vue'
import {
  OIWRadioButton,
  OIWRadioButtonGroup,
  OIWButton,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import {
  systemLessonPermission,
  specialCoursePermission,
} from '@/hooks/usePermission'
import {
  postSpecialTopicDetails,
  postSystemTopicDetails,
} from '@/pages/microVideo/service.ts'
import usePageVisibility from '@/hooks/usePageVisibility'
import ExpandIcon from '~icons/yc/task-expand'
import InfoIcon from '~icons/yc/info-round'
import VideoIcon from '~icons/yc/task-video'
import ProblemIcon from '~icons/yc/task-problem'
import SpecialTopicIcon from '~icons/yc/text-icon'
import PPTIcon from '~icons/yc/PPTIcon'
import TipIcon from '~icons/yc/tip'
import PlayIcon from '~icons/yc/play-black'
import ListIcon from '~icons/yc/list'
import UserVideoInteraction from './UserVideoInteraction.vue'
import { getSchoolProblemDetailApi } from '@/pages/schoolResource/schoolProblem/service.ts'
import VideoModalMp4 from '@/components/VideoPlayer/VideoModalMp4.vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import SpecialTask from './SpecialTask.vue'
import PPTTask from './PPTTask.vue'

const props = defineProps<{
  task: TaskList
  measuringId: string
}>()

const message = useOIWMessage()

const { isPageActive, isVideoPlaying, toggleVideoPlaying } = usePageVisibility()
const openPanel = ref(false)
const taskItems = ref<TaskItem[]>([]) // 任务下的课时列表
const taskItem = ref<TaskItem>() // 任务下选中的课时
const taskProblemItem = ref<TaskProblemItem>() // 任务下的题目课时
const taskProblemBody = ref<YcType.ProblemType>()
const taskProblemReport = ref<TaskProblemReportRes>() // 任务下的题目课时
const taskAiExplain = ref<TaskItemAiExplain>() // 任务下的ai讲解
const taskVideoItem = ref<TaskVideoItem>() // 任务下的微课课时
const taskSpecialTopicItem = ref<SpecialInfo>() // 任务下的专题课时
const showVideoModal = ref(false)
const startTime = ref(0)
const videoPlayInfo = ref<{
  topicId: string
  clipId?: string
  specialCourseId?: string
  isSelfClip?: boolean
  videoClip?: {
    start?: number
    end?: number
  }
}>({
  topicId: '',
})
const taskVideoCheckValue = ref('1') // 1: xx人已完成 2: XX人标记没看懂 3: 交互题
const showInteractionTab = ref(false)
const showUserVideoAll = ref(false) // 展开全部学生微课详情
const showMarkVideoAll = ref(false)
const showAnswerDetail = ref(false)
const videoInteraction = ref<VideoInteractionStatistics>()
const taskProblemAiQaStat = ref<TaskProblemAiQaStat>()
const taskPPTCoursewareItem = ref<TaskPPTCoursewareItem>()

const finishingRate = (item: TaskItem) => {
  if (item.finishedUserCount + item.unfinishedUserCount === 0) return 0
  return Math.floor(
    (item.finishedUserCount /
      (item.finishedUserCount + item.unfinishedUserCount)) *
      100,
  )
}
function convertSecondsToTime(_seconds: number) {
  const seconds = Math.floor(_seconds)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`
}
const getSubGroupUserCounts = () => {
  let num = 0
  props.task.subGroupUserCounts.forEach((item) => {
    num += item.userCount
  })
  return num
}

const onTaskVideoCheckValueChange = async (val: string) => {
  taskVideoCheckValue.value = val
  if (val === '2') {
    if (!taskVideoItem.value) return
    setVideoPlayInfo(taskVideoItem.value)
  }
  if (val === '3') {
    if (!taskItem.value) return
    const res = await getVideoInteractionStatisticsApi({
      itemId: taskItem.value.id,
    })
    videoInteraction.value = res
  }
}

// 交互题作答详情
const showUserVideoInteraction = ref(false)
const userVideoInteraction = ref<TaskProblemReport[]>()
const userVideoInteractionImageUrl = ref('')
const userVideoInteractionTime = ref('')
const onUserVideoInteractionClick = async (
  interactionId: string,
  imageUrl: string,
  time: string,
) => {
  userVideoInteractionImageUrl.value = imageUrl
  userVideoInteractionTime.value = time
  const res = await getUserVideoInteractionApi({
    itemId: taskItem.value?.id || '',
    interactionId,
  })
  userVideoInteraction.value = res.rows
  showUserVideoInteraction.value = true
}
const correctRate = (item: TaskItem) => {
  if (item.finishedUserCount === 0) return 0
  return Math.floor((item.correctUserCount / item.finishedUserCount) * 100)
}

const timer = ref<any | null>(null)
onMounted(async () => {
  if (props.task.rankIdx === 0) {
    openPanel.value = true
    await getTaskItem(props.measuringId, props.task.id)
  }
  timer.value = setInterval(() => {
    // 刷新优化1: 只刷新当前展开的task数据
    if (openPanel.value && isPageActive.value && !isVideoPlaying.value) {
      getTaskItem(props.measuringId, props.task.id, openPanel.value)
      // if (taskVideoCheckValue.value === '3' && taskItem.value) {
      //   getVideoInteractionStatisticsApi({
      //     itemId: taskItem.value.id,
      //   }).then((res) => {
      //     videoInteraction.value = res
      //   })
      // }
    }
  }, 10000)
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})

const regenerateAiExplain = async () => {
  if (taskAiExplain.value?.aiExplainProgress === 'ing') {
    return
  }
  const isSuccess = await putAiExplainApi({
    problemType: 'SceneAiClassTaskProblem',
    measuringId: props.measuringId,
    measuringTaskItemId: taskItem.value?.id || '',
  })
  if (isSuccess) {
    taskAiExplain.value = await getTaskItemAiExplainApi({
      measuringId: props.measuringId,
      measuringTaskItemId: taskItem.value?.id || '',
      deleteCache: true,
    })
  } else {
    message.error('重新生成失败')
  }
}

// 获取学情分析任务子项列表.
const getTaskItem = async (
  measuringId: string,
  measuringTaskId: string,
  notFetch = true,
) => {
  const data = await getTaskItemsApi({
    measuringId,
    measuringTaskId,
  })
  taskItems.value = data.rows
  console.log('taskItems.value', taskItems.value)
  taskItem.value = taskItems.value.find(
    (item) => item.id === taskItem.value?.id,
  )
  if (notFetch) {
    await clickTaskItem(taskItem.value || taskItems.value[0])
  }
}

// 获取习题详情
const getProblemDetail = async (problemId: string, sourceType: string) => {
  if (sourceType === 'SchoolProblem') {
    // 校本资源-习题
    const res = await getSchoolProblemDetailApi({
      problemsId: [problemId],
    })
    return res.data[0]
  } else {
    // 洋葱习题
    const problemDetails = await getProblemDetailsByIdApi([problemId])
    return problemDetails[0]
  }
}

const getTaskProblemReport = async (
  problemBody: YcType.ProblemType,
  item: TaskItem,
) => {
  taskProblemReport.value = await getTaskItemProblemReportApi({
    measuringId: props.measuringId,
    measuringTaskItemId: item.id,
  })

  if (problemBody.type === 'single_choice') {
    taskProblemReport.value.answerRecords =
      taskProblemReport.value.answerRecords.map((item) => {
        return {
          ...item,
          rows: processChoiceOptions(problemBody, item.rows),
        }
      })
  } else {
    taskProblemReport.value.answerRecords =
      taskProblemReport.value.answerRecords.map((item) => {
        if (item.problemType === 'exam') {
          return item
        } else {
          return {
            ...item,
            rows: processAnswerReport(problemBody, item.rows),
          }
        }
      })
  }
}

watch(showAnswerDetail, (newVal) => {
  if (newVal) {
    getTaskProblemReport(taskProblemBody.value!, taskItem.value!)
  }
})

const clickTaskItem = async (
  item: TaskItem,
  needBury?: boolean,
  userClick?: boolean,
) => {
  if (userClick) {
    taskVideoCheckValue.value = '1'
  }
  taskItem.value = item
  if (item.sourceType === 'Problem' || item.sourceType === 'SchoolProblem') {
    taskProblemItem.value = await getTaskProblemItemApi({
      measuringId: props.measuringId,
      measuringTaskItemId: item.id,
    })
    taskProblemBody.value = await getProblemDetail(
      taskProblemItem.value.problemId,
      item.sourceType,
    )
    taskAiExplain.value = await getTaskItemAiExplainApi({
      measuringId: props.measuringId,
      measuringTaskItemId: item.id,
      deleteCache: false,
    })
    // 刷新优化2: 展开作答详情时，获取作答详情
    if (showAnswerDetail.value) {
      getTaskProblemReport(taskProblemBody.value, item)
    }
    if (showAiExplain.value) {
      const res = await getTaskProblemAiQaStatApi({
        measuringId: props.measuringId,
        measuringTaskItemId: item.id,
      })
      taskProblemAiQaStat.value = res
    }
  }
  if (
    item.sourceType === 'Video' ||
    item.sourceType === 'Clip' ||
    item.sourceType === 'SchoolVideo'
  ) {
    const res = await getTaskVideoItemApi({
      measuringId: props.measuringId,
      measuringTaskItemId: item.id,
    })
    res.userVideoList.forEach((item) => {
      // 计算观看时长百分比
      const { process } = getVideoRecordDuration(item.videoRecords)
      item.process = process
    })
    // 按照观看时长百分比从小到大排序
    res.userVideoList = res.userVideoList.sort((a, b) => {
      return a.process - b.process
    })
    taskVideoItem.value = res
  }

  if (
    taskItem.value?.sourceType === 'Video' ||
    taskItem.value?.sourceType === 'Clip'
  ) {
    const res = await getVideoInteractionStatisticsApi({
      itemId: taskItem.value.id,
    })
    videoInteraction.value = res
    if (res.statistics.length > 0) {
      showInteractionTab.value = true
    } else {
      showInteractionTab.value = false
    }
  }

  if (item.sourceType === 'SpecialTopic') {
    const res = await getMeasuringTaskItemSpecialTopicApi({
      measuringId: props.measuringId,
      measuringTaskItemId: item.id,
    })
    taskSpecialTopicItem.value = res
    console.log('res', res)
  }
  if (item.sourceType === 'PPTCourseware') {
    console.log('item', item)
    const res = await getTaskItemPPTCoursewareApi({
      measuringId: props.measuringId,
      measuringTaskItemId: item.id,
    })
    console.log('res', res)
    taskPPTCoursewareItem.value = res
    console.log('taskPPTCoursewareItem.value', taskPPTCoursewareItem.value)
    // taskPPTCoursewareItem.value = res
  }

  if (needBury) {
    buryPoint(
      'clickCourseDetailPageButton',
      {
        pageName: 'report',
        button: 'content',
        moduleId: props.measuringId,
        taskId: props.task.id,
        contentId: taskItem.value.id,
      },
      'course',
    )
  }
}

// 切换ai私教开关
const handleAiTeacherSwitch = async (val: boolean) => {
  taskVideoItem.value!.itemDetail.aiTeacherSwitch = val
    ? 'SwitchStateOpen'
    : 'SwitchStateClose'
  putAiTeacherSwitchApi({
    itemId: taskItem.value?.id || '',
    aiTeacherSwitch: val ? 'SwitchStateOpen' : 'SwitchStateClose',
  })
}

const showMP4Modal = ref(false)
watch(showMP4Modal, (newVal) => {
  toggleVideoPlaying(newVal)
})
watch(showVideoModal, (newVal) => {
  toggleVideoPlaying(newVal)
})

const mp4VideoId = ref('')

const onMarkPreviewClick = async (timePoint: number) => {
  startTime.value = timePoint
  onPreviewClick()
}

const onFullVideoClick = async () => {
  startTime.value = 0
  onPreviewClick()
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'play',
      moduleId: props.measuringId,
      taskId: props.task.id,
      contentId: taskItem.value?.id || '',
    },
    'course',
  )
}
// 点击视频预览
const onPreviewClick = async () => {
  if (!taskVideoItem.value) return
  // 校本资源--视频播放
  videoPlayInfo.value = {
    topicId: taskVideoItem.value.itemDetail.topicId,
  }
  if (taskVideoItem.value.itemDetail.sourceType === 'SchoolVideo') {
    mp4VideoId.value = taskVideoItem.value.itemDetail.sourceId
    if (taskVideoItem.value.itemDetail.videoClipExtra?.type === 'self_clip') {
      videoPlayInfo.value.isSelfClip = true
      videoPlayInfo.value.videoClip = {
        start: taskVideoItem.value.itemDetail.videoClipExtra.playRangeStart,
        end: taskVideoItem.value.itemDetail.videoClipExtra.playRangeEnd,
      }
    }
    showMP4Modal.value = true

    return
  }
  // 洋葱视频资源鉴权
  if (taskVideoItem.value.itemDetail.specialCourseId !== '') {
    const auth = await specialCoursePermission(
      taskVideoItem.value.itemDetail.specialCourseId as string,
    )
    const response = (
      await postSpecialTopicDetails({
        topicIds: [taskVideoItem.value.itemDetail.topicId],
      })
    ).data
    const isFreeTime = response[0]?.isFreeTime
    const pay = response[0]?.pay
    if (!isFreeTime && pay && !auth) {
      message.info(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  } else {
    const auth = await systemLessonPermission({
      subjectId: taskVideoItem.value.itemDetail.subjectId,
      stageId: taskVideoItem.value.itemDetail.stageId,
    })
    const response = (
      await postSystemTopicDetails({
        topicIds: [taskVideoItem.value.itemDetail.topicId],
      })
    ).data
    const isFreeTime = response[0]?.isFreeTime
    const pay = response[0]?.pay
    if (!isFreeTime && pay && !auth) {
      message.info(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  }
  setVideoPlayInfo(taskVideoItem.value)
  showVideoModal.value = true
}

const setVideoPlayInfo = (item: TaskVideoItem) => {
  // 视频播放
  if (item.itemDetail.videoClipExtra?.clipId) {
    videoPlayInfo.value = {
      topicId: item.itemDetail.topicId,
      clipId: item.itemDetail.videoClipExtra.clipId,
    }
  } else if (item.itemDetail.specialCourseId !== '') {
    videoPlayInfo.value = {
      topicId: item.itemDetail.topicId,
      specialCourseId: item.itemDetail.specialCourseId,
    }
  } else if (item.itemDetail.videoClipExtra?.type === 'self_clip') {
    videoPlayInfo.value = {
      topicId: item.itemDetail.topicId,
      isSelfClip: true,
      videoClip: {
        start: item.itemDetail.videoClipExtra.playRangeStart,
        end: item.itemDetail.videoClipExtra.playRangeEnd,
      },
    }
  } else {
    videoPlayInfo.value = {
      topicId: item.itemDetail.topicId,
    }
  }
}

watch(
  () => openPanel.value,
  () => {
    if (openPanel.value && taskItems.value.length === 0) {
      getTaskItem(props.measuringId, props.task.id)
    }
    if (!taskItem.value && taskItems.value.length !== 0) {
      taskItem.value = taskItems.value[0]
      clickTaskItem(taskItem.value)
    }
  },
)

const showAiExplain = ref(false)
const handleShowAiExplain = async (val: boolean) => {
  showAiExplain.value = val
  const res = await getTaskProblemAiQaStatApi({
    measuringId: props.measuringId,
    measuringTaskItemId: taskItem.value?.id || '',
  })
  taskProblemAiQaStat.value = res
  buryPoint(
    'clickTeacherWorkBenchAIExplainButton',
    {
      pageName: 'AI课堂学情分析',
      moduleId: props.measuringId,
      taskId: props.task.id,
      contentId: taskItem.value?.id,
    },
    'course',
  )
}

const modifyShowAiExplain = async (val: boolean) => {
  showAiExplain.value = val
  if (!taskAiExplain.value) return
  taskAiExplain.value = {
    ...taskAiExplain.value,
    showAiExplain: val,
  }
  try {
    await putShowAiExplainApi({
      measuringId: props.measuringId,
      measuringTaskItemId: taskItem.value?.id,
      showAiExplain: val,
    })
    taskAiExplain.value = await getTaskItemAiExplainApi({
      measuringId: props.measuringId,
      measuringTaskItemId: taskItem.value?.id || '',
      deleteCache: true,
    })
  } catch (error) {
    taskAiExplain.value = {
      ...taskAiExplain.value,
      showAiExplain: !val,
    }
  }
}
</script>

<style lang="scss" scoped>
.video-interaction-item {
  &:hover {
    .video-interaction {
      display: flex;
    }
  }

  .video-interaction-time {
    position: absolute;
    bottom: 36px;
    left: 8px;
    display: inline-block;
    width: 53px;
    height: 24px;
    // padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
    line-height: 24px;
    color: #ffffff;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
  }
}

.video-interaction {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(18, 37, 77, 0.6);
  backdrop-filter: blur(4px);
}

.video-interaction-play {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84px;
  height: 28px;
  margin: 4px 0;
  font-size: 12px;
  font-weight: 600;
  color: #393548;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 100px;

  .play-white-icon {
    display: none;
  }

  &:hover {
    background: #ffd633;
    border-radius: 100px;
  }
}

.user-video-item-box .user-video-item:nth-child(odd) {
  border-right: 1px solid #dcd8e7;
}

.task-header {
  display: flex;
  align-items: center;
  width: 1200px;
  height: 66px;
  padding: 0 24px;

  /* 灰（标签色）表头色 */
  background: #f7f7f9;
  border-radius: 12px;
  opacity: 1;

  .num {
    font-family: MiSansHeavy;
    font-size: 26px;
    font-weight: 700;
  }
}

.is-odd-numbers {
  border-bottom: 1px solid #dcd8e7;

  .user-video-item:last-child {
    border-bottom: none;
  }
}

.task-item-list {
  // max-height: 465px;
  // overflow-y: auto;
  // width: 346px;

  .task-item {
    display: flex;
    align-items: center;
    width: 346px;
    height: 72px;
    padding-right: 40px;
    padding-left: 24px;
    margin: 10px 0;
    cursor: pointer;
    // background: #f4f6ff;
    border-radius: 16px;

    .rank-idx {
      margin-right: 16px;
      font-size: 20px;
      font-weight: normal;
      line-height: 20px;
      color: #393548;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      margin-right: 16px;
      background: #f4f6ff;
      border-radius: 12px;
    }

    .item-name {
      width: 193px;
      font-size: 16px;
      font-variation-settings: 'opsz' auto;
      font-weight: 600;
      line-height: 24px;

      /* n8标题文本 */
      color: #393548;
      letter-spacing: 0px;
    }

    &.active {
      background: #f4f6ff;

      .rank-idx {
        font-family: MiSansHeavy;
        font-size: 20px;
        font-weight: 900;
        line-height: 20px;

        /* b5 标准色 辅助按钮default */
        color: #5e80ff;
        letter-spacing: 0px;
      }

      .icon {
        background: #fff !important;
      }
    }
  }
}
</style>
