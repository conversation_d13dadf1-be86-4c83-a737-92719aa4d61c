<template>
  <div class="mt-16px">
    <div class="flex items-center text-14px color-#393548">
      <CorrectIcon
        v-if="item.isCorrect"
        class="w-18px h-18px mr-8px flex-shrink-0"
      />
      <WrongIcon
        v-else
        class="w-18px h-18px mr-8px flex-shrink-0"
      />
      <n-popover
        v-if="item.answers.join(' | ').length > 100"
        trigger="hover"
        arrow-class="paper-preferences-arrow-popover"
        content-class="paper-preferences-content-popover"
        class="paper-preferences-wrapper-popover"
        placement="top"
        :to="false"
        raw
      >
        <template #trigger>
          <KatexRender
            class="cursor-pointer answer-popover"
            :content="getAnswerContent(item.answers)"
          />
        </template>
        <KatexRender
          style="max-width: 700px; color: #fff"
          :content="getAnswerContent(item.answers)"
        />
      </n-popover>
      <KatexRender v-else :content="getAnswerContent(item.answers)" />
      <span v-if="item.users.length > 0" class="ml-8px"
        >({{ item.users.length }}人)</span
      >
    </div>
    <div
      v-if="item.users.length > 0"
      class="text-14px color-#393548 lh-20px mt-4px"
    >
      <span
        v-for="(user, userIndex) in item.users"
        :key="user.id"
        :class="
          user.correctsPicture !== '' || problemType === 'exam'
            ? 'corrects-picture-user cursor-pointer'
            : ''
        "
        @click="toggleUserRevisalDrawer(user)"
      >
        {{ user.name }}
        <RevisalIcon
          v-if="user.correctsPicture !== ''"
          class="w-16px h-16px mx-2px"
        />
        {{ userIndex < item.users.length - 1 ? '、' : '' }}
      </span>
    </div>
    <div v-else class="text-14px color-#393548 lh-20px mt-4px">暂无</div>
  </div>
</template>

<script setup lang="ts">
import { KatexRender } from '@guanghe-pub/onion-problem-render'
import WrongIcon from '~icons/yc/wrong'
import CorrectIcon from '~icons/yc/correct'
import RevisalIcon from '~icons/yc/revisal'
import type { TaskProblemReport } from './service'

interface ProblemAnswerItemProps {
  item: TaskProblemReport
  problemType: string
  idx: number
}

const props = defineProps<ProblemAnswerItemProps>()

const emit =
  defineEmits<(e: 'userClick', user: TaskProblemReport['users'][0], problemType: string, idx: number) => void>()

const getAnswerContent = (answers: string[]) => {
  return answers.join(' | ')
}

const toggleUserRevisalDrawer = (user: TaskProblemReport['users'][0]) => {
  if (user.correctsPicture !== '' || props.problemType === 'exam') {
    emit('userClick', user, props.problemType, props.idx)
  }
}
</script>

<style scoped lang="scss">
.answer-popover {
  max-width: 692px;
  height: 1.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-name {
  // cursor: pointer;
}

.corrects-picture-user {
  display: inline-flex;
  align-items: center;
  color: #5e80ff;
}

.corrects-picture-user:hover {
  text-decoration: underline;
  cursor: pointer;
}
</style>
