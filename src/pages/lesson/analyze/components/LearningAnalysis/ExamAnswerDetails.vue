<template>
  <OIWModal
    v-model:show="modalShow"
    preset="card"
    :closable="false"
    size="large"
    style="width: 1130px; height: 752px"
    class="user-video-interaction-modal"
  >
    <div
      v-if="answerDetail && !showUserDetail"
      class="inline-flex flex-wrap gap-16px max-h-710px! overflow-y-auto onion-scroll"
    >
      <div
        v-for="(item, index) in answerDetail.rows"
        :key="index"
        class="inline-flex flex-wrap gap-16px"
      >
        <div
          v-for="answer in item.answers"
          :key="answer"
          class="inline-flex flex-wrap gap-16px"
        >
          <div
            v-for="user in item.users"
            :key="user.id"
            class="w-253px cursor-pointer"
            @click="toggleUserDetail(user, item.isCorrect)"
          >
            <div class="flex items-center mb-8px">
              <WrongIcon
                v-if="!item.isCorrect && problemType !== 'combination'"
                class="mr-8px"
              />
              <CorrectIcon
                v-else-if="problemType !== 'combination'"
                class="mr-8px"
              />
              <span class="font-600 color-#393548 text-14px">{{
                user.name
              }}</span>
              <RevisalIcon v-if="user.correctsPicture !== ''" class="ml-4px" />
            </div>
            <div
              v-if="user.answerPicture.length > 0"
              class="w-253px h-122px user-answer-picture"
            >
              <img :src="user.answerPicture[0]" alt="" class="w-100% h-100%" />
            </div>
            <div
              v-for="answerItem in user.answer"
              :key="answerItem"
              class="text-14px color-#393548 lh-20px mt-8px overflow-hidden whitespace-normal"
              style="
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
              "
            >
              {{ answerItem }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showUserDetail" class="user-detail">
      <div v-if="userDetail" class="h-650px! overflow-y-auto mb-16px">
        <div class="user-detail-header-title flex items-center my-8px">
          <WrongIcon
            v-if="!userDetail.isCorrect && problemType !== 'combination'"
            class="mr-8px"
          />
          <CorrectIcon
            v-else-if="problemType !== 'combination'"
            class="mr-8px"
          />
          <span class="font-600 color-#393548 text-20px">{{
            userDetail.name
          }}</span>
        </div>
        <div class="text-14px font-600 color-#393548 lh-20px my-8px">
          作答详情
        </div>
        <div class="pb-60px">
          <OriginalPaper
            v-if="userDetail.answerPicture.length > 0"
            class="w-100% h-532px! rounded-16px! p-0!"
            disabled-wheel
            :photoUrls="userDetail.answerPicture"
          >
          </OriginalPaper>
        </div>
        <div>
          <div
            v-for="answer in userDetail.answer"
            :key="answer"
            class="text-14px color-#393548 lh-20px mt-8px"
          >
            {{ answer }}
          </div>
        </div>
        <div
          v-if="userDetail.correctsPicture !== ''"
          class="text-14px font-600 color-#393548 lh-20px my-8px"
        >
          已订正
        </div>
        <div v-if="userDetail.correctsPicture !== ''" class="pb-60px">
          <OriginalPaper
            class="w-100% h-532px! rounded-16px! p-0!"
            disabled-wheel
            :photoUrls="[userDetail.correctsPicture]"
          >
          </OriginalPaper>
        </div>
      </div>

      <div class="flex items-center justify-end">
        <OIWButton type="primary" @click="handleBack"> 返回 </OIWButton>
      </div>
    </div>
    <CloseIcon class="close-round-icon" @click="modalShow = false" />
  </OIWModal>
</template>

<script setup lang="ts">
import { OIWModal, OIWButton } from '@guanghe-pub/onion-ui-web'
import type { TaskProblemReportRes } from './service'
import WrongIcon from '~icons/yc/wrong'
import CorrectIcon from '~icons/yc/correct'
import RevisalIcon from '~icons/yc/revisal'
import OriginalPaper from '@/pages/practice/report/answerCard/components/OriginalPaper.vue'
import CloseIcon from '~icons/yc/close-round'

const props = defineProps<{
  show: boolean
  answerDetail: TaskProblemReportRes['answerRecords'][0] | undefined
  problemType: string
  userId: string
}>()
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'update:user-id', val: string): void
}>()
const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

const computedUserId = computed(() => {
  return props.userId
})
watch(
  () => [props.userId, props.show],
  ([val, show]) => {
    if (val !== '' && show) {
      // nextTick(() => {
      showUserDetail.value = true
      userDetail.value = props.answerDetail?.rows
        .find((row) => row.users.find((user) => user.id === val))
        ?.users.find((user) => user.id === val)
      console.log('>>>>>>>>>>', props.answerDetail, userDetail.value)
      // })
    } else {
      showUserDetail.value = false
    }
  },
)
const showUserDetail = ref(false)
const userDetail =
  ref<TaskProblemReportRes['answerRecords'][0]['rows'][0]['users'][0]>()
const toggleUserDetail = (
  user: TaskProblemReportRes['answerRecords'][0]['rows'][0]['users'][0],
  isCorrect: boolean,
) => {
  showUserDetail.value = true
  userDetail.value = user
  userDetail.value.isCorrect = isCorrect
}

const handleBack = () => {
  showUserDetail.value = false
  emits('update:user-id', '')
}
</script>

<style scoped lang="scss">
::v-deep(.original-paper .control-box) {
  bottom: -52px !important;
}

.user-video-interaction-modal {
  .close-round-icon {
    position: absolute;
    right: -70px;
    bottom: 0px;
  }
}

.user-answer-picture {
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid #c5c1d4;
  border-radius: 16px;
}

.user-detail {
  display: flex;
  flex-direction: column;
}
</style>
