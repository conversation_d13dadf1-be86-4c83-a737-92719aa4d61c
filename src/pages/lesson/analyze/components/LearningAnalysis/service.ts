import request from '@/utils/request.ts'
// import { apiSchoolDomain } from '@/utils/apiUrl.ts'
// import dayjs from 'dayjs'

export const formatDuration = (durationInSeconds: number) => {
  return (durationInSeconds / 60).toFixed(1).toString() + '分钟'
}

export interface TaskList {
  id: string
  /**
   * 序号.
   */
  rankIdx: number
  /**
   * 任务名称.
   */
  name: string
  /**
   * 视频数.
   */
  videoCount: number
  /**
   * 题数.
   */
  problemCount: number
  /**
   * 完成人数.
   */
  finishedUserCount: number
  /**
   * 未完成人数.
   */
  unfinishedUserCount: number
  unfinishedUsers: {
    id: string
    name: string
  }[]
  subGroupUserCounts: {
    subGroupId: string
    subGroupName: string
    userCount: number
  }[]
  tag: 1 | 2 | 3 // 1:自学 2:快背 3:PPT课件
  itemCount: number
}

export const getTaskList = async (params: { measuringId: string }) => {
  return request.get<{ rows: TaskList[] }>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-stat',
    {
      params,
    },
  )
}

export interface TaskItem {
  id: string
  /**
   * 序号.
   */
  sourceType:
    | 'ItemTypeNone'
    | 'Video'
    | 'Problem'
    | 'Clip'
    | 'SchoolVideo'
    | 'SchoolProblem'
    | 'SpecialTopic'
    | 'PPTCourseware'
  /**
   * 任务名称.
   */
  name: string
  pptMarkUserCount: number

  /**
   * 标记人数..
   */
  markUserCount: number
  /**
   * 正确人数..
   */
  correctUserCount: number
  /**
   * 完成人数.
   */
  finishedUserCount: number
  /**
   * 未完成人数.
   */
  unfinishedUserCount: number
  rankIdx: number
}
// 获取学情分析任务子项列表.
export const getTaskItemsApi = async (params: {
  measuringId: string
  measuringTaskId: string
}) => {
  return request.get<{ rows: TaskItem[] }>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-stat',
    {
      params,
    },
  )
}

export interface TaskProblemItem {
  problemId: string
  answerCount: number // 作答人数.
  correctCount: number // 正确人数.
  wrongCount: number // 错误人数.
  correctUsers: string[]
  wrongUsers: string[]
  correctsPictureCount: number // 订正人数.
  unfinishedUserCount: number // 未作答人数.
}
// 获取学情分析任务内容题目详情.
export const getTaskProblemItemApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.get<TaskProblemItem>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-problem',
    {
      params,
    },
  )
}

export const putAiExplainApi = async (params: {
  problemType: string
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.put<{ success: boolean }>(
    '/teacher-ai-class/teacher/ai-explain',
    params,
  )
}
export interface TaskItemAiExplain {
  enableAiExplain: boolean // 是否开启ai讲解.
  enableAiQa: boolean // 是否开启ai问答.
  showAiExplain: boolean // 是否展示ai讲解.
  aiExplainProgress: string // 生成ai讲解进度，ing：生成中、done：完成、failed：已失败.
  aiExplain: string // 生成ai讲解内容.
  latency: number // 生成ai讲解耗时，单位：秒.
}
const aiExplainCache = new Map<string, TaskItemAiExplain>()

// 获取ai讲解内容.
export const getTaskItemAiExplainApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
  deleteCache: boolean
}) => {
  // console.log('aiExplainCache', aiExplainCache, params.useCache)
  if (params.deleteCache) {
    aiExplainCache.delete(params.measuringTaskItemId)
  }
  if (aiExplainCache.has(params.measuringTaskItemId)) {
    return aiExplainCache.get(params.measuringTaskItemId)!
  } else {
    const res = await request.get<TaskItemAiExplain>(
      '/teacher-ai-class/teacher/item-ai-explain',
      {
        params,
      },
    )
    if (res.aiExplainProgress === 'done') {
      aiExplainCache.set(params.measuringTaskItemId, res)
    }
    return res
  }
}

export interface VideoClipExtra {
  playRangeStart: number
  playRangeEnd: number
  clipId: string
  type: string
}

export interface ItemDetail {
  measuringTaskId: string
  sourceType: string
  name: string
  sourceId: string
  topicId: string
  duration: number
  stageId: number
  subjectId: number
  publisherId: number
  semesterId: number
  id: string
  specialCourseId: string
  answerCount: number
  videoClipExtra: VideoClipExtra
  rankIdx: number
  aiTeacherSwitch: 'SwitchStateNone' | 'SwitchStateOpen' | 'SwitchStateClose'
}

export interface VideoRecord {
  id: string
  measuringTaskItemId: string
  userId: string
  userName: string
  isFinished: boolean
  duration: number //  视频播放时长 V
  stayDuration: number // 视频播放器停留时长 D https://guanghe.feishu.cn/docx/Hxm2dBn4CoWk2wxM8F2c17XJned
  timePoint: number
  videoRecords: number[]
  createdAt: string
  updatedAt: string
  process: number // 观看时长百分比 前段极端
}

export interface Mark {
  createdAt: string // ISO 8601 格式的日期字符串
  id: string // 唯一标识符
  measuringTaskItemId: string // 测量任务项的唯一标识符
  screenshotUrl: string // 截图 URL，可能为空字符串
  tagName: string // 标签名称
  timePoint: number // 时间点（通常以秒为单位）
  updatedAt: string | null // 更新日期，可能为 null
  userId: string // 用户唯一标识符
  userName: string // 用户名
  videoPositionTag: string // 视频位置标签
}

export interface TaskVideoItem {
  itemDetail: ItemDetail
  marks: Mark[]
  userVideoList: VideoRecord[]
}
// 获取学情分析任务内容视频详情.
export const getTaskVideoItemApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.get<TaskVideoItem>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-video',
    {
      params,
    },
  )
}

// 切换ai私教开关
export const putAiTeacherSwitchApi = async (params: {
  itemId: string
  aiTeacherSwitch: 'SwitchStateNone' | 'SwitchStateOpen' | 'SwitchStateClose'
}) => {
  return request.put('/teacher-ai-class/teacher/ai-teacher-switch', params)
}
export interface TaskProblemReport {
  answers: string[]
  users: {
    answer: string[]
    id: string
    name: string
    correctsPicture: string
    answerPicture: string[]
    isCorrect?: boolean
  }[]
  isCorrect: boolean
  [key: string]: unknown
}
export interface TaskProblemReportRes {
  answerRecords: {
    idx: number
    problemType: string
    rows: TaskProblemReport[]
  }[]
  rows: TaskProblemReport[]
  correctUserCount: number
  wrongUserCount: number
  unfinishedUserCount: number
  unfinishedUsers: {
    id: string
    name: string
  }[]
}
// 获取学情分析任务内容题目回答详情.
export const getTaskItemProblemReportApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
  problemId?: string
}) => {
  return request.get<TaskProblemReportRes>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-problem/report',
    {
      params,
    },
  )
}

export interface PutShowAiExplainReq {
  /**
   * 任务id.
   */
  measuringTaskItemId?: string
  /**
   * 课时id.
   */
  measuringId?: string
  /**
   * 是否展示ai讲解.
   */
  showAiExplain?: boolean
  [k: string]: unknown
}

export type PutShowAiExplainRes = Record<string, unknown>

/**
 * @description 设置是否显示ai讲解.
 * https://yapi.yc345.tv/project/2740/interface/api/124334
 * <AUTHOR>
 * @date 2025-03-27
 * @export
 * @param {PutShowAiExplainReq} data
 * @returns {Promise<PutShowAiExplainRes>}
 */
export const putShowAiExplainApi = (
  data: PutShowAiExplainReq,
): Promise<PutShowAiExplainRes> => {
  return request.put<PutShowAiExplainRes>(
    `/teacher-ai-class/teacher/show-ai-explain`,
    data,
  )
}

export interface VideoInteractionStatistics {
  topicId: string
  videoId: string
  statistics: {
    accuracy: number
    answerCount: number
    imageUrl: string
    interactionId: string
    time: number
  }[]
}
// 获取内容交互题统计.
export const getVideoInteractionStatisticsApi = async (params: {
  itemId: string
}) => {
  return request.get<VideoInteractionStatistics>(
    '/teacher-ai-class/teacher/dashboard/video-interaction-statistics',
    {
      params,
    },
  )
}

export interface UserVideoInteraction {
  userId: string
  name: string
  answer: string[]
  isCorrect: boolean
}
// 获取内容交互题记录.
export const getUserVideoInteractionApi = async (params: {
  itemId: string
  interactionId: string
}) => {
  const response = await request.get<{
    userVideoInteraction: UserVideoInteraction[]
  }>('/teacher-ai-class/teacher/dashboard/user-video-interaction', {
    params,
  })

  // 将返回数据转换为TaskProblemReport格式
  const result: TaskProblemReport[] = []
  const answerMap = new Map<
    string,
    {
      users: {
        id: string
        name: string
        correctsPicture: string
      }[]
      isCorrect: boolean
    }
  >()

  response.userVideoInteraction.forEach((user) => {
    const userInfo = {
      id: user.userId,
      name: user.name,
      correctsPicture: '',
    }

    // 将答案转换为字符串作为Map的key
    const answerKey = user.answer.join('|')
    if (!answerMap.has(answerKey)) {
      answerMap.set(answerKey, {
        users: [],
        isCorrect: user.isCorrect,
      })
    }

    answerMap.get(answerKey)?.users.push(userInfo)
  })

  // 将Map转换为TaskProblemReport数组
  // 将Map转换为数组并按字母顺序排序
  const entries = Array.from(answerMap.entries())

  // 检查是否包含字母选项（A、B、C、D...）
  const hasLetterOptions = entries.some(([key]) =>
    key.split('|').some((answer) => /^[A-Z]$/.test(answer)),
  )

  // 如果包含字母选项，按字母顺序排序
  if (hasLetterOptions) {
    entries.sort(([keyA], [keyB]) => {
      const answersA = keyA.split('|')
      const answersB = keyB.split('|')
      // 按第一个字母选项排序
      return answersA[0].localeCompare(answersB[0])
    })
  }

  // 将排序后的数组转换为TaskProblemReport数组
  entries.forEach(([key, value]) => {
    result.push({
      answers: key.split('|'),
      users: value.users,
      isCorrect: value.isCorrect,
    })
  })

  return { rows: result }
}

// 专题知识点接口
interface SpecialPoint {
  id: string // 知识点ID
  name: string // 知识点名称
  studyUserCount: number // 学习此知识点的用户数
  proficiency: number // 熟练度百分比
}

// 专题信息接口
export interface SpecialInfo {
  id: string // 专题ID
  name: string // 专题名称
  finishedUserCount: number // 完成的用户数
  finishedUsers: {
    id: string
    name: string
  }[]
  unfinishedUserCount: number // 未完成的用户数
  unfinishedUsers: {
    id: string
    name: string
  }[]
  stageId: number // 阶段ID
  subjectId: number // 学科ID
  moduleId: string // 模块ID
  specialId: string // 专题ID
  specialPoints: SpecialPoint[] // 专题知识点列表
}
export const getMeasuringTaskItemSpecialTopicApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.get<SpecialInfo>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-special-topic',
    {
      params,
    },
  )
}

// 用户信息接口
interface StudyUser {
  id: string
  name: string
}

// 学习状态枚举
type StudyStatus = 'HASGRASP' | 'NEEDREVIEW' | 'NEEDSTUDY'
// - HASGRASP: 已掌握
// - NEEDREVIEW: 待巩固
// - NEEDSTUDY: 待学习
// 学习状态信息接口
interface StatusInfo {
  status: StudyStatus
  users: StudyUser[]
}

// 问题信息接口
interface StudyProblem {
  problemId: string
  answerUserCount: number
  correctUserCount: number
  wrongUserCount: number
}

// 专题内容接口
export interface SpecialTopicContent {
  id: string
  name: string
  content: string
  explainImages: string[]
  statusInfo: StatusInfo[]
  problems: StudyProblem[]
}

// 专题状态接口（基于service.ts中的SpecialInfo）

export const getMeasuringTaskItemSpecialPointApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
  stageId: string
  subjectId: string
  moduleId: string
  specialId: string
  pointId: string
}) => {
  return request.get<SpecialTopicContent>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-special-point',
    {
      params,
    },
  )
}

export interface TaskProblemAiQaStat {
  totalUserCount: number
  rows: {
    question: string
    users: {
      id: string
      name: string
    }[]
    maxCreatedAt: string
  }[]
}

// 获取问题ai答疑统计情况.
export const getTaskProblemAiQaStatApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.get<TaskProblemAiQaStat>(
    '/teacher-ai-class/teacher/dashboard/problem-qa-stat?limit=50',
    { params },
  )
}

export interface TaskProblemAiQaAnswer {
  question: string
  id: string
  answer: string
  latency: string
}
export const getQaAnswerByQuestionApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
  question: string
}) => {
  return request.get<TaskProblemAiQaAnswer>(
    '/teacher-ai-class/teacher/dashboard/latest-qa-answer',
    { params },
  )
}

export interface TaskPPTCoursewareItem {
  id: string
  name: string
  pptMarkUserCount: number
  sourceId: string
  sourceUserId: string
  markPages: {
    imageUrl: string
    pageNumber: number
    users: {
      id: string
      name: string
    }[]
  }[]
}

export const getTaskItemPPTCoursewareApi = async (params: {
  measuringId: string
  measuringTaskItemId: string
}) => {
  return request.get<TaskPPTCoursewareItem>(
    '/teacher-ai-class/teacher/dashboard/measuring-task-item-ppt',
    { params },
  )
}

export interface GetFilePDFUrlByFileIDRes {
  url: string
  fileName: string
  fileType: 'FOLDER' | 'UNKNOWN' | 'PPT' | 'DOCX'
  //  - FOLDER: 文件夹.
  //  - UNKNOWN: 未知文件类型.
  //  - PPT: PPT.
  //  - DOCX: docx.
}
// 获取PDF下载地址.
export const getFilePDFUrlByFileIDApi = (fileId: string) => {
  return request.post<GetFilePDFUrlByFileIDRes>(
    `/teacher-desk/cloud-disk/file-pdf-url-by-fileid`,
    {
      fileId,
    },
  )
}
