<template>
  <div>
    <ProblemRender
      :showIndex="false"
      :difficultyHeaderShow="false"
      :currentProblem="problem"
      mode="government"
      class="problem-render-government py-24px"
    >
      <template #header>
        <slot name="header"></slot>
        <span
          v-if="problem.pool"
          class="problem-pool problem-tag"
          :class="
            problem.pool === 'goal'
              ? 'border-1px border-#5E80FF! color-#5E80FF'
              : ''
          "
          >{{
            problem.pool === 'step'
              ? '梯子题'
              : problem.pool === 'goal'
                ? '目标题'
                : ''
          }}</span
        >
        <span
          v-if="typeText && !problem.isHideExamType"
          class="type-text problem-tag"
          >{{ typeText }}</span
        >
        <span
          v-if="problemTypeText && problem.isShowProblemType"
          class="type-text problem-tag"
          >{{ problemTypeText }}</span
        >
        <span class="difficulty-text problem-tag">{{ difficultyText }}</span>
      </template>
      <div class="flex items-center mt-10px justify-between">
        <div class="flex items-center">
          <!-- <span class="data-bottom">{{ answerCount }}人作答</span> -->
          <span
            v-if="accuracy !== undefined && accuracy !== null && accuracy >= 0"
            :class="[
              accuracy > 80
                ? 'color-#4ECC5E! bg-#4ECC5E/15!'
                : accuracy > 40
                  ? 'color-#FEA345! bg-#FEA345/15!'
                  : 'bg-#fa5a65/15! color-#FA5A65!',
            ]"
            class="data-bottom"
            >正确率{{ accuracy }}%</span
          >
          <div
            class="flex items-center answer-detail-btn"
            @click="toggleAnswerDetail"
          >
            <span>已作答 {{ answerCount }}</span>
            <span>未作答{{ unfinishedUserCount }}</span>
            <span>已订正 {{ correctsPictureCount }}</span>
            <ArrowIcon
              class="px-8px w-32px h-10px"
              :class="showAnswerDetail ? '' : 'rotate-180'"
            />
          </div>
        </div>
        <div class="flex items-center color-#393548! font-600!">
          <n-popover
            v-if="taskAiExplain?.enableAiExplain"
            trigger="hover"
            arrow-class="paper-preferences-arrow-popover"
            content-class="paper-preferences-content-popover"
            class="paper-preferences-wrapper-popover"
            placement="top"
            :to="false"
            raw
          >
            <template #trigger>
              <span
                class="ml-8px problem-explain problem-operation flex items-center cursor-pointer border border-solid border-#505153 ai-btn"
                @click="toggleAiExplain"
              >
                <AiIcon class="w-16px h-16px mr-4px" />
                AI讲解
                <ArrowIcon
                  class="ml-2px w-10px h-10px"
                  :class="showAiExplain ? '' : 'rotate-180'"
                />
              </span>
            </template>
            <span>讲解由DeepSeek-R1深度推理模型生成</span>
          </n-popover>
          <span
            class="ml-8px problem-explain problem-operation flex items-center cursor-pointer border border-solid border-#C5C1D4"
            @click="toggleExplain"
          >
            解析
            <ArrowIcon
              class="ml-2px w-10px h-10px"
              :class="showExplain ? '' : 'rotate-180'"
            />
          </span>
          <ClassModeBtn
            v-if="!isHideClassMode"
            :problem="problem"
            :problem-no="index"
            :from="from"
            :extra-data="{
              measuringId,
              measuringTaskId,
              measuringTaskItemId,
            }"
          />

          <slot name="insert"></slot>
        </div>
      </div>
    </ProblemRender>
    <div v-if="showAnswerDetail" class="rounded-12px bg-#F7F7F9 p-16px mb-8px">
      <div class="mb-16px border-b border-solid border-#DFDCE8 pb-16px">
        <div class="answer-detail-title gary">
          未作答{{ taskProblemReport.unfinishedUsers?.length }}人
        </div>
        <div
          v-if="taskProblemReport.unfinishedUsers?.length > 0"
          class="answer-detail-content"
        >
          {{
            taskProblemReport.unfinishedUsers
              .map((item) => item.name)
              .join('、')
          }}
        </div>
      </div>
      <div class="flex items-center mb-12px">
        <div class="answer-detail-title green mr-22px">
          正确{{ taskProblemReport.correctUserCount }}人
        </div>
        <div class="answer-detail-title red flex-1">
          错误{{ taskProblemReport.wrongUserCount }}人
        </div>
        <OIWButton
          v-if="
            taskProblemReport.answerRecords?.length === 1 &&
            problem.type === 'exam'
          "
          type="info"
          size="small"
          ghost
          round
          @click="
            handleShowExamAnswerDetails(taskProblemReport.answerRecords[0], '')
          "
        >
          查看作答详情
        </OIWButton>
      </div>
      <div
        v-if="taskProblemReport.answerRecords?.length > 1"
        class="flex items-center justify-between"
      >
        <OIWRadioButtonGroup
          v-model:value="currentAnswerRecordIdx"
          @update:value="onUpdateCurrentAnswerRecordIdx"
        >
          <OIWRadioButton
            v-for="item in taskProblemReport.answerRecords"
            :key="item.idx"
            :value="item.idx + 1"
            >第{{ item.idx + 1 }}题</OIWRadioButton
          >
        </OIWRadioButtonGroup>
        <OIWButton
          v-if="
            taskProblemReport.answerRecords[currentAnswerRecordIdx - 1]
              .problemType === 'exam'
          "
          type="info"
          size="small"
          ghost
          round
          @click="
            handleShowExamAnswerDetails(
              taskProblemReport.answerRecords[currentAnswerRecordIdx - 1],
              '',
            )
          "
        >
          查看作答详情
        </OIWButton>
      </div>

      <div
        v-for="item in taskProblemReport.answerRecords?.filter(
          (item) => item.idx === currentAnswerRecordIdx - 1,
        )"
        :key="item.idx"
      >
        <ProblemAnswerItem
          v-for="(itemRow, indexRow) in item.rows"
          :key="indexRow"
          :idx="item.idx"
          :problem-type="item.problemType"
          :item="itemRow"
          @user-click="toggleUserRevisalDrawer"
        />
      </div>

      <div
        v-if="taskProblemReport.rows?.length === 0"
        class="text-14px text-center color-#393548 lh-20px py-16px"
      >
        暂无作答详情
      </div>
    </div>
    <div v-if="showExplain" class="report-explain-wrap">
      <div class="rounded-12px bg-#F7F7F9 p-16px">
        <div v-if="problem.type && problem.type === 'combination'">
          <div
            v-for="(subproblem, subproblemIndex) in problem.subproblems"
            :key="subproblem.id"
            class="mb-16px border-b border-solid border-#C5C1D4 pb-16px"
          >
            <div class="mb-8px">第{{ subproblemIndex + 1 }}题:</div>
            <CorrectAnswer
              :style="{ fontSize: '14px' }"
              :currentProblem="subproblem"
              mode="government"
            />
            <AnalysisExplain
              :style="{ fontSize: '14px' }"
              :currentProblem="subproblem"
              mode="government"
            />
          </div>
        </div>
        <div v-else>
          <CorrectAnswer
            :style="{ fontSize: '14px' }"
            :currentProblem="problem"
            mode="government"
          />
          <AnalysisExplain
            :style="{ fontSize: '14px' }"
            :currentProblem="problem"
            mode="government"
          />
        </div>
      </div>
    </div>
    <div
      v-if="showAiExplain"
      class="report-explain-wrap mt-8px rounded-12px bg-#F7F7F9 p-16px"
    >
      <OIWRadioButtonGroup
        v-model:value="showAiExplainType"
        class="mb-16px"
        @update:value="onUpdateShowAiExplainType"
      >
        <OIWRadioButton :value="0">AI 讲解详情</OIWRadioButton>
        <OIWRadioButton :value="1"
          >AI 互动答疑提问（{{
            taskAiQaStat?.totalUserCount
          }}人次）</OIWRadioButton
        >
      </OIWRadioButtonGroup>
      <div
        v-if="taskAiExplain && showAiExplainType === 0"
        class="rounded-12px bg-#F7F7F9 pt-16px text-14px!"
      >
        <div class="flex justify-between mb-12px">
          <span class="color-#9792AC">{{
            taskAiExplain.showAiExplain
              ? '以下内容由DeepSeek-R1生成，若您不想提供给学生，可关闭后面的开关'
              : '以下内容由DeepSeek-R1生成，目前学生端不可见，若您想提供可开启后面的开关'
          }}</span>
          <div class="flex items-center gap-16px">
            <div class="flex items-center gap-4px">
              <n-switch
                :value="taskAiExplain?.showAiExplain"
                size="small"
                @update:value="handleStudentAiExplain"
              />
              <span
                class="cursor-pointer color-#5E80FF"
                @click="handleStudentAiExplain(!taskAiExplain?.showAiExplain)"
                >{{
                  taskAiExplain?.showAiExplain ? '学生可见' : '学生不可见'
                }}</span
              >
            </div>
            <n-popover
              trigger="hover"
              arrow-class="paper-preferences-arrow-popover"
              content-class="paper-preferences-content-popover"
              class="paper-preferences-wrapper-popover"
              placement="top"
              :to="false"
              raw
            >
              <template #trigger>
                <span
                  class="flex items-center cursor-pointer"
                  :class="
                    taskAiExplain?.aiExplainProgress === 'ing'
                      ? 'color-#9792AC cursor-not-allowed'
                      : 'color-#5E80FF'
                  "
                  @click="handleRegenerateAiExplain"
                >
                  <RedoIcon class="w-16px h-16px mr-4px" />
                  重新生成
                </span>
              </template>
              <span>新生成的答案将覆盖原答案</span>
            </n-popover>
          </div>
        </div>
        <ThinkModelRender
          v-if="taskAiExplain?.aiExplainProgress === 'done'"
          :content="taskAiExplain?.aiExplain"
          :thinkTime="Math.floor(taskAiExplain?.latency / 1000)"
        />
        <div
          v-else-if="taskAiExplain?.aiExplainProgress === 'ing'"
          class="flex justify-center items-center h-full"
        >
          <span class="content-ing-icon" />
          <span class="content-ing-text"> 讲解生成中... </span>
        </div>
        <div
          v-else-if="taskAiExplain?.aiExplainProgress === 'failed'"
          class="flex justify-center items-center h-full"
        >
          讲解未生成,您可点击右上角的「重新生成」生成讲解
        </div>
      </div>
      <div v-if="taskAiQaStat && showAiExplainType === 1">
        <div>
          <div
            v-for="(item, qaIndex) in showAiQaStatAll
              ? taskAiQaStat.rows
              : taskAiQaStat.rows.slice(0, 10)"
            :key="item.question"
          >
            <div class="flex items-center">
              <span
                class="inline-block w-42px h-20px text-12px text-center leading-20px font-bold color-#fff bg-#FEA345 rounded-4px"
                >问题{{ qaIndex + 1 }}</span
              >
              <span class="text-14px color-#57526C mx-8px font-bold flex-1">{{
                item.question
              }}</span>
              <span
                class="text-14px font-bold color-#5E80FF cursor-pointer"
                @click="getQaAnswerByQuestion(item.question)"
                >查看AI讲解</span
              >
            </div>
            <div class="mt-8px mb-16px text-14px">
              <span class="font-bold">{{ item.users.length }}人提问：</span>
              <span>{{ item.users.map((user) => user.name).join('、') }}</span>
            </div>
          </div>
          <div
            v-if="!showAiQaStatAll && taskAiQaStat.rows.length > 10"
            class="text-center"
          >
            <span
              class="text-14px color-#5E80FF cursor-pointer"
              @click="showAiQaStatAll = true"
              >查看全部</span
            >
          </div>
          <AiQaModal
            v-model:show="showAiQaModal"
            :qa-answer="qaAnswer"
            :qa-question="qaQuestion"
          />
        </div>
      </div>
    </div>
    <ExamAnswerDetails
      v-model:show="showExamAnswerDetails"
      v-model:user-id="modalUserDetailId"
      :answer-detail="currentAnswerDetail"
      :problem-type="problem.type"
    />
    <UserRevisalDrawer
      v-model:show="showUserRevisalDrawer"
      :user-id="userRevisalDrawerUserId"
      :users="usersRevisal"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ProblemRender,
  ExamProblemTypeEnums,
  DifficultyEnums,
  ProblemTypesEnums,
  CorrectAnswer,
  AnalysisExplain,
  ThinkModelRender,
} from '@guanghe-pub/onion-problem-render'
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWButton,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
// import StarIcon from '~icons/yc/star'
// import StarBlankIcon from '~icons/yc/star-blank'
import ArrowIcon from '~icons/yc/arrow-up'
import RedoIcon from '~icons/yc/redo'
import AiIcon from '~icons/yc/ai-1'
import ProblemAnswerItem from './ProblemAnswerItem.vue'
import ExamAnswerDetails from './ExamAnswerDetails.vue'

import ClassModeBtn from '@/components/ClassRoomMode/ClassModeBtn.vue'
import type { ProblemDetailType } from '@/pages/problem/utils.ts'
import { buryPoint } from '@/utils/buryPoint.ts'
import { getQaAnswerByQuestionApi } from './service'
import type {
  TaskProblemReport,
  TaskItemAiExplain,
  TaskProblemAiQaStat,
  TaskProblemAiQaAnswer,
  TaskProblemReportRes,
} from './service'
import AiQaModal from '@/pages/lesson/analyze/components/LearningAnalysis/AiQaModal.vue'
import UserRevisalDrawer from './UserRevisalDrawer.vue'

const props = defineProps<{
  problem: ProblemDetailType
  index: number
  from?: string
  accuracy?: number
  isHideClassMode?: boolean
  // correctUsers?: string[]
  // wrongUsers?: string[]
  // correctUserCount?: string
  // wrongUserCount?: string
  answerCount: number
  unfinishedUserCount: number
  correctsPictureCount?: number
  taskProblemReport: TaskProblemReportRes
  taskAiExplain?: TaskItemAiExplain // 任务下的ai讲解
  taskAiQaStat?: TaskProblemAiQaStat
  measuringTaskItemId?: string
  measuringTaskId?: string
  measuringId?: string
  // enableAiExplain: boolean
  // showAnswerDetail: boolean
}>()

// const emits = defineEmits<(e: 'onFavorite') => void>()
const emit = defineEmits<{
  (e: 'update:showAnswerDetail', value: boolean): void
  (e: 'update:taskAiExplain'): void
  (e: 'update:showAiExplain', value: boolean): void
  (e: 'modifyStudentAiExplain', value: boolean): void
}>()

const message = useOIWMessage()

const showExplain = ref(false)
const showAnswerDetail = ref(false)
const showAiExplain = ref(false)
const showAiExplainType = ref(0)
const showAiQaStatAll = ref(false) // 是否显示所有提问
const showAiQaModal = ref(false)
const currentAnswerRecordIdx = ref(1)
const showExamAnswerDetails = ref(false)
const modalUserDetailId = ref('')
const currentAnswerDetail = ref<TaskProblemReportRes['answerRecords'][0]>()
const showUserRevisalDrawer = ref(false)
const userRevisalDrawerUserId = ref('')
const usersRevisal = ref<TaskProblemReport['users']>([])

const difficultyText = computed(() => {
  return (
    DifficultyEnums.find(
      (item) => `${item.score}` === props.problem.difficulty?.toString(),
    )?.name || '其他'
  )
})

// const getAnswerContent = (answers: string[]) => {
//   return answers.join(' | ')
// }

const typeText = computed(() => {
  return ExamProblemTypeEnums[props.problem?.examType || props.problem.type]
})

const problemTypeText = computed(() => {
  return ProblemTypesEnums[props.problem?.type]
})

const handleShowExamAnswerDetails = (
  item: TaskProblemReportRes['answerRecords'][0],
  userId: string,
) => {
  showExamAnswerDetails.value = true
  currentAnswerDetail.value = item
  modalUserDetailId.value = userId
  console.log(item)
}

const handleRegenerateAiExplain = () => {
  emit('update:taskAiExplain')
}

const onUpdateShowAiExplainType = (value: number) => {
  showAiExplainType.value = value
  showAiQaStatAll.value = false
  buryPoint(
    'clickAIClassReportAIExplainSwitchTab',
    {
      button: value === 1 ? 'AI互动答疑' : 'AI讲解',
      moduleId: props.measuringId,
      taskId: props.measuringTaskId,
      contentId: props.measuringTaskItemId,
    },
    'course',
  )
}
const qaAnswer = ref<TaskProblemAiQaAnswer>()
const qaQuestion = ref('')
const getQaAnswerByQuestion = (question: string) => {
  qaQuestion.value = question
  showAiQaModal.value = true
  if (props.measuringId && props.measuringTaskItemId) {
    getQaAnswerByQuestionApi({
      measuringId: props.measuringId,
      measuringTaskItemId: props.measuringTaskItemId,
      question,
    }).then((res) => {
      qaAnswer.value = res
    })
  } else {
    message.warning('暂无数据')
  }
  buryPoint(
    'clickAIClassReportAIInteractiveButton',
    {
      button: '点击查看AI讲解',
      moduleId: props.measuringId,
      taskId: props.measuringTaskId,
      contentId: props.measuringTaskItemId,
    },
    'course',
  )
}

// const showUserRevisalDrawer = ref(false)
const toggleUserRevisalDrawer: (
  user: TaskProblemReport['users'][0],
  problemType: string,
  idx: number,
) => void = (
  user: TaskProblemReport['users'][0],
  problemType: string,
  idx: number,
) => {
  if (user.correctsPicture !== '' && problemType !== 'exam') {
    showUserRevisalDrawer.value = true
    userRevisalDrawerUserId.value = user.id
    usersRevisal.value = props.taskProblemReport.answerRecords[
      idx
    ].rows.flatMap((row) =>
      row.users.filter((user) => user.correctsPicture !== ''),
    )
  }
  console.log('problemType>>>>', problemType)
  if (problemType === 'exam') {
    handleShowExamAnswerDetails(
      props.taskProblemReport.answerRecords[idx],
      user.id,
    )
  }
}

const toggleAnswerDetail = () => {
  showAnswerDetail.value = !showAnswerDetail.value
  showExplain.value = false
  showAiExplain.value = false
  emit('update:showAnswerDetail', showAnswerDetail.value)
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'answer',
      moduleId: props.measuringId,
      taskId: props.measuringTaskId,
      contentId: props.measuringTaskItemId,
    },
    'course',
  )
}

const toggleExplain = () => {
  showExplain.value = !showExplain.value
  showAnswerDetail.value = false
  showAiExplain.value = false
  buryPoint(
    'clickCourseDetailPageButton',
    {
      pageName: 'report',
      button: 'explain',
      moduleId: props.measuringId,
      taskId: props.measuringTaskId,
      contentId: props.measuringTaskItemId,
    },
    'course',
  )
}

const toggleAiExplain = () => {
  showExplain.value = false
  showAnswerDetail.value = false
  showAiExplain.value = !showAiExplain.value
  emit('update:showAiExplain', showAiExplain.value)
}

const handleStudentAiExplain = async (value: boolean) => {
  emit('modifyStudentAiExplain', value)
}

const onUpdateCurrentAnswerRecordIdx = (value: number) => {
  currentAnswerRecordIdx.value = value
}
</script>

<style lang="scss">
.onion-problem-render__thinkModel-content {
  // margin-top: 16px;
  color: #393548 !important;
}

.problem-operation {
  padding: 4px 12px;
  border: 1px solid #c5c1d4;
  border-radius: 8px;
}
</style>

<style lang="scss" scoped>
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

::v-deep(.oiw-radio-button-group) {
  background-color: #fff !important;
}

::v-deep(.oiw-radio-button.n-radio-button--checked) {
  background-color: #f4f6ff !important;
  box-shadow: none !important;
}

.problem-tag {
  padding: 2px 6px;
  margin-right: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #393548;
  border: 1px solid #c5c1d4;
  border-radius: 4px;
}

.answer-detail-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;

  /* n8标题文本 */
  color: #393548;
  letter-spacing: normal;

  &.gary::before {
    display: inline-block;
    width: 6px;
    height: 20px;
    margin-right: 8px;
    content: '';
    background: #c5c1d4;
    border-radius: 10px;
  }

  &.green::before {
    display: inline-block;
    width: 6px;
    height: 20px;
    margin-right: 8px;
    content: '';
    background: #4ecc5e;
    border-radius: 10px;
  }

  &.red::before {
    display: inline-block;
    width: 6px;
    height: 20px;
    margin-right: 8px;
    content: '';
    background: #fa5a65;
    border-radius: 10px;
  }
}

.answer-detail-content {
  margin-top: 12px;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;

  /* n8标题文本 */
  color: #393548;
  text-align: justify; /* 浏览器可能不支持 */
  letter-spacing: normal;
}

.answer-detail-btn {
  box-sizing: border-box;
  height: 28px;
  cursor: pointer;
  border: 1px solid #c5c1d4;
  border-radius: 8px;

  span {
    display: inline-block;
    height: 22px;
    padding: 0 8px;
    font-weight: 600;
    line-height: 22px;
    color: #393548;
    border-right: 1px solid #dfdce8;
  }
}

.content-ing-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background: url('https://fp.yangcong345.com/onion-extension/333-0deb3b0ae5925ec7ab9de06c7019a851.png')
    no-repeat center center;
  background-size: 100% 100%;
  animation: loading 1s linear infinite;
}

.content-ing-text {
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  color: #5e80ff;
}

.answer-popover {
  max-width: 692px;
  height: 1.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ai-btn {
  height: 31px;
  color: #fff;
  background: linear-gradient(
    138deg,
    rgb(149, 130, 244) 0%,
    rgb(100, 130, 255) 68%,
    rgb(104, 192, 249) 107%
  );
  border: none;
  // box-shadow: inset 0px 0.4px 0.5px 0px rgba(255, 255, 255, 0.8);
}

.problem-render-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 17px;
  font-variation-settings: 'opsz' auto;
  font-weight: 500;
  line-height: 20px;

  /* n7web气泡底色 、正文 */
  color: #57526c;
  letter-spacing: 0px;
}

.data-bottom {
  height: 28px;
  padding: 0 8px;
  margin-right: 8px;
  font-size: 12px;
  font-variation-settings: 'opsz' auto;
  font-weight: 600;
  line-height: 28px;

  /* n8标题文本 */
  color: #393548;
  text-align: center;
  letter-spacing: 0px;

  /* 灰（标签色）表头色 */
  background: #f7f7f9;
  border-radius: 8px;
  opacity: 1;

  &.correct-count {
    &::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 4px;
      content: '';

      /* g1正确含义相关 */
      background: #4ecc5e;
      border-radius: 50%;
      opacity: 1;
    }
  }

  &.wrong-count {
    &::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 4px;
      content: '';
      background: rgba(250, 90, 101, 1);
      border-radius: 50%;
      opacity: 1;
    }
  }
}

.problem-render-government {
  font-size: 14px !important;
}

::v-deep(.onion-problem-render__expand) {
  padding: 16px;
  margin-top: 20px;
  background: #f7f7f9;
  border-radius: 12px;

  .onion-problem-render__answer {
    margin-top: 0;
  }
}

::v-deep(
    .onion-problem-render__answer--government
      .onion-problem-render__answer--header
  ) {
  margin-bottom: 8px !important;
  font-size: 17px !important;
  color: #57526c;
}

::v-deep(
    .onion-problem-render__explain--government
      .onion-problem-render__explain--header
  ) {
  margin-bottom: 8px !important;
  font-size: 17px !important;
  color: #57526c;
}

::v-deep(
    .oiw-button--box.n-button--info-type.n-button--ghost:not(
        .n-button--disabled
      ):focus
  ) {
  color: #393548 !important;
  background: transparent !important;
}

::v-deep(
    .oiw-button--box.n-button--info-type.n-button--ghost:not(
        .n-button--disabled
      ):focus
      .n-button__border
  ) {
  border: 1px solid #d4d1dd !important;
}

::v-deep(.onion-problem-render__option--text) {
  font-size: 1em !important;
  color: #393548 !important;
}

::v-deep(.oiw-button--box.n-button--small-type) {
  min-width: 108px !important;
  height: 40px;
  padding: 0 16px;
  font-size: 14px !important;
  font-weight: 400 !important;
}

::v-deep(.onion-problem-render__option) {
  display: flex;
  flex-wrap: wrap;
}

::v-deep(.onion-problem-render__option--no) {
  font-size: 1em !important;
  color: #393548 !important;
  background: transparent !important;
}

::v-deep(.onion-problem-render__option--no-dot) {
  display: unset !important;
}

::v-deep(.onion-problem-render__option--item) {
  width: 50%;
  border: none !important;
}

::v-deep(.onion-problem-render__main) {
  font-size: 14px !important;
}

::v-deep(.onion-problem-render__examBox-show) {
  display: none;
}

::v-deep(.onion-problem-render__main img) {
  max-height: 200px;
}

::v-deep(.multi-line) {
  border: unset !important;
  border-bottom: 1px solid #505153 !important;
  border-radius: 0 !important;

  &::after {
    content: none !important;
  }
}

::v-deep(.onion-problem-render__examBox-show + .onion-problem-render__option) {
  display: none !important;
}
</style>
