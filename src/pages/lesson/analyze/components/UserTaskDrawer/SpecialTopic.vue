<template>
  <div class="problem-task-container">
    <div class="task-item-title">
      <span class="task-item-back" @click="handleTaskItemClick">返回</span>
    </div>
    <div v-if="specialInfo" class="special-list onion-scroll">
      <div
        v-for="item in specialInfo.points"
        :key="item.id"
        class="flex special-item"
      >
        <div v-if="item.difficult === 'basic'" class="tag-item">基础</div>
        <div v-if="item.difficult === 'advance'" class="tag-item">进阶</div>
        <div class="text-16px font-600 color-#393548 flex-1">
          {{ item.name }}
        </div>
        <div class="proficiency">熟练度 {{ item.proficiency }}%</div>
        <div
          v-if="item.status === 'HASGRASP'"
          class="tag-item-status has-grasp"
        >
          已掌握
        </div>
        <div
          v-if="item.status === 'NEEDREVIEW'"
          class="tag-item-status need-review"
        >
          待巩固
        </div>
        <div
          v-if="item.status === 'NEEDSTUDY'"
          class="tag-item-status need-study"
        >
          待学习
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Special } from './service'
import { getUserSpecialDetailApi } from './service'

const props = defineProps<{
  userId: string
  measuringId: string
  measuringTaskItemId: string
  // sourceType: string
}>()
const emits = defineEmits<(e: 'taskItemBack') => void>()
const specialInfo = ref<Special>()

onMounted(async () => {
  await getUserSpecialDetail()
})

const getUserSpecialDetail = async () => {
  const res = await getUserSpecialDetailApi({
    userId: props.userId,
    measuringId: props.measuringId,
    measuringTaskItemId: props.measuringTaskItemId,
  })
  specialInfo.value = res.special
}

const handleTaskItemClick = () => {
  emits('taskItemBack')
}
</script>

<style lang="scss" scoped>
.special-list {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
}

.special-item {
  align-items: center;
  height: 56px;
  background: #ffffff;

  /* 下分割线（内阴影） */
  box-shadow: inset 0px -1px 0px 0px #dcd8e7;
}

.tag-item {
  box-sizing: border-box;
  width: 36px;
  height: 20px;
  margin-right: 8px;

  /* 自动布局 */
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  color: #393548;
  text-align: center;

  /* n5不可点击色 */

  /* 样式描述：输入框提示文本色 */
  border: 1px solid #c5c1d4;
  border-radius: 4px;
}

.proficiency {
  height: 20px;
  padding: 0 6px;
  margin-right: 8px;
  font-family: MiSansHeavy;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  color: #5e80ff;
  background: rgba(94, 128, 255, 0.1);
  border-radius: 4px;
}

.tag-item-status {
  width: 48px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  line-height: 20px;
  color: #ffffff;
  text-align: center;

  border-radius: 4px;

  &.has-grasp {
    background: #4ecc5e;
  }

  &.need-review {
    background: #fea345;
  }

  &.need-study {
    background: #9792ac;
  }
}

.image-container {
  display: flex;
  width: 240px;
  height: 240px;
  overflow: hidden;
  border-radius: 12px;

  ::v-deep(img) {
    width: 240px;
    height: 240px;
    object-fit: cover !important;
  }
}

.problem-task-container {
  padding: 8px 0;

  .task-item-title {
    .task-item-back {
      position: relative;
      display: inline-block;
      padding-left: 20px;
      font-size: 14px;
      font-weight: 600;
      line-height: 24px;
      color: #9792ac;
      cursor: pointer;

      &::before {
        position: absolute;
        top: 6px;
        left: 0;
        width: 12px;
        height: 12px;
        content: '';
        background: url('https://fp.yangcong345.com/onion-extension/111-57ff4a62c08eb772695a6b807b97c62c.png')
          no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }

  .user-problem-header {
    display: flex;
    gap: 12px;
    align-items: center;

    .tag-item {
      padding: 4px 6px;
      font-size: 12px;
      font-weight: 600;
      line-height: 12px;
      color: #ffffff;
      background: #504b64;
      border-radius: 4px;
    }
  }

  .problem-info-analysis {
    margin-top: 24px;
  }
}
</style>
