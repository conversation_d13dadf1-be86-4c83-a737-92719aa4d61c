import request from '@/utils/request.ts'
import { apiSchoolDomain } from '@/utils/apiUrl.ts'
import type { Tags } from '../service'

export interface FilterListItem {
  title: string
  value: string
  select?: boolean
  [key: string]: any
}
export interface FilterItem {
  key: string
  tabStr: string
  list: FilterListItem[]
  multiple?: boolean
  value?: string | string[]
}

export interface AIClassListQuery {
  page?: number
  pageSize?: number
  subjectId: string
  groupIds: string[]
  stageId: string
  startAt?: string
  endAt?: string
}

export interface Task {
  id: number
  name: string
  finishCount: number // 完成该任务所有内容的学生数
  totalCount: number // 该课时下当前任务指定范围的任务已参与学生数, 前端计算完成率：finishCount/totalCount 四舍五入
  measuringId: string // 课时Id
  rankIdx: number // 任务排序索引
  totalVideo: number // 微课总数
  totalProblem: number // 题目总数
  totalItem: number // item总数
  tag: Tags
}

export interface AIClassItem {
  id: string
  name: string
  startAt: string
  endAt: string
  groupId: string
  roomName?: string // 后端接口不给，需要前端自己拼
  subjectId?: string
  stageId: string
  state: number // 状态 1:已发布 2:未发布
  tasks: Task[]
  enableAiExplain?: boolean
  enableAiQa?: boolean
  studyTiming?: 'AfterPublish' | 'AfterClassStart' | undefined
}

// 获取已发布的AI课堂列表
export const postAIClassListApi = async (data: AIClassListQuery) => {
  return request.post<{
    rows: AIClassItem[]
    count: number
  }>(`${apiSchoolDomain}/teacher-ai-class/teacher/measuring-list`, data)
}

/**
 * 获取AI课堂草稿数量
 * https://yapi.yc345.tv/project/2740/interface/api/121785
 * @returns
 */
export const getMeasuringDraftCountApi = async () => {
  return request.get<{
    count: number
  }>(`${apiSchoolDomain}/teacher-ai-class/teacher/measuring-draft-count`)
}

export interface PutMeasuringTimeReq {
  id?: string
  startAt?: string
  endAt?: string
  [k: string]: unknown
}

export interface PutMeasuringTimeRes {
  id?: string
  [k: string]: unknown
}

/**
 * @description 更新课时的时间字段,用于日历
 * https://yapi.yc345.tv/project/2740/interface/api/122142
 * <AUTHOR>
 * @date 2025-02-19
 * @export
 * @param {PutMeasuringTimeReq} data
 * @returns {Promise<PutMeasuringTimeRes>}
 */
export const putMeasuringTime = (
  data: PutMeasuringTimeReq,
): Promise<PutMeasuringTimeRes> => {
  return request.put<PutMeasuringTimeRes>(
    `${apiSchoolDomain}/teacher-ai-class/teacher/measuring-time`,
    data,
  )
}

export const downloadMeasuringPDFApi = async (data: {
  measuringId: string
  isShowAnswer: boolean
  isShowExplain: boolean
}) => {
  return request.post<{
    url: string
  }>(`${apiSchoolDomain}/teacher-ai-class/teacher/pdf-download`, data)
}

// AI课堂教学班
// https://yapi.yc345.tv/project/2531/interface/api/126398
export const getTeacherRoomListAIClass = async () => {
  return request.get<{ rows: YcType.TeacherRoom[] }>(
    `${apiSchoolDomain}/teacher-common/teacher-school/admin-room/teacher/rooms-for-ai-class`,
  )
}
