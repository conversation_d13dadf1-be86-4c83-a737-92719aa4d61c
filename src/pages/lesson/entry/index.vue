<template>
  <section class="w-1200px mx-auto pb-86px">
    <EntryHeader
      :class="{ 'sticky-header': checkValue === 'calendar' }"
      :mode="checkValue"
      @change-option="handleChangeOption"
      @room-list="handleRoomList"
    >
      <template #right>
        <n-select
          :value="checkValue"
          :options="checkOptions"
          label-field="title"
          value-field="value"
          show-checkmark
          :render-label="checkRenderLabel"
          :menu-props="{ class: 'custom-multiple-option-menu' }"
          class="check-option-select"
          @update:value="handleSwitchScene"
        />
      </template>
    </EntryHeader>
    <OIWLoading
      v-if="isLoading"
      :show="isLoading"
      width="200px"
      height="200px"
    />
    <AIClassList
      v-if="!isLoading && checkValue === 'list'"
      v-model:page="page"
      v-model:page-size="pageSize"
      :aiClassList="aiClassList"
      :total="total"
    />
    <AIClassCalendar
      v-if="!isLoading && checkValue === 'calendar'"
      v-model:ai-class-list="aiClassList"
      :timestamp="timestamp"
    />
  </section>
</template>

<script setup lang="tsx">
import { OIWLoading } from '@guanghe-pub/onion-ui-web'
import EntryHeader from '@/pages/lesson/entry/components/EntryHeader.vue'
import AIClassList from '@/pages/lesson/entry/components/AIClassList.vue'
import AIClassCalendar from '@/pages/lesson/entry/components/AIClassCalendar/index.vue'
import { postAIClassListApi } from '@/pages/lesson/entry/service.ts'
import type {
  AIClassListQuery,
  AIClassItem,
} from '@/pages/lesson/entry/service.ts'
import dayjs from 'dayjs'
import { buryPoint } from '@/utils/buryPoint.ts'
import type { SelectOption } from 'naive-ui'

const roomsList = ref<YcType.TeacherRoom[]>([]) // 班级列表
const checkValue = ref<'calendar' | 'list'>(
  (window.localStorage.getItem('AIClassLessonEntryCheck') as
    | 'calendar'
    | 'list') || 'calendar',
)
const checkOptions = ref([
  {
    value: 'calendar',
    title: '日历',
  },
  {
    value: 'list',
    title: '列表',
  },
])
const homeMainEl = ref<HTMLElement | null>(null)
const navBarEl = ref<HTMLElement | null>(null)
const secondNavEl = ref<HTMLElement | null>(null)
// 获取列表接口
const page = ref(1)
const pageSize = ref(10000)
const total = ref(0)
const optionValue = ref<AIClassListQuery>({
  groupIds: [],
  subjectId: '0',
  stageId: '0',
  startAt: dayjs().startOf('week').toISOString(),
  endAt: dayjs().endOf('week').toISOString(),
})
const isLoading = ref(true)
const aiClassList = ref<AIClassItem[]>([]) // 课时列表数据
const timestamp = ref<number>(0)
const fetchData = async (option: AIClassListQuery) => {
  try {
    isLoading.value = true
    const params: AIClassListQuery = {
      subjectId: option.subjectId === 'all' ? '0' : option.subjectId,
      groupIds: option.groupIds,
      stageId: option.stageId,
      startAt: option.startAt,
      endAt: option.endAt,
      pageSize: pageSize.value,
      page: page.value,
    }
    if (checkValue.value === 'list') {
      delete params.startAt
      delete params.endAt
    }
    const res = await postAIClassListApi(params)
    total.value = res.count || 0
    aiClassList.value = res.rows.map((item: AIClassItem) => {
      const room = roomsList.value.find(
        (roomItem) => roomItem.groupId === item.groupId,
      )
      item.tasks = item.tasks.sort((a, b) => a.rankIdx - b.rankIdx)
      return {
        ...item,
        roomName: room?.name || '',
      }
    })
  } finally {
    isLoading.value = false
  }
}

const handleChangeOption = (option: AIClassListQuery) => {
  page.value = 1
  optionValue.value = {
    ...option,
    startAt: option.startAt || dayjs().startOf('week').toISOString(),
    endAt: option.endAt || dayjs().endOf('week').toISOString(),
  }
  timestamp.value = dayjs(option.startAt).valueOf()
}

const handleSwitchScene = (value: 'calendar' | 'list') => {
  checkValue.value = value
  window.localStorage.setItem('AIClassLessonEntryCheck', value)
}

const handleRoomList = (list: YcType.TeacherRoom[]) => {
  roomsList.value = list
}

watch(
  () => checkValue.value,
  (newValue) => {
    page.value = 1
    if (newValue === 'list') {
      total.value = 0
      pageSize.value = 30
    } else {
      pageSize.value = 10000
    }
    nextTick(() => {
      homeMainEl.value = document.querySelector('#homeMain')
      navBarEl.value = document.querySelector('.nav-bar')
      secondNavEl.value = document.querySelector('.second-route-box')
      newValue === 'calendar' ? setCalendarModeStyles() : resetStyles()
    })
    buryPoint(
      'enterAIClassCoursePage',
      {
        browserResolution: `${screen.width}x${screen.height}`,
        pageName: newValue,
      },
      'course',
    )
  },
  { immediate: true },
)

watch(
  () => [page.value, pageSize.value, optionValue.value],
  () => {
    fetchData(optionValue.value)
  },
  {
    deep: true,
  },
)

onBeforeUnmount(() => {
  resetStyles()
})

function setCalendarModeStyles() {
  if (homeMainEl.value) {
    homeMainEl.value.style.paddingTop = '0px'
  }
  if (navBarEl.value) {
    navBarEl.value.style.position = 'sticky'
    navBarEl.value.style.top = '0'
    navBarEl.value.style.zIndex = '101'
  }
  if (secondNavEl.value) {
    secondNavEl.value.style.position = 'sticky'
    secondNavEl.value.style.top = '64px'
    secondNavEl.value.style.zIndex = '100'
    secondNavEl.value.style.background = '#fff'
  }
}

function resetStyles() {
  if (homeMainEl.value) {
    homeMainEl.value.style.paddingTop = '64px'
  }
  if (navBarEl.value) {
    navBarEl.value.style.position = ''
    navBarEl.value.style.top = ''
    navBarEl.value.style.zIndex = ''
  }
  if (secondNavEl.value) {
    secondNavEl.value.style.position = ''
    secondNavEl.value.style.top = ''
    secondNavEl.value.style.zIndex = ''
    secondNavEl.value.style.background = ''
  }
}

const checkRenderLabel = (option: SelectOption) => {
  return (
    <div class="flex items-center entry-check-option-select">
      <span
        class={`switch-scene-icon ${
          option.value === 'calendar'
            ? 'switch-scene-icon-calendar'
            : 'switch-scene-icon-list'
        }`}
      />
      <span class={'entry-check-text'}>{option.title}</span>
    </div>
  )
}
</script>

<style lang="scss" scoped>
.sticky-header {
  position: sticky;
  top: 136px;
  z-index: 100;
  background: #fff;
}

.check-option-select {
  width: 116px;
  height: 40px;

  &::v-deep(.n-base-selection) {
    height: 100%;

    &.n-base-selection--active .n-base-selection__border {
      border: 1px solid #5e80ff;
      border-radius: 12px;
    }

    .n-base-selection__state-border {
      border-radius: 12px;
      box-shadow: none;
    }

    .n-base-selection-label {
      height: 100%;
      background-color: transparent;
    }

    .n-base-selection__border {
      border: 1px solid #d4d1dd;
      border-radius: 12px;
    }
  }
}
</style>

<style lang="scss">
.entry-check-option-select {
  .switch-scene-icon {
    width: 24px;
    height: 24px;

    &.switch-scene-icon-calendar {
      background: url('https://fp.yangcong345.com/onion-extension/222-2e58429d2ec2dee1c13e4885787d7353.png')
        no-repeat center center;
      background-size: 24px 24px;
    }

    &.switch-scene-icon-list {
      background: url('https://fp.yangcong345.com/onion-extension/111-ff3b5069e54296715abe59943ee80d03.png')
        no-repeat center center;
      background-size: 24px 24px;
    }
  }

  .entry-check-text {
    margin-left: 4px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #3d3d3d;
  }
}
</style>
