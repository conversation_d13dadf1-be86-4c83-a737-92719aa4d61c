<template>
  <div class="flex items-center justify-between pb-24px">
    <div class="flex justify-start">
      <div class="select-option-block">
        <div class="select-label">班级:</div>
        <n-select
          :value="changeOption.groupIds"
          :options="roomWithAllList"
          label-field="title"
          value-field="value"
          show-checkmark
          multiple
          :max-tag-count="1"
          :menu-props="{ class: 'custom-multiple-option-menu' }"
          class="multiple-option-select"
          @update:value="updateGroupIds"
        />
      </div>
      <div class="select-option-block">
        <div class="select-label">学科:</div>
        <n-cascader
          v-model:value="csvValue"
          class="oiw-cascader"
          label-field="name"
          value-field="treeId"
          placeholder="选择学科"
          expand-trigger="hover"
          :options="options"
          check-strategy="child"
          filterable
          @update:value="onCsvSelectChange"
        />
      </div>
      <div
        v-if="mode === 'calendar'"
        class="select-option-block select-week-block"
      >
        <button
          class="select-week-pre"
          @click="
            updateTimestamp(
              (changeOption.timestamp || 0) - 7 * 24 * 60 * 60 * 1000,
            )
          "
        />
        <button
          class="select-week-current"
          :disabled="currentWeekDisabled"
          @click="updateTimestamp(startCurrentWeek)"
        >
          本周
        </button>
        <button
          class="select-week-next"
          @click="
            updateTimestamp(
              (changeOption.timestamp || 0) + 7 * 24 * 60 * 60 * 1000,
            )
          "
        />
        <n-date-picker
          :value="changeOption.timestamp"
          type="week"
          :format="weekFormat"
          :first-day-of-week="0"
          input-readonly
          class="select-week-picker"
          @update:value="updateTimestamp"
        />
      </div>
    </div>
    <div class="flex items-center justify-end gap-8px">
      <div class="draft-btn" @click="goDraft">
        草稿箱({{ measuringDraftCount }})
      </div>
      <OIWButton class="entry-create-btn" type="info" @click="goLessonCreate">
        + 新建课时
      </OIWButton>
      <slot name="right" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { OIWButton } from '@guanghe-pub/onion-ui-web'
import { useAuth } from '@/hooks/useAuth.ts'
import { useCvs } from '@/hooks/useCvs'
import type { CascaderOption } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import type { AIClassListQuery } from '@/pages/lesson/entry/service.ts'
import {
  getMeasuringDraftCountApi,
  getTeacherRoomListAIClass,
} from '@/pages/lesson/entry/service.ts'
import { useServerTime } from '@/hooks/useServerTime'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  mode: 'calendar' | 'list'
}>()

const emits = defineEmits<{
  (e: 'changeOption', value: AIClassListQuery): void
  (e: 'roomList', value: YcType.TeacherRoom[]): void
}>()

// 埋点方法
const buryPointFun = (button: string) => {
  buryPoint(
    'clickAIClassCoursePageButton',
    {
      pageName: props.mode,
      button,
    },
    'course',
  )
}

const { serverTime } = useServerTime()
// 班级及学科的筛选项
const { subjectId, stageId } = useAuth()
const router = useRouter()
const csv = useCvs()
const options = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = [{ id: 'all', name: '全部' }].concat(
      item.subjects ? (item.subjects as any[]) : [],
    )
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})
const measuringDraftCount = ref(0)

const changeOption = ref<{
  groupIds: string[]
  subjectId: string
  stageId: string
  startAt: string
  endAt: string
  timestamp: number | null
}>({
  groupIds: [],
  subjectId: `${subjectId.value || '-1'}`,
  stageId: `${stageId.value || '-1'}`,
  startAt: '',
  endAt: '',
  timestamp: null,
})

const csvValue = ref<string>(`${stageId.value}-${subjectId.value}`)
const startCurrentWeek = ref(0)
const currentWeekDisabled = computed(() => {
  return (
    dayjs(changeOption.value.timestamp).valueOf() === startCurrentWeek.value
  )
})
const weekFormat = computed(() => {
  return `yyyy年MM月dd日-${dayjs(changeOption.value.timestamp)
    .endOf('week')
    .format('DD')}日`
})
const roomWithAllList = ref<
  { title: string; value: string; disabled?: boolean }[]
>([])

onMounted(() => {
  initWeekTimestamp()
  teacherRoomList()
  getMeasuringDraftCount()
})

const initWeekTimestamp = () => {
  startCurrentWeek.value = dayjs(serverTime.value).startOf('week').valueOf()
  changeOption.value.timestamp = dayjs(serverTime.value)
    .startOf('week')
    .valueOf()
  changeOption.value.startAt = dayjs(serverTime.value)
    .startOf('week')
    .toISOString()
  changeOption.value.endAt = dayjs(serverTime.value).endOf('week').toISOString()
}

const defaultChangeOption = () => {
  const query = window.localStorage.getItem('AIClassListQuery')
  if (query) {
    const temp = JSON.parse(query)
    temp.groupIds = temp.groupIds.filter((item: string) =>
      roomWithAllList.value.some((room) => room.value === item),
    )
    if (temp.groupIds.length === 0) {
      temp.groupIds = []
    }
    changeOption.value.groupIds = temp.groupIds
    changeOption.value.subjectId = temp.subjectId
    changeOption.value.stageId = temp.stageId
    csvValue.value = `${temp.stageId}-${temp.subjectId}`
  }

  emits('changeOption', changeOption.value)
}

const teacherRoomList = async () => {
  // 获取班级列表
  const res = await getTeacherRoomListAIClass()
  if (res) {
    const roomList = res.rows.map((item: YcType.TeacherRoom) => {
      return {
        title: item.name,
        value: item.groupId,
      }
    })
    roomWithAllList.value = roomList
    emits('roomList', res.rows)
    nextTick(() => {
      defaultChangeOption()
    })
  }
}

const updateGroupIds = (value: string[]) => {
  if (value.length) {
    changeOption.value.groupIds = value
  } else {
    changeOption.value.groupIds = []
  }
  window.localStorage.setItem(
    'AIClassListQuery',
    JSON.stringify(changeOption.value),
  )
  emits('changeOption', changeOption.value)
  buryPointFun('class')
}

const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  changeOption.value.subjectId = subjectId
  changeOption.value.stageId = stageId
  window.localStorage.setItem(
    'AIClassListQuery',
    JSON.stringify(changeOption.value),
  )
  emits('changeOption', changeOption.value)
  buryPointFun('subject')
}

const updateTimestamp = (value: number) => {
  changeOption.value.timestamp = value
  changeOption.value.startAt = dayjs(value).startOf('week').toISOString()
  changeOption.value.endAt = dayjs(value).endOf('week').toISOString()
  emits('changeOption', changeOption.value)
}

const getMeasuringDraftCount = async () => {
  const res = await getMeasuringDraftCountApi()
  measuringDraftCount.value = res.count
}

const goDraft = () => {
  buryPointFun('draft')
  router.push({
    name: 'LessonDraft',
  })
}
const goLessonCreate = () => {
  buryPointFun('new')
  router.push({
    name: 'LessonCreate',
  })
}
</script>

<style lang="scss" scoped>
.select-option-block {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  & + .select-option-block {
    margin-left: 16px;
  }

  .select-label {
    font-size: 16px;
    line-height: 20px;
    color: #393548;
  }

  .multiple-option-select {
    width: 220px;
    height: 40px;

    &::v-deep(.n-base-selection--multiple) {
      height: 100%;

      &.n-base-selection--active .n-base-selection__border {
        border: 1px solid #5e80ff;
        border-radius: 12px;
      }

      .n-base-selection__state-border {
        border-radius: 12px;
        box-shadow: none;
      }

      .n-base-selection-label {
        height: 100%;
        background-color: transparent;
      }

      .n-base-selection__border {
        border: 1px solid #d4d1dd;
        border-radius: 12px;
      }
    }

    &::v-deep(.n-base-selection-tags) {
      height: 100%;
      padding-top: 7px;
      padding-bottom: 7px;

      .n-base-selection-tag-wrapper {
        padding: 0;

        + .n-base-selection-tag-wrapper {
          margin-left: 4px;
        }

        .n-tag {
          padding: 6px 8px;
          background: #f4f6ff;
          border-radius: 8px;

          .n-tag__content {
            max-width: 100px;
            overflow: hidden;
            font-size: 14px;
            line-height: 20px;
            color: #393548;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .n-tag__border {
            display: none;
          }
        }
      }
    }
  }

  .oiw-cascader {
    width: 152px;
    height: 40px;

    ::v-deep(.n-base-selection) {
      padding-top: 3px;
      padding-bottom: 3px;
      border-radius: 12px;
    }
  }
}

.select-week-block {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .select-week-pre,
  .select-week-next {
    box-sizing: border-box;
    width: 40px;
    height: 40px;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid #c5c1d4;
    border-radius: 12px;
  }

  .select-week-pre {
    background-image: url('https://fp.yangcong345.com/onion-extension/111-5c0fe608a941e84f34b03a981d3c004c.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
  }

  .select-week-next {
    background-image: url('https://fp.yangcong345.com/onion-extension/222-f576847555fbbf9ec459651ff2cce180.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
  }

  .select-week-current {
    box-sizing: border-box;
    width: 60px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    line-height: 38px;
    color: #393548;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid #c5c1d4;
    border-radius: 12px;

    &:disabled {
      color: #c5c1d4;
      cursor: not-allowed;
      opacity: 0.8;
    }
  }

  .select-week-picker {
    width: 214px;
    height: 40px;

    &::v-deep(.n-input) {
      height: 100%;

      .n-input-wrapper {
        height: 100%;

        .n-input__input-el {
          height: 100%;
          font-size: 14px;
          color: #393548;
          cursor: pointer;
        }
      }

      .n-input__border {
        border: 1px solid #c5c1d4;
        border-radius: 12px;
      }

      .n-input__state-border {
        border-radius: 12px;
      }
    }
  }
}

.draft-btn {
  height: 20px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: #5e80ff;
  cursor: pointer;
}

.entry-create-btn {
  &.n-button--medium-type {
    width: 108px;
    padding-right: 14px;
    padding-left: 14px;
    margin-right: 8px;
  }
}
</style>
