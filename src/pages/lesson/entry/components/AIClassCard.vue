<template>
  <div class="card">
    <div class="time-box">
      <div class="flex justify-start items-center text-20px font-600">
        <span class="color-#393548">{{ dateStr }}</span>
        <span class="w-2px h-20px bg-#9792AC mx-8px" />
        <span class="color-#9792AC">{{ weekStr }}</span>
      </div>
      <div class="tag-box">
        <span class="tag" :class="getStyle" />
        <span>{{ tagText }}</span>
      </div>
      <span class="round-icon" />
    </div>
    <div class="card-content">
      <div class="content-left mt-8px">
        <div class="flex justify-start items-end line-height-20px">
          <div class="color-#393548 text-20px font-600">
            {{ item.roomName }}
          </div>
          <span class="w-1px h-14px bg-#393548 mx-8px" />
          <span class="color-#393548 text-14px line-height-14px">
            {{ timeStr }}
          </span>
        </div>
        <div class="flex justify-start items-center my-16px">
          <span v-if="item.subjectId" class="subject-tag">
            {{ SubjectEnum[Number(item.subjectId)] }}
          </span>
          <div class="ml-8px color-#393548 text-16px font-600">
            {{ item.name }}
          </div>
        </div>
        <div class="max-w-60%">
          <TaskItem
            v-for="(taskItem, index) in threeTasks"
            :key="taskItem.id"
            :index-num="index + 1"
            :task="taskItem"
          />
        </div>
        <div v-if="item.tasks.length > 3" class="mt-16px">
          <OIWButton
            round
            text
            size="small"
            type="info"
            @click="modalShow = true"
          >
            查看全部
          </OIWButton>
          <span class="color-#C5C1D4 text-14px">
            ({{ foldTasksNum }}个环节被折叠)
          </span>
        </div>
      </div>
      <div class="content-right">
        <OIWButton round size="small" @click="goClassAnalyze">
          课时详情
        </OIWButton>
        <n-space style="margin-top: 12px">
          <OIWButton round text type="info" size="small" @click="goUpdate">
            编辑
          </OIWButton>
          <OIWButton round text type="info" size="small" @click="goCopyCreate">
            复制
          </OIWButton>
          <OIWButton
            round
            text
            type="info"
            size="small"
            :loading="downloadLoading"
            :disabled="downloadLoading"
            @click="downloadPaper"
          >
            下载题目
          </OIWButton>
        </n-space>
      </div>
    </div>
  </div>
  <OIWModal v-model:show="modalShow" size="large" preset="card">
    <div class="px-16px max-h-60vh overflow-scroll">
      <TaskItem
        v-for="(taskItem, index) in item.tasks"
        :key="taskItem.id + 'modal'"
        :index-num="index + 1"
        :task="taskItem"
      />
    </div>
  </OIWModal>
  <PaperChangeModal
    v-model:show="downloadPaperShow"
    type="download"
    :file-format="['FILE_FORMAT_PDF']"
    @cancel="handleCancelDownload"
    @confirm="handleConfirmDownload"
  />
</template>

<script lang="ts" setup>
import type { AIClassItem } from '@/pages/lesson/entry/service.ts'
import { downloadMeasuringPDFApi } from '@/pages/lesson/entry/service'
import TaskItem from '@/pages/lesson/entry/components/TaskItem.vue'
import { OIWButton, OIWModal, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import dayjs from 'dayjs'
import { useCvsEnum } from '@/hooks/useCvs.ts'
import { buryPoint } from '@/utils/buryPoint.ts'
import PaperChangeModal from '@/components/PaperChangeModal.vue'
import { downloadFile } from '@/utils/download.ts'

const props = defineProps<{
  item: AIClassItem
  serverTime: Date
}>()

const { SubjectEnum } = useCvsEnum()

const downloadPaperShow = ref(false)

// 处理课时的状态
const tagText = computed(() => {
  if (dayjs(props.serverTime).isBefore(dayjs(props.item.startAt))) {
    return '未开始'
  }
  if (dayjs(props.serverTime).isAfter(dayjs(props.item.endAt))) {
    return '已结束'
  }
  return '进行中'
})
const getStyle = computed(() => {
  if (dayjs(props.serverTime).isBefore(dayjs(props.item.startAt))) {
    return 'state-no-start'
  }
  if (dayjs(props.serverTime).isAfter(dayjs(props.item.endAt))) {
    return 'state-end'
  }
  return 'state-ing'
})

// 显示的3个task
const threeTasks = computed(() => {
  return props.item.tasks.slice(0, 3)
})
// 折叠的task数量
const foldTasksNum = computed(() => {
  return props.item.tasks.length - 3
})
// 是否显示查看全部的弹窗
const modalShow = ref(false)
// 月日
const dateStr = computed(() => {
  return dayjs(props.item.startAt).format('MM月DD日')
})
// 周几
const weekStr = computed(() => {
  return dayjs(props.item.startAt).format('ddd')
})
// 计算 startAt与endAt 之间的时长 并格式化为分钟
const timeStr = computed(() => {
  if (props.item.startAt && props.item.endAt) {
    return (
      dayjs(props.item.startAt).format('HH:mm') +
      ' - ' +
      dayjs(props.item.endAt).format('HH:mm') +
      ' ' +
      dayjs(props.item.endAt).diff(props.item.startAt, 'minute') +
      '分钟'
    )
  }
  return ''
})

const router = useRouter()
// 跳转课时详情
const goClassAnalyze = () => {
  buryPoint(
    'clickAIClassCoursePageButton',
    {
      pageName: 'list',
      button: 'detail',
    },
    'course',
  )
  router.push({
    name: 'LessonAnalyze',
    query: {
      id: props.item.id,
      groupId: props.item.groupId,
    },
  })
}
// 跳转编辑课时
const goUpdate = () => {
  buryPoint(
    'clickAIClassCoursePageButton',
    {
      pageName: 'list',
      button: 'edit',
    },
    'course',
  )
  router.push({
    name: 'LessonEdit',
    query: {
      id: props.item.id,
    },
  })
}
const message = useOIWMessage()
// 下载试卷
const downloadPaper = () => {
  console.log('下载试卷', props.item)
  const totalProblem = props.item.tasks.reduce((acc, task) => {
    return acc + task.totalProblem
  }, 0)
  if (totalProblem === 0) {
    message.warning('该课时没有题目，无法下载')
    return
  }
  downloadPaperShow.value = true
}
const handleCancelDownload = () => {
  downloadPaperShow.value = false
}
interface ModalData {
  with: 'WITH_NONE' | 'WITH_ANSWER' | 'WITH_ANSWER_AND_EXPLAIN'
  size: 'A4' | 'A3' | 'A5'
  fileFormat: 'FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF'
}
const downloadLoading = ref(false)
const handleConfirmDownload = (val: ModalData) => {
  const data = {
    measuringId: props.item.id,
    isShowAnswer:
      val.with === 'WITH_ANSWER' || val.with === 'WITH_ANSWER_AND_EXPLAIN',
    isShowExplain: val.with === 'WITH_ANSWER_AND_EXPLAIN',
  }
  message.info('下载中...')
  downloadLoading.value = true
  downloadMeasuringPDFApi(data)
    .then((res) => {
      if (res.url) {
        downloadFile(res.url, `${props.item.name}.pdf`, false)
      } else {
        message.error('下载失败')
      }
    })
    .finally(() => {
      downloadLoading.value = false
    })
}

const goCopyCreate = () => {
  router.push({
    name: 'LessonCreate',
    query: {
      copyId: props.item.id,
    },
  })
}
</script>

<style lang="scss" scoped>
.card {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}

.time-box {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 193px;
  padding: 20px 8px;
  box-shadow: inset 0 1px 0 0 #dcd8e7;

  .round-icon {
    position: absolute;
    top: -4px;
    right: -5px;
    width: 8px;
    height: 8px;
    background: #ffffff;
    border: 2px solid #9792ac;
    border-radius: 50%;
  }
}

.tag-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 28px;
  margin-top: 12px;
  font-size: 12px;
  font-weight: 600;
  line-height: 12px;
  color: #393548;
  background: #f7f7f9;
  border-radius: 28px;

  .tag {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    border-radius: 12px;
  }

  .state-end {
    background: #fa5a65;
  }

  .state-no-start {
    background: #9792ac;
  }

  .state-ing {
    background: #5e80ff;
  }
}

.card-content {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px 14px 24px 40px;
  border-left: 2px dashed #dfdce8;
  box-shadow: inset 0 1px 0 0 #dcd8e7;

  .content-left {
    flex: 1;
  }

  .content-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
  }
}

.subject-tag {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  line-height: 12px;
  text-align: center;
  border: 1px solid #c5c1d4;
  border-radius: 8px;
}
</style>
