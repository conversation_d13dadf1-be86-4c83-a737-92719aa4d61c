<template>
  <div
    ref="cardInfoRef"
    class="card-info-box"
    :style="{
      left: cardLeft,
      top: cardTop,
    }"
  >
    <div class="close-btn" @click="closeCardInfo" />
    <div class="card-info-header">
      <span class="card-info-header-name">{{ aiClassInfo.roomName }}</span>
      <span class="card-info-header-time">｜{{ startAt }}～{{ endAt }}</span>
      <span class="card-info-header-status" :class="status">{{
        statusText
      }}</span>
    </div>
    <div class="card-info-name">课程名称{{ aiClassInfo.name }}</div>
    <div class="card-info-task-list">
      <div
        v-for="(task, index) in aiClassInfo.tasks"
        :key="task.id"
        class="card-info-task-item"
      >
        <div
          class="text-12px color-#393548 border-1px border-#C5C1D4 px-5px py-3px rounded-4px leading-12px"
        >
          {{ tagName(task.tag) }}
        </div>
        <div class="card-info-task-item-index">
          环节{{ index + 1 }}
          <n-ellipsis
            v-if="task.name"
            style="max-width: 160px; margin-left: 4px"
          >
            {{ task.name }}
          </n-ellipsis>
        </div>
        <div
          v-if="
            (task.tag === 'FastReciteTag' || task.tag === 'LectureTag') &&
            task.totalItem > 0
          "
          class="card-info-task-item-topic"
        >
          {{
            `${
              task.tag === 'FastReciteTag'
                ? '专题'
                : task.tag === 'LectureTag'
                  ? '课件'
                  : '未知'
            }`
          }}
          <span class="card-info-task-item-count">{{ task.totalItem }}</span>
        </div>
        <div v-if="task.totalVideo > 0" class="card-info-task-item-video">
          微课
          <span class="card-info-task-item-count">{{ task.totalVideo }}</span>
        </div>
        <div v-if="task.totalProblem > 0" class="card-info-task-item-problem">
          题目
          <span class="card-info-task-item-count">{{ task.totalProblem }}</span>
        </div>
      </div>
    </div>
    <div class="card-info-btn-box">
      <div class="flex-1">
        <OIWButton round text type="info" size="small" @click="goUpdate"
          >编辑</OIWButton
        >
        <OIWButton round text type="info" size="small" @click="goCopyCreate"
          >复制</OIWButton
        >
        <OIWButton
          round
          text
          type="info"
          size="small"
          :loading="downloadLoading"
          :disabled="downloadLoading"
          @click="downloadPaper"
        >
          下载题目
        </OIWButton>
      </div>
      <div class="box-right">
        <OIWButton round @click="goClassAnalyze">课时详情</OIWButton>
      </div>
    </div>
  </div>
  <PaperChangeModal
    v-model:show="downloadPaperShow"
    type="download"
    :block-scroll="false"
    :file-format="['FILE_FORMAT_PDF']"
    @cancel="handleCancelDownload"
    @confirm="handleConfirmDownload"
  />
</template>

<script setup lang="ts">
import { OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import type { AIClassItem } from '@/pages/lesson/entry/service.ts'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { ref, onMounted, onUnmounted } from 'vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import PaperChangeModal from '@/components/PaperChangeModal.vue'
import { downloadMeasuringPDFApi } from '@/pages/lesson/entry/service'
import type { Tags } from '@/pages/lesson/service'
import { downloadFile } from '@/utils/download.ts'

const props = defineProps<{
  aiClassInfo: AIClassItem
  targetRef: HTMLDivElement | undefined
  serverTime: Date
}>()

const emits = defineEmits<(e: 'closeCardInfo') => void>()

const router = useRouter()

const startAt = computed(() => {
  return dayjs(props.aiClassInfo.startAt).format('HH:mm')
})

const endAt = computed(() => {
  return dayjs(props.aiClassInfo.endAt).format('HH:mm')
})

const status = computed(() => {
  if (dayjs(props.serverTime).isBefore(dayjs(props.aiClassInfo.startAt))) {
    return 'state-no-start'
  }
  if (dayjs(props.serverTime).isAfter(dayjs(props.aiClassInfo.endAt))) {
    return 'state-end'
  }
  return 'state-ing'
})
const statusText = computed(() => {
  if (dayjs(props.serverTime).isBefore(dayjs(props.aiClassInfo.startAt))) {
    return '未开始'
  }
  if (dayjs(props.serverTime).isAfter(dayjs(props.aiClassInfo.endAt))) {
    return '已结束'
  }
  return '进行中'
})

const cardLeft = computed(() => {
  return props.targetRef?.offsetLeft + 'px'
})

const cardTop = computed(() => {
  return (
    (props.targetRef
      ? props.targetRef.offsetTop + props.targetRef.offsetHeight
      : 0) +
    8 +
    66 +
    'px'
  )
})

const cardInfoRef = ref<HTMLDivElement>()

onMounted(() => {
  // 添加全局点击事件监听
  document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleOutsideClick)
})

const handleOutsideClick = (event: MouseEvent) => {
  // 如果点击的是卡片本身或者目标卡片，不关闭
  if (
    cardInfoRef.value?.contains(event.target as Node) ||
    props.targetRef?.contains(event.target as Node) ||
    downloadPaperShow.value ||
    downloadLoading.value
  ) {
    return
  }

  // 关闭卡片信息
  closeCardInfo()
}

const tagName = (tag: Tags) => {
  switch (tag) {
    case 'SelfStudyTag':
      return '自学'
    case 'FastReciteTag':
      return '快背'
    case 'LectureTag':
      return '讲授'
    default:
      return '自学'
  }
}

const closeCardInfo = () => {
  emits('closeCardInfo')
}
// 跳转课时详情
const goClassAnalyze = () => {
  buryPoint(
    'clickAIClassCoursePageButton',
    {
      pageName: 'calendar',
      button: 'detail',
    },
    'course',
  )
  router.push({
    name: 'LessonAnalyze',
    query: {
      id: props.aiClassInfo.id,
      groupId: props.aiClassInfo.groupId,
    },
  })
  emits('closeCardInfo')
}
// 跳转编辑课时
const goUpdate = () => {
  buryPoint(
    'clickAIClassCoursePageButton',
    {
      pageName: 'calendar',
      button: 'edit',
    },
    'course',
  )
  router.push({
    name: 'LessonEdit',
    query: {
      id: props.aiClassInfo.id,
    },
  })
  emits('closeCardInfo')
}
const message = useOIWMessage()
const downloadPaperShow = ref(false)
// 下载试卷
const downloadPaper = () => {
  console.log('下载试卷', props)
  const totalProblem = props.aiClassInfo.tasks.reduce((acc, task) => {
    return acc + task.totalProblem
  }, 0)
  if (totalProblem === 0) {
    message.warning('该课时没有题目，无法下载')
    return
  }
  downloadPaperShow.value = true
  // if (props.item.tasks.)
}
const handleCancelDownload = () => {
  downloadPaperShow.value = false
}
interface ModalData {
  with: 'WITH_NONE' | 'WITH_ANSWER' | 'WITH_ANSWER_AND_EXPLAIN'
  size: 'A4' | 'A3' | 'A5'
  fileFormat: 'FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF'
}
const downloadLoading = ref(false)
const handleConfirmDownload = (val: ModalData) => {
  const data = {
    measuringId: props.aiClassInfo.id,
    isShowAnswer:
      val.with === 'WITH_ANSWER' || val.with === 'WITH_ANSWER_AND_EXPLAIN',
    isShowExplain: val.with === 'WITH_ANSWER_AND_EXPLAIN',
  }
  message.info('下载中...')
  downloadLoading.value = true
  downloadMeasuringPDFApi(data)
    .then((res) => {
      if (res.url) {
        downloadFile(res.url, `${props.aiClassInfo.name}.pdf`, false)
      } else {
        message.error('下载失败')
      }
    })
    .finally(() => {
      downloadLoading.value = false
      closeCardInfo()
    })
}

const goCopyCreate = () => {
  router.push({
    name: 'LessonCreate',
    query: {
      copyId: props.aiClassInfo.id,
    },
  })
}
</script>

<style lang="scss" scoped>
.card-info-box {
  position: absolute;
  z-index: 50;
  width: 490px;
  padding: 32px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/111-7321401529a3544627ce512d5194b1c4.png')
      no-repeat center center;
    background-size: 100% 100%;
  }

  .card-info-header {
    display: flex;
    align-items: center;
    width: 100%;

    .card-info-header-name {
      font-size: 20px;
      font-weight: 600;
      color: #393548;
    }

    .card-info-header-time {
      font-size: 20px;
      font-weight: 400;
      color: #9792ac;
    }

    .card-info-header-status {
      display: flex;
      flex-direction: row;
      gap: 4px;
      align-items: center;
      width: 64px;
      height: 24px;
      padding: 6px 8px;
      margin-left: 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: 12px;
      color: #393548;
      background: #f7f7f9;
      border-radius: 100px;

      &.state-end {
        &::before {
          display: inline-block;
          width: 8px;
          height: 8px;
          content: '';
          background: #fa5a65;
          border-radius: 8px;
        }
      }

      &.state-no-start {
        &::before {
          display: inline-block;
          width: 8px;
          height: 8px;
          content: '';
          background: #9792ac;
          border-radius: 8px;
        }
      }

      &.state-ing {
        &::before {
          display: inline-block;
          width: 8px;
          height: 8px;
          content: '';
          background: #5e80ff;
          border-radius: 8px;
        }
      }
    }
  }

  .card-info-name {
    margin-top: 16px;
    overflow: hidden;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #393548;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .card-info-task-list {
    padding-top: 8px;

    .card-info-task-item {
      display: flex;
      flex-direction: row;
      gap: 16px;
      align-items: center;
      margin-top: 8px;

      .card-info-task-item-index {
        min-width: 48px;
        padding: 4px 9px;
        font-size: 12px;
        font-weight: 600;
        line-height: 12px;
        color: #57526c;
        background: #f7f7f9;
        border-radius: 4px;
      }

      .card-info-task-item-topic,
      .card-info-task-item-video,
      .card-info-task-item-problem {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #393548;
      }

      .card-info-task-item-count {
        color: #5e80ff;
      }
    }
  }

  .card-info-btn-box {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
    margin-top: 32px;

    .box-left {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .box-right {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .copy-item {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #5e80ff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
