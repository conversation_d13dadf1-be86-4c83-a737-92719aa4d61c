<template>
  <div v-if="aiClassList.length === 0" class="mt-156px">
    <OIWStateBlock type="empty" title="点击右上角按钮新建课时" />
  </div>
  <div v-if="aiClassList.length > 0">
    <AIClassCard
      v-for="item in aiClassList"
      :key="item.id"
      :item="item"
      :serverTime="serverTime"
    />
  </div>

  <n-space v-if="aiClassList.length" justify="end" style="margin-top: 20px">
    <n-pagination
      v-model:page="page"
      v-model:pageSize="pageSize"
      class="custom-pagination"
      :item-count="total"
    />
  </n-space>
</template>

<script setup lang="ts">
import { OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import AIClassCard from '@/pages/lesson/entry/components/AIClassCard.vue'
import { useServerTime } from '@/hooks/useServerTime'
import type { AIClassItem } from '@/pages/lesson/entry/service.ts'
const props = defineProps<{
  aiClassList: AIClassItem[]
  total: number
  page: number
  pageSize: number
}>()
const emits = defineEmits<{
  (e: 'update:page', page: number): void
  (e: 'update:pageSize', pageSize: number): void
}>()

const page = computed({
  get() {
    return props.page
  },
  set(value) {
    emits('update:page', value)
  },
})

const pageSize = computed({
  get() {
    return props.pageSize
  },
  set(value) {
    emits('update:pageSize', value)
  },
})

const { serverTime } = useServerTime()

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
  })
}
scrollToTop()
</script>
