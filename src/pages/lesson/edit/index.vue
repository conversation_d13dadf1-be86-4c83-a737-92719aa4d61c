<template>
  <section class="mt-30px pb-140px">
    <div class="ml-120px">
      <OIWBreadcrumb>
        <OIWBreadcrumbItem @click="onCancel">AI课堂</OIWBreadcrumbItem>
        <OIWBreadcrumbItem>编辑课时</OIWBreadcrumbItem>
      </OIWBreadcrumb>
    </div>
    <div
      :class="[isDrawerVisible ? 'ml-20px' : '']"
      class="w-860px mx-auto mt-24px space-y-24px"
    >
      <div class="row">
        <div class="text-16px mr-16px line-height-40px">
          选择班级<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content line-height-40px font-600">
          {{ currentSelectRoomsText }}
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-40px">
          开始时间<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content flex items-center">
          <div class="w-220px">
            <n-date-picker
              v-model:value="formModel.startTime"
              type="datetime"
              size="large"
              clearable
            ></n-date-picker>
          </div>
          <div class="w-140px ml-8px">
            <n-input-number
              v-model:value="formModel.duration"
              :min="5"
              :step="5"
              :max="1440"
              size="large"
            >
              <template #suffix> 分钟 </template>
            </n-input-number>
          </div>
          <div v-if="displayEndTime" class="ml-17px">{{ displayEndTime }}</div>
        </div>
      </div>

      <div class="row">
        <div class="text-16px mr-16px line-height-40px">
          课时名称<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <div class="w-486px">
            <OIWInput
              v-model:value="formModel.name"
              placeholder="请输入课时名称"
              clearable
              maxlength="30"
              show-count
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-40px w-72px">
          学科<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <div class="w-486px">
            <n-cascader
              v-model:value="csvValue"
              class="oiw-cascader"
              label-field="name"
              value-field="treeId"
              placeholder="选择学科"
              expand-trigger="click"
              :options="options"
              check-strategy="child"
              filterable
              @update:value="onCsvSelectChange"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-24px">
          学习时机<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.studyTiming">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio value="AfterClassStart" class="custom-oiw-radio"
                >课时开始后才可学习</n-radio
              >
              <n-radio value="AfterPublish" class="custom-oiw-radio"
                >发布后立即可学习</n-radio
              >
            </n-space>
          </n-radio-group>
        </div>
      </div>
      <div v-if="isAIExplainAuth" class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          AI讲解<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.enableAiExplain">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio :value="1" class="custom-oiw-radio">提供AI讲解</n-radio>
              <n-radio :value="0" class="custom-oiw-radio"
                >不提供AI讲解</n-radio
              >
            </n-space>
          </n-radio-group>
          <div class="text-12px text-#9792AC mt-8px">
            若提供，学生提交习题后可查看由<span class="color-#7B66FF font-600"
              >DeepSeek-R1</span
            >生成的深度讲解内容，解答可能不够准确，任务发布后您可于「学情分析」查看AI讲解
          </div>
        </div>
      </div>
      <div v-if="isAIExplainAuth && formModel.enableAiExplain" class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          AI互动<br />答疑<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.enableAiQa">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio :value="1" class="custom-oiw-radio"
                >提供AI互动答疑</n-radio
              >
              <n-radio :value="0" class="custom-oiw-radio"
                >不提供AI互动答疑</n-radio
              >
            </n-space>
          </n-radio-group>
          <div class="text-12px text-#9792AC mt-8px">
            若提供，学生提交习题后可通过<span class="color-#7B66FF font-600"
              >DeepSeek-R1</span
            >进行答疑互动，为控制课堂节奏请谨慎提供～
            <n-popover trigger="hover">
              <template #trigger>
                <span class="ai-qa-illustration">图示</span>
              </template>
              <div class="ai-qa-illustration-img">
                <img
                  src="https://fp.yangcong345.com/onion-extension/screenshot-20250327-211215-cf785310a27754cdd4bbb1eb1be3aa47.png"
                  alt="关联题目"
                />
              </div>
            </n-popover>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          环节<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content space-y-16px">
          <TaskPanel
            v-for="(task, index) of tasks"
            :id="`lesson-task-panel-${index}`"
            :key="index"
            class="border-1px border-solid"
            :class="[
              index === currentIndex ? 'border-#5E80FF' : 'border-#D4D1DD',
            ]"
            :index="index"
            :task="task"
            is-edit
            :is-last="index === tasks.length - 1"
            page="edit"
            @on-add="onAddTaskItemDrawerShow(index, task.tag)"
            @on-up="onUpMove(index)"
            @on-down="onDownMove(index)"
            @on-del="onDelTask(index)"
            @on-preview="onPreview"
            @on-edit-sub-group="onEditSubGroup"
          />
          <div class="flex items-center gap-15px">
            <div
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskSelfStudy"
            >
              <AddIcon class="w-24px h-24px" />
              添加自学环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>「自学环节」教师可以添加微课或题目供学生学习</div>
              </n-popover>
            </div>
            <div
              v-if="isShowFastReciteBtn"
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskFastRecite"
            >
              <AddIcon class="w-24px h-24px" />
              添加快背环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>
                  「快背环节」针对学科中短小易懂的微知识点，设计了新的学习模式。<br />
                  通过"先做题检验-学习讲解-重复强化"的路径，与常规课程互补，<br />帮助学生快速掌握基础知识体系
                </div>
              </n-popover>
            </div>
            <div
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskLecture"
            >
              <AddIcon class="w-24px h-24px" />
              添加讲授环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>
                  讲授环节可以添加课件，方便在课时详情页面打开，<br />
                  学生可以在平板查看课件，标记「有疑问」的部分
                </div>
              </n-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{ 'left-20px': isDrawerVisible, 'w-800px': isDrawerVisible }"
      class="bg-#fff py-24px fixed left-0 right-0 bottom-0"
    >
      <div
        class="w-800px px-24px mx-auto h-80px rounded-12px bg-#F7F7F9 flex justify-center items-center"
      >
        <div>
          <span>课时内容：</span>
          <span>{{ tasks.length }}个环节</span>
          <span v-if="totalTaskTimeCount"
            >｜预计时长：{{ secondFomatter(totalTaskTimeCount) }}分钟</span
          >
        </div>
        <div class="ml-auto">
          <OIWButton ghost type="info" @click="onCancel">取消</OIWButton>
          <OIWButton class="ml-16px" @click="onCreate">保存</OIWButton>
        </div>
      </div>
    </div>
  </section>
  <SlideResource
    v-model:show="addDrawerShow"
    scene="edit"
    @add="onAddTaskItem"
  />
  <SlideFastRecite
    v-model:show="addDrawerFastReciteShow"
    @add="onAddTaskItem"
  />
  <SlideLecture v-model:show="addDrawerLectureShow" @add="onAddTaskItem" />
  <VideoModal
    v-model:show="videoPreviewShow"
    :clip-id="previewVideoInfo.clipId"
    :special-course-id="previewVideoInfo.specialCourseId"
    :topic-id="previewVideoInfo.topicId"
    :videoBuryPointParams="taskVideoBuryPointParams"
    :videoClip="previewVideoInfo.selfClip"
  />
  <ProblemPreviewModal
    v-model:show="problemPreviewShow"
    :problem-id="problemPreviewInfo.problemId"
    :type="problemPreviewInfo.type"
  />
  <VideoModalMp4
    v-model:show="schoolVideoPreviewModalShow"
    :video-id="schoolVideoPreviewInfo.id"
    :buryPointParams="{
      videoType: 'school',
      videoScene: 'AIClassEditTaskSchoolVideo',
    }"
    :videoClip="schoolVideoPreviewInfo.selfClip"
  />
  <ClassGroupModal
    v-model:show="classGroupModalShow"
    :room-id="currentRoom?.id"
    :room-ref-id="currentRoom?.ref"
    :room="editSubGroup"
    @on-sub-group-change="onSubGroupChange"
  />
</template>

<script lang="ts" setup>
import ClassGroupModal from '../components/ClassGroupModal.vue'
import VideoModal from '@/components/SideResource/Video/VideoModal.vue'
import ProblemPreviewModal from '../components/ProblemPreviewModal.vue'
import { useAuth } from '@/hooks/useAuth'
import {
  OIWBreadcrumb,
  OIWBreadcrumbItem,
  OIWInput,
  OIWButton,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { useCvs } from '@/hooks/useCvs'
import type { CascaderOption } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import type {
  EditableTaskGroup,
  EditableTaskGroupSubGroup,
  LessonTaskDetail,
  CreateTaskItem,
} from '../service'
import { getLessonTaskDetailApi, updateLessonTaskApi } from '../service'
import AddIcon from '~icons/yc/add'
import TaskPanel from '../components/TaskPanel.vue'
import SlideResource from '@/components/SideResource/index.vue'
import SlideFastRecite from '@/components/SlideFastRecite/index.vue'
import SlideLecture from '@/components/SlideLecture/index.vue'
import { v1 as uuidv1 } from 'uuid'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { secondFomatter } from '../utils'
import { useCreateHooks } from '../create/hook'
import { getSchemesApi, type GroupingScheme } from '@/pages/classManage/service'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useStayTime } from '@/hooks/useStayTime'
import VideoModalMp4 from '@/components/VideoPlayer/VideoModalMp4.vue'
import QuestionIcon from '~icons/yc/question-color.svg'
import { useCvsEnum } from '@/hooks/useCvs'
import useSideResourceStore from '@/components/SideResource/store'
import { getTeacherRoomListAIClass } from '@/pages/lesson/entry/service'

const props = defineProps<{
  id: string
}>()
const { SubjectEnum } = useCvsEnum()

dayjs.extend(duration)

// 埋点-页面停留时长
useStayTime('getNewCoursePageStayDuration', 'course', {
  pageName: 'edit',
})
const sideResourceStore = useSideResourceStore()
const formModel = reactive<{
  groupId: string
  startTime?: number
  duration?: number
  name?: string
  subjectId?: number
  stageId?: number
  enableAiExplain?: number
  enableAiQa?: number
  studyTiming?: 'AfterPublish' | 'AfterClassStart'
}>({
  groupId: '',
  startTime: undefined,
  duration: undefined,
  name: undefined,
  subjectId: undefined,
  stageId: undefined,
  enableAiExplain: undefined,
  enableAiQa: undefined,
  studyTiming: 'AfterClassStart',
})

const isShowFastReciteBtn = computed(() => {
  return ['语文', '英语', '生物', '地理'].includes(
    SubjectEnum.value[Number(formModel.subjectId || 1)],
  )
})

const roomGroups = ref<GroupingScheme[]>([])
async function fetchRoomGroup() {
  await fetchRooms()
  if (currentRoom.value) {
    const res = await getSchemesApi(currentRoom.value.id)
    res.forEach((item) => {
      item.label = `${item.name}（${item.creatorName}）`
    })
    roomGroups.value = res
  }
  return []
}
const taskDetail = ref<LessonTaskDetail>()
async function fetchTaskDetail() {
  const res = await getLessonTaskDetailApi(props.id)
  taskDetail.value = res
  await fetchRoomGroup()
  const {
    groupId,
    startAt,
    endAt,
    name,
    subjectId,
    stageId,
    tasks: taskList,
    enableAiExplain,
    enableAiQa,
    studyTiming,
  } = res
  const startTime = dayjs(startAt)
  const endTime = dayjs(endAt)
  formModel.groupId = groupId
  formModel.startTime = startTime.toDate().getTime()
  formModel.duration = dayjs.duration(endTime.diff(startTime)).asMinutes()
  formModel.name = name
  formModel.subjectId = subjectId
  formModel.stageId = stageId
  formModel.enableAiExplain = enableAiExplain ? 1 : 0
  formModel.enableAiQa = enableAiQa ? 1 : 0
  formModel.studyTiming = studyTiming || 'AfterPublish'
  const tempItemList = taskList.reduce((total, task) => {
    const temp = task.items && task.items.length ? task.items : []
    return total.concat(temp)
  }, [] as CreateTaskItem[])
  const tempTaskItems = await getProblemBodyList(tempItemList)
  const tempTaskList = taskList.map((task) => {
    task.items = task.items.map((item) => {
      if (
        item.sourceType === 'Problem' ||
        item.sourceType === 'SchoolProblem'
      ) {
        const temp =
          tempTaskItems.find(
            (tempItem) =>
              tempItem.id === item.id && tempItem.sourceId === item.sourceId,
          ) || item
        return {
          ...temp,
        }
      } else {
        return item
      }
    }) as CreateTaskItem[]
    return {
      ...task,
    }
  })
  tasks.value = tempTaskList.map((task) => {
    return {
      ...task,
      items: task.items.map((item) => {
        return {
          ...item,
          key: uuidv1(),
        }
      }),
      subGroupInfo: [task.subGroupInfo]
        .map((group) => {
          if (!group.schemeId) {
            return {
              groupId: group.groupId,
              schemeId: '',
              name: currentRoom.value?.name,
              memberCount: currentRoom.value?.memberCount,
              subGroup: [],
            }
          }
          const roomGroup = roomGroups.value.find(
            (roomGroup) => roomGroup.id === group.schemeId,
          )
          if (roomGroup) {
            return {
              groupId: group.groupId,
              schemeId: group.schemeId,
              name: currentRoom.value?.name,
              memberCount: currentRoom.value?.memberCount,
              subGroup: group.subGroupIds
                .map((subGroupId) => {
                  const subGroup = roomGroup?.groups.find(
                    (subGroup) => subGroup.id === subGroupId,
                  )
                  if (subGroup) {
                    return {
                      name: `${roomGroup?.name}-${subGroup.name}`,
                      id: subGroupId,
                      memberCount: subGroup.members.length,
                    }
                  }
                  return undefined
                })
                .filter((el): el is EditableTaskGroupSubGroup => !!el),
            }
          }
          return undefined
        })
        .filter((el): el is EditableTaskGroup => !!el),
    }
  })
  csvValue.value = `${stageId}-${subjectId}`
}
fetchTaskDetail()

const displayEndTime = computed(() => {
  if (formModel?.startTime && formModel.duration) {
    const startDay = dayjs(formModel.startTime)
    return `${startDay.format('HH:mm')} - ${startDay
      .add(formModel.duration, 'minutes')
      .format('HH:mm')}`
  }
})
const { subjectId, stageId, isAIExplainAuth } = useAuth()
const csv = useCvs()
const options = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = item.subjects as CascaderOption[]
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})

const csvValue = ref<string>(`${stageId.value}-${subjectId.value}`)
const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
}

const rooms = ref<YcType.TeacherRoom[]>([])
const currentRoom = computed(() => {
  return rooms.value.find((room) => room.groupId === taskDetail.value?.groupId)
})
const currentSelectRoomsText = computed(() => {
  return `${currentRoom.value?.name} | 学生（${currentRoom.value?.memberCount}）`
})
async function fetchRooms() {
  const res = await getTeacherRoomListAIClass()
  rooms.value = res.rows
}

const {
  tasks,
  addNewTaskSelfStudy,
  addNewTaskFastRecite,
  addNewTaskLecture,
  totalTaskTimeCount,
  currentIndex,
  addDrawerShow,
  addDrawerFastReciteShow,
  addDrawerLectureShow,
  onAddTaskItemDrawerShow,
  onAddTaskItem,
  onUpMove,
  onDownMove,
  onDelTask,
  onPreview,
  onCancel,
  previewVideoInfo,
  videoPreviewShow,
  problemPreviewShow,
  problemPreviewInfo,
  schoolVideoPreviewModalShow,
  schoolVideoPreviewInfo,
  getProblemBodyList,
} = useCreateHooks()

const isDrawerVisible = computed(
  () =>
    addDrawerShow.value ||
    addDrawerFastReciteShow.value ||
    addDrawerLectureShow.value,
)

watch(
  () => tasks.value,
  (val) => {
    const problems = val.reduce((total, task) => {
      let temp: string[] = []
      if (task.tag === 'SelfStudyTag') {
        temp = task.items
          .filter(
            (item) =>
              item.sourceType === 'Problem' ||
              item.sourceType === 'SchoolProblem',
          )
          .map((item) => item.sourceId as string)
      }
      return total.concat(temp as string[])
    }, [] as string[])
    sideResourceStore.setSelectProblemIds(problems)
  },
  {
    deep: true,
  },
)

const onAddNewTaskSelfStudy = () => {
  addNewTaskSelfStudy()
  nextTick(() => updateTaskSubGroup())
  buryPoint(
    'clickNewCoursePageButton',
    {
      button: 'task',
      pageName: 'edit',
    },
    'course',
  )
}

const onAddNewTaskFastRecite = () => {
  addNewTaskFastRecite()
  nextTick(() => updateTaskSubGroup())
}

const onAddNewTaskLecture = () => {
  addNewTaskLecture()
  nextTick(() => updateTaskSubGroup())
}

watch(
  () => formModel.enableAiExplain,
  (value) => {
    if (!value) {
      formModel.enableAiQa = 0
    }
  },
  {
    deep: true,
  },
)
function updateTaskSubGroup() {
  if (currentRoom.value) {
    tasks.value.forEach((task) => {
      const addGroups = task.subGroupInfo.some(
        (subGroup) => subGroup.groupId === currentRoom.value!.groupId,
      )
      if (!addGroups) {
        task.subGroupInfo.push({
          groupId: currentRoom.value!.groupId,
          roomId: currentRoom.value!.id,
          ref: currentRoom.value!.ref,
          name: currentRoom.value!.name,
          memberCount: currentRoom.value!.memberCount,
          subGroup: [],
          schemeId: '',
        })
      }
    })
  }
}
const dialog = useOIWDialog()
const message = useOIWMessage()

const onCreate = () => {
  const { stageId, name, startTime, duration, subjectId } = formModel
  if (
    !stageId ||
    !subjectId ||
    !name ||
    !startTime ||
    !duration ||
    !tasks.value.length ||
    !tasks.value.some((task) => task.items.length)
  ) {
    message.warning('请填写必填项')
    return
  }
  if (totalTaskTimeCount.value && totalTaskTimeCount.value / 60 > duration) {
    dialog.create({
      showIcon: false,
      type: 'warning',
      title: '提示',
      content: '学习内容预计时长超出课时时间，是否继续发布？',
      positiveText: '确定',
      negativeText: '返回',
      onPositiveClick: () => {
        dialog.create({
          showIcon: false,
          type: 'info',
          title: '提示',
          content: '发布后学生即可查看课时内容，是否确认发布？',
          positiveText: '发布',
          negativeText: '返回',
          onPositiveClick: () => saveUpdate(),
        })
      },
    })
  } else {
    saveUpdate()
  }
}

const classGroupModalShow = ref(false)
const editSubGroup = ref<EditableTaskGroup>()
const onEditSubGroup = (subGroup: EditableTaskGroup) => {
  editSubGroup.value = subGroup
  classGroupModalShow.value = true
}
const onSubGroupChange = (subGroup: EditableTaskGroup) => {
  if (editSubGroup.value) {
    editSubGroup.value.subGroup = subGroup.subGroup
    editSubGroup.value.schemeId = subGroup.schemeId
  }
}

const router = useRouter()
async function saveUpdate() {
  const {
    stageId,
    name,
    startTime,
    duration,
    groupId,
    subjectId,
    enableAiExplain,
    enableAiQa,
    studyTiming,
  } = formModel
  await updateLessonTaskApi(taskDetail.value!.id, {
    name: name!,
    startAt: dayjs(startTime).toDate(),
    endAt: dayjs(startTime).add(duration!, 'minutes').toDate(),
    groupId,
    subjectId: subjectId!,
    stageId: stageId!,
    enableAiExplain: !!enableAiExplain,
    enableAiQa: !!enableAiQa,
    studyTiming: studyTiming,
    tasks: tasks.value
      .filter((el) => el.items.length)
      .map((task) => {
        return {
          name: task.name,
          id: task.id,
          subGroupInfo: task.subGroupInfo.map((group) => {
            return {
              groupId: group.groupId,
              schemeId: group.schemeId,
              subGroupIds: group.subGroup.map((el) => el.id),
            }
          })[0],
          tag: task.tag || 'MeasuringTaskTagNone',
          items: task.items.map((el) => {
            const {
              sourceType,
              sourceId,
              topicId,
              duration,
              stageId,
              subjectId,
              publisherId,
              semesterId,
              specialCourseId,
              videoClipExtra,
              name,
              measuringTaskId,
              id,
              chapterId,
              sectionId,
              subSectionId,
              importFrom,
              paperId,
              extra,
            } = el
            return {
              sourceId,
              sourceType,
              topicId,
              duration,
              stageId,
              subjectId,
              publisherId,
              semesterId,
              specialCourseId,
              videoClipExtra,
              name,
              id: id || '',
              measuringTaskId: measuringTaskId || '',
              chapterId,
              sectionId,
              subSectionId,
              importFrom,
              paperId,
              extra,
            }
          }),
        }
      }),
  })
  window.sessionStorage.setItem('AI_LESSON_EDITING_LEAVE', '1')
  router.replace({
    name: 'LessonEntry',
  })
  buryPoint(
    'clickNewCoursePageButton',
    {
      button: 'publish',
      pageName: 'edit',
    },
    'course',
  )
}

buryPoint('enterNewCoursePage', { pageName: 'edit' }, 'course')
// 编辑课时-任务-预览微课的视频埋点
const taskVideoBuryPointParams = computed(() => {
  return {
    videoType: previewVideoInfo.value.clipId ? 'course_clip' : 'course',
    videoScene: 'AIClassEditTaskPublicVideo',
  }
})
</script>

<style lang="scss" scoped>
::v-deep(.n-checkbox__label) {
  display: flex;
  flex: 1;
}

.row {
  display: flex;
  justify-content: center;
  user-select: none;
}

.row-content {
  width: 711px;
}

::v-deep(.n-date-picker) {
  .n-input__border {
    border: 1px solid #c5c1d4;
    border-radius: 12px;
  }

  .n-input__state-border {
    border-radius: 12px;
  }
}

::v-deep(.n-input-number) {
  .n-input__border {
    border-radius: 12px;
  }

  .n-input__state-border {
    border-radius: 12px !important;
  }

  .n-input:not(.n-input--disabled):hover .n-input__state-border {
    border: 1px solid #5381fe;
    border-radius: 12px;
  }
}

.oiw-cascader {
  ::v-deep(.n-base-selection) {
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 12px;
  }
}

.ai-qa-illustration {
  margin-left: 8px;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  color: #7290ff;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.ai-qa-illustration-img {
  height: 300px;

  img {
    display: inline-block;
    height: 300px;
    object-fit: cover;
  }
}
</style>
