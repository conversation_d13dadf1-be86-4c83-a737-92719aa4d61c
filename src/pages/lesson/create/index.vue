<template>
  <section class="mt-30px pb-140px">
    <div class="ml-120px">
      <OIWBreadcrumb>
        <OIWBreadcrumbItem @click="onCancel">AI课堂</OIWBreadcrumbItem>
        <OIWBreadcrumbItem>新建课时</OIWBreadcrumbItem>
      </OIWBreadcrumb>
    </div>
    <div
      :class="[isDrawerVisible ? 'ml-20px' : '']"
      class="w-860px mx-auto mt-24px space-y-24px"
    >
      <div
        class="w-800px mx-auto rounded-12px flex items-center px-16px h-48px bg-#F4F6FF my-24px"
      >
        <TipIcon class="mr-8px" />
        <div class="text-14px font-600 color-#393548">
          AI课堂属于学校付费权益，仅支持布置给学校管理员创建的行政班、AI课堂教学班
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px pt-4px">
          选择班级<span class="color-#FA5A65">*</span>
        </div>
        <div
          class="row-content rounded-12px border-#D4D1DD border-solid border-1px py-20px px-16px box-border"
        >
          <n-scrollbar class="w-full max-h-300px">
            <n-checkbox-group v-model:value="formModel.groupIds">
              <n-space vertical>
                <n-checkbox
                  v-for="room of rooms"
                  :key="room.groupId"
                  class="w-full"
                  :value="room.groupId"
                >
                  <div class="flex items-center">
                    <n-ellipsis style="max-width: 250px; margin-left: 8px">
                      {{ room.name }}
                    </n-ellipsis>
                    <span
                      v-if="room.type === 'admin'"
                      class="h-22px ml-8px px-4px text-center rounded-4px border-1px border-solid border-[rgba(254,163,69,0.6)] text-12px font-600 leading-20px text-#FEA345"
                      >行政班
                    </span>
                    <span
                      v-if="room.type === 'teaching-good'"
                      class="h-22px ml-8px px-4px text-center rounded-4px border-1px border-solid border-[rgba(78, 204, 94, 0.6)] text-12px font-600 leading-20px text-#4ECC5E"
                      >AI课堂教学班
                    </span>
                  </div>
                  <span class="ml-auto">学生（{{ room.memberCount }}）</span>
                </n-checkbox>
              </n-space>
            </n-checkbox-group>
          </n-scrollbar>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-40px">
          开始时间<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content flex items-center">
          <div class="w-220px">
            <n-date-picker
              v-model:value="formModel.startTime"
              type="datetime"
              size="large"
              clearable
            ></n-date-picker>
          </div>
          <div class="w-140px ml-8px">
            <n-input-number
              v-model:value="formModel.duration"
              :min="5"
              :step="5"
              :max="1440"
              size="large"
            >
              <template #suffix> 分钟 </template>
            </n-input-number>
          </div>
          <div v-if="displayEndTime" class="ml-17px">{{ displayEndTime }}</div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-40px">
          课时名称<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <div class="w-486px">
            <OIWInput
              v-model:value="formModel.name"
              class="course-name-input"
              placeholder="请输入课时名称"
              clearable
              maxlength="30"
              show-count
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-40px w-72px">
          学科<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <div class="w-486px">
            <n-cascader
              v-model:value="csvValue"
              class="oiw-cascader"
              label-field="name"
              value-field="treeId"
              placeholder="选择学科"
              expand-trigger="click"
              :options="options"
              check-strategy="child"
              filterable
              @update:value="onCsvSelectChange"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-24px">
          学习时机<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.studyTiming">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio value="AfterClassStart" class="custom-oiw-radio"
                >课时开始后才可学习</n-radio
              >
              <n-radio value="AfterPublish" class="custom-oiw-radio"
                >发布后立即可学习</n-radio
              >
            </n-space>
          </n-radio-group>
        </div>
      </div>
      <div v-if="isAIExplainAuth" class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          AI讲解<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.enableAiExplain">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio :value="1" class="custom-oiw-radio">提供AI讲解</n-radio>
              <n-radio :value="0" class="custom-oiw-radio"
                >不提供AI讲解</n-radio
              >
            </n-space>
          </n-radio-group>
          <div class="text-12px text-#9792AC mt-8px">
            若提供，学生提交习题后可查看由<span class="color-#7B66FF font-600"
              >DeepSeek-R1</span
            >生成的深度讲解内容，解答可能不够准确，任务发布后您可于「学情分析」查看AI讲解
          </div>
        </div>
      </div>
      <div v-if="isAIExplainAuth && formModel.enableAiExplain" class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          AI互动<br />答疑<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content">
          <n-radio-group v-model:value="formModel.enableAiQa">
            <n-space item-style="display: flex; align-items: center;">
              <n-radio :value="1" class="custom-oiw-radio"
                >提供AI互动答疑</n-radio
              >
              <n-radio :value="0" class="custom-oiw-radio"
                >不提供AI互动答疑</n-radio
              >
            </n-space>
          </n-radio-group>
          <div class="text-12px text-#9792AC mt-8px">
            若提供，学生提交习题后可通过<span class="color-#7B66FF font-600"
              >DeepSeek-R1</span
            >进行答疑互动，为控制课堂节奏请谨慎提供～
            <n-popover trigger="hover">
              <template #trigger>
                <span class="ai-qa-illustration">图示</span>
              </template>
              <div class="ai-qa-illustration-img">
                <img
                  src="https://fp.yangcong345.com/onion-extension/screenshot-20250327-211215-cf785310a27754cdd4bbb1eb1be3aa47.png"
                  alt="关联题目"
                />
              </div>
            </n-popover>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="text-16px mr-16px line-height-24px w-72px">
          环节<span class="color-#FA5A65">*</span>
        </div>
        <div class="row-content space-y-16px">
          <TaskPanel
            v-for="(task, index) of tasks"
            :id="`lesson-task-panel-${index}`"
            :key="index"
            class="border-1px border-solid"
            :class="[
              index === currentIndex ? 'border-#5E80FF' : 'border-#D4D1DD',
            ]"
            :index="index"
            :task="task"
            :is-last="index === tasks.length - 1"
            :page="isFromDraft ? 'edit' : 'new'"
            @on-add="onAddTaskItemDrawerShow(index, task.tag)"
            @on-up="onUpMove(index)"
            @on-down="onDownMove(index)"
            @on-del="onDelTask(index)"
            @on-preview="onPreview"
            @on-edit-sub-group="onEditSubGroup"
          />
          <div class="flex items-center gap-15px">
            <div
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskSelfStudy"
            >
              <AddIcon class="w-24px h-24px" />
              添加自学环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>「自学环节」教师可以添加微课或题目供学生学习</div>
              </n-popover>
            </div>
            <div
              v-if="isShowFastReciteBtn"
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskFastRecite"
            >
              <AddIcon class="w-24px h-24px" />
              添加快背环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>
                  「快背环节」针对学科中短小易懂的微知识点，设计了新的学习模式。<br />
                  通过"先做题检验-学习讲解-重复强化"的路径，与常规课程互补，<br />帮助学生快速掌握基础知识体系
                </div>
              </n-popover>
            </div>
            <div
              class="cursor-pointer flex-1 flex items-center justify-center color-#57526C font-600 text-16px w-711px h-56px rounded-12px border-1px border-dashed border-#D4D1DD"
              @click="onAddNewTaskLecture"
            >
              <AddIcon class="w-24px h-24px" />
              添加讲授环节
              <n-popover trigger="hover">
                <template #trigger>
                  <QuestionIcon
                    class="color-#9792AC w-16px h-16px ml-2px outline-none"
                  />
                </template>
                <div>
                  讲授环节可以添加课件，方便在课时详情页面打开，<br />
                  学生可以在平板查看课件，标记「有疑问」的部分
                </div>
              </n-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{ 'left-20px': isDrawerVisible, 'w-900px': isDrawerVisible }"
      class="bg-#fff py-24px fixed left-0 right-0 bottom-0"
    >
      <div
        class="w-900px px-24px mx-auto h-80px rounded-12px bg-#F7F7F9 flex justify-center items-center"
      >
        <div>
          <span>课时内容：</span>
          <span>{{ tasks.length }}个环节</span>
          <span v-if="totalTaskTimeCount"
            >｜预计时长：{{ secondFomatter(totalTaskTimeCount) }}分钟</span
          >
        </div>
        <div class="ml-auto">
          <OIWButton
            v-if="isFromDraft"
            class="mr-16px"
            type="error"
            @click="onDeleteDraft"
            >删除</OIWButton
          >
          <OIWButton ghost type="info" @click="onCancelConfrim">取消</OIWButton>
          <OIWButton class="ml-16px" ghost type="info" @click="onSaveDraft">{{
            isFromDraft ? '保存' : '存草稿'
          }}</OIWButton>
          <OIWButton class="ml-16px" @click="onCreate">发布</OIWButton>
        </div>
      </div>
    </div>
  </section>
  <SlideResource
    v-model:show="addDrawerShow"
    :scene="isFromDraft ? 'draft' : 'create'"
    @add="onAddTaskItem"
  />
  <SlideFastRecite
    v-model:show="addDrawerFastReciteShow"
    @add="onAddTaskItem"
  />
  <SlideLecture v-model:show="addDrawerLectureShow" @add="onAddTaskItem" />
  <VideoModal
    v-model:show="videoPreviewShow"
    :clip-id="previewVideoInfo.clipId"
    :special-course-id="previewVideoInfo.specialCourseId"
    :topic-id="previewVideoInfo.topicId"
    :videoBuryPointParams="taskVideoBuryPointParams"
    :videoClip="previewVideoInfo.selfClip"
  />
  <ProblemPreviewModal
    v-model:show="problemPreviewShow"
    :problem-id="problemPreviewInfo.problemId"
    :type="problemPreviewInfo.type"
  />
  <VideoModalMp4
    v-model:show="schoolVideoPreviewModalShow"
    :video-id="schoolVideoPreviewInfo.id"
    :buryPointParams="taskMP4VideoBuryPointParams"
    :videoClip="schoolVideoPreviewInfo.selfClip"
  />
  <ClassGroupModal
    v-model:show="classGroupModalShow"
    :room-ref-id="editSubGroup?.ref"
    :room-id="editSubGroup?.roomId"
    :room="editSubGroup"
    @on-sub-group-change="onSubGroupChange"
  />
</template>

<script lang="ts" setup>
import ProblemPreviewModal from '../components/ProblemPreviewModal.vue'
import VideoModal from '@/components/SideResource/Video/VideoModal.vue'
import { useAuth } from '@/hooks/useAuth'
import {
  OIWBreadcrumb,
  OIWBreadcrumbItem,
  OIWInput,
  OIWButton,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { useCvs, useCvsEnum } from '@/hooks/useCvs'
import type { CascaderOption } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import type {
  EditableTaskGroup,
  EditableTaskGroupSubGroup,
  CreateTaskItem,
} from '../service'
import {
  createNewTaskLessonApi,
  deleteMeasuringDraftApi,
  getAllRoomSchemesApi,
  getMeasuringDraftApi,
  saveMeasuringDraftApi,
  updateMeasuringDraftApi,
  getLessonTaskDetailApi,
} from '../service'
import AddIcon from '~icons/yc/add'
import TaskPanel from '../components/TaskPanel.vue'
import SlideResource from '@/components/SideResource/index.vue'
import SlideFastRecite from '@/components/SlideFastRecite/index.vue'
import SlideLecture from '@/components/SlideLecture/index.vue'
import dayjs from 'dayjs'
import { secondFomatter } from '../utils'
import { useCreateHooks } from './hook'
import VideoModalMp4 from '@/components/VideoPlayer/VideoModalMp4.vue'
import ClassGroupModal from '../components/ClassGroupModal.vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useStayTime } from '@/hooks/useStayTime'
import { v1 as uuidv1 } from 'uuid'
import useSideResourceStore from '@/components/SideResource/store'
import TipIcon from '~icons/yc/tip-blue'
import QuestionIcon from '~icons/yc/question-color.svg'
import { getTeacherRoomListAIClass } from '@/pages/lesson/entry/service'

const props = defineProps<{
  draftId?: string
  startTime?: number | undefined
  duration?: number | undefined
  copyId?: string
}>()
const sideResourceStore = useSideResourceStore()
const { stageId, subjectId, isAIExplainAuth } = useAuth()
const { SubjectEnum } = useCvsEnum()

const formModel = reactive<{
  groupIds: string[]
  startTime?: number
  duration?: number
  name?: string
  subjectId?: number
  stageId?: number
  enableAiExplain?: number
  enableAiQa?: number
  studyTiming?: 'AfterPublish' | 'AfterClassStart'
}>({
  groupIds: [],
  startTime: props.startTime
    ? new Date(props.startTime).getTime()
    : new Date().getTime(),
  duration: props.duration || 40,
  name: dayjs().format('M月D日数学课时'),
  subjectId: subjectId.value,
  stageId: stageId.value,
  enableAiExplain: isAIExplainAuth.value ? 1 : 0,
  enableAiQa: 0,
  studyTiming: 'AfterClassStart',
})

const isShowFastReciteBtn = computed(() => {
  return ['语文', '英语', '生物', '地理'].includes(
    SubjectEnum.value[Number(formModel.subjectId || 1)],
  )
})

const csvValue = ref<string>(`${stageId.value}-${subjectId.value}`)
const displayEndTime = computed(() => {
  if (formModel.startTime && formModel.duration) {
    const startDay = dayjs(formModel.startTime)
    return `${startDay.format('HH:mm')} - ${startDay
      .add(formModel.duration, 'minutes')
      .format('HH:mm')}`
  }
})

const csv = useCvs()
const options = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = item.subjects as CascaderOption[]
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})
const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
}

const rooms = ref<YcType.TeacherRoom[]>([])
async function fetchRooms() {
  const res = await getTeacherRoomListAIClass()
  rooms.value = res.rows
}

const route = useRoute()
const isFromDraft = computed(
  () => route.name === 'LessonCreateFromDraft' && props.draftId,
)
watchEffect(() => {
  if (isFromDraft.value) {
    if (formModel.stageId && formModel.subjectId) {
      csvValue.value = `${formModel.stageId}-${formModel.subjectId}`
    }
  } else if (subjectId.value && stageId.value) {
    formModel.name = `${dayjs().format('M月D日')}${
      SubjectEnum.value[subjectId.value]
    }课时`
    formModel.subjectId = subjectId.value
    formModel.stageId = stageId.value
    csvValue.value = `${stageId.value}-${subjectId.value}`
  }
})

const buryPointPageName = computed(() => {
  if (isFromDraft.value) {
    return 'edit'
  } else {
    return 'new'
  }
})
async function init() {
  if (isFromDraft.value) {
    const [res] = await Promise.all([
      getMeasuringDraftApi(props.draftId!),
      fetchRooms(),
    ])
    const allSchemes = await getAllRoomSchemesApi(
      rooms.value.map((el) => el.id),
    )
    // 根据当前最新的班级列表和班级分组信息过滤仍然有效的分组和班级数据
    const filterTasks = res.content.tasks.map((task) => {
      return {
        ...task,
        subGroupInfo: task.subGroupInfo
          .map((group) => {
            const currentRoom = rooms.value.find(
              (room) => room.groupId === group.groupId,
            )
            const currentRoomSchemes = allSchemes.rooms.find(
              (room) => room.roomId === currentRoom?.id,
            )
            const currentScheme = currentRoomSchemes?.schemes.find(
              (scheme) => scheme.id === group.schemeId,
            )
            const isFilter =
              (!!currentRoom && !!currentScheme) ||
              (!!currentRoom && !group.subGroup.length)
            if (isFilter) {
              return {
                ...group,
                name: currentRoom.name,
                subGroup: group.subGroup
                  .map((subGroup) => {
                    const matchSubGroup = currentScheme?.groups.find(
                      (el) => el.id === subGroup.id,
                    )
                    return matchSubGroup
                      ? {
                          ...subGroup,
                          name: `${currentScheme?.name}-${matchSubGroup.name}`,
                        }
                      : undefined
                  })
                  .filter((el): el is EditableTaskGroupSubGroup => !!el),
              }
            }
            return undefined
          })
          .filter((el): el is EditableTaskGroup => !!el),
      }
    })
    const {
      groupIds,
      name,
      startAt,
      duration,
      subjectId,
      stageId,
      enableAiExplain,
      enableAiQa,
      studyTiming,
    } = res.content
    const selectRooms = rooms.value.filter(
      (el) => groupIds?.includes(el.groupId),
    )
    formModel.groupIds = selectRooms.map((el) => el.groupId) || []
    formModel.name = name
    formModel.startTime = startAt
      ? dayjs(startAt).toDate().getTime()
      : new Date().getTime()
    formModel.duration = duration ? duration : 40
    formModel.subjectId = subjectId ? subjectId : 1
    formModel.stageId = stageId ? stageId : 2
    formModel.enableAiExplain = enableAiExplain ? 1 : 0
    formModel.enableAiQa = enableAiQa ? 1 : 0
    formModel.studyTiming = studyTiming || 'AfterPublish'
    const tempItemList = filterTasks.reduce((total, task) => {
      const temp = task.items && task.items.length ? task.items : []
      return total.concat(temp)
    }, [] as CreateTaskItem[])
    const tempTaskItems = await getProblemBodyList(tempItemList)
    const tempTaskList = filterTasks.map((task) => {
      task.items = task.items.map((item) => {
        if (
          item.sourceType === 'Problem' ||
          item.sourceType === 'SchoolProblem'
        ) {
          const temp =
            tempTaskItems.find(
              (tempItem) =>
                tempItem.id === item.id && tempItem.sourceId === item.sourceId,
            ) || item
          return {
            ...temp,
          }
        } else {
          return item
        }
      }) as CreateTaskItem[]
      return {
        ...task,
      }
    })
    tasks.value = tempTaskList
  } else if (props.copyId) {
    const [res] = await Promise.all([
      getLessonTaskDetailApi(props.copyId),
      fetchRooms(),
    ])
    formModel.groupIds = [rooms.value[0].groupId]
    const { name, tasks: taskList } = res
    formModel.name = name
    const tempItemList = taskList.reduce((total, task) => {
      const temp = task.items && task.items.length ? task.items : []
      return total.concat(temp)
    }, [] as CreateTaskItem[])
    const tempTaskItems = await getProblemBodyList(tempItemList)
    const tempTaskList = taskList.map((task) => {
      task.items = task.items.map((item) => {
        if (
          item.sourceType === 'Problem' ||
          item.sourceType === 'SchoolProblem'
        ) {
          const temp =
            tempTaskItems.find(
              (tempItem) =>
                tempItem.id === item.id && tempItem.sourceId === item.sourceId,
            ) || item
          return {
            ...temp,
          }
        } else {
          return item
        }
      }) as CreateTaskItem[]
      return {
        ...task,
      }
    })
    tasks.value = tempTaskList.map((task) => {
      return {
        ...task,
        items: task.items.map((item) => {
          return {
            ...item,
            key: uuidv1(),
            answerCount: undefined,
          }
        }),
        subGroupInfo: [],
      }
    })
    updateTaskSubGroup()
  } else {
    await fetchRooms()
    formModel.groupIds = [rooms.value[0].groupId]
  }
}
onMounted(() => {
  init()

  // 埋点-页面停留时长
  useStayTime('getNewCoursePageStayDuration', 'course', {
    pageName: buryPointPageName.value,
  })
  // 页面进入埋点
  buryPoint(
    'enterNewCoursePage',
    { pageName: buryPointPageName.value },
    'course',
  )
})

const {
  tasks,
  addNewTaskSelfStudy,
  addNewTaskFastRecite,
  addNewTaskLecture,
  totalTaskTimeCount,
  currentIndex,
  addDrawerShow,
  addDrawerFastReciteShow,
  addDrawerLectureShow,
  onAddTaskItemDrawerShow,
  onAddTaskItem,
  onUpMove,
  onDownMove,
  onDelTask,
  onPreview,
  onCancel,
  previewVideoInfo,
  videoPreviewShow,
  problemPreviewShow,
  problemPreviewInfo,
  schoolVideoPreviewModalShow,
  schoolVideoPreviewInfo,
  getProblemBodyList,
  confirmLeave,
} = useCreateHooks()

const isDrawerVisible = computed(
  () =>
    addDrawerShow.value ||
    addDrawerFastReciteShow.value ||
    addDrawerLectureShow.value,
)
watch(
  () => tasks.value,
  (val) => {
    const problems = val.reduce((total, task) => {
      let temp: string[] = []
      if (task.tag === 'SelfStudyTag') {
        temp = task.items
          .filter(
            (item) =>
              item.sourceType === 'Problem' ||
              item.sourceType === 'SchoolProblem',
          )
          .map((item) => item.sourceId as string)
      }
      return total.concat(temp as string[])
    }, [] as string[])
    sideResourceStore.setSelectProblemIds(problems)
  },
  {
    deep: true,
  },
)

const onCancelConfrim = () => {
  if (props.draftId) {
    onDraftCancel()
  } else {
    onCancel()
  }
}
const onDraftCancel = () => {
  confirmLeave(
    {
      name: 'LessonDraft',
    },
    () => {
      window.sessionStorage.setItem('AI_LESSON_CREATING_LEAVE', '1')
    },
  )
}

const onAddNewTaskSelfStudy = () => {
  addNewTaskSelfStudy()
  nextTick(() => updateTaskSubGroup())
  buryPoint(
    'clickNewCoursePageButton',
    {
      button: 'task',
      pageName: buryPointPageName.value,
    },
    'course',
  )
}

const onAddNewTaskFastRecite = () => {
  addNewTaskFastRecite()
  nextTick(() => updateTaskSubGroup())
}

const onAddNewTaskLecture = () => {
  addNewTaskLecture()
  nextTick(() => updateTaskSubGroup())
}

watch(
  () => formModel.groupIds,
  (value) => {
    updateTaskSubGroup()
    sideResourceStore.setGroupIds(value)
  },
  {
    deep: true,
  },
)

watch(
  () => formModel.enableAiExplain,
  (value) => {
    if (!value) {
      formModel.enableAiQa = 0
    }
  },
  {
    deep: true,
  },
)

function updateTaskSubGroup() {
  if (rooms.value.length) {
    tasks.value.forEach((task) => {
      const removeGroup = task.subGroupInfo.find(
        (el) => !formModel.groupIds.includes(el.groupId),
      )
      if (removeGroup) {
        task.subGroupInfo = task.subGroupInfo.filter(
          (el) => el.groupId !== removeGroup.groupId,
        )
      }
      const addGroups = formModel.groupIds.filter(
        (el) => !task.subGroupInfo.some((subGroup) => subGroup.groupId === el),
      )
      if (addGroups.length) {
        addGroups.forEach((addGroup) => {
          const room = rooms.value.find((el) => el.groupId === addGroup)
          task.subGroupInfo.push({
            groupId: addGroup,
            roomId: room!.id,
            ref: room!.ref,
            name: room?.name ?? '',
            memberCount: room?.memberCount ?? 0,
            subGroup: [],
            schemeId: '',
          })
        })
      }
    })
  }
}

const classGroupModalShow = ref(false)
const editSubGroup = ref<EditableTaskGroup>()
const onEditSubGroup = (subGroup: EditableTaskGroup) => {
  editSubGroup.value = subGroup
  classGroupModalShow.value = true
}

const onSubGroupChange = (subGroup: EditableTaskGroup) => {
  if (editSubGroup.value) {
    editSubGroup.value.subGroup = subGroup.subGroup
    editSubGroup.value.schemeId = subGroup.schemeId
  }
}

const dialog = useOIWDialog()
const message = useOIWMessage()

const onCreate = () => {
  const { stageId, name, startTime, duration, groupIds, subjectId } = formModel
  if (
    !stageId ||
    !subjectId ||
    !name ||
    !startTime ||
    !duration ||
    !groupIds.length ||
    !tasks.value.length ||
    !tasks.value.some((task) => task.items.length)
  ) {
    message.warning('请填写必填项')
    return
  }
  if (totalTaskTimeCount.value && totalTaskTimeCount.value / 60 > duration) {
    dialog.create({
      showIcon: false,
      type: 'warning',
      title: '提示',
      content: '学习内容预计时长超出课时时间，是否继续发布？',
      positiveText: '确定',
      negativeText: '返回',
      onPositiveClick: () => {
        dialog.create({
          showIcon: false,
          type: 'info',
          title: '提示',
          content: '发布后学生即可查看课时内容，是否确认发布？',
          positiveText: '发布',
          negativeText: '返回',
          onPositiveClick: () => createNewPost(),
        })
      },
    })
  } else {
    createNewPost()
  }
}

const router = useRouter()
async function createNewPost() {
  const {
    stageId,
    name,
    startTime,
    duration,
    groupIds,
    subjectId,
    enableAiExplain,
    enableAiQa,
    studyTiming,
  } = formModel
  await createNewTaskLessonApi({
    name: name!,
    startAt: dayjs(startTime).toDate(),
    endAt: dayjs(startTime).add(duration!, 'minutes').toDate(),
    groupIds,
    subjectId: subjectId!,
    stageId: stageId!,
    enableAiExplain: !!enableAiExplain,
    enableAiQa: !!enableAiQa,
    studyTiming: studyTiming,
    tasks: tasks.value
      .filter((el) => el.items.length)
      .map((task) => {
        return {
          name: task.name,
          subGroupInfo: task.subGroupInfo.map((group) => {
            return {
              groupId: group.groupId,
              schemeId: group.schemeId,
              subGroupIds: group.subGroup.map((el) => el.id),
            }
          }),
          tag: task.tag,
          items: task.items.map((el) => {
            const {
              sourceType,
              sourceId,
              topicId,
              duration,
              stageId,
              subjectId,
              publisherId,
              semesterId,
              specialCourseId,
              videoClipExtra,
              name,
              chapterId,
              sectionId,
              subSectionId,
              importFrom,
              paperId,
              extra,
            } = el
            return {
              sourceId,
              sourceType,
              topicId,
              duration,
              stageId,
              subjectId,
              publisherId,
              semesterId,
              specialCourseId,
              videoClipExtra,
              name,
              chapterId,
              sectionId,
              subSectionId,
              importFrom,
              paperId,
              extra,
            }
          }),
        }
      }),
  })
  if (props.draftId) {
    await deleteMeasuringDraftApi(props.draftId)
  }
  window.sessionStorage.setItem('AI_LESSON_CREATING_LEAVE', '1')
  router.replace({
    name: 'LessonEntry',
  })
  buryPoint(
    'clickNewCoursePageButton',
    {
      button: 'publish',
      pageName: buryPointPageName.value,
    },
    'course',
  )
}

const onDeleteDraft = () => {
  confirmLeave(
    {
      name: 'LessonEntry',
    },
    async () => {
      await deleteMeasuringDraftApi(props.draftId!)
      window.sessionStorage.setItem('AI_LESSON_CREATING_LEAVE', '1')
    },
    '确定要删除此草稿吗？',
    '删除',
    '返回',
  )
}

async function onSaveDraft() {
  const draft = {
    name: formModel.name,
    roomName: rooms.value
      .filter((el) => formModel.groupIds.includes(el.groupId))
      .map((el) => el.name)
      .join(','),
    startAt: dayjs(formModel.startTime).toDate(),
    endAt: dayjs(formModel.startTime)
      .add(formModel.duration!, 'minutes')
      .toDate(),
    duration: formModel.duration,
    groupIds: formModel.groupIds,
    subjectId: formModel.subjectId!,
    stageId: formModel.stageId!,
    tasks: tasks.value,
    enableAiExplain: !!formModel.enableAiExplain,
    enableAiQa: !!formModel.enableAiQa,
    studyTiming: formModel.studyTiming,
  }
  if (props.draftId) {
    await updateMeasuringDraftApi(props.draftId, draft)
  } else {
    await saveMeasuringDraftApi({
      content: draft,
    })
  }
  window.sessionStorage.setItem('AI_LESSON_CREATING_LEAVE', '1')
  router.replace({
    name: 'LessonDraft',
  })
  buryPoint(
    'clickNewCoursePageButton',
    {
      button: 'draft',
      pageName: buryPointPageName.value,
    },
    'course',
  )
}

// 新建课时-任务-预览微课的视频埋点
const taskVideoBuryPointParams = computed(() => {
  return {
    videoType: previewVideoInfo.value.clipId ? 'course_clip' : 'course',
    videoScene: isFromDraft.value
      ? 'AIClassEditTaskPublicVideo'
      : 'AIClassNewTaskPublicVideo',
  }
})
const taskMP4VideoBuryPointParams = computed(() => {
  return {
    videoType: 'school',
    videoScene: isFromDraft.value
      ? 'AIClassEditTaskSchoolVideo'
      : 'AIClassNewTaskSchoolVideo',
  }
})
</script>

<style lang="scss" scoped>
::v-deep(.n-checkbox__label) {
  display: flex;
  flex: 1;
}

.row {
  display: flex;
  justify-content: center;
  user-select: none;
}

.row-content {
  width: 711px;
}

::v-deep(.n-date-picker) {
  .n-input__border {
    border: 1px solid #c5c1d4;
    border-radius: 12px;
  }

  .n-input__state-border {
    border-radius: 12px;
  }
}

::v-deep(.n-input-number) {
  .n-input__border {
    border-radius: 12px;
  }

  .n-input__state-border {
    border-radius: 12px !important;
  }

  .n-input:not(.n-input--disabled):hover .n-input__state-border {
    border: 1px solid #5381fe;
    border-radius: 12px;
  }
}

.oiw-cascader {
  ::v-deep(.n-base-selection) {
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 12px;
  }
}

.course-name-input {
  ::v-deep(.n-input__border) {
    border-radius: 12px;
  }

  ::v-deep(.n-input__state-border) {
    border-radius: 12px;
  }
}

.ai-qa-illustration {
  margin-left: 8px;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  color: #7290ff;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.ai-qa-illustration-img {
  height: 300px;

  img {
    display: inline-block;
    height: 300px;
    object-fit: cover;
  }
}
</style>
