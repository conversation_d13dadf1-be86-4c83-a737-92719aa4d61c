import type { InsertParams } from '@/components/SideResource/types'
import type { EditableTask, CreateTaskItem, Tags } from '../service'
import {
  systemLessonPermission,
  specialCoursePermission,
} from '@/hooks/usePermission'
import { v1 as uuidv1 } from 'uuid'
import { useOIWDialog, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import {
  postSpecialTopicDetails,
  postSystemTopicDetails,
} from '@/pages/microVideo/service'
import { postProblemDetail } from '@/pages/problem/service'
import { getSchoolProblemDetailApi } from '@/pages/schoolResource/schoolProblem/service'
import { usePageLeaveConfirmation } from '@/hooks/usePageLeaveConfirmation'

interface SelfClipType {
  start: number
  end: number
}

export const useCreateHooks = () => {
  const hasUnsavedChanges = () => {
    if (window.sessionStorage.getItem('AI_LESSON_CREATING_LEAVE')) return false
    if (window.sessionStorage.getItem('AI_LESSON_EDITING_LEAVE')) return false
    return true
  }
  const { confirmLeave } = usePageLeaveConfirmation(
    hasUnsavedChanges,
    '确定要离开此页面吗？所做的更改都不会被保存',
    '离开',
    '留在当前页',
    () => {
      window.sessionStorage.removeItem('AI_LESSON_CREATING_LEAVE')
      window.sessionStorage.removeItem('AI_LESSON_EDITING_LEAVE')
    },
  )
  const tasks = ref<EditableTask[]>([])
  const addNewTaskSelfStudy = () => {
    tasks.value.push({
      name: '',
      items: [],
      subGroupInfo: [],
      tag: 'SelfStudyTag',
    })
    nextTick(() => onScrollBottom())
  }
  const addNewTaskFastRecite = () => {
    tasks.value.push({
      name: '',
      items: [],
      subGroupInfo: [],
      tag: 'FastReciteTag',
    })
    nextTick(() => onScrollBottom())
  }
  const addNewTaskLecture = () => {
    tasks.value.push({
      name: '',
      items: [],
      subGroupInfo: [],
      tag: 'LectureTag',
    })
    nextTick(() => onScrollBottom())
  }
  const totalTaskTimeCount = computed(() => {
    if (tasks.value.length) {
      return tasks.value.reduce((count, task) => {
        const taskTimeAcc = task.items.reduce((taskCount, taskItem) => {
          const acc = taskCount + taskItem.duration
          return acc
        }, 0)
        const acc = count + taskTimeAcc
        return acc
      }, 0)
    }
  })
  const currentIndex = ref<number>(-1)
  const addDrawerShow = ref(false)
  const addDrawerFastReciteShow = ref(false)
  const addDrawerLectureShow = ref(false)
  const onAddTaskItemDrawerShow = (
    index: number,
    tag: Tags = 'SelfStudyTag',
  ) => {
    switch (tag) {
      case 'SelfStudyTag':
        addDrawerShow.value = true
        break
      case 'FastReciteTag':
        addDrawerFastReciteShow.value = true
        break
      case 'LectureTag':
        addDrawerLectureShow.value = true
        break
      default:
        addDrawerShow.value = true
        break
    }
    currentIndex.value = index
  }

  watchEffect(() => {
    if (!addDrawerShow.value) {
      currentIndex.value = -1
    }
  })

  const dialog = useOIWDialog()
  const message = useOIWMessage()
  const onAddTaskItem = async (data: InsertParams) => {
    if (tasks.value[currentIndex.value].items.length > 49) {
      dialog.create({
        showIcon: false,
        type: 'warning',
        title: '提示',
        content: '单个课时内容上限为50个',
        positiveText: '确认',
      })
    } else {
      if (
        data.sourceType === 'Problem' ||
        data.sourceType === 'SchoolProblem'
      ) {
        const key = uuidv1()
        tasks.value[currentIndex.value].items.push({
          ...data,
          subjectId: data.subjectId ? Number(data.subjectId) : undefined,
          stageId: data.stageId ? Number(data.stageId) : undefined,
          publisherId: data.publisherId ? Number(data.publisherId) : undefined,
          semesterId: data.semesterId ? Number(data.semesterId) : undefined,
          key,
          measuringTaskId: '',
          problemBody: '',
          id: '',
        } as any)
        const temp = await getProblemBodyList([data])
        tasks.value[currentIndex.value].items = tasks.value[
          currentIndex.value
        ].items.map((item) => {
          if (item.key && item.key === key) {
            return {
              ...item,
              problemBody: temp[0].problemBody,
            }
          } else {
            return item
          }
        })
      } else {
        tasks.value[currentIndex.value].items.push({
          ...data,
          subjectId: data.subjectId ? Number(data.subjectId) : undefined,
          stageId: data.stageId ? Number(data.stageId) : undefined,
          publisherId: data.publisherId ? Number(data.publisherId) : undefined,
          semesterId: data.semesterId ? Number(data.semesterId) : undefined,
          key: uuidv1(),
          measuringTaskId: '',
          id: '',
        } as any)
      }

      nextTick(() => {
        const taskPanelId = `lesson-task-panel-${currentIndex.value}`
        const taskPanel = document.getElementById(taskPanelId)
        if (taskPanel) {
          const panelRect = taskPanel.getBoundingClientRect()
          const panelBottom = panelRect.bottom + window.scrollY
          const viewportHeight = window.innerHeight
          const scrollTarget = panelBottom - viewportHeight + 150
          window.scrollTo({
            top: Math.max(0, scrollTarget),
            behavior: 'smooth',
          })
        }
      })
    }
  }

  const getProblemBodyList = async (dataParams: InsertParams[]) => {
    const itemTypeProblems = [
      ...new Set(
        dataParams
          .filter((item) => item.sourceType === 'Problem')
          .map((item) => item.sourceId),
      ),
    ]
    const schoolProblems = [
      ...new Set(
        dataParams
          .filter((item) => item.sourceType === 'SchoolProblem')
          .map((item) => item.sourceId),
      ),
    ]

    if (itemTypeProblems.length === 0 && schoolProblems.length === 0) {
      return dataParams
    }

    const [itemTypeResponses, schoolResponses] = await Promise.all([
      itemTypeProblems.length > 0
        ? postProblemDetail({ problemsId: itemTypeProblems })
        : Promise.resolve([]),
      schoolProblems.length > 0
        ? getSchoolProblemDetailApi({ problemsId: schoolProblems })
        : Promise.resolve({ data: [] }),
    ])
    return dataParams.map((item) => {
      const problem = [
        ...itemTypeResponses,
        ...(schoolResponses.data || []),
      ].find((problem) => problem.id === item.sourceId)
      return {
        ...item,
        problemBody: problem?.body,
      }
    })
  }
  const onUpMove = (index: number) => {
    const preTask = tasks.value[index - 1]
    tasks.value[index - 1] = tasks.value[index]
    tasks.value[index] = preTask
  }
  const onDownMove = (index: number) => {
    const nextTask = tasks.value[index + 1]
    tasks.value[index + 1] = tasks.value[index]
    tasks.value[index] = nextTask
  }
  const onDelTask = (index: number) => {
    tasks.value.splice(index, 1)
  }

  const onCancel = () => {
    confirmLeave(
      {
        name: 'LessonEntry',
      },
      () => {
        window.sessionStorage.setItem('AI_LESSON_CREATING_LEAVE', '1')
      },
    )
  }

  const schoolVideoPreviewModalShow = ref(false)
  const schoolVideoPreviewInfo = ref<{
    id: string
    selfClip?: SelfClipType
  }>({
    id: '',
  })

  const previewVideoInfo = ref<{
    clipId?: string
    specialCourseId?: string
    topicId: string
    selfClip?: SelfClipType
  }>({
    clipId: '',
    specialCourseId: '',
    topicId: '',
  })
  const videoPreviewShow = ref(false)
  const problemPreviewShow = ref(false)
  const problemPreviewInfo = ref<{
    problemId: string
    type: string
  }>({
    problemId: '',
    type: '',
  })
  const onAuthCheck = async (taskItem: CreateTaskItem) => {
    if (taskItem.subjectId && taskItem.stageId) {
      if (!taskItem.specialCourseId) {
        if (taskItem.topicId) {
          const topic = await postSystemTopicDetails({
            topicIds: [taskItem.topicId],
          })
          const item = topic.data?.[0]
          if (item?.isFreeTime || !item?.pay) {
            return true
          }
        }
        const hasAuth = await systemLessonPermission({
          subjectId: taskItem.subjectId,
          stageId: taskItem.stageId,
        })
        return hasAuth
      } else {
        if (taskItem.subjectId && taskItem.stageId && taskItem.topicId) {
          const detail = await postSpecialTopicDetails({
            topicIds: [taskItem.topicId],
          })
          const item = detail.data?.[0]
          if (item?.isFreeTime || !item?.pay) {
            return true
          }
          const hasAuth = await specialCoursePermission(
            taskItem.specialCourseId,
          )
          return hasAuth
        }
      }
    }
  }
  const onPreview = async (taskItem: CreateTaskItem) => {
    if (taskItem.sourceType === 'Clip' || taskItem.sourceType === 'Video') {
      const hasAuth = await onAuthCheck(taskItem)
      if (hasAuth) {
        videoPreviewShow.value = true
        previewVideoInfo.value = {
          clipId: taskItem.videoClipExtra?.clipId,
          specialCourseId: taskItem.specialCourseId,
          topicId: taskItem.topicId!,
          selfClip:
            taskItem.videoClipExtra &&
            taskItem.videoClipExtra.type === 'self_clip'
              ? {
                  start: taskItem.videoClipExtra.playRangeStart,
                  end: taskItem.videoClipExtra.playRangeEnd,
                }
              : undefined,
        }
      } else {
        message.warning(
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        )
      }
    } else if (taskItem.sourceType === 'SchoolVideo') {
      schoolVideoPreviewInfo.value = {
        id: taskItem.sourceId,
        selfClip:
          taskItem.videoClipExtra &&
          taskItem.videoClipExtra.type === 'self_clip'
            ? {
                start: taskItem.videoClipExtra.playRangeStart,
                end: taskItem.videoClipExtra.playRangeEnd,
              }
            : undefined,
      }
      schoolVideoPreviewModalShow.value = true
    } else {
      problemPreviewInfo.value = {
        problemId: taskItem.sourceId,
        type: taskItem.sourceType,
      }
      problemPreviewShow.value = true
    }
  }

  const onScrollBottom = () => {
    window.requestAnimationFrame(() => {
      window.document.scrollingElement?.scrollTo({
        top: window.document.scrollingElement?.scrollHeight,
      })
    })
  }

  return {
    tasks,
    addNewTaskSelfStudy,
    addNewTaskFastRecite,
    addNewTaskLecture,
    totalTaskTimeCount,
    currentIndex,
    addDrawerShow,
    addDrawerFastReciteShow,
    addDrawerLectureShow,
    onAddTaskItemDrawerShow,
    onAddTaskItem,
    onUpMove,
    onDownMove,
    onDelTask,
    onCancel,
    previewVideoInfo,
    videoPreviewShow,
    problemPreviewShow,
    problemPreviewInfo,
    onPreview,
    schoolVideoPreviewInfo,
    schoolVideoPreviewModalShow,
    onScrollBottom,
    getProblemBodyList,
    confirmLeave,
  }
}
