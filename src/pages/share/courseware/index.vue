<template>
  <div class="courseware-box">
    <OIWLoading v-if="load" :show="load" width="200px" height="200px" />
    <div v-else-if="coursewareId" class="courseware-content-main">
      <div v-if="coursewareInfo" class="courseware-info">
        <n-space justify="space-between" align="center">
          <div class="flex items-center">
            <img
              v-if="coursewareInfo.isFine"
              class="w-32px h-20px mr-8"
              src="https://fp.yangcong345.com/onion-extension/%<EMAIL>"
              alt=""
            />
            <div class="courseware-title">
              {{ formatName(coursewareInfo.name) }}
            </div>
          </div>
          <OIWButton
            type="info"
            :disabled="loading"
            :loading="loading"
            @click="joinCourseCenter"
            >加入备课中心</OIWButton
          >
        </n-space>
        <n-flex class="courseware-user" align="center">
          <span>分享者：</span>
          <span class="name">{{ coursewareInfo.shareUserName }}</span>
          <span>分享时间：</span>
          <span class="time">{{
            formatTime(coursewareInfo.createdAt || '')
          }}</span>
          <span class="validity">剩余有效期：</span>
          <span class="validity">{{
            formatRemainSeconds(
              coursewareInfo.remainSeconds || 0,
              coursewareInfo.expiration || 0,
            )
          }}</span>
        </n-flex>
      </div>

      <div v-if="token" class="courseware-content">
        <div v-if="!auth && coursewareInfo?.isFine" class="courseware-empty">
          <OIWStateBlock
            type="empty"
            title="该课件为精品课件，请联系洋葱工作人员开通使用权限"
          />
        </div>
        <PdfPreview
          v-if="pdfUrl"
          :resourceType="resourceType"
          :pdfUrl="pdfUrl"
        />
      </div>
      <div
        v-else
        class="courseware-cover"
        :class="{ 'courseware-cover-fine': coursewareInfo?.isFine }"
        @click="toLogin"
      >
        {{ formatName(coursewareInfo?.name) }}
      </div>
    </div>
    <div v-else class="courseware-empty">
      <OIWStateBlock type="empty" :title="errorText" />
    </div>
    <n-modal v-model:show="showModal">
      <div class="message-board-content">
        <div class="close-icon" @click="showModal = false" />
        <div class="success-icon" />
        <div class="text">加入成功</div>
        <div class="illustrate">
          已成功加入备课中心，<span class="arrow-icon" @click="goToView"
            >前往查看</span
          >
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  OIWLoading,
  OIWStateBlock,
  OIWButton,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { useRouter } from 'vue-router'
import PdfPreview from './pdfPreview.vue'
import type { GetShareCoursewareRes } from './service'
import { getShareCourseware, getCouserwareCreateCloudDiskFile } from './service'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import useAuthStore from '@/store/auth'
import { couserwareFineAuth } from '@/hooks/usePermission'

const props = defineProps<{
  shareId: string
  resourceType: string
}>()
const router = useRouter()
const message = useOIWMessage()
const store = useAuthStore()
const { token } = storeToRefs(store)
console.log(token.value)
dayjs.extend(duration)
const dialog = useDialog()
const load = ref(true)
const loading = ref(false)
const pdfUrl = ref<string>('')
const coursewareId = ref<string>('')
const coursewareInfo = ref<GetShareCoursewareRes | null>(null)
const errorText = ref('当前分享不存在')
const hasDialogShown = ref(false)
const showModal = ref(false)

const handleScroll = (e: any) => {
  e.preventDefault()
  if (window.scrollY > 0 && !hasDialogShown.value) {
    dialogModel()
    hasDialogShown.value = true
  }
  window.scrollTo(0, 0)
}

watch(
  () => token.value,
  (newVal) => {
    if (!newVal && !hasDialogShown.value) {
      window.addEventListener('scroll', handleScroll)
    } else {
      window.removeEventListener('scroll', handleScroll)
    }
  },
  {
    immediate: true,
  },
)

const auth = ref()

onMounted(async () => {
  await init()
  if (token.value) {
    auth.value = await couserwareFineAuth({
      subjectId: coursewareInfo.value!.subjectId,
      stageId: coursewareInfo.value!.stageId,
    })
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

const init = async () => {
  load.value = true
  try {
    const res = await getShareCourseware({
      shareId: props.shareId,
    })
    coursewareInfo.value = res
    coursewareId.value = res.coursewareId || ''
    pdfUrl.value = res.pdfUrl || ''
  } catch (error: any) {
    if (
      error &&
      error.response &&
      error.response.data &&
      error.response.data.code === 409
    ) {
      errorText.value = '当前资源已过期无法查看'
    }
  } finally {
    load.value = false
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const formatName = (name: string | undefined) => {
  return name ? name.split('.pptx')[0] : ''
}

const formatRemainSeconds = (time: number, expiration: number) => {
  if (time === 0) {
    return '永久'
  }

  const dur = dayjs.duration(time * 1000)

  const days = Math.ceil(dur.asDays())
  const hours = Math.ceil(dur.asHours())

  switch (expiration) {
    case 86400:
      return `${hours}小时`
    case 604800:
    case 2592000:
      return `${days}天`
    default:
      return `${days}天${hours % 24}小时`
  }
}

const dialogModel = () => {
  dialog.info({
    showIcon: false,
    title: '提示',
    content: '请登录洋葱学园教师端账号后查看完整内容和更多优质资源',
    onPositiveClick: goTeacherLogin,
    onMaskClick: () => {
      hasDialogShown.value = false
    },
    onClose: () => {
      hasDialogShown.value = false
    },
    onNegativeClick: () => {
      hasDialogShown.value = false
    },
    negativeText: '取消',
    positiveText: '去登录',
  })
}

const goTeacherLogin = () => {
  const encodedUrl = encodeURIComponent(window.location.href)
  window.location.href = `${window.location.origin}/login?workbench=${encodedUrl}`
}

const joinCourseCenter = async () => {
  if (!token.value) {
    dialogModel()
    return false
  }
  try {
    await getCouserwareCreateCloudDiskFile({
      coursewareId: coursewareId.value,
      resourceType: props.resourceType,
    })
    showModal.value = true
  } catch (error) {
    if (error.response.data.message === '没有操作权限') {
      dialog.info({
        showIcon: false,
        title: '提示',
        content: '该课件为精品课件，请联系洋葱工作人员开通使用权限',
        positiveText: '确认',
      })
      showModal.value = false
      return
    }
    message.error(error.response.data.message)
    showModal.value = false
  }
}

const toLogin = () => {
  if (!token.value) {
    dialogModel()
    return false
  }
}

const goToView = async () => {
  showModal.value = false
  router.push({
    name: 'prepareCenterList',
  })
}
</script>

<style lang="scss" scoped>
.courseware-box {
  width: 1200px;
  margin: 0 auto;

  .courseware-content-main {
    padding-top: 32px;

    .courseware-title {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
      color: #393548;
    }

    .courseware-user {
      font-size: 14px;
      font-weight: normal;
      color: #8a869e;

      .name {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #57526c;
      }

      .time {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #57526c;
      }

      .validity {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #fa5a65;
      }
    }
  }

  .courseware-content {
  }

  .courseware-cover {
    width: 1056px;
    height: 594px;
    margin: 32px auto 0;
    // 单行省略
    overflow: hidden;
    font-size: 40px;
    font-weight: 600;
    line-height: 594px;

    color: #3d3d3d;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: url('https://fp.yangcong345.com/onion-extension/111-8af7769feb4536f678be6a144c376d3d.png')
      no-repeat;
    background-size: cover;
  }

  .courseware-cover-fine {
    background: url('https://fp.yangcong345.com/onion-extension/切图 21@4x (1)-ad551b8fc33ed0e15fa78f50699d6b0d.png')
      no-repeat;
    background-size: cover;
  }

  .courseware-empty {
    margin-top: 25vh;
  }
}

.message-board-content {
  width: 480px;
  height: 244px;
  padding-top: 32px;
  text-align: center;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0px 3.2px 3.2px 0px rgba(80, 75, 100, 0.08);

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/333-7f233642b5621eaf6073f882cfff68a9.png')
      no-repeat;
    background-size: 32px 32px;
  }

  .success-icon {
    display: inline-block;
    width: 80px;
    height: 80px;
    background: url('https://fp.yangcong345.com/onion-extension/222-05e9baf8202fe9f0e87c061fd58375ee.png')
      no-repeat;
    background-size: 80px 80px;
  }

  .text {
    margin-top: 16px;
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    color: #393548;
  }

  .illustrate {
    margin-top: 24px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    color: #57526c;

    .arrow-icon {
      position: relative;
      display: inline-block;
      padding-right: 24px;
      color: #5e80ff;
      cursor: pointer;

      &::before {
        position: absolute;
        top: 2px;
        right: 0;
        width: 16px;
        height: 16px;
        cursor: pointer;
        content: '';
        background: url('https://fp.yangcong345.com/onion-extension/444-7dd8455e85d87dd365efd15009e8b2e9.png')
          no-repeat;
        background-size: 16px 16px;
      }
    }
  }
}
</style>
