<template>
  <div
    :class="{
      'test-paper-problem-main': true,
      'test-paper-problem-main-hover': isShown,
    }"
    @mouseover="showElements"
    @mouseout="hideElements"
  >
    <div
      :class="{
        'operating-bar-box': true,
        'operating-bar-box-hidden': !isShown,
      }"
    >
      <div
        :class="{
          'only-answer': true,
          'only-answer-hidden': !isProblemShown,
        }"
        @mouseover="showAnalyzeElements"
        @mouseout="hideAnalyzeElements"
      >
        <ProblemRender
          :currentProblem="currentProblem"
          :problemNo="problemNo"
          :difficultyHeaderShow="false"
          explains
          analyze
          showIndex
          class="problem-detail"
          mode="government"
        />
      </div>
      <div class="operating-bar">
        <button
          class="move-btn"
          @mouseover="showAnalyzeElements"
          @mouseout="hideAnalyzeElements"
        >
          答案解析
        </button>
        <div class="br-btn-box">
          <span class="text">添加答题区域：</span>
          <span class="subtract" @click="subtractClick">-</span>
          <span class="content">{{ currentProblem.design.br || 0 }}</span>
          <span class="add" @click="addClick">+</span>
        </div>
        <button
          :disabled="problemNo === 1 && indexGroups === 0"
          class="move-btn"
          @click="handleMoveUp"
        >
          上移
        </button>
        <button
          :disabled="
            totalIndex === problemNo && totalGroups === indexGroups + 1
          "
          class="move-btn"
          @click="handleMoveDown"
        >
          下移
        </button>
        <button class="delete-btn" @click="handleDelete">删除</button>
      </div>
    </div>
    <ProblemRender
      :currentProblem="currentProblem"
      :problemNo="problemNo"
      :difficultyHeaderShow="false"
      showIndex
      class="problem-detail"
    />
    <ul v-if="currentProblem.design.br" class="br-list">
      <li
        v-for="item in currentProblem.design.br"
        :key="item"
        class="br-item"
      />
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ProblemRender } from '@guanghe-pub/onion-problem-render'
import { useOIWDialog } from '@guanghe-pub/onion-ui-web'

const props = defineProps<{
  totalIndex: number
  problemNo: number
  groupId: string
  indexGroups: number
  totalGroups: number
  currentProblem: any
}>()
const emits = defineEmits<{
  (
    e: 'moveUpProblem',
    problemNo: number,
    problemId: string,
    groupId: string,
  ): void
  (
    e: 'moveDownProblem',
    problemNo: number,
    problemId: string,
    groupId: string,
  ): void
  (
    e: 'deleteProblem',
    problemNo: number,
    problemId: string,
    groupId: string,
  ): void
  (
    e: 'designUpdateBr',
    problemNo: number,
    problemId: string,
    groupId: string,
    br: number,
  ): void
}>()
const isShown = ref(false)
const isProblemShown = ref(false)
const dialog = useOIWDialog()
const showElements = () => {
  isShown.value = true
}

const hideElements = () => {
  isShown.value = false
}
const showAnalyzeElements = () => {
  isProblemShown.value = true
}

const hideAnalyzeElements = () => {
  isProblemShown.value = false
}
const handleMoveUp = () => {
  emits(
    'moveUpProblem',
    props.problemNo,
    props.currentProblem.problemId,
    props.groupId,
  )
}
const handleMoveDown = () => {
  emits(
    'moveDownProblem',
    props.problemNo,
    props.currentProblem.problemId,
    props.groupId,
  )
}
const handleDelete = () => {
  dialog.create({
    title: '删除',
    content: '要删除题目吗？',
    positiveText: '确定',
    negativeText: '取消',
    closable: false,
    onPositiveClick: () => {
      emits(
        'deleteProblem',
        props.problemNo,
        props.currentProblem.problemId,
        props.groupId,
      )
    },
  })
}

const subtractClick = () => {
  const br = props.currentProblem.design.br || 0
  if (br > 0) {
    emits(
      'designUpdateBr',
      props.problemNo,
      props.currentProblem.problemId,
      props.groupId,
      br - 1,
    )
  }
}
const addClick = () => {
  const br = props.currentProblem.design.br || 0
  if (br < 99) {
    emits(
      'designUpdateBr',
      props.problemNo,
      props.currentProblem.problemId,
      props.groupId,
      br + 1,
    )
  }
}
</script>

<style lang="scss" scoped>
.test-paper-problem-main {
  position: relative;
  padding: 16px;

  &.test-paper-problem-main-hover {
    border: 1px solid #5e80ff;
    border-radius: 16px;
  }

  .operating-bar-box {
    position: absolute;
    top: -50px;
    left: 50%;
    height: 54px;
    transform: translateX(-50%);

    &.operating-bar-box-hidden {
      display: none;
    }

    .operating-bar {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 500px;
      height: 40px;
      padding: 4px;
      margin-bottom: 14px;
      user-select: none;
      background: #ffffff;
      border: 1px solid #dfdce8;
      border-radius: 12px;
      box-shadow: 0px 4px 4px 0px rgba(80, 75, 100, 0.08);

      &::after {
        position: absolute;
        bottom: -6px;
        left: 50%;
        width: 0;
        height: 0;
        content: '';
        border-top: 7px solid #ffffff;
        border-right: 7px solid transparent;
        border-left: 7px solid transparent;
        transform: translateX(-50%);
      }

      .move-btn {
        padding: 6px 16px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #57526c;
        cursor: pointer;
        background: transparent;
        border-radius: 8px;

        &:disabled {
          color: #dfdce8;
          cursor: not-allowed;
        }

        &:not(:disabled):hover {
          color: #5e80ff;
          background: #f4f6ff;
        }
      }

      .delete-btn {
        padding: 6px 16px;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #fa5a65;
        cursor: pointer;
        background: transparent;
        border-radius: 8px;

        &:disabled {
          color: #dfdce8;
          cursor: not-allowed;
        }

        &:not(:disabled):hover {
          background: #f4f6ff;
        }
      }

      .br-btn-box {
        display: flex;
        align-items: center;
        padding: 6px 12px;

        .text {
          width: 100px;
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          color: #57526c;
        }

        .subtract {
          width: 24px;
          height: 24px;
          font-size: 20px;
          line-height: 19px;
          text-align: center;
          cursor: pointer;
          border: 1px solid #c5c1d4;
          border-radius: 8px;
        }

        .add {
          width: 24px;
          height: 24px;
          font-size: 20px;
          line-height: 19px;
          text-align: center;
          cursor: pointer;
          border: 1px solid #c5c1d4;
          border-radius: 8px;
        }

        .content {
          width: 40px;
          height: 24px;
          padding-right: 9px;
          padding-left: 9px;
          margin-right: 2px;
          margin-left: 2px;
          font-weight: 600;
          color: #57526c;
          text-align: center;
          border: 1px solid #c5c1d4;
          border-radius: 8px;
        }
      }
    }
  }

  .problem-detail {
    padding-top: 0;

    ::v-deep(.onion-problem-render__choice) {
      margin-top: 0;
    }

    ::v-deep(.onion-problem-render__blank) {
      margin-top: 0;
    }

    ::v-deep(.multi-line) {
      margin-bottom: -10px;
    }

    ::v-deep(.katex .mfrac .frac-line) {
      margin-bottom: -10px;
    }

    ::v-deep(.onion-problem-render__option) {
      display: flex;
      flex-wrap: wrap;
      font-weight: bold;
    }

    ::v-deep(.onion-problem-render__option--item) {
      width: 50%;
      margin-bottom: 0;
      border: none !important;
    }

    ::v-deep(.onion-problem-render__main) {
      font-size: 17px;
    }
    /* stylelint-disable selector-class-pattern */
    ::v-deep(.onion-problem-render__examBox-show) {
      display: none;
    }

    ::v-deep(.onion-problem-render__main img) {
      max-height: 200px;
    }

    ::v-deep(
        .onion-problem-render__examBox-show + .onion-problem-render__option
      ) {
      display: none;
    }

    ::v-deep(.onion-problem-render__answer) {
      padding: 0 !important;
      margin: 0;
      font-weight: bold !important;
      border: none !important;
      box-shadow: none !important;

      .onion-problem-render__answer--content {
        padding-top: 2px;
        font-weight: bold !important;
      }

      .onion-problem-render__answer--header {
        font-weight: bold !important;
      }
    }

    ::v-deep(.onion-problem-render__explain) {
      padding: 0 !important;
      margin: 0;
      font-weight: bold !important;
      border: none !important;
      box-shadow: none !important;

      .onion-problem-render__explain--content {
        padding-top: 2px;
        font-weight: bold !important;
      }

      .onion-problem-render__explain--header {
        font-weight: bold !important;
      }
    }
  }

  .only-answer {
    position: absolute;
    bottom: 51px;
    left: -150px;
    z-index: 10;
    width: 680px;
    max-height: 192px;
    padding: 16px;
    overflow-y: scroll;
    background: #ffffff;
    border: 1px solid #dfdce8;
    border-radius: 12px;
    box-shadow: 0px 4px 4px 0px rgba(80, 75, 100, 0.08);

    &::-webkit-scrollbar {
      display: none;
    }

    &.only-answer-hidden {
      display: none;
    }

    .problem-detail {
      font-size: 14px;
    }

    ::v-deep(.onion-problem-render__choice) {
      display: none !important;
    }

    ::v-deep(.onion-problem-render__blank) {
      display: none !important;
    }

    ::v-deep(.onion-problem-render__answer) {
      padding: 0 !important;
      margin: 0;
      font-weight: bold !important;
      border: none !important;
      box-shadow: none !important;

      .onion-problem-render__answer--content {
        padding-top: 2px;
        font-weight: bold !important;
      }

      .onion-problem-render__answer--header {
        font-weight: bold !important;
      }
    }
  }

  .br-list {
    .br-item {
      width: 100%;
      height: 20px;
    }
  }
}
</style>
