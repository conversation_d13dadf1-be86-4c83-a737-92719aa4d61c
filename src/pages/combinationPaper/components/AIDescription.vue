<template>
  <div
    v-if="hintDisplay"
    class="text-14px color-#FA5A65 font-400 leading-24px mt-6px"
  >
    请重新选择学科学段
  </div>
  <div class="mt-24px">
    <OIWInput
      v-model:value="AIDescriptionContext"
      type="textarea"
      placeholder="我要一套「章节或知识点」、「题型+题量」、「难度」的试卷"
      maxlength="500"
      show-count
      :autosize="{ minRows: 6, maxRows: 6 }"
    />
    <div class="flex items-center justify-between mt-10px">
      <div class="text-12px leading-12px color-#393548">
        示例：
        <span class="color-#5E80FF cursor-pointer" @click="handleExampleContent"
          >高中数学解三角方程与不等式</span
        >
      </div>
      <div class="text-12px leading-12px color-#9792AC">单次最多生成100题</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { OIWInput } from '@guanghe-pub/onion-ui-web'
import type { TreeOption } from 'naive-ui'
import { getAiUserTestPaperTemplateExamType } from '../service'

const props = defineProps<{
  cvs?: {
    publisherId?: YcType.CsvId
    stageId?: YcType.CsvId
    subjectId?: YcType.CsvId
    semesterId?: YcType.CsvId
  }
  systemTree: TreeOption[] | undefined
}>()

const emits = defineEmits<{
  (
    e: 'changeAiPrompts',
    value: string,
    knowledgePoints: { name: string; id: string }[],
  ): void
  (e: 'changeExamList', list: { enum: string; name: string }[]): void
}>()

const examTypes = ref<{ enum: string; name: string }[]>([])

const hintDisplay = computed(() => {
  return props.systemTree?.length === 0
})

// 收集树结构中所有叶子节点的name和id
const collectLeafNodes = (tree: any[]): { name: string; id: string }[] => {
  const result: { name: string; id: string }[] = []

  function traverse(nodes: any[]) {
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      } else {
        // 叶子节点
        result.push({
          name: node.name,
          id: node.id,
        })
      }
    }
  }

  traverse(tree)
  return result
}

const createAIQuestionPrompt = (
  userInput: string,
  knowledgePoints: string[],
  examTypes: string[],
) => {
  return `### 任务描述
请从用户输入中提取以下信息，并以**严格JSON格式**返回（仅包含实际存在的字段）：
- 学科、学段、学期、教材版本（字符串类型）
- 知识点列表（字符串数组）
- 题型列表（数组类型，包含对象，对象包含题型名称和数量）
- 难度（字符串类型）
### 处理规则
1. **字段要求**：
- 仅保留存在的字段，输出字段必须属于：\`['学科', '学段', '学期', '教材版本', '知识点列表', '题型列表', '难度']\`
- 题型列表根据输入动态生成（如 \`"选择题": 5\`）
- 缺失字段不返回
2. **数据过滤**：
- 知识点：仅保留输入中实际存在的知识点
- 题型：仅保留题型枚举中定义的题型
3. **输出格式**：
\`\`\`json
{
  "学科": "示例学科",
  "学段": "示例学段",
  "学期": "示例学期",
  "教材版本": "示例版本",
  "知识点列表": ["知识点1", "知识点2"],
  "题型列表": [
    {"name": "题型1", "count": 数量},
    {"name": "题型2", "count": 数量}
  ],
  "难度": "示例难度"
}
### 输入参数
- 用户输入：${userInput}
- 知识点列表：${JSON.stringify(knowledgePoints)}（字符串数组）
- 题型枚举：${JSON.stringify(examTypes)}（字符串数组）
- 难度枚举：["简单", "中等", "困难"]（字符串数组）
### 示例
###### 输入
- 用户输入：生成10道填空题，5道选择题的初中数学人教版七年级上的三角函数的试卷。
- 知识点列表：["三角函数", "方程讲解", "二次函数"]
- 题型枚举：["选择题", "填空题"]
###### 输出
\`\`\`json
{
  "学科": "数学",
  "学段": "初中",
  "学期": "七年级上",
  "教材版本": "人教版",
  "知识点列表": ["三角函数"],
  "题型列表": [{"name": "填空题", "count": 10}, {"name": "选择题", "count": 5}],
  "难度": "中等"
}
\`\`\`
### 处理逻辑
- 解析用户输入，匹配学科/学段/学期/版本信息
- 从知识点列表中筛选实际出现在输入中的知识点
- 从题型枚举中筛选输入提及的题型，并提取题目数
- 生成JSON（不存在的字段自动忽略）`
}

const AIDescriptionContext = ref('')

const handleExampleContent = () => {
  AIDescriptionContext.value =
    '我要一套「高中数学解三角方程与不等式」、「5道单选题，3道填空题，2道简答题」、「中等难度」的试卷。'
}

const getExamTypes = async () => {
  const res = await getAiUserTestPaperTemplateExamType({
    stageId: props.cvs?.stageId || '1',
    subjectId: props.cvs?.subjectId || '2',
  })
  examTypes.value = res.examTypes
  emits('changeExamList', res.examTypes)
}

const resetPage = () => {
  AIDescriptionContext.value = ''
}

defineExpose({
  resetPage,
})

watch(
  () => AIDescriptionContext.value,
  (value) => {
    if (value) {
      const knowledgePoints = collectLeafNodes(props.systemTree || []).map(
        (item) => item.name,
      )
      const prompt = createAIQuestionPrompt(
        value,
        knowledgePoints,
        examTypes.value.map((item) => item.name),
      )
      emits('changeAiPrompts', prompt, collectLeafNodes(props.systemTree || []))
    } else {
      emits('changeAiPrompts', '', [])
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => props.cvs,
  (value) => {
    if (value) {
      getExamTypes()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
}
</style>
