<template>
  <div class="mt-16px">
    <div class="flex color-#393548 font-400 text-14px leading-24px mb-6px">
      <span>知识点</span>
      <span class="color-#FA5A65">*</span>
      <span>：</span>
    </div>
    <n-cascader
      class="oiw-cascader"
      :value="teachingValue"
      placeholder="请选择知识点"
      :options="teachingOptions"
      check-strategy="parent"
      :show-path="false"
      filterable
      multiple
      :max-tag-count="0"
      value-field="id"
      label-field="name"
      size="large"
      :render-label="renderLabelCascader"
      @update:value="onTeachChange"
    />
    <ul v-if="teachingList.length" class="scope-mission-ul">
      <li
        v-for="item in teachingList"
        :key="item.id"
        class="scope-mission-item"
      >
        <div class="scope-mission-item-text">
          {{ item.name }}
        </div>
        <div class="scope-mission-item-del" @click="handleDel(item.id)" />
      </li>
    </ul>
  </div>
  <div class="identifier-box">
    <span class="text-16px color-#393548 font-600 leading-24px">试卷设置</span>
    <span class="text-16px color-#FA5A65 font-600 leading-24px">*</span>
    <span class="text-12px color-#9792AC font-600 leading-12px ml-4px">：</span>
  </div>
  <div class="mt-16px">
    <div class="flex color-#393548 font-400 text-14px leading-24px mb-6px">
      <span>试卷难度</span>
      <span class="color-#FA5A65">*</span>
      <span>：</span>
    </div>
    <OIWSelect
      :value="difficulty"
      :options="difficultyList"
      placeholder="请选择难度"
      @update:value="onDifficultyChange"
    />
  </div>
  <div class="question-type-box mt-16px">
    <div class="flex color-#393548 font-400 text-14px leading-24px mb-6px">
      <span>包含题型</span>
      <span class="color-#FA5A65">*</span>
      <span>：</span>
    </div>
    <div class="line" />
    <ExamTypesItem
      v-for="item in examList"
      :key="item.id"
      :examOptions="examOptions"
      :item="item"
      @minus="handleMinusExam"
      @add="handleAddExam"
      @delete="handleDeleteExam"
      @input-number="handleInputNumberExam"
      @select-change="handleSelectChangeExam"
    />
    <div class="line" />
    <div class="flex items-center gap-8px mt-10px">
      <OIWButton
        style="width: 108px; padding-right: 5px; padding-left: 5px"
        type="info"
        ghost
        @click="addQuestionType"
      >
        <Add class="w-16px h-16px" />
        <span>添加题型</span>
      </OIWButton>
      <div class="text-12px color-#9792AC leading-12px">单次最多生成100题</div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { NPopover } from 'naive-ui'
import type { TreeOption } from 'naive-ui'
import { OIWSelect, OIWButton } from '@guanghe-pub/onion-ui-web'
import ExamTypesItem from './ExamTypesItem.vue'
import Add from '~icons/yc/self-add'
import { v1 as uuidv1 } from 'uuid'
import { getAiUserTestPaperTemplateExamType } from '../service'

const props = defineProps<{
  cvs?: {
    publisherId?: YcType.CsvId
    stageId?: YcType.CsvId
    subjectId?: YcType.CsvId
    semesterId?: YcType.CsvId
  }
  systemTree: TreeOption[] | undefined
}>()
const emits = defineEmits<{
  (e: 'changeTeaching', list: { id: string; name: string }[]): void
  (e: 'changeDifficulty', str: string): void
  (
    e: 'changeExam',
    list: {
      id: string
      name: string | null
      examType: string | null
      count: number
    }[],
  ): void
}>()

const examTypes = ref<{ enum: string; name: string }[]>([])
const examList = ref<
  {
    id: string
    name: string | null
    examType: string | null
    count: number
  }[]
>([])
const teachingValue = ref<string[] | null>(null)
const teachingList = ref<{ id: string; name: string }[]>([])
const topicList = ref<{ id: string; name: string }[]>([])
const teachingOptions = ref<TreeOption[]>([])
const difficulty = ref<string | null>(null)
const difficultyList = [
  {
    label: '简单',
    value: 'EASY',
  },
  {
    label: '中等',
    value: 'NORMAL',
  },
  {
    label: '困难',
    value: 'HARD',
  },
]

const examOptions = computed(() => {
  return examTypes.value.map((item) => ({
    label: item.name,
    value: item.enum,
    disabled: examList.value.some((el) => el.examType === item.enum),
  }))
})

const renderLabelCascader = (option: any) => {
  return option && option.name.length > 8
    ? h(
        NPopover,
        {},
        {
          trigger: () => h('span', {}, `${option.name}`),
          default: () => h('span', {}, `${option.name}`),
        },
      )
    : h('span', {}, `${option.name}`)
}

const getExamTypes = async () => {
  const res = await getAiUserTestPaperTemplateExamType({
    stageId: props.cvs?.stageId || '1',
    subjectId: props.cvs?.subjectId || '2',
  })
  examTypes.value = res.examTypes
  const temp: {
    id: string
    name: string
    examType: string
    count: number
  }[] = []
  res.examTypes.forEach((item) => {
    if (['单选题', '多选题', '填空题', '简答题'].includes(item.name)) {
      temp.push({
        id: uuidv1(),
        name: item.name,
        examType: item.enum,
        count: 0,
      })
    }
  })
  examList.value = temp
  emits('changeExam', temp)
}

const onTeachChange = (value: string[], option: any) => {
  teachingValue.value = value
  teachingList.value = option.map((item: any) => ({
    id: item.id,
    name: item.name,
  }))

  // 递归查找叶子节点
  const findLeafNodes = (node: any): any[] => {
    if (!node.children || node.children.length === 0) {
      return [{ id: node.id, name: node.name }]
    }

    let leafNodes: any[] = []
    node.children.forEach((child: any) => {
      leafNodes = [...leafNodes, ...findLeafNodes(child)]
    })
    return leafNodes
  }

  // 处理选中的节点
  topicList.value = option.flatMap((item: any) => {
    const lastNode = item
    if (!lastNode.children || lastNode.children.length === 0) {
      return [
        {
          id: lastNode.id,
          name: lastNode.name,
        },
      ]
    } else {
      return findLeafNodes(lastNode)
    }
  })
  emits('changeTeaching', topicList.value)
}

const onDifficultyChange = (value: string) => {
  difficulty.value = value
  emits('changeDifficulty', value)
}

const handleDel = (id: string) => {
  teachingList.value = teachingList.value.filter((item) => item.id !== id)
  teachingValue.value =
    teachingValue.value?.filter((itemId) => itemId !== id) || []
}

const handleMinusExam = (item: { count: number }) => {
  item.count--
  emits('changeExam', examList.value)
}

const handleAddExam = (item: { count: number }) => {
  item.count++
  emits('changeExam', examList.value)
}

const handleDeleteExam = (item: { id: string }) => {
  examList.value = examList.value.filter((el) => el.id !== item.id)
  emits('changeExam', examList.value)
}

const handleInputNumberExam = (item: { count: number }, value: number) => {
  item.count = value
  emits('changeExam', examList.value)
}

const handleSelectChangeExam = (
  item: { examType: string | null; name?: string | null },
  value: string,
  name: string,
) => {
  item.examType = value
  item.name = name
  emits('changeExam', examList.value)
}

const addQuestionType = () => {
  examList.value.push({
    id: uuidv1(),
    name: null,
    examType: null,
    count: 0,
  })
}

const resetPage = () => {
  teachingValue.value = null
  teachingList.value = []
  topicList.value = []
  difficulty.value = null
  const temp: {
    id: string
    name: string
    examType: string
    count: number
  }[] = []
  examTypes.value.forEach((item) => {
    if (['单选题', '多选题', '填空题', '简答题'].includes(item.name)) {
      temp.push({
        id: uuidv1(),
        name: item.name,
        examType: item.enum,
        count: 0,
      })
    }
  })
  examList.value = temp
}

const removeExamList = () => {
  examList.value = examList.value.filter((item) => !item.examType)
}

defineExpose({
  resetPage,
  removeExamList,
})

watch(
  () => props.cvs,
  (value) => {
    if (value) {
      getExamTypes()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => props.systemTree,
  (value) => {
    if (value) {
      teachingOptions.value = value
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<style lang="scss" scoped>
.identifier-box {
  position: relative;
  padding-left: 14px;
  margin-top: 24px;

  &::after {
    position: absolute;
    top: 4px;
    left: 0;
    width: 6px;
    height: 16px;
    content: '';
    background: #fea345;
    border-radius: 62px;
  }
}

.question-type-box {
  margin-bottom: 50px;

  .line {
    height: 1px;
    background: #dfdce8;
  }
}

.scope-mission-ul {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .scope-mission-item {
    display: inline-flex;
    gap: 8px;
    align-items: center;
    max-width: 200px;
    padding: 4px 8px;
    margin-top: 8px;
    margin-right: 16px;
    cursor: pointer;
    background: #f4f6ff;
    border-radius: 4px;

    .scope-mission-item-text {
      max-width: 160px;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-weight: 600;
      line-height: 12px;
      color: #57526c;
      user-select: none;
    }

    .scope-mission-item-del {
      width: 16px;
      height: 16px;
      background: url('https://fp.yangcong345.com/onion-extension/222-c5b76afab3beb10f8d660e5e3aee8a1c.png')
        no-repeat;
      background-size: cover;

      &:hover {
        background: url('https://fp.yangcong345.com/onion-extension/333-c7fa81c8ac8b24532f97e1cc57bb2068.png')
          no-repeat;
        background-size: cover;
      }
    }
  }
}
</style>
