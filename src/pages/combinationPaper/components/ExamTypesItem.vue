<template>
  <div class="theme-setting-item">
    <div class="w-106px mr-14px">
      <OIWSelect
        :value="item.examType"
        :options="examOptions"
        placeholder="请选择题型"
        @update:value="handleSelectChange"
      />
    </div>
    <button
      class="theme-setting-minus"
      :disabled="item.count <= 0"
      @click="handleMinus()"
    >
      <MinusIcon class="w-16px h-16px" />
    </button>
    <n-input-number
      :value="item.count"
      class="theme-setting-number"
      max="100"
      min="0"
      :show-button="false"
      @update:value="handleChange"
    />
    <button
      class="theme-setting-add"
      :disabled="item.count >= 100"
      @click="handleAdd()"
    >
      <AddIcon class="w-16px h-16px" />
    </button>
    <button class="theme-setting-delete" @click="handleDelete()">
      <DeleteIcon class="w-16px h-16px" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { OIWSelect } from '@guanghe-pub/onion-ui-web'
import MinusIcon from '~icons/yc/minus'
import AddIcon from '~icons/yc/add'
import DeleteIcon from '~icons/yc/delete'

const props = defineProps<{
  item: {
    id: string
    name: string | null
    examType: string | null
    count: number
    [k: string]: unknown
  }
  examOptions: { label: string; value: string; disabled: boolean }[]
}>()

const emits = defineEmits<{
  (e: 'minus', item: { count: number }): void
  (e: 'add', item: { count: number }): void
  (e: 'delete', item: { id: string }): void
  (e: 'inputNumber', item: { count: number }, value: number): void
  (
    e: 'selectChange',
    item: { examType: string | null },
    value: string,
    name: string,
  ): void
}>()

const handleMinus = () => {
  emits('minus', props.item)
}

const handleAdd = () => {
  emits('add', props.item)
}

const handleDelete = () => {
  emits('delete', props.item)
}

const handleChange = (value: number | null) => {
  emits('inputNumber', props.item, value || 0)
}

const handleSelectChange = (value: string) => {
  emits(
    'selectChange',
    props.item,
    value,
    props.examOptions?.find((item) => item.value === value)?.label || '',
  )
}
</script>

<style lang="scss" scoped>
.theme-setting-item {
  display: flex;
  align-items: center;
  padding: 8px 0;

  &:last-of-type {
    padding: 8px 0 16px;
  }

  &:first-of-type {
    padding: 16px 0 8px;
  }

  .theme-setting-name {
    width: 70px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #57526c;
  }

  .theme-setting-minus,
  .theme-setting-add,
  .theme-setting-delete {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    line-height: 40px;
    background: transparent;
    border: 1px solid #c5c1d4;
    border-radius: 12px;

    &:disabled {
      cursor: not-allowed;
    }

    &:not(:disabled):hover {
      color: #5e80ff;
      border: 1px solid #5e80ff;
    }
  }

  .theme-setting-delete {
    margin-left: 14px;
  }

  .theme-setting-number {
    ::v-deep(.n-input) {
      width: 88px;
      padding: 3px 0px;
      margin: 0px 8px;
      border-radius: 12px;

      .n-input__border {
        border: 1px solid #c5c1d4;
      }
    }
  }
}
</style>
