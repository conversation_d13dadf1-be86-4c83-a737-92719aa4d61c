<template>
  <div class="pwd-login-box">
    <n-tabs type="line" size="large" animated @update:value="onTabChange">
      <n-tab-pane name="账号登录">
        <div>
          <div class="row">
            <n-input v-model:value="model.username" clearable />
          </div>
          <div class="row">
            <NInput
              v-model:value="model.password"
              type="password"
              show-password-on="click"
              clearable
            />
          </div>
        </div>
      </n-tab-pane>
      <NTabPane name="验证码登录">
        <div>
          <div class="row">
            <NInput v-model:value="phoneModel.phone" />
          </div>
          <div class="row">
            <Captcha
              ref="captchaRef"
              v-model:captchaCode="phoneModel.captchaCode"
              :phone="phoneModel.phone"
            />
          </div>
          <div class="row">
            <NGrid :cols="5" :x-gap="10">
              <NGridItem :span="3">
                <NInput
                  v-model:value="phoneModel.code"
                  :disabled="captchaCodeInputDisabled"
                />
              </NGridItem>

              <NGridItem :span="2">
                <NButton
                  style="border-radius: 34px"
                  type="info"
                  block
                  :disabled="codeStatus.sendFlag"
                  @click="getCaptcha"
                >
                  {{
                    codeStatus.sendFlag
                      ? `重发${codeStatus.timer}s`
                      : '发送验证码'
                  }}
                </NButton>
              </NGridItem>
            </NGrid>
          </div>
        </div>
      </NTabPane>
    </n-tabs>
    <div class="footer">
      <div class="row">
        <n-button style="border-radius: 34px" block type="info" @click="login">
          登录
        </n-button>
      </div>
    </div>
  </div>
  <ResetPwdDialog
    v-model:show="resetModalShow"
    :username="resetData.username"
    :password="resetData.password"
    @reset-success="onResetSuccess"
  />
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { passwordRegex, phoneRegex } from '@/utils/reg'
import Captcha from './captcha.vue'
import ResetPwdDialog from './resetPwdDialog.vue'
import {
  getCaptchaWithPhoneApi,
  loginByPwdApi,
  loginByCodeApi,
} from '@/pages/login/service'
import useAuthStore from '@/store/auth'
import useCvsStore from '@/store/csv'
import { useRouter } from 'vue-router'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

defineEmits<(e: 'changeScene', val: string) => void>()

const router = useRouter()
const authStore = useAuthStore()
const cvsStore = useCvsStore()

/**
 * 切换登录类型
 */
const loginModel = ref<'pwd' | 'phoneCode'>('pwd')
const onTabChange = (value: string) => {
  if (value === '账号登录') {
    loginModel.value = 'pwd'
  } else {
    loginModel.value = 'phoneCode'
  }
}
const model = reactive({
  username: '',
  password: '',
  code: '',
})
const phoneModel = reactive({
  phone: '',
  code: '',
  captchaCode: '',
})

const message = useOIWMessage()

/**
 * 输入校验
 */
const checkPhoneNumber = () => {
  if (!phoneRegex.test(phoneModel.phone)) {
    message.error('请输入正确的手机号码')
    return false
  }
  return true
}
const checkCaptcha = () => {
  if (!phoneModel.captchaCode) {
    message.error('验证码不能为空')
    return false
  }
  return true
}

const checkUsername = () => {
  if (!model.username) {
    message.error('请输入正确的账号')
    return false
  }
  return true
}

const checkPass = () => {
  if (!model.password) {
    message.error('请输入密码')
    return false
  }
  return true
}

/**
 * 手机号验证码相关逻辑
 */
const captchaRef = ref()

const getCaptcha = () => {
  if (checkPhoneNumber()) {
    getCaptchaWithPhoneApi({
      phone: phoneModel.phone,
      graphCode: phoneModel.captchaCode,
    }).then((res) => {
      if (res) {
        if (!res.success && res.isNeed) {
          captchaRef?.value.setCaptchaCode(res.graph)
          captchaRef?.value.setCaptchaError(true)
        } else {
          afterSendCode()
          captchaRef?.value.setCaptchaError(false)
        }
      }
    })
  }
}
const codeStatus = reactive({
  sendFlag: false,
  timer: 0,
})
const resetCodeStatus = () => {
  codeStatus.sendFlag = false
  codeStatus.timer = 0
}
const captchaCodeInputDisabled = ref(true)
let timer: number | null = null
const afterSendCode = () => {
  codeStatus.sendFlag = true
  codeStatus.timer = 60
  captchaCodeInputDisabled.value = false
  if (timer) {
    window.clearInterval(timer)
  }
  timer = window.setInterval(() => {
    if (codeStatus.timer === 0) {
      timer && window.clearInterval(timer)
      resetCodeStatus()
    } else {
      codeStatus.timer--
    }
  }, 1000)
}

onMounted(() => {
  if (timer) {
    window.clearInterval(timer)
  }
})
/**
 * 登录逻辑
 */
const login = () => {
  if (loginModel.value === 'phoneCode') {
    if (!checkPhoneNumber()) {
      return
    }
    if (!checkCaptcha()) {
      return
    }
    loginByCodeApi({
      phone: phoneModel.phone,
      captcha: phoneModel.code,
    }).then(({ token, me }) => {
      authStore.login(token, me)
      router.push({
        name: 'MicroVideoList',
      })
    })
  } else {
    // 账号密码登录
    if (!checkUsername()) {
      return
    }
    if (!checkPass()) {
      return
    }
    loginByPwd(model.username, model.password)
  }
}
/**
 * 账号密码登录逻辑判断
 */
const loginByPwd = (name: string, password: string) => {
  loginByPwdApi({ name, password }).then(({ token, me }) => {
    authStore.login(token, me)
    authStore.getAIEntry()
    authStore.getAIClassAuth()
    authStore.getTeacherEEV2()
    authStore.fetchUserAuth()
    cvsStore.getCvsEnum()
    if (!passwordRegex.test(model.password)) {
      resetData.username = model.username
      resetData.password = model.password
      resetModalShow.value = true
      model.password = ''
    } else {
      router.push({
        name: 'MicroVideoList',
      })
    }
  })
}
/**
 * 简单密码重置回调
 */
const onResetSuccess = (val: string) => {
  loginByPwd(model.username, val)
}

const resetModalShow = ref(false)
const resetData = reactive({
  username: '',
  password: '',
})
</script>

<style lang="scss" scoped>
.row {
  & + .row {
    margin-top: 20px;
  }
}

.footer {
  margin-top: 20px;
}

.button-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 15px;
  margin-top: 15px;
  border-bottom: 1px solid #f0f0f0;

  .col {
    width: 1px;
    height: 14px;
    margin: 0 10px;
    background: #c2c2c2;
  }
}
</style>

<style lang="scss">
.pwd-login-box {
  input:-webkit-autofill,
  input:autofill {
    -webkit-box-shadow: 0 0 0 1000px transparent inset;
    transition: background-color 5000s ease-in-out 0s;
  }
}
</style>
