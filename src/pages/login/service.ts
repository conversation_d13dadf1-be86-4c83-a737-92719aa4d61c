import type { MeTypes } from '@/service/auth'
import { apiSchoolDomain } from '@/utils/apiUrl'
import request from '@/utils/request'
import type { AxiosResponse } from 'axios'

export interface LoginQrCode {
  expiredDate: string
  id: string
  url: string
}

export const getQrCodeApi = () => {
  return request.post<LoginQrCode>(`${apiSchoolDomain}/public/qr-code`)
}

export interface QrCodeValidate {
  state: 0 | 3
}

// 校验登录二维码有效时间
export const validateOrCodeTimeApi = (id: string) => {
  return request.get<QrCodeValidate>(`${apiSchoolDomain}/public/qr-code/${id}`)
}

interface getCaptchaData {
  phone: string
  graphCode: string
}

export interface CaptChaRes {
  graph: string
  success: boolean
  isNeed: boolean
}
export const getCaptchaWithPhoneApi = (data: getCaptchaData) => {
  return request.post<CaptChaRes>(
    `${apiSchoolDomain}/public/sms-captcha/sms`,
    {
      ...data,
      countryCode: 'CN',
      type: 'login',
    },
    {
      decrypt: true,
    },
  )
}

export const refreshCaptchApi = (phone: string) => {
  return request.post<{
    graph: string
  }>(
    `${apiSchoolDomain}/public/sms-captcha/captcha/refresh`,
    {
      phone,
      countryCode: 'CN',
      type: 'login',
    },
    {
      decrypt: true,
    },
  )
}

interface LoginPwdData {
  name: string
  password: string
}

export const loginByPwdApi = async (data: LoginPwdData) => {
  return request
    .post<AxiosResponse<MeTypes>>(`${apiSchoolDomain}/public/login`, data, {
      decrypt: true,
      needReturnComplate: true,
    })
    .then((res) => {
      return {
        token: res.headers?.authorization,
        me: res.data,
      }
    })
}

interface LoginCodeData {
  phone: string
  captcha: string
}

export const loginByCodeApi = async (data: LoginCodeData) => {
  return request
    .post<AxiosResponse<MeTypes>>(
      `${apiSchoolDomain}/public/user/captcha/verify/phone`,
      {
        ...data,
        code: 'CN',
        type: 'login',
        from: 'pc',
      },
      {
        decrypt: true,
        needReturnComplate: true,
      },
    )
    .then((res) => {
      return {
        token: res.headers?.authorization,
        me: res.data,
      }
    })
}

interface UpdatePwdData {
  password: string
  newPassword: string
}

export const updatePwdApi = async (data: UpdatePwdData) => {
  return request.put(`${apiSchoolDomain}/me/password`, data, {
    decrypt: true,
  })
}

interface ResetPwdData {
  phone: string
  password: string
  code: string
}

export const resetPwdApi = async (data: ResetPwdData) => {
  return request
    .put<AxiosResponse>(
      `${apiSchoolDomain}/user/password`,
      {
        countryCode: 'CN',
        type: 'login',
        ...data,
      },
      {
        decrypt: true,
        needReturnComplate: true,
      },
    )
    .then((res) => {
      if (res.headers?.authorization) {
        return res.headers.authorization
      }
    })
}

// 退出登录接口
export const logoutApi = async () => {
  return request.post(
    `${apiSchoolDomain}/teacher-common/user/device-manage/logout`,
  )
}
