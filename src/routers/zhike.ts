import type { RouteRecordRaw } from 'vue-router'

const zhikeRoutes: RouteRecordRaw[] = [
  {
    name: 'eeV2',
    path: '/eeV2',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/eeV2/list',
    children: [
      /**
       * 首页列表
       */
      {
        name: 'eeV2List',
        path: 'list',
        component: () => import('@/pages/eeV2/eeList.vue'),
      },
      /**
       * 班级详情
       */
      {
        name: 'eeV2Detail',
        path: 'detail',
        props: (to) => {
          return {
            id: to.query.id,
          }
        },
        component: () => import('@/pages/eeV2/classLearningTask/detail.vue'),
      },
      /**
       * 班级考点预览列表
       */
      {
        name: 'eeV2PlanInfo',
        path: 'planInfo',
        props: (to) => {
          return {
            id: to.query.id,
          }
        },
        component: () => import('@/pages/eeV2/classLearningTask/planInfo.vue'),
      },
      /**
       * 班级考点预览微课、知识点
       */
      {
        name: 'eeV2PointPdf',
        path: 'pointPdf',
        props: (to) => {
          return {
            id: to.query.id,
            pointId: to.query.pointId,
            difficult: to.query.difficult,
          }
        },
        component: () => import('@/pages/eeV2/classLearningTask/pointPdf.vue'),
      },
      /**
       * 校级考点预览
       */
      {
        name: 'eeV2SchoolTask',
        path: 'schoolLearningTask',
        component: null,
        children: [
          /**
           * 校级考点预览列表
           */
          {
            name: 'eeV2SchoolTaskList',
            path: 'list',
            component: () => import('@/pages/eeV2/schoolLearningTask/list.vue'),
          },
          /**
           * 校级考点预览详情
           */
          {
            name: 'eeV2SchoolTaskDetail',
            path: 'detail',
            component: () =>
              import('@/pages/eeV2/schoolLearningTask/detail.vue'),
          },
          /**
           * 校级预览微课、知识点
           */
          {
            name: 'eeV2SchoolTaskDetailPdf',
            path: 'pdf',
            component: () =>
              import('@/pages/eeV2/schoolLearningTask/pointPdf.vue'),
          },
        ],
      },
    ],
  },
  /**
   * 班级试卷预览
   */
  {
    name: 'eeV2TestPaper',
    path: '/eeV2/testPaper',
    props: (to) => {
      return {
        id: to.query.id,
        planName: to.query.planName,
        testPaperId: to.query.testPaperId,
      }
    },
    component: () => import('@/pages/eeV2/classLearningTask/testPaper.vue'),
  },
]

export default zhikeRoutes
