import type { RouteRecordRaw } from 'vue-router'

const resourceRoutes: RouteRecordRaw[] = [
  {
    path: '/resourceCenter',
    name: 'ResourceCenter',
    component: () => import('@/pages/home/<USER>'),
    redirect: {
      name: 'CoursewareList',
    },
    children: [
      {
        name: 'CommonResource',
        path: 'commonResource',
        props: () => ({
          title: '公共资源',
        }),
        component: () => import('@/pages/home/<USER>'),
        redirect: {
          name: 'MicroVideoList',
        },
        children: [
          {
            name: 'CoursewareList',
            path: 'courseList',
            component: () => import('@/pages/courseware/list/index.vue'),
          },
          {
            name: 'CoursewarePreview',
            path: 'coursePreview',
            component: () => import('@/pages/courseware/preview/index.vue'),
          },
          {
            name: 'MicroVideoList',
            path: 'microVideoList',
            component: () => import('@/pages/microVideo/list.vue'),
          },
          {
            name: 'MicroVideoPreview',
            path: 'microVideoPreview',
            props: (route) => ({
              topicId: route.query.topicId,
              checkValue: route.query.checkValue,
              specialCourseId: route.query.specialCourseId || '',
              subjectId: route.query.subjectId || '',
              stageId: route.query.stageId || '',
              publisherId: route.query.publisherId || '',
              semesterId: route.query.semesterId || '',
              clipId: route.query.clipId,
            }),
            component: () => import('@/pages/microVideo/preview.vue'),
          },
          {
            name: 'ProblemList',
            path: 'problemList',
            component: () => import('@/pages/problem/problemList/index.vue'),
          },
          {
            name: 'PaperList',
            path: 'paperList',
            props: (route) => ({
              defaultSubjectId: route.query.subjectId || '',
              defaultStageId: route.query.stageId || '',
              name: route.query.name || '',
            }),
            component: () => import('@/pages/paper/paperList/index.vue'),
          },
          {
            name: 'TeachingResearchVideo',
            path: 'teachingResearchVideo',
            props: (route) => ({
              videoId: route.query.videoId,
            }),
            component: () => import('@/pages/microVideo/researchVideo.vue'),
          },
        ],
      },
      // {
      //   name: 'PersonalResource',
      //   path: 'personalResource',
      //   props: () => ({ title: '个人资源' }),
      //   component: () => import('@/pages/home/<USER>'),
      //   redirect: { name: 'MyCombinationPaper' },
      //   children: [
      //     {
      //       name: 'MyCombinationPaper',
      //       path: 'myCombinationPaper',
      //       component: () => import('@/pages/combinationPaper/list/index.vue'),
      //     },
      //     {
      //       name: 'MyCollection',
      //       path: 'myCollection',
      //       props: () => ({
      //         title: '我的收藏',
      //       }),
      //       component: () => import('@/pages/home/<USER>'),
      //       redirect: {
      //         name: 'MicroVideoCollection',
      //       },
      //       children: [
      //         {
      //           name: 'MicroVideoCollection',
      //           path: 'microVideoCollection',
      //           component: () => import('@/pages/microVideo/collection.vue'),
      //         },
      //         {
      //           name: 'ProblemCollection',
      //           path: 'problemCollection',
      //           props: () => ({
      //             from: 'myCollection',
      //           }),
      //           component: () => import('@/pages/problem/collection/index.vue'),
      //         },
      //         {
      //           name: 'PaperCollection',
      //           path: 'paperCollection',
      //           component: () => import('@/pages/paper/collection/index.vue'),
      //         },
      //       ],
      //     },
      //   ],
      // },
      {
        name: 'SchoolResource',
        path: 'schoolResource',
        props: () => ({
          title: '校本资源',
        }),
        component: () => import('@/pages/home/<USER>'),
        meta: {
          accessKey: ['school-resources'], // 权限校验字段
        },
        redirect: {
          name: 'SchoolMicroVideoList',
        },
        children: [
          {
            name: 'SchoolCoursewareList',
            path: 'schoolCoursewareList',
            component: () =>
              import('@/pages/schoolResource/schoolCourseware/list.vue'),
          },
          {
            name: 'SchoolMicroVideoList',
            path: 'schoolMicroVideoList',
            component: () =>
              import('@/pages/schoolResource/schoolVideo/list.vue'),
          },
          {
            name: 'SchoolProblemList',
            path: 'schoolProblemList',
            props: (route) => ({
              subjectId: route.query.subjectId,
              stageId: route.query.stageId,
              publisherId: route.query.publisherId,
              semesterId: route.query.semesterId,
            }),
            component: () =>
              import('@/pages/schoolResource/schoolProblem/list.vue'),
          },
          {
            name: 'SchoolProblemEntering',
            path: 'schoolProblemEntering',
            props: (route) => ({
              url: route.query.url,
              targetId: route.query.targetId,
              subjectId: route.query.subjectId,
              stageId: route.query.stageId,
              publisherId: route.query.publisherId,
              semesterId: route.query.semesterId,
              sense: route.query.sense, // 场景：校本题目、校本试卷
              problemSettingList: route.query.problemSettingList, // 录题设置：录为题目、录为试卷
              filename: route.query.filename, // 文件名
            }),
            component: () =>
              import('@/pages/schoolResource/schoolProblem/entering.vue'),
          },
          {
            name: 'SchoolPaperList',
            path: 'schoolPaperList',
            props: (route) => ({
              subjectId: route.query.subjectId,
              stageId: route.query.stageId,
            }),
            component: () =>
              import('@/pages/schoolResource/schoolPaper/list.vue'),
          },
        ],
      },
      {
        name: 'PaperPreview',
        path: 'paperPreview',
        props: (to) => {
          return {
            id: to.query.id,
            paperType: to.query.paperType,
          }
        },
        component: () => import('@/pages/paper/preview/index.vue'),
      },
      {
        name: 'SchoolPaperPreview',
        path: 'schoolPaperPreview',
        props: (to) => {
          return {
            id: to.query.id,
          }
        },
        component: () =>
          import('@/pages/schoolResource/schoolPaper/preview.vue'),
      },
    ],
  },
  {
    name: 'SchoolCoursewareCreateAIPPT',
    path: '/resourceCenter/schoolResource/schoolCoursewareCreateAIPPT',
    props: (route) => ({
      targetId: route.query.targetId,
      stageId: route.query.stageId,
      subjectId: route.query.subjectId,
      publisherId: route.query.publisherId,
      semesterId: route.query.semesterId,
      chapterId: route.query.chapterId,
      sectionId: route.query.sectionId,
      subsectionId: route.query.subsectionId,
    }),
    component: () =>
      import('@/pages/schoolResource/schoolCourseware/createAIPPT.vue'),
  },
]

export default resourceRoutes
