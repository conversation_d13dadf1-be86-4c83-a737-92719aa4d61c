import type { RouteRecordRaw } from 'vue-router'

const shareRoutes: RouteRecordRaw[] = [
  {
    path: '/share',
    name: 'Share',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/',
    children: [
      {
        name: 'ShareExam',
        path: 'exam/:id',
        meta: {
          excludeLogin: true,
        },
        props: (route) => {
          return {
            shareId: route.params.id,
          }
        },
        component: () => import('@/pages/share/exam/index.vue'),
      },
      {
        name: 'ShareCourseware',
        path: 'courseware/:id/:type',
        meta: {
          excludeLogin: true,
        },
        props: (route) => {
          return {
            shareId: route.params.id,
            resourceType: route.params.type,
          }
        },
        component: () => import('@/pages/share/courseware/index.vue'),
      },
    ],
  },
]

export default shareRoutes
