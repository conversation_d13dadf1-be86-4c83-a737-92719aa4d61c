import type { RouteRecordRaw } from 'vue-router'

const userRoutes: RouteRecordRaw[] = [
  {
    path: '/userCenter',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/userCenter/info',
    children: [
      {
        name: 'UserCenterInfo',
        path: 'info',
        component: () => import('@/pages/user/index.vue'),
      },
      {
        name: 'UserCenterContactUs',
        path: 'contactUs',
        component: () => import('@/pages/user/contactUs.vue'),
      },
    ],
  },
]

export default userRoutes
