import type { RouteRecordRaw } from 'vue-router'

const slideRoutes: RouteRecordRaw[] = [
  {
    path: '/slide/edit',
    name: 'SlideEdit',
    props: (to) => {
      return {
        fileId: to.query.fileId,
        id: to.query.id,
        fileName: to.query.fileName,
        platform: to.query.platform || '',
        sourceId: to.query.sourceId || '',
      }
    },
    component: () => import('@/pages/presentation/edit.vue'),
  },
  {
    path: '/slide/preview',
    name: 'SlidePreview',
    props: (to) => {
      return {
        fileId: to.query.fileId,
        platform: to.query.platform,
        sourceId: to.query.sourceId || '',
      }
    },
    component: () => import('@/components/PPTPreview/pagePreview.vue'),
  },
  {
    path: '/slide/aiCreate',
    name: 'createAIPPT',
    component: () => import('@/pages/createAIPPT/index.vue'),
  },
  {
    path: '/word/edit',
    name: 'WordEdit',
    props: (to) => {
      return {
        fileId: to.query.fileId,
        id: to.query.id,
        fileName: to.query.fileName,
        platform: to.query.platform || '',
      }
    },
    component: () => import('@/pages/word/edit.vue'),
  },
]

export default slideRoutes
