import type { RouteRecordRaw } from 'vue-router'

const personalResourceRoutes: RouteRecordRaw[] = [
  {
    path: '/personalResource',
    name: 'PersonalResource',
    component: () => import('@/pages/home/<USER>'),
    redirect: {
      name: 'prepareCenterList', // 默认定位到备课
    },
    children: [
      {
        name: 'PersonalResourceNavigation',
        path: 'navigation',
        props: () => ({
          title: '我的',
        }),
        component: () => import('@/pages/home/<USER>'),
        redirect: {
          name: 'prepareCenterList',
        },
        children: [
          // 备课中心
          {
            name: 'prepareCenterList',
            path: 'prepareCenterlist',
            component: () => import('@/pages/prepare/index.vue'),
          },
          {
            // 微课
            name: 'PersonalVideo',
            path: 'personalVideo',
            component: () =>
              import('@/pages/personalResource/personalVideo/index.vue'),
          },
          {
            // 题库
            name: 'PersonalProblem',
            path: 'personalProblem',
            component: () =>
              import('@/pages/personalResource/personalProblem/index.vue'),
          },
          {
            // 试卷
            name: 'PersonalExamPaper',
            path: 'personalExamPaper',
            component: () =>
              import('@/pages/personalResource/personalExamPaper/index.vue'),
          },
        ],
      },
    ],
  },
]

export default personalResourceRoutes
