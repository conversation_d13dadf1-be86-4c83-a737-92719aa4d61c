import type { RouteRecordRaw } from 'vue-router'

const classManageRoutes: RouteRecordRaw[] = [
  {
    path: '/classManage',
    name: 'ClassManage',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/classManage/list',
    children: [
      {
        name: 'ClassManageList',
        path: 'list',
        component: () => import('@/pages/classManage/list.vue'),
      },
      // 行政班班级详情
      {
        name: 'ClassDetail',
        path: 'detail',
        props: (route) => ({
          type: route.query.type,
          roomId: route.query.roomId,
          name: route.query.name,
        }),
        component: () => import('@/pages/classManage/adminRoom.vue'),
      },
      // 教学班详情
      {
        name: 'TeachingRoomDetail',
        path: 'teachingRoom',
        props: (route) => ({
          roomRef: route.query.roomRef,
          name: route.query.name,
        }),
        component: () => import('@/pages/classManage/teachingRoom.vue'),
      },
      // Al课堂教学班详情
      {
        name: 'TeachingGoodRoomDetail',
        path: 'teachingGoodRoom',
        props: (route) => ({
          roomRef: route.query.roomRef,
          name: route.query.name,
        }),
        component: () => import('@/pages/classManage/teachingGoodRoom.vue'),
      },
      // 班级账号列表
      {
        name: 'ClassAccountList',
        path: 'accountList',
        props: (route) => ({
          type: route.query.type,
          roomRid: route.query.roomRid,
        }),
        component: () =>
          import('@/pages/classManage/components/accountList.vue'),
      },
      {
        name: 'ClassLearning',
        path: 'learning',
        props: (route) => ({
          tab: route.query.tab,
          roomId: route.query.roomId,
          groupId: route.query.groupId,
        }),
        component: () => import('@/pages/classLearning/index.vue'),
      },
      {
        name: 'MistakesPreview',
        path: 'mistakesPreview',
        props: (route) => ({
          groupId: route.query.groupId,
          startAt: route.query.startAt,
          endAt: route.query.endAt,
          subjectId: route.query.subjectId,
          stageId: route.query.stageId,
          publisherId: route.query.publisherId,
          semesterId: route.query.semesterId,
          chapterId: route.query.chapterId,
          sectionId: route.query.sectionId,
          source: route.query.source,
          answerNum: route.query.answerNum,
          accuracy: route.query.accuracy,
        }),
        component: () =>
          import('@/pages/classLearning/questionData/mistakesPreview.vue'),
      },
    ],
  },
]

export default classManageRoutes
