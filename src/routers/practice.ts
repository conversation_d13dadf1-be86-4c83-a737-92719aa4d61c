import type { RouteRecordRaw } from 'vue-router'

const practiceRoutes: RouteRecordRaw[] = [
  {
    path: '/practice',
    name: 'PracticeCenter',
    component: () => import('@/pages/home/<USER>'),
    redirect: {
      name: 'PracticeList',
    },
    children: [
      {
        // 布置练习
        name: 'ConfigurePractice',
        path: 'configure',
        component: () => import('@/pages/practice/configure/index.vue'),
      },
      {
        name: 'UnPublishPractice',
        path: 'unpublish',
        component: () => import('@/pages/practice/unpublish/index.vue'),
      },
      {
        // 布置答题卡任务
        name: 'CreateAnswerCard',
        path: 'createAnswerCard',
        component: () => import('@/pages/practice/answerCard/index.vue'),
        meta: {
          accessKey: ['answer-sheets'], // 权限校验字段
        },
      },
      {
        // 编辑答题卡任务
        name: 'EditAnswerCard',
        path: 'editAnswerCard',
        props: (route) => {
          return {
            measuringId: route.query.measuringId || '',
          }
        },
        component: () => import('@/pages/practice/answerCard/edit.vue'),
        meta: {
          accessKey: ['answer-sheets'], // 权限校验字段
        },
      },
      {
        name: 'report',
        path: 'report',
        component: () => import('@/pages/practice/report/report.vue'),
        children: [
          {
            name: 'SystemCourseReport',
            path: 'systemCourse',
            props: (route) => {
              return {
                measuringId: route.query.measuringId || '',
                type: 'systemCourse',
              }
            },
            component: () =>
              import('@/pages/practice/report/systemCourse/index.vue'),
          },
          {
            name: 'SyncPracticeReport',
            path: 'syncPractice',
            props: (route) => {
              return {
                measuringId: route.query.measuringId || '',
                type: 'syncPractice',
              }
            },
            component: () =>
              import('@/pages/practice/report/syncPractice/index.vue'),
          },
          {
            name: 'SyncReviewReport',
            path: 'syncReview',
            props: (route) => {
              return {
                measuringId: route.query.measuringId || '',
                type: 'syncReview',
              }
            },
            component: () =>
              import('@/pages/practice/report/syncReview/index.vue'),
          },
          {
            name: 'AnswerCardReport',
            path: 'answerCard',
            props: (route) => {
              return {
                measuringId: route.query.measuringId || '',
                type: 'answerCard',
              }
            },
            component: () =>
              import('@/pages/practice/report/answerCard/index.vue'),
          },
          {
            name: 'AnswerCardUser',
            path: 'answerCardUser',
            props: (route) => {
              return {
                measuringId: route.query.measuringId || '',
                userId: route.query.userId || '',
              }
            },
            component: () =>
              import('@/pages/practice/report/answerCard/user.vue'),
          },
        ],
      },
    ],
  },
]

export default practiceRoutes
