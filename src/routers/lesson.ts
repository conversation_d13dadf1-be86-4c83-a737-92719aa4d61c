import type { RouteRecordRaw } from 'vue-router'

// AI 课堂
const lessonRoutes: RouteRecordRaw[] = [
  {
    name: 'Lesson',
    path: '/lesson',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/lesson/navigation/entry',
    children: [
      {
        name: 'PracticeNavigation',
        path: 'navigation',
        props: () => ({
          title: '练习导航',
        }),
        component: () => import('@/pages/home/<USER>'),
        redirect: {
          name: 'LessonEntry',
        },
        children: [
          {
            // AI课堂列表页
            name: 'LessonEntry',
            path: 'entry',
            component: () => import('@/pages/lesson/entry/index.vue'),
            meta: {
              accessKey: ['ai-class'], // 权限校验字段
            },
          },
          {
            // 作业列表页
            name: 'PracticeList',
            path: 'practiceList',
            component: () => import('@/pages/practice/list/index.vue'),
          },
        ],
      },
      {
        name: 'LessonDraft',
        path: 'draft',
        component: () => import('@/pages/lesson/draft/index.vue'),
      },
      {
        name: 'LessonCreate',
        path: 'create',
        props(to) {
          return {
            startTime: Number(to.query.startTime),
            duration: Number(to.query.duration),
            copyId: to.query.copyId || '',
          }
        },
        component: () => import('@/pages/lesson/create/index.vue'),
      },
      {
        name: 'LessonCreateFromDraft',
        path: 'createFromDraft',
        props(to) {
          return {
            draftId: to.query.draftId,
          }
        },
        component: () => import('@/pages/lesson/create/index.vue'),
      },
      {
        name: 'LessonEdit',
        path: 'edit',
        props(to) {
          return {
            id: to.query.id,
          }
        },
        component: () => import('@/pages/lesson/edit/index.vue'),
      },
      {
        name: 'LessonAnalyze',
        path: 'lessonAnalyze',
        props: (route) => ({
          id: route.query.id,
          groupId: route.query.groupId,
        }),
        component: () => import('@/pages/lesson/analyze/index.vue'),
      },
    ],
  },
  {
    name: 'LessonStatus',
    path: '/lessonStatus',
    component: () => import('@/pages/home/<USER>'),
    redirect: '/learningStatus/schoolReport',
    children: [
      {
        name: 'LessonStatusSchoolReport',
        path: 'schoolReport',
        component: () => import('@/pages/lesson/schoolReport/index.vue'),
      },
    ],
  },
]

export default lessonRoutes
