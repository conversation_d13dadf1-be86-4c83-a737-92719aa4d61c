import type { RouteLocationNormalized } from 'vue-router'
import { createWebHistory, createRouter } from 'vue-router'
import useAuthStore from '@/store/auth'
import personalResourceRoutes from '@/routers/personalResourceRoutes.ts'
import resourceRoutes from './resource'
import slideRoutes from './slide'
import shareRoutes from './share'
import practiceRoutes from './practice'
import userRoutes from './user'
import classManageRoutes from './classManage'
import zhikeRoutes from './zhike'
import { SeewoDomain } from '@/utils/apiUrl'
import lessonRoutes from './lesson'
import { checkIsZhiPu } from '@/utils/zhipu'

const publicPath = `/${import.meta.env.VITE_APP_PROJECT_NAME}/`

const router = createRouter({
  history: createWebHistory(publicPath),
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
  routes: [
    {
      name: 'index',
      path: '/',
      component: () => import('@/pages/home/<USER>'),
      // 不使用redirect的原因是需要之前需要权限判断，所以做在了home组件中
      // redirect: {
      //   name: 'MicroVideoList',
      // },
    },
    {
      name: 'login',
      path: '/login',
      component: () => import('@/pages/login/index.vue'),
    },
    {
      name: 'VideoPlay',
      path: '/videoPlay/:specialCourseId?/:topicId',
      props: (route) => {
        return {
          specialCourseId: route.params.specialCourseId,
          topicId: route.params.topicId,
          clipId: route.query.clipId,
          type: route.query.type || '',
        }
      },
      component: () => import('@/pages/videoPlay/index.vue'),
    },
    {
      name: 'ProblemInfo',
      path: '/problemInfo/:problemId',
      props: (route) => {
        return {
          problemId: route.params.problemId,
          courseId: route.query.courseId,
          type: route.query.type || '',
        }
      },
      component: () => import('@/pages/problem/problemInfo/index.vue'),
    },
    {
      name: 'TemplateManage',
      path: '/templateManage',
      component: () => import('@/pages/templateManage/index.vue'),
    },
    {
      // AI组卷
      name: 'AICombinePaper',
      path: '/aiCombinePaper',
      component: () => import('@/pages/combinationPaper/ai/index.vue'),
    },
    {
      name: 'CombinationPaperEdit',
      path: '/combinationPaperEdit/:id',
      props: (to) => {
        return {
          id: to.params.id,
          fromPageName: to.query.fromPageName, // [1:资源中心-公共资源;2:资源中心-个人资源;3:其它]
        }
      },
      component: () => import('@/pages/combinationPaper/edit/index.vue'),
    },
    ...personalResourceRoutes, // 我的
    ...resourceRoutes, // 资源中心
    ...slideRoutes, // 演示
    ...shareRoutes, // 分享
    ...practiceRoutes, // 练习中心
    ...userRoutes, // 个人中心
    ...classManageRoutes, // 班级管理
    ...lessonRoutes, // AI课堂
    ...zhikeRoutes, // 智课3.0
  ],
})

const checkMeData = async () => {
  const authStore = useAuthStore()
  if (!authStore.me) {
    try {
      await authStore.getMe()
      await authStore.getRealTeacher()
    } catch (error) {
      // @ts-ignore
      // eslint-disable-next-line
      console.log(error)
    }
  }
}

const checkIsAuth = async (to: RouteLocationNormalized) => {
  const authStore = useAuthStore()
  if (!authStore.token) {
    if (to.meta.excludeLogin) {
      return true
    }
    // /teacher-workbench/videoPlay/398548ac-57f7-11e7-920f-0fd3a424083c?type=seewo
    // https://school-test.yangcongxueyuan.com/teacher-workbench/videoPlay/398548ac-57f7-11e7-920f-0fd3a424083c?clipId=a231b960-6a26-11e9-9a33-5dee6ed63be4?type=seewo
    // seewo会直接拼接?type=seewo，不管之前有没有query
    // 希沃环境下 拦截跳转，跳转到see-wo-fe项目
    if (window.location.search.includes('type=seewo')) {
      const query = window.location.href
        .split('/teacher-workbench/videoPlay/')[1]
        .split('?type=seewo')[0]
      window.location.replace(
        `${SeewoDomain}/videoBench/${query}`,
        // `https://seewo-test.yangcongxueyuan.com/videoBench/398548ac-57f7-11e7-920f-0fd3a424083c`,
      )
      return
    }
    if (to.name !== 'login') {
      if (`${import.meta.env.VITE_APP_BASE_API}` === '/teacher-workbench/') {
        return {
          name: 'login',
          query: {
            redirect: to.query.redirect,
          },
        }
      } else {
        const encodedUrl = encodeURIComponent(window.location.href)
        window.location.replace(
          `${window.location.origin}/login?workbench=${encodedUrl}`,
        )
      }
    } else {
      return true
    }
  }
  await checkMeData()
}
const checkShareToken = async (to: RouteLocationNormalized) => {
  if (to.query.shareToken) {
    const authStore = useAuthStore()
    await authStore.setShareToken(decodeURI(to.query.shareToken as string))
    const newQuery = { ...to.query }
    delete newQuery.shareToken
    await router.replace({
      ...to,
      query: newQuery,
    })
  }
  return true
}

const createRouterGuard = async (to: RouteLocationNormalized) => {
  const isZhiPu = checkIsZhiPu(to, router)
  if (isZhiPu === false) {
    return false
  }
  await checkShareToken(to)
  const isNoLogin = await checkIsAuth(to)
  if (isNoLogin) {
    return isNoLogin
  }
  // 路由权限校验
  const authStore = useAuthStore()
  if (
    to.meta.accessKey &&
    Array.isArray(to.meta.accessKey) &&
    to.meta.accessKey.length &&
    authStore.token &&
    authStore.me
  ) {
    const isPermission = authStore.hasAuthCenterPermission(to.meta.accessKey)
    if (!isPermission) {
      // 无页面权限则重定向到公共资源-微课
      await router.replace({
        name: 'MicroVideoList',
      })
    }
  }
  return true
}

router.beforeEach(async (to) => {
  return createRouterGuard(to)
})
export default router
