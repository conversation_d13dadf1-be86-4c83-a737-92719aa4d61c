<template>
  <div class="class-problem-main">
    <div class="class-problem-content">
      <ProblemRender
        v-if="contentType"
        class="class-problem"
        :current-problem="currentProblem"
        :problemNo="currentIndex"
        :user-answer-show="detailsShow"
        :difficultyHeaderShow="false"
        :style="{ fontSize: problemFontSize + 'px !important' }"
      />
      <div v-if="detailsShow" class="mt-24px">
        <div class="rounded-12px bg-#F7F7F9 p-16px">
          <div
            v-if="currentProblem.type && currentProblem.type === 'combination'"
          >
            <div
              v-for="(subproblem, subproblemIndex) in (
                currentProblem as YcType.ProblemType
              ).subproblems"
              :key="subproblem.id"
              class="mb-16px border-b border-solid border-#C5C1D4 pb-16px"
            >
              <div
                class="mb-8px"
                :style="{ fontSize: problemFontSize + 'px !important' }"
              >
                第{{ subproblemIndex + 1 }}题:
              </div>
              <CorrectAnswer
                :style="{ fontSize: problemFontSize + 'px !important' }"
                :currentProblem="subproblem"
                mode="government"
              />
              <AnalysisExplain
                :style="{ fontSize: problemFontSize + 'px !important' }"
                :currentProblem="subproblem"
                mode="government"
              />
            </div>
          </div>
          <div v-else>
            <CorrectAnswer
              :style="{ fontSize: problemFontSize + 'px !important' }"
              :currentProblem="currentProblem"
              mode="government"
            />
            <AnalysisExplain
              :style="{ fontSize: problemFontSize + 'px !important' }"
              :currentProblem="currentProblem"
              mode="government"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-change-bar">
      <slot name="front-bar" />
      <button class="change-detail-btn" @click="handleDetailsClick">
        {{ detailsShow ? '收起详情' : '展开详情' }}
      </button>
      <div class="change-slider-box">
        <div class="change-text change-text-long">
          图片:{{ problemImgScale }}%
        </div>
        <button
          class="left-btn"
          :disabled="problemImgScale <= 10"
          @click="imgScaleChange(false)"
        >
          <MinusIcon />
        </button>
        <n-slider
          v-model:value="problemImgScale"
          class="problem-slider"
          :min="10"
          :max="100"
          :step="10"
          :keyboard="false"
          :marks="undefined"
        />
        <button
          class="right-btn"
          :disabled="problemImgScale >= 100"
          @click="imgScaleChange(true)"
        >
          <AddIcon />
        </button>
      </div>
      <div class="change-slider-box">
        <div class="change-text">字号:{{ problemFontSize }}</div>
        <button
          class="left-btn"
          :disabled="problemFontSize <= 14"
          @click="fontSizeChange(false)"
        >
          <MinusIcon />
        </button>
        <n-slider
          v-model:value="problemFontSize"
          class="problem-slider"
          :min="14"
          :max="50"
          :step="2"
          :keyboard="false"
          :marks="undefined"
        />
        <button
          class="right-btn"
          :disabled="problemFontSize >= 50"
          @click="fontSizeChange(true)"
        >
          <AddIcon />
        </button>
      </div>
      <slot name="change-bar" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  ProblemRender,
  CorrectAnswer,
  AnalysisExplain,
} from '@guanghe-pub/onion-problem-render'
import MinusIcon from '~icons/yc/minus'
import AddIcon from '~icons/yc/add'

const props = defineProps<{
  problem?: YcType.ProblemType
  problems?: YcType.ProblemType[]
  currentIndex?: number // 从1开始
}>()

const currentProblem = computed(() => {
  let temp: YcType.ProblemType | undefined
  if (props.problems) {
    temp = props.problems[props.currentIndex ? props.currentIndex - 1 : 0]
  } else {
    temp = (props.problem as YcType.ProblemType) || {}
  }
  if (!temp.answer || !temp.answer.length) {
    const answer = []
    if (temp.blanks && temp.blanks.length) {
      for (let item of temp.blanks) {
        answer.push(item)
      }
    } else if (temp.extendedBlanks && temp.extendedBlanks.length) {
      for (let item of temp.extendedBlanks) {
        answer.push(item[0])
      }
    }
    return {
      ...temp,
      answer: answer,
    }
  }
  return temp
})
const contentType = ref<boolean>(true)
const detailsShow = ref<boolean>(false)
const problemImgScale = ref<number>(40)
const problemFontSize = ref<number>(36)

watch(
  () => problemImgScale.value,
  (val) => {
    const imgElements = document.querySelectorAll('.class-problem-content img')
    imgElements.forEach((img: any) => {
      img.style.width = val + '%'
      img.style.maxWidth = val + '%'
      img.style.height = 'auto'
      img.style.maxHeight = 'none'
    })
  },
  {
    immediate: true,
  },
)

onMounted(() => {
  initImg()
})

const initImg = () => {
  setTimeout(() => {
    const imgElements = document.querySelectorAll('.class-problem-content img')
    imgElements.forEach((img: any) => {
      img.style.width = problemImgScale.value + '%'
      img.style.maxWidth = problemImgScale.value + '%'
      img.style.height = 'auto'
      img.style.maxHeight = 'none'
    })
  }, 150)
}

const handleDetailsClick = () => {
  contentType.value = false
  nextTick(() => {
    contentType.value = true
    detailsShow.value = !detailsShow.value
    initImg()
  })
}
const imgScaleChange = (isAdd: boolean) => {
  if (isAdd && problemImgScale.value < 100) {
    problemImgScale.value += 10
  }
  if (!isAdd && problemImgScale.value > 10) {
    problemImgScale.value -= 10
  }
}
const fontSizeChange = (isAdd: boolean) => {
  if (isAdd && problemFontSize.value < 50) {
    problemFontSize.value += 2
  }
  if (!isAdd && problemFontSize.value > 14) {
    problemFontSize.value -= 2
  }
}
</script>

<style lang="scss" scoped>
.class-problem-main {
  position: relative;
  width: 100%;
  max-width: 1312px;
  height: 100vh;
  margin: 0 auto;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .class-problem-content {
    min-height: calc(100vh - 100px);
    padding-bottom: 100px;

    &::-webkit-scrollbar {
      display: none;
    }

    .class-problem {
      padding-top: 20px;
      user-select: none;

      ::v-deep(.onion-problem-render__choice) {
        margin-top: 0;
      }

      ::v-deep(.onion-problem-render__blank) {
        margin-top: 0;
      }

      ::v-deep(.onion-problem-render__combination) {
        margin-top: 0;
      }

      ::v-deep(.onion-problem-render__option) {
        display: flex;
        flex-wrap: wrap;
        font-weight: bold;
      }

      ::v-deep(.onion-problem-render__option--item) {
        width: 50%;
        border: none !important;
      }

      /* stylelint-disable selector-class-pattern */
      ::v-deep(.onion-problem-render__examBox-show) {
        display: none;
      }

      ::v-deep(
          .onion-problem-render__examBox-show + .onion-problem-render__option
        ) {
        display: none;
      }

      ::v-deep(.onion-problem-render__judgment-right::after) {
        display: none;
      }

      ::v-deep(.onion-problem-render__option--item-success::after) {
        display: none;
      }

      ::v-deep(.onion-problem-render__option--item-success) {
        color: #4ecc5e;
      }

      ::v-deep(.onion-problem-render__judgment-right) {
        color: #4ecc5e;
      }
    }
  }

  .bottom-change-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 96px;
    background: #ffffff;
    box-shadow: inset 0px 1px 0px 0px #dcd8e7;

    .change-detail-btn {
      box-sizing: border-box;
      width: 130px;
      margin-left: 16px;
      font-size: 16px;
      font-weight: 600;
      line-height: 46px;
      color: #393548;
      text-align: center;
      cursor: pointer;
      user-select: none;
      background: transparent;
      border: 1px solid #c5c1d4;
      border-radius: 12px;
    }

    .change-slider-box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 20px;
      margin-left: 16px;
      border: 1px solid #c5c1d4;
      border-radius: 12px;

      .change-text {
        width: 60px;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #393548;
        text-align: left;
        user-select: none;

        &.change-text-long {
          width: 80px;
        }
      }

      .left-btn {
        padding: 4px;
        margin-right: 4px;
        cursor: pointer;
        background: transparent;
        border-radius: 8px;

        svg {
          width: 16px;
          height: 16px;
          color: #393548;
        }

        &:disabled {
          cursor: not-allowed;
          background: transparent;

          &:hover {
            background: transparent;

            svg {
              color: #393548;
            }
          }
        }

        &:hover {
          background: #f4f6ff;

          svg {
            color: #7290ff;
          }
        }
      }

      .problem-slider {
        width: 118px;
      }

      .right-btn {
        padding: 4px;
        margin-left: 4px;
        cursor: pointer;
        background: transparent;
        border-radius: 8px;

        &:disabled {
          cursor: not-allowed;
          background: transparent;

          &:hover {
            background: transparent;

            svg {
              color: #393548;
            }
          }
        }

        svg {
          width: 16px;
          height: 16px;
          color: #393548;
        }

        &:hover {
          background: #f4f6ff;

          svg {
            color: #7290ff;
          }
        }
      }
    }
  }
}
</style>
