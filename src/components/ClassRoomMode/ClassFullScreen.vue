<template>
  <teleport to="body">
    <div v-if="visible" ref="fullscreenRef" class="fullscreen-container">
      <ClassProblem
        :problem="problem"
        :problems="problems"
        :currentIndex="currentIndex"
      >
        <template #front-bar>
          <div
            v-if="problems?.length"
            class="flex items-center justify-center rounded-12px mr-133px bg-#E5EBFF h-48px w-128px text-16px leading-24px color-#393548"
          >
            {{ currentIndex }}/{{ problems?.length }}
          </div>
        </template>
        <template #change-bar>
          <OIWButton
            type="error"
            class="ml-16px"
            size="large"
            ghost
            @click="quitFullScreen"
            >退出</OIWButton
          >
          <OIWButton
            v-if="problems?.length"
            class="ml-16px"
            size="large"
            type="info"
            @click="continueProblem"
            >继续</OIWButton
          >
        </template>
      </ClassProblem>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import ClassProblem from './ClassProblem.vue'
import { useFullscreen } from '@vueuse/core'
import { OIWButton } from '@guanghe-pub/onion-ui-web'
const props = defineProps<{
  visible: boolean
  problem?: YcType.ProblemType
  problems?: YcType.ProblemType[]
  problemNo?: number // 从1开始
}>()
const emits = defineEmits<(e: 'update:visible', val: boolean) => void>()
const currentIndex = ref(props.problemNo || 0)
const fullscreenRef = ref<HTMLElement>()
const { enter, exit, isFullscreen } = useFullscreen(fullscreenRef)
watch(
  () => props.visible,
  (val) => {
    if (val) {
      nextTick(() => {
        enter()
        currentIndex.value = props.problemNo || 0
        fullscreenRef.value?.scrollTo(0, 0)
      })
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => isFullscreen.value,
  (val) => {
    if (!val) {
      emits('update:visible', false)
    }
  },
  {
    immediate: true,
  },
)
const quitFullScreen = () => {
  exit()
}
const continueProblem = () => {
  if (props.problems) {
    if (currentIndex.value < props.problems.length) {
      currentIndex.value++
    } else {
      exit()
    }
  }
}
</script>

<style scoped>
.fullscreen-container {
  position: relative;
  z-index: 100;
  padding: 0 20px;
  background: #ffffff;
}
</style>
