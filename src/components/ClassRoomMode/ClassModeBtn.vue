<template>
  <span
    class="problem-operation ml-8px inline-flex items-center cursor-pointer border border-solid border-#C5C1D4"
    @click="showClassMode"
    >课堂模式</span
  >
  <ClassFullScreen
    v-model:visible="visible"
    :problem="problem"
    :problems="problems"
    :problemNo="problemNo"
  />
</template>

<script setup lang="ts">
import { buryPoint } from '@/utils/buryPoint'
import ClassFullScreen from './ClassFullScreen.vue'
import { postUsedCountApi } from '@/pages/presentation/service.ts'

const props = defineProps<{
  problem: YcType.ProblemType
  problems?: YcType.ProblemType[]
  problemNo?: number // 从1开始
  from?: string
  extraData?: Record<string, any>
}>()
const visible = ref(false)
const showClassMode = () => {
  visible.value = true
  if (props.from === 'report') {
    buryPoint(
      'clickCourseDetailPageButton',
      {
        pageName: 'report',
        button: 'mode',
        moduleId: props.extraData?.measuringId || '',
        taskId: props.extraData?.measuringTaskId || '',
        contentId: props.extraData?.measuringTaskItemId || '',
      },
      'course',
    )
    buryPoint('enter_teachingCenter', { fromPageName: 'aiClass' }, 'course')
  } else {
    buryPoint('enter_teachingCenter', { fromPageName: 'other' }, 'course')
  }
  if (props.from === 'correctionNotebook') {
    buryPoint(
      'clickAIClassWrongBookPageButton',
      {
        button: 'mode',
      },
      'course',
    )
  }
  postUsedCountApi({ resourceId: props.problem.id, resourceType: 'problem' })
}
</script>
