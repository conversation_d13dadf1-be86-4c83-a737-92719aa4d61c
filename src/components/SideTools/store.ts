import { acceptHMRUpdate, defineStore } from 'pinia'
import { useRoute } from 'vue-router'
// 侧边栏工具 Store
const useSideToolsStore = defineStore('SideTools', () => {
  const route = useRoute()
  const preventClick = ref(false)
  const showProblemDrawer = ref(false)
  const isShowAIToolModel = ref(false)
  const isShowFeedEntry = computed(() => {
    return ['prepareCenterList'].includes((route.name as string) || '')
  })
  const isShowFeedModel = computed(() => {
    return ['prepareCenterList'].includes((route.name as string) || '')
  })
  const isShowBooksEntry = computed(() => {
    return [
      'PersonalProblem',
      'SchoolProblemList',
      'ProblemList',
      'PaperPreview',
      'MicroVideoPreview',
    ].includes((route.name as string) || '')
  })

  const isAIToolEntry = computed(() => {
    return [
      // 我的
      'prepareCenterList',
      'PersonalVideo',
      'PersonalProblem',
      'PersonalExamPaper',
      // 公共资源
      'CoursewareList',
      'MicroVideoList',
      'ProblemList',
      'PaperList',
      // 校本资源
      'SchoolMicroVideoList',
      'SchoolProblemList',
      'SchoolPaperList',
    ].includes((route.name as string) || '')
  })

  // 返回store暴露的属性和方法
  return {
    preventClick,
    showProblemDrawer,
    isShowFeedEntry,
    isShowFeedModel,
    isShowBooksEntry,
    isAIToolEntry,
    isShowAIToolModel,
  }
})

// 启用热更新
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useSideToolsStore, import.meta.hot))
}

export default useSideToolsStore
