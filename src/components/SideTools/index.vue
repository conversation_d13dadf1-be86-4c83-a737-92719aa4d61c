<template>
  <div ref="sideTools" class="side-tools-main" @mousedown="startDrag">
    <BooksEntry v-if="isShowBooksEntry" />
    <AIToolEntry v-if="isAIToolEntry" />
    <FeedbackEntry v-if="isShowFeedEntry" />
  </div>
  <FeedbackModal v-if="isShowFeedModel" />
  <BooksModal v-if="isShowBooksEntry" />
</template>

<script setup lang="ts">
import FeedbackModal from './components/FeedbackModal.vue'
import FeedbackEntry from './components/FeedbackEntry.vue'
import AIToolEntry from './components/AIToolEntry.vue'
import BooksEntry from './components/BooksEntry.vue'
import BooksModal from './components/BooksModal.vue'
import useSideTools from './store'

const store = useSideTools()
const {
  preventClick,
  isShowFeedEntry,
  isShowFeedModel,
  isAIToolEntry,
  isShowBooksEntry,
} = storeToRefs(store)

const sideTools = ref<HTMLElement | null>(null)
const isDragging = ref(false)
let offsetX = 0
let offsetY = 0

const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  preventClick.value = false

  const target = event.target as HTMLElement

  const rect = target.getBoundingClientRect()
  offsetX = event.clientX - rect.left
  offsetY = event.clientY - rect.top

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value || !sideTools.value) return

  preventClick.value = true

  const clientX = event.clientX
  const clientY = event.clientY

  const newLeft = clientX - offsetX
  const newTop = clientY - offsetY

  const maxX = window.innerWidth - sideTools.value.offsetWidth
  const maxY = window.innerHeight - sideTools.value.offsetHeight

  sideTools.value.style.left = `${Math.max(0, Math.min(newLeft, maxX))}px`
  sideTools.value.style.top = `${Math.max(0, Math.min(newTop, maxY))}px`
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}
</script>

<style lang="scss" scoped>
.side-tools-main {
  position: fixed;
  top: 48%;
  right: 16px;
  z-index: 1999;
  width: 64px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 2px 24px 8px rgba(73, 100, 138, 0.1);

  > div:not(:last-child) {
    position: relative;

    &::after {
      position: absolute;
      bottom: 0;
      left: 8px;
      width: calc(100% - 16px);
      height: 1px;
      content: '';
      background-color: #dcd8e7;
    }
  }
}
</style>
