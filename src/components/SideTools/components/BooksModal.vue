<template>
  <n-drawer v-model:show="showProblemDrawer" :width="728" placement="right">
    <n-drawer-content
      title="选题本"
      closable
      class="drawer-content relative pb-80px"
    >
      <OIWLoading :show="loading" width="200px" height="200px">
        <template v-if="booksIds.length">
          <div v-for="(book, number) of bookDetails" :key="number">
            <div class="text-16px color-#3D3D3D fw-600">
              {{ setNumber(number) }}、{{ book.examTypeValue }} |
              {{ book.problems.length }}题
            </div>
            <div class="divide-y-[1px] divide-[#DCD8E7]">
              <ProblemItem
                v-for="problem of book.problems"
                :key="problem.id"
                :problem="problem"
                :index="problem.index"
                :tagConfig="{
                  isHideBaseTags: true,
                }"
                isHideExplain
              >
                <template #insert>
                  <span
                    v-if="booksIds.includes(problem.id)"
                    class="problem-operation cursor-pointer ml-16px flex items-center color-#FA5A65"
                    @click="deleteBooks(problem.id)"
                  >
                    <DeteleIcon class="w-16px h-16px mr-4px" color="#FA5A65" />
                    移出
                  </span>
                </template>
              </ProblemItem>
            </div>
          </div>
          <div
            class="absolute w-680px h-64px flex items-center justify-between bottom-24px left-24px right-24px bg-#F5F7FE rounded-16px px-16px"
          >
            <div>共{{ booksIds.length }}题</div>
            <div class="flex items-center">
              <div
                class="w-104px h-40px lh-40px text-center text-14px color-#393548 rounded-12px border-1px border-#C5C1D4 mr-16px cursor-pointer"
                @click="deleteAllBooks"
              >
                清空题目
              </div>
              <div
                class="w-104px h-40px lh-40px text-center fw-600 text-14px color-#393548 rounded-12px bg-#FFD633 cursor-pointer"
                @click="create"
              >
                生成试卷
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <OIWStateBlock
            type="empty"
            title="暂无资源"
            style="margin-top: 204px"
          />
        </template>
      </OIWLoading>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import useBooksProblem from '@/store/booksProblem'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import { setNumber } from '@/utils/reg.ts'
import DeteleIcon from '~icons/yc/minus'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useCvsEnum } from '@/hooks/useCvs'
import {
  OIWLoading,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { buryPoint } from '@/utils/buryPoint'
import useSideTools from '@/components/SideTools/store'

const toolStore = useSideTools()
const { showProblemDrawer } = storeToRefs(toolStore)

const message = useOIWMessage()
const dialog = useDialog()
const router = useRouter()
const bookStore = useBooksProblem()
const { SubjectEnum, StageEnum } = useCvsEnum()
const { booksIds, bookDetails } = storeToRefs(bookStore)
const loading = ref(true)

watch(
  () => showProblemDrawer.value,
  (val) => {
    if (val) {
      clickEntry()
    }
  },
)

const clickEntry = async () => {
  loading.value = true
  await bookStore.getBookProblems()
  await bookStore.getBookProblemsDetail()
  loading.value = false
}

const deleteBooks = async (id: string) => {
  await bookStore.deleteBookProblems([id], true)
  message.success('已移出')
}

const deleteAllBooks = () => {
  dialog.info({
    title: '提示',
    content: '要清空题目篮吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      await bookStore.deleteBookProblems(booksIds.value, true)
      message.success('已清空题目篮')
    },
  })
}

const create = async () => {
  buryPoint('click_resourceCenter_exam_create', {}, 'course')
  const date = dayjs().format('MM月DD日')
  const stageId = bookDetails.value[0].problems[0].stageId
  const subjectId = bookDetails.value[0].problems[0].subjectId
  const name = `${date}${StageEnum.value[stageId]}${SubjectEnum.value[subjectId]}试卷`

  const userTestPaperId = await bookStore.createTestPaper(name)
  const href = router.resolve({
    name: 'CombinationPaperEdit',
    params: {
      id: userTestPaperId,
    },
    query: {
      fromPageName: '3',
    },
  }).href
  window.open(href, '_blank')
  showProblemDrawer.value = false
  loading.value = true
}
</script>

<style lang="scss" scoped>
::v-deep(
    .onion-problem-render__main--government .onion-problem-render__option--item
  ) {
  margin: 0;
}
</style>
