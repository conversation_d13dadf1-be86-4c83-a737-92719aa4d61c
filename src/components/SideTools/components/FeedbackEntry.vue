<template>
  <div class="feedback" @click="handleClick">提点建议</div>
</template>

<script setup lang="ts">
import useSideTools from '@/components/SideTools/store'

const toolStore = useSideTools()
const { preventClick } = storeToRefs(toolStore)

// 点击处理
const handleClick = () => {
  if (!preventClick.value) {
    window.open(
      'https://guanghe.feishu.cn/share/base/form/shrcnHS3wuszRfGRG34i8H1LcJd',
    )
  }
}
</script>

<style scoped lang="scss">
.feedback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 64px;
  padding: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #57526c;
  cursor: pointer;

  /* n1 纯白 */
  background: #ffffff;
  border-radius: 12px;
  opacity: 1;

  &:hover {
    color: #5e80ff;
    background: #e5ebff;

    &::before {
      content: '';
      background: url('https://fp.yangcong345.com/onion-extension/组 3514 (1)-cc7e9391f1673f0e46b109318ea7625e.png')
        no-repeat;
      background-size: contain;
    }
  }

  &:active {
    color: #5e80ff;
    background: #d4dfff;
  }

  &::before {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-bottom: 6px;
    content: '';
    background: url('https://fp.yangcong345.com/onion-extension/组 3514-e033185d72ef25de442822e4286393bd.png')
      no-repeat;
    background-size: contain;
  }
}
</style>
