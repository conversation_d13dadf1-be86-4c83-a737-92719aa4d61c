<template>
  <div
    class="ai-tool-entry"
    @mouseover="showElements"
    @mouseleave="handleMouseLeave"
  >
    <div class="ai-tool-content">
      <div class="ai-icon" />
      <div
        class="text-12px leading-12px font-600 color-#57526C select-none mt-4px"
      >
        工具箱
      </div>
    </div>
    <AIToolModal />
  </div>
</template>

<script setup lang="ts">
import AIToolModal from './AIToolModal.vue'
import useSideTools from '@/components/SideTools/store'

const toolStore = useSideTools()
const { isShowAIToolModel } = storeToRefs(toolStore)

const showElements = () => {
  isShowAIToolModel.value = true
}

const hideElements = () => {
  isShowAIToolModel.value = false
}

const handleMouseLeave = (event: MouseEvent) => {
  // 检查鼠标是否移动到AIToolModal上
  const relatedTarget = event.relatedTarget as HTMLElement
  if (!relatedTarget?.closest('.ai-tools-modal')) {
    hideElements()
  }
}
</script>

<style lang="scss" scoped>
.ai-tool-entry {
  position: relative;
}

.ai-tool-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 64px;
  padding: 8px;
  border-radius: 12px;

  &:hover {
    color: #5e80ff;
    background: #e5ebff;
  }

  .ai-icon {
    width: 32px;
    height: 32px;
    user-select: none;
    background: url('https://fp.yangcong345.com/onion-extension/ai_icon-5789c176edba741b244e89ad538cdf70.png');
    background-size: 100%;
  }
}
</style>
