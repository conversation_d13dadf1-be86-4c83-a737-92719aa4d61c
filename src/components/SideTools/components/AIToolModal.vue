<template>
  <transition name="slide-fade">
    <div v-if="!preventClick && isShowAIToolModel" class="ai-tools-modal">
      <div class="ai-tools-content">
        <div class="ai-generated-courseware" @click="handleClick(1)">
          <AIPencilIcon class="w-16px h-16px" />
          <span>AI生成课件</span>
        </div>
        <div class="ai-paper-generation" @click="handleClick(2)">
          <AIStarIcon class="w-16px h-16px" />
          <span>AI组卷</span>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import AIStarIcon from '~icons/yc/ai-star'
import AIPencilIcon from '~icons/yc/ai-pencil'
import { useRouter } from 'vue-router'
import useSideTools from '@/components/SideTools/store'

const toolStore = useSideTools()
const { preventClick, isShowAIToolModel } = storeToRefs(toolStore)
const router = useRouter()

const handleClick = (type: number) => {
  let fullPath
  if (type === 1) {
    fullPath = router.resolve({
      name: 'createAIPPT',
    })
  }
  if (type === 2) {
    fullPath = router.resolve({
      name: 'AICombinePaper',
    })
  }
  isShowAIToolModel.value = false
  window.open((fullPath as any).href, '_blank')
}
</script>

<style lang="scss" scoped>
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.2s ease-in;
}

.slide-fade-enter-from {
  opacity: 0;
}

.slide-fade-leave-to {
  opacity: 0;
}

.ai-tools-modal {
  position: absolute;
  top: 50%;
  left: -169px;
  width: 169px;
  height: 116px;
  background: transparent;
  transform: translateY(-50%);

  .ai-tools-content {
    position: relative;
    width: 149px;
    padding: 12px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 6px 32px -4px rgba(97, 113, 136, 0.2);

    &::after {
      position: absolute;
      top: 50%;
      right: -12px;
      width: 0;
      height: 0;
      content: '';
      border-top: 11px solid transparent;
      border-bottom: 11px solid transparent;
      border-left: 12px solid #ffffff;
      transform: translateY(-50%);
    }

    .ai-generated-courseware {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      width: 125px;
      height: 40px;
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      color: #ffffff;
      cursor: pointer;
      user-select: none;
      background: linear-gradient(
        138deg,
        #9582f4 0%,
        #6482ff 68%,
        #68c0f9 107%
      );
      border-radius: 12px;
    }

    .ai-paper-generation {
      position: relative;
      z-index: 1;
      display: flex;
      gap: 4px;
      align-items: center;
      justify-content: center;
      width: 125px;
      height: 40px;
      margin-top: 12px;
      overflow: hidden;
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      color: #ffffff;
      cursor: pointer;
      user-select: none;
      background: #7b66ff;
      border-radius: 12px;
      box-shadow: 0px 2px 24px 8px rgba(73, 100, 138, 0.1);

      &::before {
        position: absolute;
        top: 27px;
        left: 25px;
        z-index: -1;
        width: 80px;
        height: 31px;
        content: '';
        background: #bc9bff;
        filter: blur(20px);
        border-radius: 100px;
      }
    }
  }
}
</style>
