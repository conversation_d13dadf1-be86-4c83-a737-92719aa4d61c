<template>
  <n-modal v-model:show="show" preset="card" closable style="width: 660px">
    <div class="flex flex-col items-center justify-center">
      <img
        class="w-200px h-158px"
        src="https://fp.yangcong345.com/onion-extension/组 <EMAIL>"
        alt=""
      />
      <div class="text-20px color-#57526C font-bold mt-5">
        请给备授课平台提点建议吧~
      </div>
      <OIWButton class="mt-40px mb-20px" round @click="toQuestionnaire"
        >提点建议</OIWButton
      >
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { OIWButton } from '@guanghe-pub/onion-ui-web'
const show = ref(false)
onMounted(() => {
  if (!window.localStorage.getItem('feedback-modal-hidden')) {
    show.value = true
    window.localStorage.setItem('feedback-modal-hidden', 'true')
  }
})
const toQuestionnaire = () => {
  window.open(
    'https://guanghe.feishu.cn/share/base/form/shrcnHS3wuszRfGRG34i8H1LcJd',
    '_blank',
  )
  show.value = false
}
</script>
