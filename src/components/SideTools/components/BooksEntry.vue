<template>
  <div class="books-problem-entry" @click="clickEntry">
    <span class="text-24px lh-24px fw-600">{{ bookProblemsNum }}</span>
    <span class="mt-2px">选题本</span>
  </div>
</template>

<script setup lang="ts">
import useBooksProblem from '@/store/booksProblem'
import useSideTools from '@/components/SideTools/store'

const toolStore = useSideTools()
const { showProblemDrawer, preventClick } = storeToRefs(toolStore)
const bookStore = useBooksProblem()
const { bookProblemsNum } = storeToRefs(bookStore)

const clickEntry = () => {
  if (!preventClick.value) {
    showProblemDrawer.value = true
  }
}
</script>

<style scoped lang="scss">
.books-problem-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  font-size: 12px;
  font-weight: 600;
  color: #57526c;
  cursor: pointer;
  background: #ffffff;
  border-radius: 12px;

  &:hover {
    background: #e5ebff;

    span {
      color: #5e80ff;
    }
  }

  &:active {
    span {
      color: #446cff;
    }
  }
}
</style>
