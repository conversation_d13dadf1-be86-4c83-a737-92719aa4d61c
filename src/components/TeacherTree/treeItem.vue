<template>
  <div
    v-for="(node, index) in flattenedNodes"
    :key="node.id"
    :class="{
      'tree-node': true,
      'tree-node-first': !node.level,
      'tree-node-active': isNodeOpen(index),
    }"
    :style="{ marginLeft: node.level ? 16 + 'px' : '0px' }"
  >
    <div
      :class="{
        'node-label': true,
        'node-label-first': !node.level,
        'node-label-active': isNodeOpen(index),
        'node-label-no-child': !hasChildren(node),
      }"
      @click="toggleNode(index, node)"
    >
      <Triangle
        v-if="hasChildren(node) && node.level"
        theme="filled"
        size="8"
        :fill="isNodeOpen(index) ? '#000' : '#C5C1D4'"
        class="expand-icon-left"
      />
      {{ node.name }}
      <Triangle
        v-if="hasChildren(node) && !node.level"
        theme="filled"
        size="8"
        fill="#000"
        class="expand-icon-right"
      />
    </div>
    <transition name="fade">
      <div v-if="isNodeOpen(index)" class="sub-tree">
        <TeacherTreeItem :flattenedNodes="node.children" />
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue'
import { Triangle } from '@icon-park/vue-next'
import TeacherTreeItem from '@/components/TeacherTree/treeItem.vue'
interface TreeType {
  id: string
  name: string
  children: TreeType[] | null
  [key: string]: any
}
defineProps<{
  flattenedNodes: TreeType[] | null
}>()
interface ParentMethods {
  isNodeOpen: (index: number, expandedNodes: any) => boolean
  toggleNode: (data: {
    index: number
    node: TreeType
    expandedNodes: any
    lastExpandedNodeIndex: any
  }) => void
}
const parentMethod = inject<ParentMethods>('teacherTreeList')
const expandedNodes = ref(new Set<number>())
let lastExpandedNodeIndex = ref<number | null>(null)
const toggleNode = (index: number, node: any) => {
  parentMethod?.toggleNode({
    index,
    node,
    expandedNodes: expandedNodes.value,
    lastExpandedNodeIndex,
  })
}

const isNodeOpen = (index: number) => {
  return parentMethod?.isNodeOpen(index, expandedNodes.value)
}

const hasChildren = (node: TreeType) => {
  return node.children && node.children.length > 0
}
</script>

<style lang="scss" scoped>
.tree-node {
  position: relative;

  &.tree-node-active {
    margin-bottom: 5px;

    .sub-tree {
      position: relative;

      &::after {
        position: absolute;
        top: 5px;
        left: 5px;
        width: 1px;
        height: calc(100% - 10px);
        content: '';
        background: #dcd8e7;
      }
    }

    &.tree-node-first {
      padding-bottom: 10px;

      & > .sub-tree {
        &::after {
          display: none;
        }
      }

      &::before {
        position: absolute;
        bottom: 0;
        left: 16px;
        width: calc(100% - 32px);
        height: 1px;
        content: '';
        background: #dcd8e7;
      }
    }
  }

  .node-label {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background: #f4f6ff;
      border-radius: 8px;
    }

    &.node-label-active {
      .expand-icon-right {
        transform: translateY(-50%) rotate(180deg);
      }

      .expand-icon-left {
        transform: translateY(-50%) rotate(180deg);
      }

      &.node-label-first {
        background: #f4f6ff;
        border-radius: 8px;
      }

      &.node-label-no-child {
        color: #5e80ff;
        background: #f4f6ff;
        border-radius: 8px;
      }
    }

    .expand-icon-left {
      position: absolute;
      top: 50%;
      left: 3px;
      cursor: pointer;
      transition: transform 0.3s;
      transform: translateY(-50%) rotate(90deg);
    }

    .expand-icon-right {
      position: absolute;
      top: 50%;
      right: 16px;
      cursor: pointer;
      transition: transform 0.3s;
      transform: translateY(-50%);
    }
  }
}
</style>
