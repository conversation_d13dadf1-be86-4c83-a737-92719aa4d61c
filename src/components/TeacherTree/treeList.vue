<template>
  <div v-if="data && data.length" class="tree">
    <TeacherTreeItem :flattenedNodes="flattenedNodes" />
  </div>
  <div v-else class="empty-list">
    <img
      src="https://fp.yangcong345.com/onion-extension/111-d4422038b60b352ad5ae5eb8920590ad.png"
      alt=""
      class="empty-img"
    />
    <div class="empty-text">{{ empty }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed, provide } from 'vue'
import TeacherTreeItem from '@/components/TeacherTree/treeItem.vue'
import type { TreeType } from './types'

const props = withDefaults(
  defineProps<{
    data: TreeType[] | null
    mode?: 'single' | 'multiple'
    empty?: string
  }>(),
  {
    data: null,
    mode: 'single',
    empty: '暂无内容',
  },
)
const emits = defineEmits<{
  (e: 'change', val: TreeType): void
  (e: 'expand', val: TreeType): void
}>()

const flattenedNodes = computed(() => flattenTree(props.data?.slice() || []))

const flattenTree = (tree: any[], level = 0): any[] => {
  return tree.reduce((acc, node) => {
    const newNode = { ...node, level }
    newNode.children = []
    if (node.children && node.children.length > 0) {
      newNode.children.push(...flattenTree(node.children, level + 1))
    } else {
      newNode.children = null
    }
    acc.push(newNode)
    return acc
  }, [] as any[])
}

const toggleNode = ({
  index,
  node,
  expandedNodes,
  lastExpandedNodeIndex,
}: {
  index: number
  node: TreeType
  expandedNodes: any
  lastExpandedNodeIndex: any
}) => {
  if (props.mode === 'single') {
    if (lastExpandedNodeIndex.value !== null) {
      expandedNodes.delete(lastExpandedNodeIndex.value)
    }
    lastExpandedNodeIndex.value = index
    expandedNodes.add(index)
  } else {
    if (expandedNodes.has(index)) {
      expandedNodes.delete(index)
    } else {
      expandedNodes.add(index)
    }
  }

  if (!node.children) {
    emits('change', node)
  } else {
    emits('expand', node)
  }
}

const isNodeOpen = (index: number, expandedNodes: any) => {
  return expandedNodes.has(index)
}
provide('teacherTreeList', {
  toggleNode,
  isNodeOpen,
})
</script>

<style lang="scss" scoped>
.tree {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #393548;
}

.empty-list {
  padding: 40px 0;
  text-align: center;

  .empty-img {
    display: inline-block;
    width: 80px;
    height: 80px;
  }

  .empty-text {
    margin-top: 23px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #dcd8e7;
  }
}
</style>
