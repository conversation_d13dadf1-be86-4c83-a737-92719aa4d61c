<template>
  <n-cascader
    class="oiw-cascader"
    :value="csvValue"
    placeholder="请选择学科"
    expand-trigger="click"
    :options="options"
    check-strategy="child"
    :render-label="renderLabel"
    show-path
    filterable
    value-field="treeId"
    size="large"
    label-field="name"
    @update:value="onChange"
  />
</template>

<script setup lang="ts">
import type { CascaderOption } from 'naive-ui'
import useCvsStore from '@/store/csv'
import { useAuth } from '@/hooks/useAuth'
import { useCurrentBook, useCvsTree } from '@/hooks/useCvs'
import { cloneDeep } from 'lodash-es'
import { NPopover } from 'naive-ui'

const props = defineProps<{
  defaultCsv?: {
    publisherId: YcType.CsvId
    stageId: YcType.CsvId
    subjectId: YcType.CsvId
    semesterId: YcType.CsvId
  }
  publisherSemesterCanAll?: boolean // true 代表： 教材版本,年级 包含“不限”; 不限的选项返回ID为0
  notUpdateCurrentTextBook?: boolean // true代表：不更新缓存
}>()

const emits = defineEmits<
  (
    e: 'change',
    val: {
      publisherId: YcType.CsvId
      stageId: YcType.CsvId
      subjectId: YcType.CsvId
      semesterId: YcType.CsvId
    },
    isInit: boolean,
  ) => void
>()

const renderLabel = (option: any) => {
  return option && option.name.length > 8
    ? h(
        NPopover,
        {},
        {
          trigger: () => h('span', {}, `${option.name}`),
          default: () => h('span', {}, `${option.name}`),
        },
      )
    : h('span', {}, `${option.name}`)
}
const csvTree = useCvsTree()
const { me } = useAuth()

const options = computed(() => {
  const cloneTree = cloneDeep(csvTree.value)
  if (!props.publisherSemesterCanAll) {
    ;(cloneTree as unknown as CascaderOption[]).forEach((a) => {
      a.treeId = `${a.id}`
      a.children?.forEach((b) => {
        b.treeId = `${a.treeId}-${b.id}`
        b.children?.forEach((c) => {
          c.treeId = `${b.treeId}-${c.id}`
          c.children?.forEach((d) => {
            d.treeId = `${c.treeId}-${d.id}`
          })
        })
      })
    })
  } else {
    ;(cloneTree as unknown as CascaderOption[]).forEach((a) => {
      a.treeId = `${a.id}`
      a.children?.forEach((b) => {
        b.treeId = `${a.treeId}-${b.id}`
        b.children?.unshift({
          id: 0,
          name: '不限',
          children: [],
        })
        b.children?.forEach((c) => {
          c.children?.unshift({
            id: 0,
            name: '不限',
          })
          c.treeId = `${b.treeId}-${c.id}`
          c.children?.forEach((d) => {
            d.treeId = `${c.treeId}-${d.id}`
          })
        })
      })
    })
  }
  return cloneTree as unknown as CascaderOption[]
})

const csvStore = useCvsStore()

const csvValue = ref<undefined | string>()

useCurrentBook(({ stageId, subjectId, publisherId, semesterId }) => {
  // 背景：只有浙江的教师才有科学学科（subjectId = 19）
  //      教师如果在其他地方选择了科学，pc会有展示问题，因为csv接口里没有对应科学数据，但currentTextBook接口有科学记录，所以这里做了特殊处理
  // 代码hack：如果用户非浙江省，【且】currentTextBook又有科学学科（subjectId = 19），则默认选择1-2-1-13

  let stage = stageId
  let subject = subjectId
  let publisher = publisherId
  let semester = semesterId
  if (subjectId === 19 && me.value?.school.provinceRegionCode !== '330000') {
    stage = 2
    subject = 1
    publisher = 1
    semester = 13
  }
  // 版本，年级不含"不限"时，需要有值
  if (
    props.defaultCsv?.publisherId &&
    props.defaultCsv?.stageId &&
    props.defaultCsv?.subjectId &&
    props.defaultCsv?.semesterId &&
    !props.publisherSemesterCanAll
  ) {
    stage = props.defaultCsv.stageId
    subject = props.defaultCsv.subjectId
    publisher = props.defaultCsv.publisherId
    semester = props.defaultCsv.semesterId
  }
  // 版本，年级包含"不限"时
  if (
    props.publisherSemesterCanAll &&
    props.defaultCsv?.stageId &&
    props.defaultCsv?.subjectId
  ) {
    stage = props.defaultCsv.stageId
    subject = props.defaultCsv.subjectId
    publisher = props.defaultCsv.publisherId
    semester = props.defaultCsv.semesterId
  }
  csvValue.value = `${stage}-${subject}-${publisher}-${semester}`
  emits(
    'change',
    {
      publisherId: publisher,
      stageId: stage,
      subjectId: subject,
      semesterId: semester,
    },
    true,
  )
})

const selectCsv = computed(() => {
  if (csvValue.value) {
    const values = csvValue.value.split('-')
    return {
      publisherId: values[2],
      stageId: values[0],
      subjectId: values[1],
      semesterId: values[3],
    }
  }
  return undefined
})
const onChange = (val: string) => {
  csvValue.value = val
  if (selectCsv.value) {
    emits(
      'change',
      {
        publisherId: selectCsv.value.publisherId,
        stageId: selectCsv.value.stageId,
        subjectId: selectCsv.value.subjectId,
        semesterId: selectCsv.value.semesterId,
      },
      false,
    )
    if (!props.notUpdateCurrentTextBook) {
      csvStore.updateCurrentTextBook({
        publisherId: selectCsv.value.publisherId,
        stageId: selectCsv.value.stageId,
        subjectId: selectCsv.value.subjectId,
        semesterId: selectCsv.value.semesterId,
      })
    }
  }
}
</script>

<style lang="scss">
.oiw-cascader {
  .n-base-selection {
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 12px;
  }
}
</style>
