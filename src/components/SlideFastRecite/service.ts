import request from '@/utils/request'

export interface GetRecitationResourceListReq {
  /**
   * 学段id.
   */
  stageId?: string
  /**
   * 学科id.
   */
  subjectId?: string
  [k: string]: unknown
}

export interface KnowledgePoints {
  id?: string
  name?: string
  knowledgePointIds?: string[]
  [k: string]: unknown
}
export interface ModulesList {
  id?: string
  name?: string
  topics?: KnowledgePoints[]
  [k: string]: unknown
}

export interface GetRecitationResourceListRes {
  modules?: ModulesList[]
  [k: string]: unknown
}

/**
 * @description 获取快背相关的资源列表
 * https://yapi.yc345.tv/project/2740/interface/api/125898
 * <AUTHOR>
 * @date 2025-04-29
 * @export
 * @param {GetRecitationResourceListReq} params
 * @returns {Promise<GetRecitationResourceListRes>}
 */
export const getRecitationResourceListApi = (
  params: GetRecitationResourceListReq,
): Promise<GetRecitationResourceListRes> => {
  return request.get<GetRecitationResourceListRes>(
    `/teacher-ai-class/teacher/recitation-resource-list`,
    {
      params,
    },
  )
}

export interface PostRecitationKnowledgePointsReq {
  ids?: string[]
  [k: string]: unknown
}

export type NoName4 = string[]
export type NoName5 = number
export type StringExplainVideo7 = {
  id?: string
  difficult?: number
  [k: string]: unknown
}[]
export type TopicId = string
export type TopicList = {
  id?: string
  difficult?: 'basic' | 'advance'
  content?: string
  explain?: string
  explainImages?: string[]
  problems?: StringExplainVideo7
  topicId?: TopicId
  [k: string]: unknown
}[]

export interface PostRecitationKnowledgePointsRes {
  list?: TopicList
  [k: string]: unknown
}

/**
 * @description 获取专题下知识点信息
 * https://yapi.yc345.tv/project/2740/interface/api/125899
 * <AUTHOR>
 * @date 2025-04-29
 * @export
 * @param {PostRecitationKnowledgePointsReq} data
 * @returns {Promise<PostRecitationKnowledgePointsRes>}
 */
export const postRecitationKnowledgePointsApi = (
  data: PostRecitationKnowledgePointsReq,
): Promise<PostRecitationKnowledgePointsRes> => {
  return request.post<PostRecitationKnowledgePointsRes>(
    `/teacher-ai-class/teacher/recitation/knowledge-points`,
    data,
  )
}
