<template>
  <div class="mt-14px">
    <div class="text-24px font-600 color-#393548 leading-28px">
      {{ knowledgePoints?.name || '' }}
    </div>
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else-if="topicList && topicList.length">
      <div
        v-for="item in topicList"
        :key="item.id"
        class="py-24px border-b-1px border-#DCD8E7"
      >
        <div class="flex">
          <span
            class="mr-8px border-1px border-#C5C1D4 px-5px py-3px text-12px rounded-4px leading-12px shrink-0 h-20px"
            >{{ difficultyText(item.difficult) }}</span
          ><span class="text-16px font-500 color-#393548 leading-18px">{{
            item.content
          }}</span>
        </div>
        <ParseText :text="item.explain" />
        <div
          v-if="item.explainImages && item.explainImages.length"
          class="grid grid-cols-3 gap-23px mt-16px"
        >
          <div
            v-for="img in item.explainImages"
            :key="img"
            class="border-1px border-#393548 w-150px h-150px"
          >
            <img
              class="inline-block w-148px h-148px object-cover"
              :src="img"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
    <OIWStateBlock
      v-else
      class="mt-120px text-center"
      type="empty"
      title="暂无知识点"
    />
  </div>
</template>

<script setup lang="ts">
import { OIWLoading, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import useSlideFastReciteStore from './store'
import ParseText from '../ParseText.vue'

const store = useSlideFastReciteStore()
const { loading, knowledgePoints, topicList } = storeToRefs(store)

const difficultyText = (difficult: 'basic' | 'advance' | undefined) => {
  switch (difficult) {
    case 'basic':
      return '基础'
    case 'advance':
      return '进阶'
    default:
      return '其他'
  }
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
}
</style>
