import { acceptHMRUpdate, defineStore } from 'pinia'
import {
  getRecitationResourceListApi,
  postRecitationKnowledgePointsApi,
} from './service'
import type { ModulesList, KnowledgePoints, TopicList } from './service'

// 侧边栏资源管理 Store
const useSlideFastReciteStore = defineStore('SlideFastRecite', () => {
  const getLocalFastRecite = (item: 'stageId' | 'subjectId' | 'moduleId') => {
    const localCsv = JSON.parse(
      window.localStorage.getItem('csvFastRecite') || '{}',
    )
    if (localCsv && localCsv[item]) {
      return localCsv[item]
    }
    return ''
  }
  const setLocalFastRecite = (type: 'csv' | 'moduleId' = 'moduleId') => {
    if (
      type === 'csv' &&
      formModel.value.stageId &&
      formModel.value.subjectId
    ) {
      window.localStorage.setItem(
        'csvFastRecite',
        JSON.stringify({
          stageId: formModel.value.stageId,
          subjectId: formModel.value.subjectId,
          moduleId: getLocalFastRecite('moduleId') || '',
        }),
      )
    }
    if (type === 'moduleId' && moduleId.value) {
      window.localStorage.setItem(
        'csvFastRecite',
        JSON.stringify({
          stageId: formModel.value.stageId,
          subjectId: formModel.value.subjectId,
          moduleId: moduleId.value,
        }),
      )
    }
  }
  const loading = ref<boolean>(false)
  const pageType = ref<'list' | 'detail'>('list')
  const moduleInfo = ref<ModulesList[]>([])
  const moduleId = ref<string>('')
  const moduleOptions = ref<{ label: string; value: string }[]>([])
  const moduleTopics = computed(() => {
    return moduleInfo.value.find((item) => item.id === moduleId.value)?.topics
  })
  const pointIds = computed(() => {
    return knowledgePoints.value?.knowledgePointIds || []
  })

  const knowledgePoints = ref<KnowledgePoints | undefined>()

  const topicList = ref<TopicList>([])

  const formModel = ref<{
    subjectId?: number
    stageId?: number
  }>({
    subjectId: 0,
    stageId: 0,
  })

  watch(
    () => pointIds.value,
    (val) => {
      if (val.length) {
        getTopics()
      } else {
        topicList.value = []
      }
    },
    {
      immediate: true,
    },
  )

  watch(
    () => moduleId.value,
    () => {
      setLocalFastRecite('moduleId')
    },
    {
      immediate: true,
    },
  )

  const onCsvSelectChange = (stageId: string, subjectId: string) => {
    formModel.value.subjectId = Number(subjectId)
    formModel.value.stageId = Number(stageId)
    setLocalFastRecite('csv')
  }

  const onChangePageType = (type: 'list' | 'detail') => {
    pageType.value = type
  }

  const getModelList = async () => {
    try {
      loading.value = true
      const res = await getRecitationResourceListApi({
        stageId: `${formModel.value.stageId}`,
        subjectId: `${formModel.value.subjectId}`,
      })
      moduleOptions.value =
        res.modules?.map((item) => ({
          label: item.name || '',
          value: item.id || '',
        })) || []
      moduleInfo.value = res.modules || []
      if (
        moduleOptions.value.find(
          (item) => item.value === getLocalFastRecite('moduleId'),
        )
      ) {
        moduleId.value = getLocalFastRecite('moduleId')
      } else {
        moduleId.value = moduleOptions.value[0].value
      }
    } finally {
      loading.value = false
    }
  }

  const getTopics = async () => {
    try {
      loading.value = true
      const res = await postRecitationKnowledgePointsApi({
        ids: pointIds.value || [],
      })
      topicList.value = res.list || []
    } finally {
      loading.value = false
    }
  }

  const setPreviewTopic = (item: any) => {
    knowledgePoints.value = item
  }

  // 返回store暴露的属性和方法
  return {
    loading,
    pageType,
    onChangePageType,
    moduleId,
    moduleOptions,
    getModelList,
    moduleTopics,
    onCsvSelectChange,
    formModel,
    setPreviewTopic,
    knowledgePoints,
    pointIds,
    topicList,
    getTopics,
    getLocalFastRecite,
  }
})

// 启用热更新
if (import.meta.hot) {
  import.meta.hot.accept(
    acceptHMRUpdate(useSlideFastReciteStore, import.meta.hot),
  )
}

export default useSlideFastReciteStore
