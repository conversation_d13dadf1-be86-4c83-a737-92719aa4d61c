<template>
  <n-drawer
    v-model:show="drawerShow"
    :show-mask="false"
    width="544px"
    placement="right"
    :block-scroll="false"
  >
    <div class="p-24px">
      <div class="flex justify-between">
        <div
          v-if="pageType === 'list'"
          class="text-24px font-600 leading-28px color-#393548"
        >
          快背环节
        </div>
        <div
          v-else
          class="font-14px font-600 flex items-center cursor-pointer color-#9792AC"
          @click="onBackList"
        >
          <BackIcon
            class="w-14px cursor-pointer h-14px font-600 font-size-18px color-#9792AC"
          />
          返回
        </div>
        <CloseIcon class="cursor-pointer ml-auto" @click="onClose" />
      </div>
      <div class="w-100%">
        <FastReciteList v-if="pageType === 'list'" @insert="onInsert" />
        <FastReciteInfo v-if="pageType === 'detail'" />
      </div>
    </div>
  </n-drawer>
</template>

<script setup lang="ts">
import type { InsertParams } from '@/components/SideResource/types'
import BackIcon from '~icons/yc/back'
import CloseIcon from '~icons/yc/close-round-gray'
import FastReciteList from './FastReciteList.vue'
import FastReciteInfo from './FastReciteInfo.vue'
import useSlideFastReciteStore from './store'

const props = defineProps<{
  show: boolean
}>()
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'add', val: InsertParams): void
}>()
const drawerShow = computed({
  get() {
    return props.show
  },
  set(val) {
    emits('update:show', val)
  },
})

const store = useSlideFastReciteStore()
const { pageType } = storeToRefs(store)

const onBackList = () => {
  store.onChangePageType('list')
}

const onClose = () => {
  drawerShow.value = false
}

const onInsert = (data: InsertParams) => {
  emits('add', data)
}
</script>
