<template>
  <div class="mt-24px">
    <div class="flex items-center gap-16px pb-16px border-b-1px border-#DFDCE8">
      <n-cascader
        :value="stageAndSubject"
        class="oiw-cascader"
        label-field="name"
        value-field="treeId"
        placeholder="选择学科"
        expand-trigger="click"
        :options="csvOptions"
        check-strategy="child"
        filterable
        @update:value="onCsvSelectChange"
      />
      <OIWSelect
        v-model:value="moduleId"
        :options="moduleOptions"
        placeholder="选择模块"
      />
    </div>
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else-if="moduleTopics && moduleTopics.length">
      <div class="text-16px font-600 color-#393548 leading-24px pt-20px">
        找到{{ moduleTopics?.length }}个专题
      </div>
      <div
        class="rounded-12px flex items-center px-16px h-48px bg-#F4F6FF my-8px"
      >
        <TipIcon class="mr-8px" />
        <div class="text-14px font-600 color-#393548">
          会根据学生掌握度，推荐适合学生学力的知识点练习
        </div>
      </div>
      <div
        v-for="item in moduleTopics"
        :key="item.id"
        class="border-b-1px border-#DCD8E7 py-8px flex items-center gap-16px"
      >
        <div class="pr-10px">
          <div class="ellipsis color-#393548 w-276px text-16px leading-24px">
            {{ item.name }}
          </div>
          <div class="mt-2px text-14px color-#9792AC leading-22px">
            知识点{{ item.knowledgePointIds?.length }}
          </div>
        </div>
        <OIWButton size="small" type="info" ghost @click="handlePreview(item)"
          >预览</OIWButton
        >
        <OIWButton size="small" type="info" @click="handleInsert(item)"
          >插入</OIWButton
        >
      </div>
    </div>
    <OIWStateBlock
      v-else
      class="mt-120px text-center"
      type="empty"
      title="暂无资源"
    />
  </div>
</template>

<script setup lang="ts">
import {
  OIWSelect,
  OIWButton,
  OIWLoading,
  OIWStateBlock,
} from '@guanghe-pub/onion-ui-web'
import TipIcon from '~icons/yc/tip-blue'
import { useAuth } from '@/hooks/useAuth'
import { useCvsEnum } from '@/hooks/useCvs'
import useSlideFastReciteStore from './store'
import type { InsertParams } from '@/components/SideResource/types'
import type { KnowledgePoints } from './service'

const emits = defineEmits<(e: 'insert', val: InsertParams) => void>()

const store = useSlideFastReciteStore()

const { loading, formModel, moduleId, moduleOptions, moduleTopics } =
  storeToRefs(store)

interface Options {
  id: string
  name: string
  treeId: string
  disabled?: boolean
  children?: Options[]
  [key: string]: unknown
}
const { StageEnum, SubjectEnum } = useCvsEnum()
const { stageId, subjectId } = useAuth()
const csvOptions = computed(() => {
  let options: Options[] = []
  StageEnum.value.forEach((stage, index) => {
    if (stage === '初中') {
      let temp: Options = {
        id: `${index}`,
        name: stage,
        treeId: `${index}`,
        disabled: false,
        children: [],
      }
      SubjectEnum.value.forEach((subject, indexes) => {
        if (
          subject === '语文' ||
          subject === '英语' ||
          subject === '生物' ||
          subject === '地理'
        ) {
          ;(temp.children as Options[]).push({
            id: `${index}-${indexes}`,
            name: subject,
            treeId: `${index}-${indexes}`,
            disabled: false,
            children: undefined,
          })
        }
      })
      options.push(temp)
    }
    if (stage === '高中') {
      let temp: Options = {
        id: `${index}`,
        name: stage,
        treeId: `${index}`,
        disabled: false,
        children: [],
      }
      SubjectEnum.value.forEach((subject, indexes) => {
        if (subject === '语文' || subject === '英语') {
          ;(temp.children as Options[]).push({
            id: `${index}-${indexes}`,
            name: subject,
            treeId: `${index}-${indexes}`,
            disabled: false,
            children: undefined,
          })
        }
      })
      options.push(temp)
    }
  })
  return options
})
const stageAndSubject = computed(() => {
  return `${formModel.value.stageId}-${formModel.value.subjectId}`
})

watch(
  () => stageAndSubject.value,
  () => {
    store.getModelList()
  },
)

onMounted(() => {
  initStageAndSubject()
})

const initStageAndSubject = () => {
  let value = ''
  let tempSubject: Options[] = []
  if (csvOptions.value.some((item) => item.id === `${stageId.value}`)) {
    tempSubject = csvOptions.value.find(
      (item) => item.id === `${stageId.value}`,
    )?.children as Options[]
  } else {
    tempSubject = csvOptions.value[0].children as Options[]
  }
  if (
    tempSubject.some(
      (item) => item.id === `${stageId.value}-${subjectId.value}`,
    )
  ) {
    value = `${
      (
        tempSubject.find(
          (item) => item.id === `${stageId.value}-${subjectId.value}`,
        ) as Options
      ).treeId
    }`
  } else {
    value = `${tempSubject[0].treeId as string}`
  }
  const [stageIdValue, subjectIdValue] = value.split('-')
  store.onCsvSelectChange(
    store.getLocalFastRecite('stageId') || stageIdValue,
    store.getLocalFastRecite('subjectId') || subjectIdValue,
  )
}

const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  store.onCsvSelectChange(stageId, subjectId)
}

const handleInsert = (item: KnowledgePoints) => {
  emits('insert', {
    sourceType: 'SpecialTopic',
    sourceId: item.id as string,
    name: item.name as string,
    duration: 180,
    subjectId: formModel.value?.subjectId,
    stageId: formModel.value?.stageId,
    importFrom: 'Recitation',
    extra: {
      moduleId: moduleId.value,
    },
  })
}

const handlePreview = (item: KnowledgePoints) => {
  store.setPreviewTopic(item)
  store.onChangePageType('detail')
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
}
</style>
