<template>
  <div class="p-24px">
    <div class="flex justify-between">
      <div class="text-20px color-#393548 font-600">
        班级错题本
        <span class="ml-8px color-#9792AC text-12px font-normal">
          每日10:00更新数据 | 当前统计截止昨日23:59
        </span>
      </div>
      <CloseIcon class="cursor-pointer ml-auto" @click="emits('close')" />
    </div>
    <div>
      <div class="flex items-center mt-16px w-58%">
        <span class="flex-shrink-0 mr-8px">班级:</span>
        <OIWSelect
          :value="filterModel.groupId"
          :options="rooms"
          :consistent-menu-width="false"
          placeholder="选择班级"
          @update:value="onGroupIdChange"
        />
      </div>
      <div class="flex items-center mt-16px">
        <span class="flex-shrink-0 mr-8px">时间:</span>
        <n-date-picker
          v-model:value="timeRange"
          type="daterange"
          class="select-time-picker"
          :is-date-disabled="dateDisabled"
        />
      </div>
      <div class="flex items-center mt-16px w-62%">
        <div class="flex-shrink-0 mr-8px flex items-center">
          <n-popover
            trigger="hover"
            arrow-class="paper-preferences-arrow-popover"
            content-class="paper-preferences-content-popover"
            class="paper-preferences-wrapper-popover"
          >
            <template #trigger>
              <QuestionIcon class="w-16px h-16px mr-4px" />
            </template>
            <div>
              AI课堂：AI课堂任务中学生产生的错题 <br />
              练习：教师布置的练习中产生的错题 <br />
              自学：学生使用洋葱学园进行自学产生的错题 <br />
              活动：校园PK赛活动产生的错题数据
            </div>
          </n-popover>
          来源:
        </div>
        <OIWSelect
          :value="filterModel.source"
          :options="sourceOptions"
          @update:value="onSourceChange"
        />
      </div>
      <div class="flex items-center mt-16px">
        <div class="flex-shrink-0 mr-8px">教材:</div>
        <QuestionTree
          class="question-tree-style"
          @on-tree-node-select="handleNodeSelect"
        />
      </div>
      <div class="flex items-center mt-16px justify-between">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">作答人次:</span>
          <OIWSelect
            :value="localFilter.answerNum"
            :options="exerciseNumberOptions"
            :consistent-menu-width="false"
            placeholder="选择作答人次"
            @update:value="onAnswerNumChange"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">正确率:</span>
          <OIWSelect
            :value="localFilter.accuracy"
            :options="accuracyOptions"
            :consistent-menu-width="false"
            placeholder="选择正确率"
            @update:value="onAccuracyChange"
          />
        </div>
      </div>
    </div>
    <div class="flex mt-24px justify-between items-center">
      <div>筛选到题目总数：{{ total }}</div>
    </div>
    <OIWLoading v-if="loading" :show="loading" width="200px" />
    <div v-else-if="problems.length">
      <div class="divide-y-[1px] divide-[#DCD8E7]">
        <ProblemItem
          v-for="(problem, index) of problems"
          :key="problem.id"
          :problem="problem"
          :index="index + 1 + (page - 1) * pageSize"
          isHideClassMode
          showInsertButton
          :insertCount="problemsCount[problem.id]"
          @on-insert="clickInsert(problem)"
        />
      </div>
      <div class="mt-59px mb-50px flex justify-end">
        <n-pagination
          class="custom-pagination"
          :page="page"
          :page-size="pageSize"
          :item-count="total"
          @update:page="onPageChange"
        />
      </div>
    </div>
    <div v-else class="flex items-center justify-center w-100% mt-90px">
      <OIWStateBlock type="empty" title="暂无资源" />
    </div>
  </div>
</template>

<script setup lang="ts">
import CloseIcon from '~icons/yc/close-round-gray'
import { useLoading } from '@/hooks/useLoading.ts'
import { OIWSelect, OIWLoading, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import type { InsertParams } from '../types'
import { useAuth } from '@/hooks/useAuth'
import useSideResourceStore from '../store'
import QuestionTree from '@/pages/classLearning/questionData/components/QuestionTree.vue'
import dayjs from 'dayjs'
import QuestionIcon from '~icons/yc/question.svg'
import type { TreeType } from '@/pages/microVideo/service.ts'
import type { ProblemDetailType } from '@/pages/problem/utils.ts'
import { getTeacherRoomListApi2 } from '@/components/TeacherHomeWorkModal/service'
import {
  accuracyOptions,
  dateDisabled,
  defaultTimeRange,
  exerciseNumberOptions,
  sourceOptions,
} from '@/pages/classLearning/utils.ts'
import type {
  CorrectionNoteProblemSimpleType,
  CorrectionNoteProblemsParams,
} from '@/pages/classLearning/service.ts'
import { getCorrectionNoteProblemsApi } from '@/pages/classLearning/service.ts'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import { postProblemDetail } from '@/pages/problem/service'
import { getSchoolProblemDetailApi } from '@/pages/schoolResource/schoolProblem/service'
import { ProblemTypesEnums } from '@guanghe-pub/onion-problem-render'
import { getDurationFromProblemDifficulty } from '../utils'
import { buryPoint } from '@/utils/buryPoint.ts'
import type { CacheDataType } from '@/hooks/useCorrectionNotebookCache.ts'
import { useCorrectionNotebookCache } from '@/hooks/useCorrectionNotebookCache.ts'

const emits = defineEmits<{
  (e: 'close'): void
  (e: 'insertProblem', val: InsertParams): void
}>()
const { getCache, setCache } = useCorrectionNotebookCache()
const { userId } = useAuth()
const sideResourceStore = useSideResourceStore()
const { groupIds, problemsCount } = storeToRefs(sideResourceStore)

const timeRange = ref<[number, number]>(defaultTimeRange)
const filterModel = reactive<{
  groupId: string
  startAt: string
  endAt: string
  subjectId: number
  stageId: number
  publisherId: number
  semesterId: number
  chapterId: string
  sectionId: string
  source: string | undefined
}>({
  groupId: '',
  startAt: '',
  endAt: '',
  subjectId: -1,
  stageId: -1,
  publisherId: -1,
  semesterId: -1,
  chapterId: '',
  sectionId: '',
  source: 'ai-class',
})

const localFilter = reactive<{
  answerNum: number | string | undefined
  accuracy: number | string | undefined
}>({
  answerNum: 5,
  accuracy: 60,
})

// 读取缓存
watchEffect(() => {
  const cacheData: CacheDataType = getCache()
  if (cacheData && cacheData.subjectId) {
    filterModel.source = cacheData.source
    localFilter.answerNum = cacheData.answerNum
    localFilter.accuracy = cacheData.accuracy
  }
})

const rooms = ref<{ label: string; value: string }[]>([])
const { toggleLoading, loading } = useLoading()

const page = ref(1)
const pageSize = ref(10)
const problems = ref<ProblemDetailType[]>([])
const originalProblems = ref<CorrectionNoteProblemSimpleType[]>([])
const filteredProblems = computed(() => {
  return originalProblems.value
    .filter((item) => {
      let answerNumFilter = true
      let accuracyFilter = true
      if (localFilter.answerNum !== 'all' && Number(localFilter.answerNum)) {
        answerNumFilter = item.answerNum >= Number(localFilter.answerNum)
      }
      if (localFilter.accuracy !== 'all' && Number(localFilter.accuracy)) {
        accuracyFilter = item.accuracy <= Number(localFilter.accuracy)
      }
      return answerNumFilter && accuracyFilter
    })
    .sort((a, b) => {
      return a.accuracy - b.accuracy
    })
})
const total = computed(() => {
  return filteredProblems.value.length
})

async function fetchRooms() {
  if (!loading.value) {
    toggleLoading()
  }
  if (userId.value) {
    const res = await getTeacherRoomListApi2(userId.value)
    rooms.value = res.map((item) => ({
      label: item.name,
      value: item.groupId,
    }))
    if (groupIds.value.length > 0) {
      filterModel.groupId = groupIds.value[0]
    } else if (rooms.value.length > 0) {
      filterModel.groupId = rooms.value[0].value
    }
  }
}

watch(
  () => timeRange.value,
  (value) => {
    if (value && value.length > 0) {
      filterModel.startAt = dayjs(value[0]).startOf('day').toISOString()
      filterModel.endAt = dayjs(value[1]).endOf('day').toISOString()
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => filterModel,
  () => {
    page.value = 1
    problems.value = []
    fetchProblems()
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => filteredProblems.value,
  () => {
    fetchProblemsInfo()
  },
  {
    immediate: true,
  },
)
watch(
  () => page.value,
  () => {
    fetchProblemsInfo()
  },
  {
    immediate: true,
  },
)

onMounted(() => {
  fetchRooms()
})

const handleNodeSelect = async (
  node: TreeType,
  csvData: {
    publisherId: YcType.CsvId
    semesterId: YcType.CsvId
    subjectId: YcType.CsvId
    stageId: YcType.CsvId
  },
) => {
  Object.assign(filterModel, {
    publisherId: csvData.publisherId,
    semesterId: csvData.semesterId,
    subjectId: csvData.subjectId,
    stageId: csvData.stageId,
  })
  const { publisherId, semesterId } = csvData
  if (
    Number(publisherId) === 0 ||
    Number(semesterId) === 0 ||
    node.id === 'all'
  ) {
    filterModel.chapterId = ''
    filterModel.sectionId = ''
  } else {
    if (node.type === 'chapter') {
      filterModel.chapterId = node.id
    }
    if (node.type === 'section') {
      filterModel.chapterId = node.ids[0]
      filterModel.sectionId = node.id
    }
  }
}

const onGroupIdChange = (value: string) => {
  filterModel.groupId = value
}

const onSourceChange = (value: string) => {
  filterModel.source = value
}

const onAnswerNumChange = (value: number) => {
  localFilter.answerNum = value
  updateLocalCache()
}

const onAccuracyChange = (value: number) => {
  localFilter.accuracy = value
  updateLocalCache()
}

const onPageChange = (val: number) => {
  page.value = val
}

async function fetchProblems() {
  if (
    !filterModel.groupId ||
    !filterModel.startAt ||
    !filterModel.endAt ||
    filterModel.subjectId === -1 ||
    filterModel.stageId === -1
  ) {
    return
  }
  if (!loading.value) {
    toggleLoading()
  }
  const params: CorrectionNoteProblemsParams = {
    groupId: filterModel.groupId,
    startAt: filterModel.startAt,
    endAt: filterModel.endAt,
    subjectId: Number(filterModel.subjectId),
    stageId: Number(filterModel.stageId),
    publisherId: Number(filterModel.publisherId),
    semesterId: Number(filterModel.semesterId),
    chapterId: filterModel.chapterId,
    sectionId: filterModel.sectionId,
    source: filterModel.source,
  }
  if (params.source === 'all') {
    delete params.source
  }
  if (params.publisherId === 0) {
    delete params.publisherId
    delete params.semesterId
  }
  if (params.publisherId !== 0 && params.semesterId === 0) {
    delete params.semesterId
  }
  const res = await getCorrectionNoteProblemsApi(params)
  if (res) {
    originalProblems.value = res.wrongProblems
    if (res.wrongProblems.length === 0 && loading.value) {
      toggleLoading()
    }
    updateLocalCache()
  }
}

async function fetchProblemsInfo() {
  if (!loading.value) {
    toggleLoading()
  }
  problems.value = []

  const startIndex = (page.value - 1) * 10
  const endIndex = startIndex + 10
  const currentPageItems = filteredProblems.value.slice(startIndex, endIndex)
  const itemTypeProblems = currentPageItems
    .filter((item) => item.problemType === 'cb_problem')
    .map((item) => item.problemId)
  const schoolProblems = currentPageItems
    .filter((item) => item.problemType === 'school_problem')
    .map((item) => item.problemId)
  const [itemTypeResponses, schoolResponses] = await Promise.all([
    itemTypeProblems.length > 0
      ? postProblemDetail({ problemsId: itemTypeProblems })
      : Promise.resolve([]),
    schoolProblems.length > 0
      ? getSchoolProblemDetailApi({ problemsId: schoolProblems })
      : Promise.resolve({ data: [] }),
  ])
  if (itemTypeProblems.length === 0 && schoolProblems.length === 0) {
    problems.value = []
    toggleLoading()
    return
  }
  const allProblems = [...itemTypeResponses, ...(schoolResponses.data || [])]
  problems.value =
    currentPageItems.reduce(
      (acc, item) => {
        const problem = allProblems.find((p) => p.id === item.problemId)
        if (problem) {
          acc.push({ ...problem, ...item })
        }
        return acc
      },
      [] as typeof allProblems,
    ) || []
  toggleLoading()
}
const getProblemTypeText = (type: string) => {
  if (type === 'exam') {
    return '简答题'
  }
  if (type === 'single_choice') {
    return '单选题'
  }
  const text = ProblemTypesEnums[type] || ''
  return text.endsWith('题') ? text : `${text}题`
}

const clickInsert = (problem: ProblemDetailType) => {
  emits('insertProblem', {
    sourceId: problem.id,
    name: getProblemTypeText(problem.type),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType:
      problem.problemType === 'school_problem' ? 'SchoolProblem' : 'Problem',
    subjectId: Number(filterModel.subjectId),
    stageId: Number(filterModel.stageId),
    publisherId: Number(filterModel.publisherId) || -1,
    semesterId: Number(filterModel.semesterId) || -1,
    chapterId: filterModel.chapterId,
    sectionId: filterModel.sectionId,
    importFrom: 'WrongBook',
  })
  const baseParams = {
    subjectId: Number(filterModel.subjectId),
    stageId: Number(filterModel.stageId),
    publisherId: Number(filterModel.publisherId) || -1,
    semesterId: Number(filterModel.semesterId) || -1,
    chapterId: filterModel.chapterId,
    sectionId: filterModel.sectionId,
    fromPageName: 'wrongBook',
  }
  buryPoint('clickCourseDrawerProbelmInsert', baseParams, 'course')
}

const updateLocalCache = () => {
  // 前端缓存
  const cacheData: CacheDataType = {
    publisherId: filterModel.publisherId,
    semesterId: filterModel.semesterId,
    subjectId: filterModel.subjectId,
    stageId: filterModel.stageId,
    chapterId: filterModel.chapterId,
    sectionId: filterModel.sectionId,
    source: filterModel.source,
    answerNum: localFilter.answerNum,
    accuracy: localFilter.accuracy,
  }
  setCache(cacheData)
}
</script>

<style lang="scss" scoped>
.select-time-picker {
  ::v-deep(.n-input) {
    height: 40px;
    border-radius: 12px;

    .n-input__input-el {
      height: 40px;
      line-height: 40px;
    }

    .n-input__border {
      border: 1px solid #c5c1d4;
    }
  }
}

.question-tree-style {
  ::v-deep(.option-name) {
    width: 190px !important;
    margin-left: 5px !important;
  }
}
</style>

<style lang="scss">
.paper-preferences-wrapper-popover {
  padding: 6px 8px !important;
  background: #57526c !important;
  border-radius: 8px !important;

  .paper-preferences-arrow-popover {
    background: #57526c !important;
  }

  .paper-preferences-content-popover {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #ffffff;
    background: #57526c;

    .paper-preferences-popover-theme-list {
      display: grid;
      grid-template-columns: repeat(2, 61px);
      grid-gap: 8px;
      padding: 4px;

      .paper-preferences-popover-theme-item {
        width: 100%;
        overflow: hidden;
        font-size: 12px;
        font-weight: 600;
        line-height: 12px;
        color: #ffffff;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
