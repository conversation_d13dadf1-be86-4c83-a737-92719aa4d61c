<template>
  <OIWLoading v-if="load" :show="load" width="200px" height="200px" />
  <div v-else-if="videoList && videoList.length" class="micro-video-list">
    <div class="micro-video-content">
      <div v-for="(item, index) in videoList" :key="index">
        <SchoolVideoCard
          :videoData="item"
          :show-self-clip="showSelfClip"
          @preview="() => handleSchoolPreview(item)"
          @insert="() => handleSchoolInsert(item)"
          @self-clip="() => handleSchoolSelfClip(item)"
        />
      </div>
    </div>
  </div>
  <div v-else class="micro-empty-box">
    <OIWStateBlock type="empty" title="暂无内容" />
  </div>
  <n-space
    v-if="topicList && topicList.length"
    justify="end"
    style="margin-top: 20px"
  >
    <n-pagination
      v-model:page="page"
      v-model:page-size="pageSize"
      class="custom-pagination"
      :item-count="total"
    />
  </n-space>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { OIWLoading, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import type { InsertParams } from '../types'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import SchoolVideoCard from './SchoolVideoCard.vue'

const props = defineProps<{
  topicList: SchoolVideoDetail[]
  total: number
  showSelfClip: boolean
}>()
const emits = defineEmits<{
  (e: 'onPreviewClick', data: SchoolVideoDetail, type: 'school'): void
  (e: 'onInsertClick', data: InsertParams): void
  (e: 'onSelfClip', data: SchoolVideoDetail, type: 'schoolVideo'): void
}>()
const load = ref(true)
const pageSize = ref(12)
const page = ref(1)

const videoList = ref<SchoolVideoDetail[]>([])

const initData = async () => {
  load.value = true
  if (props.topicList && !props.topicList.length) {
    return
  }
  videoList.value = props.topicList.filter(
    (item, index) =>
      index < page.value * pageSize.value &&
      index >= (page.value - 1) * pageSize.value,
  )
  load.value = false
}

const handleSchoolInsert = (item: SchoolVideoDetail) => {
  emits('onInsertClick', {
    sourceType: 'SchoolVideo',
    sourceId: item.id || '',
    name: item.name || '',
    duration: item.duration || 0,
    topicId: '',
    specialCourseId: '',
    subjectId: item.subjectId || '-1',
    stageId: item.stageId || '-1',
    publisherId: item.publisherId || '-1',
    semesterId: item.semesterId || '-1',
    importFrom: 'School',
  })
}
watch(
  () => props.topicList,
  () => {
    page.value = 1
    initData()
  },
  {
    immediate: true,
  },
)
watch(
  () => page.value,
  () => {
    initData()
  },
)

const handleSchoolPreview = (item: SchoolVideoDetail) => {
  emits('onPreviewClick', item, 'school')
}

const handleSchoolSelfClip = (item: SchoolVideoDetail) => {
  emits('onSelfClip', item, 'schoolVideo')
}
</script>

<style lang="scss" scoped>
.micro-empty-box {
  margin-top: 100px;
}

.micro-video-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
</style>
