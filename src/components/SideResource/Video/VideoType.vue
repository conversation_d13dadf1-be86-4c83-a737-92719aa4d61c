<template>
  <div class="p-24px">
    <div>
      <div class="flex justify-between">
        <OIWRadioButtonGroup
          v-if="isOIWRadioButtonGroupShow"
          :value="videoState.sourceType"
          :on-update-value="store.onSwitchSourceType"
        >
          <OIWRadioButton value="common">公共资源</OIWRadioButton>
          <OIWRadioButton v-if="isSchoolResourcesAuth" value="school">
            校本资源
          </OIWRadioButton>
          <OIWRadioButton value="collection">我的收藏</OIWRadioButton>
        </OIWRadioButtonGroup>
        <div
          v-else
          class="font-14px font-600 flex items-center cursor-pointer color-#9792AC"
          @click="onBackList"
        >
          <BackIcon
            class="w-14px cursor-pointer h-14px font-600 font-size-18px color-#9792AC"
          />
          返回
        </div>

        <CloseIcon class="cursor-pointer ml-auto" @click="emits('close')" />
      </div>
    </div>

    <div v-if="videoState.sourceType === 'common'">
      <VideoList
        v-show="videoState.pageType === 'list'"
        show-self-clip
        @on-preview-click="onPreviewClick"
        @on-insert-click="onInsertCommonVideo"
        @on-self-clip-click="onSelfClipShow"
      />
      <VideoDetail
        v-show="videoState.pageType === 'detail'"
        show-self-clip
        :show-insert-select="false"
        @on-easy-insert="onEasyInsert"
        @on-preview-click="onPreviewClick"
        @on-insert-click="onInsertCommonVideo"
        @on-insert-keypoint="onInsertKeypoint"
        @on-problem-insert-click="onProblemInsertClick"
        @on-self-clip="onSelfClipShow"
      />
      <VideoModal
        v-model:show="showVideoModal"
        :clip-id="videoState.videoPlayInfo.clipId"
        :special-course-id="videoState.videoPlayInfo.specialCourseId"
        :topic-id="videoState.videoPlayInfo.topicId"
        :videoBuryPointParams="videoBuryPointParams"
        to=".side-resource-drawer"
      />
    </div>
    <div v-else-if="videoState.sourceType === 'school'">
      <SchoolVideo
        v-show="schoolVideoState.pageType === 'list'"
        show-self-clip
        @on-insert="onInsertSchoolVideo"
        @on-preview="onPreviewSchoolVideo"
        @on-self-clip="onSelfClipShow"
      />
      <SchoolVideoDetail
        v-show="schoolVideoState.pageType === 'detail'"
        show-self-clip
        :show-insert-select="false"
        @on-easy-insert="onSchoolEasyInsert"
        @on-preview-click="onPreviewSchoolVideo"
        @on-insert-click="onInsertSchoolCommonVideo"
        @on-problem-insert-click="onSchoolProblemInsertClick"
        @on-self-clip="onSelfClipShow"
      />
      <VideoModalMp4
        v-model:show="schoolPreviewModalShow"
        to=".side-resource-drawer"
        :video-id="schoolVideoState.videoId"
        :buryPointParams="MP4VideoBuryPointParams"
      />
    </div>
    <div v-else>
      <VideoCollection
        show-self-clip
        to=".side-resource-drawer"
        @insert-video="onInsertCollectionVideo"
        @on-self-clip="onSelfClipShow"
      />
    </div>
  </div>
  <SelfClipModal
    v-model:visible="selfClipModalShow"
    to=".side-resource-drawer"
    :video-id="schoolVideoState.videoId"
    :special-course-id="videoState.videoPlayInfo.specialCourseId"
    :topic-id="videoState.videoPlayInfo.topicId"
    :type="selfClipModalType"
    :item="selfClipModalItem"
    @on-insert="selfClipInsert"
  />
</template>

<script setup lang="ts">
import BackIcon from '~icons/yc/back'
import {
  getSystemLessonTopicDetail,
  getSpicialCourseDetail,
} from '@/service/common.ts'
import VideoModal from './VideoModal.vue'
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'
import CloseIcon from '~icons/yc/close-round-gray'
import type { InsertParams } from '../types'
import {
  getDurationFromProblemDifficulty,
  getProblemTypeName,
  convertToNumber,
} from '../utils'
import VideoList from './VideoList.vue'
import VideoDetail from './VideoDetail.vue'
import useSideResourceStore from '../store'
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import VideoCollection from './VideoCollection.vue'
import SchoolVideo from './SchoolVideo.vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useDurationTimer } from '@/hooks/useDurationTimer'
import VideoModalMp4 from '@/components/VideoPlayer/VideoModalMp4.vue'
import SchoolVideoDetail from './SchoolVideoDetail.vue'
import SelfClipModal from './SelfClipModal.vue'
import { useAuth } from '@/hooks/useAuth.ts'
import type { TopicDetail } from '@/pages/microVideo/service'
import type { SchoolVideoDetail as SchoolVideoDetailType } from '@/pages/schoolResource/schoolVideo/service'

const props = defineProps<{
  drawerShow: boolean
  scene: 'edit' | 'create' | 'draft' // 当前页面场景 埋点用
}>()
const emits = defineEmits<{
  (e: 'close'): void
  (e: 'insertVideo', val: InsertParams): void
  (e: 'insertProblem', val: InsertParams): void
}>()
const message = useOIWMessage()
const { isSchoolResourcesAuth } = useAuth()
const store = useSideResourceStore()
const {
  videoState,
  videoDetail,
  csv,
  schoolVideoState,
  schoolVideoDetail,
  schoolCsv,
} = storeToRefs(store)
const showVideoModal = ref(false)
const schoolPreviewModalShow = ref(false)
const selfClipModalShow = ref(false)
const selfClipModalType = ref<'systemLesson' | 'specialCourse' | 'schoolVideo'>(
  'systemLesson',
)
const selfClipModalItem = ref<TopicDetail | SchoolVideoDetailType | undefined>(
  undefined,
)

const isOIWRadioButtonGroupShow = computed(() => {
  return (
    videoState.value.pageType === 'list' &&
    schoolVideoState.value.pageType === 'list'
  )
})

// 校本资源视频播放器埋点
const MP4VideoBuryPointParams = computed(() => {
  return {
    videoType: 'school',
    videoScene:
      props.scene === 'create'
        ? 'AIClassNewSchoolVideo'
        : 'AIClassEditSchoolVideo',
  }
})
// 埋点--插入视频
const buryPointInsertVideo = (eventValue: object) => {
  buryPoint('clickCourseDrawerMicroCourseInsert', eventValue, 'course')
}

// 一键插入
const onEasyInsert = async () => {
  const { topicId, specialCourseId } = videoState.value.videoPlayInfo
  const { videoType, topicNode } = videoState.value
  const [chapterId, sectionId, subSectionId] = topicNode?.ids || []
  emits('insertVideo', {
    sourceType: 'Video',
    sourceId: videoDetail.value!.id,
    name: videoDetail.value?.topicName || videoDetail.value?.name || '',
    duration: Math.ceil(videoDetail.value?.duration || 0),
    topicId,
    specialCourseId,
    subjectId: csv.value?.subjectId,
    stageId: csv.value?.stageId,
    publisherId: csv.value?.publisherId,
    semesterId: csv.value?.semesterId,
    chapterId,
    sectionId,
    subSectionId,
    importFrom: 'Public',
  })
  let practices: ProblemDetailType[][] = []

  if (videoType === 'systemLesson') {
    practices = (
      await getSystemLessonTopicDetail({
        topicId: videoState.value.videoTopicDetail!.id,
        publisherId: csv.value?.publisherId || '-1',
        semesterId: csv.value?.semesterId || '-1',
        subjectId: csv.value?.subjectId || '-1',
        stageId: csv.value?.stageId || '-1',
        doneLevel: '0',
      })
    ).practices
  } else {
    practices = (await getSpicialCourseDetail(topicId)).practices
  }
  if (practices && practices.length > 0) {
    for (const item of practices) {
      const problem = item[0]
      emits('insertProblem', {
        sourceType: 'Problem',
        sourceId: problem.problemId,
        name: getProblemTypeName(problem),
        duration: getDurationFromProblemDifficulty(
          problem.difficulty as string,
        ),
        topicId: '',
        specialCourseId: specialCourseId,
        subjectId: csv.value?.subjectId,
        stageId: csv.value?.stageId,
        publisherId: csv.value?.publisherId,
        semesterId: csv.value?.semesterId,
        chapterId,
        sectionId,
        subSectionId,
        importFrom: 'Public',
      })
    }
  }

  buryPoint(
    'clickCourseDrawerOneClickInsert',
    {
      status: specialCourseId ? '培优课' : '同步课',
      subjectId: convertToNumber(csv.value?.subjectId),
      stageId: convertToNumber(csv.value?.stageId),
      publisherId: convertToNumber(csv.value?.publisherId),
      semesterId: convertToNumber(csv.value?.semesterId),
      courseId: specialCourseId,
      topicId,
    },
    'course',
  )
}

const onPreviewClick = (clipId?: string) => {
  store.$patch((state) => {
    state.videoState.videoPlayInfo.clipId = clipId
  })
  showVideoModal.value = true
}

// 单题插入--微课配套题目
const onProblemInsertClick = (problem: ProblemDetailType) => {
  const { topicId } = videoState.value.videoPlayInfo
  const { topicNode } = videoState.value
  const { specialCourseId } = videoState.value.videoPlayInfo
  const [chapterId, sectionId, subSectionId] = topicNode?.ids || []
  emits('insertProblem', {
    sourceType: 'Problem',
    sourceId: problem.problemId,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    topicId: '',
    specialCourseId,
    subjectId: csv.value?.subjectId,
    stageId: csv.value?.stageId,
    publisherId: csv.value?.publisherId,
    semesterId: csv.value?.semesterId,
    chapterId,
    sectionId,
    subSectionId,
    importFrom: 'Public',
  })
  buryPoint(
    'clickCourseDrawerProbelmInsert',
    {
      status: specialCourseId ? '培优课' : '同步课',
      subjectId: convertToNumber(csv.value?.subjectId),
      stageId: convertToNumber(csv.value?.stageId),
      publisherId: convertToNumber(csv.value?.publisherId),
      semesterId: convertToNumber(csv.value?.semesterId),
      courseId: specialCourseId,
      chapterId,
      sectionId,
      subSectionId,
      topicId,
      fromPageName: 'relatedProblem',
    },
    'course',
  )
}
// 插入视频
const onInsertCommonVideo = () => {
  const { topicId, specialCourseId } = videoState.value.videoPlayInfo
  const { topicNode } = videoState.value
  const [chapterId, sectionId, subSectionId] = topicNode?.ids || []
  if (videoDetail.value) {
    emits('insertVideo', {
      sourceType: 'Video',
      sourceId: videoDetail.value!.id,
      name: videoDetail.value?.topicName || videoDetail.value?.name,
      duration: Math.ceil(videoDetail.value.duration),
      topicId,
      specialCourseId,
      subjectId: csv.value?.subjectId,
      stageId: csv.value?.stageId,
      publisherId: csv.value?.publisherId,
      semesterId: csv.value?.semesterId,
      chapterId,
      sectionId,
      subSectionId,
      importFrom: 'Public',
    })
    // 埋点
    buryPointInsertVideo({
      status: specialCourseId ? '培优课' : '同步课',
      subjectId: convertToNumber(csv.value?.subjectId),
      stageId: convertToNumber(csv.value?.stageId),
      publisherId: convertToNumber(csv.value?.publisherId),
      semesterId: convertToNumber(csv.value?.semesterId),
      courseId: specialCourseId,
      topicId,
      subSectionId,
      fromPageName: 'microVideoList',
      contentGroup: 'microVideo',
    })
  }
}

// 插入视频-切片
const onInsertKeypoint = (slice: YcType.KeyPoint) => {
  const { topicId, specialCourseId } = videoState.value.videoPlayInfo
  const { topicNode } = videoState.value
  const [chapterId, sectionId, subSectionId] = topicNode?.ids || []
  emits('insertVideo', {
    sourceType: 'Clip',
    sourceId: videoDetail.value!.id,
    name: `${slice.content} - ${
      videoDetail.value?.topicName || videoDetail.value?.name
    }`,
    duration: Math.ceil(slice.offset),
    topicId,
    specialCourseId,
    videoClipExtra: {
      clipId: slice.id,
      playRangeStart: slice.time,
      playRangeEnd: slice.time + slice.offset,
      type: 'system_clip',
    },
    subjectId: csv.value?.subjectId,
    stageId: csv.value?.stageId,
    publisherId: csv.value?.publisherId,
    semesterId: csv.value?.semesterId,
    chapterId,
    sectionId,
    subSectionId,
    importFrom: 'Public',
  })
  buryPointInsertVideo({
    status: specialCourseId ? '培优课' : '同步课',
    subjectId: convertToNumber(csv.value?.subjectId),
    stageId: convertToNumber(csv.value?.stageId),
    publisherId: convertToNumber(csv.value?.publisherId),
    semesterId: convertToNumber(csv.value?.semesterId),
    courseId: specialCourseId,
    topicId,
    subSectionId,
    fromPageName: 'microVideoList',
    contentGroup: 'microVideoeSegment',
  })
}

// 校本资源--外层插入视频
const onInsertSchoolVideo = () => {
  if (
    schoolVideoDetail.value?.problems &&
    schoolVideoDetail.value?.problems.length > 0
  ) {
    store.$patch((state) => {
      state.schoolVideoState.detailType = 'video'
      state.schoolVideoState.pageType = 'detail'
    })
  } else {
    const { schoolNode } = schoolVideoState.value
    const [chapterId, sectionId, subSectionId] = schoolNode?.ids || []
    emits('insertVideo', {
      sourceType: 'SchoolVideo',
      sourceId: schoolVideoDetail.value?.id || '',
      name: schoolVideoDetail.value?.name || '',
      duration: schoolVideoDetail.value?.duration || 0,
      topicId: '',
      specialCourseId: '',
      subjectId: schoolCsv.value?.subjectId,
      stageId: schoolCsv.value?.stageId,
      publisherId: schoolCsv.value?.publisherId,
      semesterId: schoolCsv.value?.semesterId,
      chapterId,
      sectionId,
      subSectionId,
      importFrom: 'School',
    })
    buryPointInsertVideo({
      subjectId: convertToNumber(schoolCsv.value?.subjectId),
      stageId: convertToNumber(schoolCsv.value?.stageId),
      publisherId: convertToNumber(schoolCsv.value?.publisherId),
      semesterId: convertToNumber(schoolCsv.value?.semesterId),
      subSectionId,
      fromPageName: 'schoolList',
    })
  }
}

// 校本资源--视频预览
const onPreviewSchoolVideo = () => {
  schoolPreviewModalShow.value = true
}

// 校本资源一键插入
const onSchoolEasyInsert = (problems: ProblemDetailType[]) => {
  const { schoolNode } = schoolVideoState.value
  const [chapterId, sectionId, subSectionId] = schoolNode?.ids || []
  emits('insertVideo', {
    sourceType: 'SchoolVideo',
    sourceId: schoolVideoDetail.value?.id || '',
    name: schoolVideoDetail.value?.name || '',
    duration: schoolVideoDetail.value?.duration || 0,
    topicId: '',
    specialCourseId: '',
    subjectId: schoolCsv.value?.subjectId,
    stageId: schoolCsv.value?.stageId,
    publisherId: schoolCsv.value?.publisherId,
    semesterId: schoolCsv.value?.semesterId,
    chapterId,
    sectionId,
    subSectionId,
    importFrom: 'School',
  })

  for (const problem of problems) {
    emits('insertProblem', {
      sourceType:
        problem.problemType === 'ItemTypeSchoolProblem'
          ? 'SchoolProblem'
          : 'Problem',
      sourceId: problem.problemId,
      name: getProblemTypeName(problem),
      duration: getDurationFromProblemDifficulty(problem.difficulty as string),
      topicId: '',
      specialCourseId: '',
      subjectId: schoolCsv.value?.subjectId || '-1',
      stageId: schoolCsv.value?.stageId || '-1',
      publisherId: schoolCsv.value?.publisherId || '-1',
      semesterId: schoolCsv.value?.semesterId || '-1',
      chapterId: chapterId || '-1',
      sectionId: sectionId || '-1',
      subSectionId: subSectionId || '-1',
      importFrom: 'School',
    })
  }
}
// 校本资源--插入视频
const onInsertSchoolCommonVideo = () => {
  const { schoolNode } = schoolVideoState.value
  const [chapterId, sectionId, subSectionId] = schoolNode?.ids || []
  emits('insertVideo', {
    sourceType: 'SchoolVideo',
    sourceId: schoolVideoDetail.value?.id || '',
    name: schoolVideoDetail.value?.name || '',
    duration: schoolVideoDetail.value?.duration || 0,
    topicId: '',
    specialCourseId: '',
    subjectId: schoolCsv.value?.subjectId,
    stageId: schoolCsv.value?.stageId,
    publisherId: schoolCsv.value?.publisherId,
    semesterId: schoolCsv.value?.semesterId,
    chapterId,
    sectionId,
    subSectionId,
    importFrom: 'School',
  })
  buryPointInsertVideo({
    subjectId: convertToNumber(schoolCsv.value?.subjectId),
    stageId: convertToNumber(schoolCsv.value?.stageId),
    publisherId: convertToNumber(schoolCsv.value?.publisherId),
    semesterId: convertToNumber(schoolCsv.value?.semesterId),
    subSectionId,
    fromPageName: 'schoolList',
  })
}
// 校本资源--插入题目
const onSchoolProblemInsertClick = (problem: ProblemDetailType) => {
  const { schoolNode } = schoolVideoState.value
  const [chapterId, sectionId, subSectionId] = schoolNode?.ids || []
  emits('insertProblem', {
    sourceType:
      problem.problemType === 'ItemTypeSchoolProblem'
        ? 'SchoolProblem'
        : 'Problem',
    sourceId: problem.problemId,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    topicId: '',
    specialCourseId: '',
    subjectId: schoolCsv.value?.subjectId || '-1',
    stageId: schoolCsv.value?.stageId || '-1',
    publisherId: schoolCsv.value?.publisherId || '-1',
    semesterId: schoolCsv.value?.semesterId || '-1',
    chapterId: chapterId || '-1',
    sectionId: sectionId || '-1',
    subSectionId: subSectionId || '-1',
    importFrom: 'School',
  })
  buryPoint(
    'clickCourseDrawerProbelmInsert',
    {
      subjectId: convertToNumber(csv.value?.subjectId),
      stageId: convertToNumber(csv.value?.stageId),
      publisherId: convertToNumber(csv.value?.publisherId),
      semesterId: convertToNumber(csv.value?.semesterId),
      chapterId,
      sectionId,
      subSectionId,
      fromPageName: 'schoolRelatedProblem',
    },
    'course',
  )
}

// 插入视频-我的收藏
const onInsertCollectionVideo = (val: InsertParams) => {
  emits('insertVideo', {
    ...val,
    importFrom: 'MyFavorite',
  })
  const { publisherId, semesterId, subjectId, stageId, topicId, sourceType } =
    val
  buryPointInsertVideo({
    subjectId: convertToNumber(subjectId),
    stageId: convertToNumber(stageId),
    publisherId: convertToNumber(publisherId),
    semesterId: convertToNumber(semesterId),
    topicId,
    fromPageName: 'collection',
    contentGroup: sourceType === 'Clip' ? 'microVideoeSegment' : 'microVideo',
  })
}

const selfClipInsert = (
  item: TopicDetail | SchoolVideoDetailType,
  start: number,
  end: number,
  type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
) => {
  if (item) {
    const importFrom =
      videoState.value.sourceType === 'common'
        ? 'Public'
        : videoState.value.sourceType === 'school'
          ? 'School'
          : 'MyFavorite'
    let sourceId = ''
    let topicId = ''
    if (type === 'schoolVideo') {
      sourceId = item.id
    } else {
      sourceId = (item.video as any).id || ''
      topicId = item.id
    }
    emits('insertVideo', {
      sourceType: type === 'schoolVideo' ? 'SchoolVideo' : 'Video',
      sourceId,
      name: item.name,
      duration: end - start,
      specialCourseId:
        type === 'specialCourse'
          ? item.specialId || item.resourceSubId
          : undefined,
      videoClipExtra: {
        type: 'self_clip',
        playRangeStart: start,
        playRangeEnd: end,
      },
      subjectId: item.subjectId || item.subject || '',
      publisherId: item.publisherId || item.publisher || '',
      semesterId: item.semesterId || item.semester || '',
      stageId: item.stageId || item.stage || '',
      topicId,
      importFrom: importFrom,
    })
    message.success('插入成功')
  }
}

const onSelfClipShow = (
  type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
  item?: TopicDetail | SchoolVideoDetailType,
) => {
  selfClipModalType.value = type
  selfClipModalShow.value = true
  selfClipModalItem.value = item
  if (item) {
    let topicId = ''
    if (type !== 'schoolVideo') {
      topicId = item.id
    }
    buryPointInsertVideo({
      status:
        type === 'specialCourse'
          ? '专项课'
          : type === 'schoolVideo'
            ? ''
            : '同步课',
      subjectId: item.subjectId || item.subject || '',
      publisherId: item.publisherId || item.publisher || '',
      semesterId: item.semesterId || item.semester || '',
      stageId: item.stageId || item.stage || '',
      courseId:
        type === 'specialCourse'
          ? item.specialId || item.resourceSubId
          : undefined,
      topicId,
      fromPageName:
        videoState.value.sourceType === 'common'
          ? 'microVideoList'
          : videoState.value.sourceType === 'school'
            ? 'schoolList'
            : 'collection',
      contentGroup: 'microVideoSelf',
    })
  }
}

// 埋点：知识点详情页面进入配套题目的埋点及停留时长
const { duration, startTime, closeTime } = useDurationTimer()
// 停留时长埋点
const buryPointDuration = (duration: number) => {
  if (duration > 0) {
    buryPoint(
      'getProblemListBrowseDuration',
      {
        pageName: 'drawer',
        fromPageName: 'relatedProblem',
        duration,
      },
      'course',
    )
    closeTime()
  }
}

const onBackList = () => {
  store.$patch((state) => {
    state.videoState.pageType = 'list'
    state.schoolVideoState.pageType = 'list'
  })
  buryPointDuration(duration.value)
}

watch(
  () => videoState.value.detailType,
  (val) => {
    if (val === 'practice') {
      buryPoint(
        'enterProblemListBrowsePage',
        {
          pageName: 'drawer',
          fromPageName: 'relatedProblem',
        },
        'course',
      )
      startTime()
    } else {
      buryPointDuration(duration.value)
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => props.drawerShow,
  (val) => {
    if (!val) {
      buryPointDuration(duration.value)
    }
  },
)

onBeforeUnmount(() => {
  closeTime()
})

// 播放器埋点
const videoBuryPointParams = computed(() => {
  return {
    videoType: videoState.value.videoPlayInfo?.clipId
      ? 'course_clip'
      : 'course',
    videoScene:
      props.scene === 'create'
        ? 'AIClassNewPublicVideo'
        : 'AIClassEditPublicVideo',
  }
})
</script>
