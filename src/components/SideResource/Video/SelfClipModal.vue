<template>
  <n-modal
    v-model:show="modalShow"
    class="self-clip-modal"
    closable
    :mask-closable="false"
    style="width: 760px; min-height: 532px"
    :to="to"
    :auto-focus="false"
  >
    <div class="video-clip-box bg-#ffffff rounded-16px">
      <div
        class="relative h-424px rounded-tl-16px rounded-tr-16px overflow-hidden"
      >
        <div v-if="type === 'schoolVideo' && mp4url" class="video-play-box">
          <VideoMp4
            :url="mp4url"
            :videoId="videoId || ''"
            :baseVideo="baseSchoolVideo"
            :buryPointParams="getBuryPointParams"
            :getPlayer="getPlayerRef"
          />
        </div>

        <SimpleVideo
          v-if="type === 'specialCourse' || type === 'systemLesson'"
          :topicId="topicId"
          :specialCourseId="specialCourseId"
          :baseVideo="baseSimpleVideo"
          :buryPointParams="getBuryPointParams"
          :getPlayer="getPlayerRef"
        />
        <div
          v-if="playerRef"
          class="flex absolute left-16px bottom-16px bg-[rgba(0,0,0,0.7)] h-56px rounded-12px w-728px after:content-[''] after:absolute after:top-0 after:left-56px after:w-1px after:h-full after:bg-[rgba(255,255,255,0.3)]"
        >
          <div
            :class="{ 'play-start': isPaused, 'play-paused': !isPaused }"
            @click="playOrPause"
          />
          <div class="px-16px pt-16px w-full relative">
            <div v-if="showClipTip" class="clip-tip-box">
              <div class="tip-content">
                <TipIcon class="w-16px h-16px mr-8px" />
                <span class="tip-text">点击插入切片节点</span>
              </div>
              <div class="tip-footer">
                <OIWButton size="small" @click="closeClipTip"
                  >我知道了</OIWButton
                >
              </div>
            </div>
            <div class="player-clip-box">
              <div
                class="clip-markers w-full h-14px bg-[rgba(255,255,255,0.6)] rounded-4px relative cursor-pointer"
                @click="handleClipBoxClick"
              >
                <div
                  class="start-marker"
                  :style="{ left: `${startMarkerPosition}%` }"
                  @mousedown="startDrag('start', $event)"
                />
                <div
                  class="selected-area"
                  :style="{
                    left: `${startMarkerPosition}%`,
                    right: `${100 - endMarkerPosition}%`,
                  }"
                />
                <div
                  class="end-marker"
                  :style="{ left: `${endMarkerPosition}%` }"
                  @mousedown="startDrag('end', $event)"
                />
              </div>
            </div>
            <div class="player-process-box mt-9px">
              <div
                class="process-bg w-full h-6px bg-[rgba(255,255,255,0.6)] rounded-4px relative cursor-pointer"
                @click="handleProcessBoxClick"
              >
                <div
                  class="process-current"
                  :style="{ width: `${progressPercentage}%` }"
                />
                <div
                  class="process-tag"
                  :style="{
                    left: `${progressPercentage}%`,
                  }"
                  @mousedown="startDrag('progress', $event)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="h-104px px-32px rounded-bl-16px rounded-br-16px flex items-center justify-between"
      >
        <div class="text-14px leading-20px color-#57526C">
          <span>自定义切片 </span>
          <span>{{ formattedStartTime }}</span>
          <span> - </span>
          <span>{{ formattedEndTime }}</span>
          <span>｜时长</span>
          <span>{{ formattedClipDuration }}</span>
        </div>
        <div class="flex items-center justify-center gap-16px">
          <OIWButton type="info" ghost @click="cancelBtn">取消</OIWButton>
          <OIWButton @click="onInsert">插入切片</OIWButton>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import SimpleVideo from '@/components/VideoPlayer/simpleVideo.vue'
import VideoMp4 from '@/components/VideoPlayer/VideoMp4.vue'
import type { VideoPlayerProps } from '@/components/VideoPlayer/service.ts'
import { getSchoolVideoPlayUrlApi } from '@/pages/schoolResource/schoolVideo/service'
import { useAuth } from '@/hooks/useAuth'
import TipIcon from '~icons/yc/tip-blue'
import type { TopicDetail } from '@/pages/microVideo/service'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import { buryPoint } from '@/utils/buryPoint.ts'

const props = defineProps<{
  type: 'systemLesson' | 'specialCourse' | 'schoolVideo'
  visible: boolean
  topicId: string
  specialCourseId?: string
  videoId?: string
  buryPointParams?: Record<string, any> // 埋点参数
  to?: string
  item?: TopicDetail | SchoolVideoDetail
}>()

const emits = defineEmits<{
  (e: 'update:visible', val: boolean): void
  (
    e: 'onInsert',
    item: TopicDetail | SchoolVideoDetail,
    start: number,
    end: number,
    type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
  ): void
}>()

const baseSimpleVideo = ref<VideoPlayerProps>({
  autoplay: false,
  controls: false,
  fluid: true,
})
const baseSchoolVideo = ref<VideoPlayerProps>({
  autoplay: false,
  controls: false,
})
const message = useOIWMessage()
const mp4url = ref('')
const playerRef = ref<any>()
const isPaused = ref<boolean>(false)

const videoDuration = ref(0)
const currentTime = ref(0)
const startMarkerPosition = ref(0) // 初始位置为0%
const endMarkerPosition = ref(100) // 初始位置为100%
const isDragging = ref(false)
const dragTarget = ref<'start' | 'end' | 'progress' | null>(null)
const { userId } = useAuth()
const CLIP_TIP_KEY = `video-clip-tip-shown-${userId.value}`
const showClipTip = ref(window.localStorage.getItem(CLIP_TIP_KEY) !== 'true')

const closeClipTip = () => {
  showClipTip.value = false
  window.localStorage.setItem(CLIP_TIP_KEY, 'true')
}

// 格式化时间为 MM:SS 格式
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`
}

// 计算开始时间（秒）
const startTime = computed(() => {
  return Math.floor((startMarkerPosition.value / 100) * videoDuration.value)
})

// 计算结束时间（秒）
const endTime = computed(() => {
  return Math.floor((endMarkerPosition.value / 100) * videoDuration.value)
})

// 计算片段时长（秒）
const clipDuration = computed(() => {
  return endTime.value - startTime.value
})

// 格式化显示的时间
const formattedStartTime = computed(() => formatTime(startTime.value))
const formattedEndTime = computed(() => formatTime(endTime.value))
const formattedClipDuration = computed(() => formatTime(clipDuration.value))

// 当前播放进度百分比
const progressPercentage = computed(() => {
  if (!videoDuration.value) return 0
  return (currentTime.value / videoDuration.value) * 100
})

const modalShow = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  },
})
const getBuryPointParams = computed(() => {
  if (props.buryPointParams) {
    return {
      videoScene: 'teacherWorkbench',
      ...props.buryPointParams,
    }
  }
  return { videoScene: 'teacherWorkbench' }
})

watch(
  () => modalShow.value,
  async (val) => {
    if (val) {
      resetClip()
      if (props.type === 'schoolVideo') {
        await fetchMp4Url()
      }
      if (window.localStorage.getItem(CLIP_TIP_KEY) !== 'true') {
        showClipTip.value = true
        endMarkerPosition.value = 86
      }
    }
  },
  {
    immediate: true,
  },
)

async function fetchMp4Url() {
  try {
    const res = await getSchoolVideoPlayUrlApi(props.videoId || '')
    mp4url.value = res.url
  } catch (e) {
    message.warning('获取微课播放地址失败')
  }
}

// 监听播放器时间更新
const setupTimeUpdate = () => {
  if (playerRef.value) {
    playerRef.value.on('timeupdate', () => {
      currentTime.value = playerRef.value.currentTime()
    })

    // 获取视频总时长
    playerRef.value.on('loadedmetadata', () => {
      videoDuration.value = playerRef.value.duration()
    })
  }
}

const getPlayerRef = (player: any) => {
  playerRef.value = player
  isPaused.value = playerRef.value?.paused()
  setupTimeUpdate()
  if (playerRef.value) {
    videoDuration.value = playerRef.value.duration() || 0
  }
}

const playOrPause = () => {
  if (playerRef.value) {
    if (playerRef.value.paused()) {
      playerRef.value.play()
      isPaused.value = false
    } else {
      playerRef.value.pause()
      isPaused.value = true
    }
  }
}

// 开始拖动标记点
const startDrag = (target: 'start' | 'end' | 'progress', e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation() // 阻止事件冒泡
  isDragging.value = true
  dragTarget.value = target

  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 处理拖动
const handleDrag = (e: MouseEvent) => {
  if (!isDragging.value || !dragTarget.value) return

  if (dragTarget.value === 'start' || dragTarget.value === 'end') {
    // 处理切片标记拖动
    const clipBox = document.querySelector('.clip-markers') as HTMLElement
    if (!clipBox) return

    const rect = clipBox.getBoundingClientRect()
    const percentage = Math.max(
      0,
      Math.min(100, ((e.clientX - rect.left) / rect.width) * 100),
    )

    if (dragTarget.value === 'start') {
      // 确保开始标记不超过结束标记
      if (percentage < endMarkerPosition.value) {
        startMarkerPosition.value = percentage
      }
    } else {
      // 确保结束标记不小于开始标记
      if (percentage > startMarkerPosition.value) {
        endMarkerPosition.value = percentage
      }
    }
  } else if (dragTarget.value === 'progress') {
    // 处理进度条拖动
    const processBox = document.querySelector('.process-bg') as HTMLElement
    if (!processBox) return

    const rect = processBox.getBoundingClientRect()
    const percentage = Math.max(
      0,
      Math.min(100, ((e.clientX - rect.left) / rect.width) * 100),
    )

    if (playerRef.value) {
      const newTime = (percentage / 100) * videoDuration.value
      playerRef.value.currentTime(newTime)
    }
  }
}

// 停止拖动
const stopDrag = () => {
  isDragging.value = false
  dragTarget.value = null
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 点击切片区域，移动最近的标记点
const handleClipBoxClick = (e: MouseEvent) => {
  e.stopPropagation()

  const clipBox = e.currentTarget as HTMLElement
  const rect = clipBox.getBoundingClientRect()
  const clickPosition = ((e.clientX - rect.left) / rect.width) * 100

  // 计算点击位置与开始和结束标记的距离
  const distanceToStart = Math.abs(clickPosition - startMarkerPosition.value)
  const distanceToEnd = Math.abs(clickPosition - endMarkerPosition.value)

  // 移动距离最近的标记点
  if (distanceToStart <= distanceToEnd) {
    if (clickPosition < endMarkerPosition.value) {
      startMarkerPosition.value = clickPosition
    }
  } else {
    if (clickPosition > startMarkerPosition.value) {
      endMarkerPosition.value = clickPosition
    }
  }
}

// 点击进度条区域，改变播放进度但不影响切片标记
const handleProcessBoxClick = (e: MouseEvent) => {
  e.stopPropagation()
  const processBox = e.currentTarget as HTMLElement
  const rect = processBox.getBoundingClientRect()
  const percentage = ((e.clientX - rect.left) / rect.width) * 100

  if (playerRef.value && percentage >= 0 && percentage <= 100) {
    const newTime = (percentage / 100) * videoDuration.value
    playerRef.value.currentTime(newTime)
  }
}

const resetClip = () => {
  startMarkerPosition.value = 0
  endMarkerPosition.value = 100
  playerRef.value = undefined
  videoDuration.value = 0
  currentTime.value = 0
  isDragging.value = false
  dragTarget.value = null
}

const cancelBtn = () => {
  resetClip()
  modalShow.value = false
}

const onInsert = () => {
  buryPoint(
    'clickCourseDrawerMicroCourseSelfCutInsert',
    {
      button: 'insertCut',
    },
    'course',
  )
  if (!props.item) return
  emits('onInsert', props.item, startTime.value, endTime.value, props.type)
}
</script>

<style lang="scss" scoped>
.clip-tip-box {
  position: absolute;
  top: -124px;
  right: 10%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 112px;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 16px;
  box-shadow: 0px 4px 4px 0px rgba(80, 75, 100, 0.08);

  &::after {
    position: absolute;
    right: 24px;
    bottom: -12px; // 调整箭头位置
    width: 0;
    height: 0;
    content: '';
    border-top: 12px solid #ffffff; // 箭头颜色与背景一致
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
  }

  .tip-content {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .tip-text {
      font-size: 16px;
      font-weight: 500;
      color: #393548;
    }
  }

  .tip-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: auto;
  }
}

.play-start {
  width: 24px;
  height: 24px;
  margin: 16px;
  cursor: pointer;
  background: url('https://fp.yangcong345.com/onion-extension/111-91ab1d9d4f9c831c73bcb4dbfdcce0b3.png')
    no-repeat;
  background-size: 24px 24px;
}

.play-paused {
  width: 24px;
  height: 24px;
  margin: 16px;
  cursor: pointer;
  background: url('https://fp.yangcong345.com/onion-extension/222-28671b38a7f70bfff87f6b120b382480.png')
    no-repeat;
  background-size: 24px 24px;
}

.player-clip-box {
  .start-marker {
    position: absolute;
    top: -9px;
    box-sizing: border-box;
    width: 12px;
    height: 23px;
    cursor: col-resize;
    background: url('https://fp.yangcong345.com/onion-extension/333-74b190778edd4874ef70dfa54879fcd3.png')
      no-repeat;
    background-size: 14px 32px;
    transform: translateX(-7px);
  }

  .end-marker {
    position: absolute;
    top: -9px;
    box-sizing: border-box;
    width: 12px;
    height: 23px;
    cursor: col-resize;
    background: url('https://fp.yangcong345.com/onion-extension/333-74b190778edd4874ef70dfa54879fcd3.png')
      no-repeat;
    background-size: 14px 32px;
    transform: translateX(-7px);
  }

  .selected-area {
    position: absolute;
    height: 100%;
    background: #ffffff;
    border-radius: 4px;
  }
}

.player-process-box {
  .process-current {
    height: 100%;
    background: #ffffff;
    border-radius: 4px;
  }

  .process-tag {
    position: absolute;
    top: -2px;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    cursor: pointer;
    background: #5e80ff;
    border: 1px solid #ffffff;
    border-radius: 50%;
    transform: translateX(-5px);
  }
}

.video-play-box {
  width: 100%;
  height: 424px;

  ::v-deep(.video-js) {
    min-width: 760px !important;
    min-height: 424px !important;
  }
}
</style>
