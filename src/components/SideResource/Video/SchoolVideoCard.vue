<template>
  <div
    class="flex flex-col w-240px group transition-all hover:bg-#F4F6FF rounded-16px cursor-pointer mt-16px"
  >
    <div
      v-if="isShow"
      class="brief-desc-header"
      :style="{
        backgroundImage: `url(https://fp.yangcong345.com/middle/1.0.2/schoolVideoDefaultBg@2x-aa96a8e233739e62d9aaca1d4d60f5e0__w.png)`,
      }"
    >
      <n-space justify="space-between" class="head-info">
        <div class="flex">
          <span
            v-if="videoData.problems && videoData.problems.length > 0"
            class="label"
          >
            关联{{ videoData.problems.length }}题
          </span>
        </div>
      </n-space>
      <div
        class="hidden group-hover:block absolute top-0 left-0 right-0 bottom-0"
      >
        <div
          class="rounded-12px h-100% w-100% px-31px flex *:min-w-45px *:inline-flex *:flex-col *:items-center *:cursor-pointer bg-#12254d/60 pt-40px backdrop-blur-4px color-#EFEEF3"
          :class="{
            'justify-center': !showSelfClip,
            'gap-32px': !showSelfClip,
            'justify-between': showSelfClip,
          }"
        >
          <div class="text-center" @click="handlePriview">
            <div
              class="hover:bg-#FFD633 transition-all w-32px h-32px rounded-50% bg-#c7c7c7 inline-flex justify-center items-center"
            >
              <EyeIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="mt-8px text-12px font-600">预览</div>
          </div>
          <div class="text-center">
            <div
              class="hover:bg-#FFD633 transition-all w-32px h-32px rounded-50% bg-#c7c7c7 inline-flex justify-center items-center"
              @click="$emit('insert')"
            >
              <AddIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="mt-8px text-12px font-600">插入</div>
          </div>
          <div v-if="showSelfClip" class="text-center">
            <div
              class="hover:bg-#FFD633 transition-all w-32px h-32px rounded-50% bg-#c7c7c7 inline-flex justify-center items-center"
              @click="$emit('selfClip')"
            >
              <FolderIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="mt-8px text-12px font-600">自定义插入</div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="brief-desc-header"
      :style="{
        backgroundImage: `url(https://fp.yangcong345.com/onion-extension/555-90f6ca756f41b5bad44b3f0516b98cb2.png)`,
      }"
    />
    <div class="brief-desc-content">
      <div class="flex justify-between">
        <div :class="{ title: true }">
          {{ name }}
        </div>
      </div>
      <n-space v-if="isShow" justify="space-between" class="info-list">
        <div>
          <n-ellipsis style="max-width: 100px"
            >分享人：{{ videoData.createdName }}</n-ellipsis
          >
          <span v-if="isSelfResource" class="text-14px color-#FEA345"
            >(自己上传)</span
          >
        </div>
        <div>
          {{ durationFormat }}
        </div>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formattedDuration } from '@/pages/microVideo/service'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import EyeIcon from '~icons/yc/eye'
import FolderIcon from '~icons/yc/folder-add'
import AddIcon from '~icons/yc/self-add'
import { useAuth } from '@/hooks/useAuth'

const props = defineProps<{
  videoData: SchoolVideoDetail
  showSelfClip: boolean
}>()

const emits = defineEmits<{
  (e: 'preview'): void
  (e: 'insert'): void
  (e: 'selfClip'): void
}>()

const { userId } = useAuth()

const isSelfResource = computed(() => {
  return props.videoData.createdBy === userId.value
})

const name = computed(() => {
  return props.videoData.resourceName || props.videoData.name
})

const isShow = computed(() => {
  return props.videoData.name
})

// 视频时长
const durationFormat = computed(() => {
  if (props.videoData?.duration) {
    return formattedDuration(props.videoData.duration)
  }
  return ''
})

const handlePriview = () => {
  emits('preview')
}
</script>

<style lang="scss" scoped>
.brief-desc-header {
  position: relative;
  width: 240px;
  height: 135px;
  background-size: cover;

  /* 卡片圆角 */
  border-radius: 16px;

  .head-info {
    margin-top: 2px;
    margin-left: 2px;

    .label {
      padding: 6px 10px;
      font-size: 12px;
      font-weight: 500;
      line-height: 12px;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px;
    }
  }
}

.brief-desc-content {
  min-height: 72px;
  padding: 12px;

  .title {
    max-width: 86%;
    overflow: hidden;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    color: #393548;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.grey {
      font-size: 16px;
      font-weight: 500;
      line-height: 18px;
      color: #c5c1d4;
    }
  }

  .info-list {
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
    color: #8a869e;
  }
}
</style>
