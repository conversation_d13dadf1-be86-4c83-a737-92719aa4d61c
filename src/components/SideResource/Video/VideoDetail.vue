<template>
  <div v-if="videoDetail" closable class="drawer-content mt-16px">
    <div class="flex items-center justify-between">
      <OIWRadioButtonGroup
        :value="videoState.detailType"
        @update:value="onUpdateDetailType"
      >
        <OIWRadioButton value="video">微课</OIWRadioButton>
        <OIWRadioButton value="practice">配套题目</OIWRadioButton>
      </OIWRadioButtonGroup>
      <n-tooltip trigger="hover">
        <template #trigger>
          <OIWButton round @click="$emit('onEasyInsert')"> 一键插入 </OIWButton>
        </template>
        插入推荐的微课及题目
      </n-tooltip>
    </div>

    <TopicVideoPractice
      v-if="videoState.detailType === 'practice'"
      :type="videoState.videoType"
      :csv="csv"
      :topicId="videoState.videoPlayInfo.topicId"
      :isShowInsertSelect="showInsertSelect"
      @insert-problem="onProblemInsertClick"
    />
    <div v-else>
      <div class="header-box mb-16px mt-12px">
        <span class="origin-item" />
        <span class="font-600 font-size-16px color-#393548 ml-8px"
          >完整微课</span
        >
        <span class="grey-text font-size-12px color-#9792AC mt-2px"
          >｜时长{{ formattedDuration(videoDetail!.duration) }}</span
        >
      </div>
      <div class="flex">
        <VideoDetailItem
          :videoDetail="videoDetail"
          :show-self-clip="showSelfClip"
          :videoType="
            videoState.videoType === 'specialCourse' ? 'special' : 'system'
          "
          @on-preview-click="onPreviewClick"
          @on-insert-click="$emit('onInsertClick')"
          @on-self-clip-click="onSelfClip"
        />
      </div>
      <div
        v-if="videoDetail.keyPoints && videoDetail.keyPoints?.length > 0"
        class="header-box mt-24px"
      >
        <span class="origin-item1" />
        <span class="font-600 font-size-16px color-#393548 ml-8px">切片</span>
      </div>

      <div v-if="videoDetail" class="flex flex-wrap justify-between">
        <div
          v-for="slice in videoDetail.keyPoints"
          :key="slice.id"
          class="mb-16px font-size-12px color-#393548;"
        >
          <div class="sub-title mb-8px mt-16px">
            {{ slice.timeline }}｜时长：{{ slice.durationFormat }}
          </div>
          <VideoDetailItem
            :slice="slice"
            :show-self-clip="false"
            :videoDetail="videoDetail"
            videoType="system-point"
            @on-preview-click="onPreviewClick"
            @on-insert-click="
              (isClip: boolean, clipId: string, cover: string, name: string) =>
                onInsertKeypoint(slice, clipId, cover, name)
            "
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'
import useSideResourceStore from '../store'
import VideoDetailItem from './VideoDetailItem.vue'
import { formattedDuration } from '@/pages/microVideo/service'
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWButton,
} from '@guanghe-pub/onion-ui-web'
import TopicVideoPractice from './TopicVideoPractice.vue'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import type { TopicDetail } from '@/pages/microVideo/service'

defineProps<{
  showSelfClip: boolean
  showInsertSelect: boolean
}>()

const emits = defineEmits<{
  (e: 'onEasyInsert'): void
  (e: 'onPreviewClick', clipId?: string): void
  (e: 'onProblemInsertClick', problem: ProblemDetailType, value?: number): void
  (e: 'onInsertClick'): void
  (
    e: 'onInsertKeypoint',
    val: YcType.KeyPoint,
    clipId: string,
    cover: string,
    name: string,
  ): void
  (
    e: 'onSelfClip',
    type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
    item?: TopicDetail | SchoolVideoDetail,
  ): void
}>()

const store = useSideResourceStore()
const { videoState, csv, videoDetail } = storeToRefs(store)
const onUpdateDetailType = (val: 'video' | 'practice') => {
  store.$patch((state) => {
    state.videoState.detailType = val
  })
}

const onPreviewClick = (isClip?: boolean, clipId?: string) => {
  emits('onPreviewClick', clipId)
}
const onProblemInsertClick = (problem: ProblemDetailType, value?: number) => {
  emits('onProblemInsertClick', problem, value)
}

const onInsertKeypoint = (
  slice: YcType.KeyPoint,
  clipId: string,
  cover: string,
  name: string,
) => {
  emits('onInsertKeypoint', slice, clipId, cover, name)
}

const onSelfClip = () => {
  emits(
    'onSelfClip',
    videoState.value.videoType as
      | 'specialCourse'
      | 'systemLesson'
      | 'schoolVideo',
    { ...(videoState.value.videoTopicDetail as TopicDetail), ...csv.value },
  )
}
</script>

<style scoped lang="scss">
.header-box {
  display: flex;
  align-items: center;

  .origin-item {
    display: inline-block;
    width: 6px;
    height: 16px;

    /* 橙（标签色） */
    background: #fea345;
    border-radius: 62px;
    opacity: 1;
  }

  .origin-item1 {
    display: inline-block;
    width: 6px;
    height: 16px;
    background: #7b66ff;
    border-radius: 62px;
    opacity: 1;
  }
}

.sub-title {
  display: flex;
  align-items: center;
  color: #393548;

  &::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    content: '';

    /* 紫（图标色、标签底色） */
    background: #7b66ff;
    border-radius: 50%;
    opacity: 1;
  }
}
</style>
