<template>
  <OIWLoading
    v-if="videoState.listLoading"
    :show="videoState.listLoading"
    width="200px"
    height="200px"
  />
  <div v-else>
    <div
      v-if="videoState.videoList && videoState.videoList.length"
      :class="{
        'micro-video-list': true,
        'micro-video-list-search': isSearch,
      }"
    >
      <div class="micro-video-content">
        <div v-for="(item, index) in videoState.videoList" :key="index">
          <BriefDesc
            :briefData="item"
            type="microVideo"
            :check-value="videoState.videoType"
            :auth="
              videoState.videoType === 'systemLesson' ? systemAuth : specialAuth
            "
            :show-self-clip="showSelfClip"
            @preview="handlePreview"
            @collect="handleCollect"
            @insert="handleInsert"
            @self-clip="handleSelfClip"
          />
        </div>
      </div>
    </div>
    <div v-else class="micro-empty-box">
      <OIWStateBlock type="empty" title="暂无内容" />
    </div>
  </div>
  <n-space
    v-if="
      videoState.topicList &&
      videoState.topicList.length &&
      videoState.topicList.length > videoState.pageSize
    "
    justify="end"
    style="margin-top: 20px"
  >
    <n-pagination
      v-if="videoState.topicList.length > videoState.pageSize"
      v-model:page="videoState.page"
      v-model:page-size="videoState.pageSize"
      class="custom-pagination"
      :item-count="videoState.topicList.length"
    />
  </n-space>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  OIWLoading,
  OIWStateBlock,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import {
  systemLessonPermission,
  specialCoursePermission,
} from '@/hooks/usePermission'
import type { TopicDetail } from '@/pages/microVideo/service'
import { postFavorite, deleteFavoriteApi } from '@/pages/microVideo/service'
import BriefDesc from '../components/BriefDesc.vue'
import { buryPoint } from '@/utils/buryPoint'
import { getQueryString } from '@guanghe-pub/onion-utils'
import type { VideoPlayType } from '../store'
import useSideResourceStore from '../store'

defineProps<{
  showSelfClip: boolean
}>()

const emits = defineEmits<{
  (e: 'update:page', val: number): void
  (e: 'update:show', val: boolean): void
  (e: 'onItemClick', data: VideoPlayType): void
  (e: 'onPreviewClick', data: VideoPlayType): void
  (e: 'onInsertClick', data: VideoPlayType): void
  (
    e: 'onSelfClip',
    data: VideoPlayType,
    type: 'systemLesson' | 'specialCourse',
    item: TopicDetail,
  ): void
}>()

const store = useSideResourceStore()
const { videoState, csv, specialCourseId } = storeToRefs(store)
const topicList = computed(() => videoState.value.topicList)
const checkValue = computed(() => videoState.value.videoType)
const subjectId = computed(() => csv.value?.subjectId)
const stageId = computed(() => csv.value?.stageId)
const publisherId = computed(() => csv.value?.publisherId)
const semesterId = computed(() => csv.value?.semesterId)
const isSearch = computed(() => !!videoState.value.searchValue)
const message = useOIWMessage()

const systemAuth = ref(false) // 同步课权限
const specialAuth = ref(false) // 培优课权限

const initData = async () => {
  if (videoState.value.videoType === 'systemLesson') {
    systemAuth.value = await systemLessonPermission({
      subjectId: subjectId.value!,
      stageId: stageId.value!,
    })
  } else {
    specialAuth.value = (await specialCoursePermission(
      specialCourseId.value!,
    )) as boolean
  }
}

watch(
  () => topicList.value,
  () => {
    initData()
  },
  {
    immediate: true,
  },
)

const handlePreview = (item: TopicDetail) => {
  if (checkValue.value === 'systemLesson') {
    if (!item.isFreeTime && item.pay && !systemAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  } else {
    if (!item.isFreeTime && item.pay && !specialAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  }
  let data: VideoPlayType = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (checkValue.value === 'specialCourse') {
    data.specialCourseId = specialCourseId.value
  }
  emits('onPreviewClick', data)
}

const handleInsert = (item: TopicDetail) => {
  if (checkValue.value === 'systemLesson') {
    if (!item.isFreeTime && item.pay && !systemAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  } else {
    if (!item.isFreeTime && item.pay && !specialAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  }
  let data: VideoPlayType = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (checkValue.value === 'specialCourse') {
    data.specialCourseId = specialCourseId.value
  }
  emits('onInsertClick', data)
}

const handleSelfClip = (item: TopicDetail) => {
  if (checkValue.value === 'systemLesson') {
    if (!item.isFreeTime && item.pay && !systemAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  } else {
    if (!item.isFreeTime && item.pay && !specialAuth.value) {
      message.warning(
        '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
      )
      return
    }
  }
  let data: VideoPlayType = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (checkValue.value === 'specialCourse') {
    data.specialCourseId = specialCourseId.value
  }
  emits('onSelfClip', data, checkValue.value, item)
}

// 因为添加hover事件，所以收藏不会触发
const handleCollect = async (item: TopicDetail) => {
  const isFavorite = !item.isFavorite
  if (isFavorite) {
    const data = await postFavorite({
      resourceType: 'preview',
      resourceSubType:
        checkValue.value === 'systemLesson' ? 'system' : 'special',
      resourceSubId:
        checkValue.value === 'specialCourse'
          ? specialCourseId.value
          : undefined,
      resourceId: item.id,
      resourceName: item.name,
      subject: subjectId.value?.toString() || '',
      stage: stageId.value?.toString() || '',
      publisher: publisherId.value?.toString() || '',
      semester: semesterId.value?.toString() || '',
    })
    if (data) {
      message.success('微课收藏成功，请前往我的收藏查看')
    }
  } else {
    await deleteFavoriteApi({
      resourceId: item.id,
      resourceSubId:
        checkValue.value === 'specialCourse'
          ? specialCourseId.value
          : undefined,
    })
    message.error('微课收藏取消')
  }
  store.fetchVideoList(false)

  if (checkValue.value === 'systemLesson') {
    buryPoint('click_prepareCenter_collect2', {
      subjectId: parseInt(subjectId.value?.toString() || ''),
      stageId: parseInt(stageId.value?.toString() || ''),
      publisherId: parseInt(publisherId.value?.toString() || ''),
      semesterId: parseInt(semesterId.value?.toString() || ''),
      videoId: item.id,
      courseId: getQueryString('fileId'),
    })
  } else {
    buryPoint('click_prepareCenter_collect4', {
      subjectId: parseInt(subjectId.value?.toString() || ''),
      stageId: parseInt(stageId.value?.toString() || ''),
      videoId: item.id,
      packageId: specialCourseId.value,
      courseId: getQueryString('fileId'),
    })
  }
}
</script>

<style lang="scss" scoped>
.micro-video-list {
  height: 100%;

  &.micro-video-list-search {
    height: auto;
    min-height: 50%;
    padding-right: 0;
    padding-left: 0;
  }
}

.micro-empty-box {
  margin-top: 100px;
}

.micro-video-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
</style>
