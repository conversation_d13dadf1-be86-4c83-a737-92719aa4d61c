<template>
  <div v-if="schoolVideoDetail" closable class="drawer-content mt-16px">
    <div class="flex items-center justify-between">
      <OIWRadioButtonGroup
        :value="schoolVideoState.detailType"
        @update:value="onUpdateDetailType"
      >
        <OIWRadioButton value="video">微课</OIWRadioButton>
        <OIWRadioButton value="practice">配套题目</OIWRadioButton>
      </OIWRadioButtonGroup>
      <n-tooltip trigger="hover">
        <template #trigger>
          <OIWButton round @click="onSchoolEasyInsert"> 一键插入 </OIWButton>
        </template>
        插入推荐的微课及题目
      </n-tooltip>
    </div>

    <div v-if="schoolVideoState.detailType === 'video'">
      <div class="header-box mb-16px mt-12px">
        <span class="origin-item" />
        <span class="font-600 font-size-16px color-#393548 ml-8px"
          >完整微课</span
        >
        <span class="grey-text font-size-12px color-#9792AC mt-2px"
          >｜时长{{ formattedDuration(schoolVideoDetail!.duration) }}</span
        >
      </div>
      <div class="flex">
        <VideoDetailItem
          :schoolVideo="schoolVideoDetail"
          videoType="school"
          :show-self-clip="showSelfClip"
          @on-preview-click="onPreviewClick"
          @on-insert-click="onInsertClick"
          @on-self-clip-click="() => onSelfClip(schoolVideoDetail)"
        />
      </div>
    </div>
    <div v-if="schoolVideoState.detailType === 'practice'">
      <SchoolVideoPractice
        :is-show-insert-select="showInsertSelect"
        @insert-problem="onProblemInsertClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'
import useSideResourceStore from '../store'
import VideoDetailItem from './VideoDetailItem.vue'
import SchoolVideoPractice from './SchoolVideoPractice.vue'
import { formattedDuration } from '@/pages/microVideo/service'
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWButton,
} from '@guanghe-pub/onion-ui-web'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import { useProblem } from '@/hooks/useProblem'

defineProps<{
  showSelfClip: boolean
  showInsertSelect: boolean
}>()

const emits = defineEmits<{
  (e: 'onEasyInsert', problems: ProblemDetailType[]): void
  (e: 'onPreviewClick'): void
  (e: 'onProblemInsertClick', problem: ProblemDetailType, value?: number): void
  (e: 'onInsertClick'): void
  (
    e: 'onSelfClip',
    type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
    item?: SchoolVideoDetail,
  ): void
}>()

const store = useSideResourceStore()
const { schoolVideoState, schoolVideoDetail } = storeToRefs(store)
const { fetchDetails } = useProblem()
const onUpdateDetailType = (val: 'video' | 'practice') => {
  store.$patch((state) => {
    state.schoolVideoState.detailType = val
  })
}
const onPreviewClick = async () => {
  emits('onPreviewClick')
}

const onInsertClick = () => {
  emits('onInsertClick')
}

const onProblemInsertClick = (problem: ProblemDetailType, value?: number) => {
  emits('onProblemInsertClick', problem, value)
}

// 校本资源一键插入
const onSchoolEasyInsert = async () => {
  const problems = await getProblemDetail()
  emits('onEasyInsert', problems)
}

async function getProblemDetail() {
  if (
    !schoolVideoDetail.value?.problems ||
    !schoolVideoDetail.value?.problems.length
  ) {
    return []
  }

  // 获取当前页的数据
  const currentPageItems = schoolVideoDetail.value?.problems

  return (await fetchDetails(currentPageItems)) as ProblemDetailType[]
}

const onSelfClip = (item?: SchoolVideoDetail) => {
  store.$patch((state) => {
    if (!item) return
    state.schoolVideoState.videoId = item.id
    state.schoolVideoState.videoInfo = item
  })
  emits('onSelfClip', 'schoolVideo', item)
}
</script>

<style scoped lang="scss">
.header-box {
  display: flex;
  align-items: center;

  .origin-item {
    display: inline-block;
    width: 6px;
    height: 16px;

    /* 橙（标签色） */
    background: #fea345;
    border-radius: 62px;
    opacity: 1;
  }

  .origin-item1 {
    display: inline-block;
    width: 6px;
    height: 16px;
    background: #7b66ff;
    border-radius: 62px;
    opacity: 1;
  }
}

.sub-title {
  display: flex;
  align-items: center;
  color: #393548;

  &::before {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    content: '';

    /* 紫（图标色、标签底色） */
    background: #7b66ff;
    border-radius: 50%;
    opacity: 1;
  }
}
</style>
