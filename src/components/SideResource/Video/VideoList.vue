<template>
  <div title="微课及配套题目" closable class="drawer-content">
    <div class="micro-video-main">
      <n-space justify="space-between" align="center" class="mb-16px">
        <div v-if="!isSearch">
          <OIWRadioButtonGroup
            :value="videoType"
            :on-update-value="store.updateVideoType"
          >
            <OIWRadioButton value="systemLesson">同步课</OIWRadioButton>
            <OIWRadioButton v-if="!isFromDingDing" value="specialCourse"
              >培优课</OIWRadioButton
            >
          </OIWRadioButtonGroup>
        </div>
        <div class="search-input">
          <OIWInput
            :value="searchValue"
            :maxlength="50"
            clearable
            placeholder="输入搜索关键词搜索资源"
            @update:value="searchValueChange"
          >
            <template #suffix>
              <Search theme="outline" size="16" fill="#C5C1D4" />
            </template>
          </OIWInput>
        </div>
      </n-space>
      <template v-if="!isSearch">
        <div>
          <SystemTree
            v-if="videoType === 'systemLesson'"
            local-sync-key="csvMicroVideo"
            :select-id="searchSelectId || ''"
            @change="store.videoTreeTopicChange"
            @tree-data-load="store.videoTreeDataChange"
          />
          <SpecialTree
            v-else
            :select-id="searchSelectId || ''"
            local-sync-key="csvSpecialCourse"
            :specialCourseId="specialCourseId"
            @change="store.videoTreeTopicChange"
            @tree-data-load="store.videoTreeDataChange"
            @change-special-id="store.videoTreeSpecialIdChange"
          />
        </div>
      </template>
      <template v-else>
        <div class="search-chapters-box">
          <div class="search-title-box">
            教材章节搜索结果<span class="grey-text"
              >｜搜索到如下教材章节，点击章节名称查看该章节所有微课</span
            >
          </div>
          <div
            v-if="videoState.searchList.length"
            class="search-chapters-content"
          >
            <div
              v-for="item in videoState.searchList"
              :key="item.id"
              class="search-chapters-list"
              @click="store.changeSearch(item.id || '')"
            >
              <div class="chapters-name">
                {{ item.name }}
              </div>
              <div class="chapters-count">
                （{{ item.coursewareCount }}个微课）
              </div>
            </div>
          </div>
          <div v-else class="search-chapters-content">
            <div class="search-chapters-list search-chapters-null">
              <div class="chapters-count">暂无数据</div>
            </div>
          </div>
        </div>
        <div class="search-title-box">微课搜索结果</div>
      </template>

      <MicroVideoList
        :show-self-clip="showSelfClip"
        @on-insert-click="handelInsertClick"
        @on-preview-click="handlePreviewClick"
        @on-self-clip="handleSelfClipShow"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@icon-park/vue-next'
import type { VideoPlayType } from '../store'
import useSideResourceStore from '../store'
import {
  OIWRadioButtonGroup,
  OIWRadioButton,
  OIWInput,
} from '@guanghe-pub/onion-ui-web'
import type { TopicDetail } from '@/pages/microVideo/service'
import { postSystemTopicDetails } from '@/pages/microVideo/service'
import MicroVideoList from './MicroVideoList.vue'
import SystemTree from '../components/SystemTree.vue'
import SpecialTree from '../components/SpecialTree.vue'
import { useAuth } from '@/hooks/useAuth'

defineProps<{
  showSelfClip: boolean
}>()

const emits = defineEmits<{
  (e: 'onPreviewClick', clipId?: string): void
  (e: 'onInsertClick'): void
  (
    e: 'onSelfClipClick',
    type: 'systemLesson' | 'specialCourse',
    item: TopicDetail,
  ): void
}>()
const store = useSideResourceStore()
const { videoState, specialCourseId, csv } = storeToRefs(store)
const { isFromDingDing } = useAuth()
const videoType = computed(() => videoState.value.videoType)
const isSearch = computed(() => !!videoState.value.searchValue)
const searchValue = computed(() => videoState.value.searchValue)
const searchSelectId = computed(() => videoState.value.searchSelectId)
const searchValueChange = (val: string) => {
  store.$patch((state) => {
    state.videoState.searchValue = val
  })
}

const handelInsertClick = async (data: VideoPlayType) => {
  store.$patch((state) => {
    state.videoState.detailType = 'video'
    if (data.specialCourseId) {
      state.videoState.videoPlayInfo = data
    } else {
      state.videoState.videoPlayInfo.topicId = data.topicId
      state.videoState.videoPlayInfo.specialCourseId = undefined
      state.videoState.videoPlayInfo.clipId = undefined
    }
  })
  const topicDetail = (
    await postSystemTopicDetails({ topicIds: [data.topicId] })
  ).data[0] as TopicDetail
  await store.$patch((state) => {
    state.videoState.videoTopicDetail = topicDetail
  })
  if (
    topicDetail.levelNum > 0 ||
    (topicDetail.video.keyPoints && topicDetail.video.keyPoints.length >= 1)
  ) {
    store.$patch((state) => {
      state.videoState.pageType = 'detail'
    })
  } else {
    emits('onInsertClick')
  }
}

const handlePreviewClick = (data: VideoPlayType) => {
  store.$patch((state) => {
    if (data.specialCourseId) {
      state.videoState.videoPlayInfo = data
    } else {
      state.videoState.videoPlayInfo.topicId = data.topicId
      state.videoState.videoPlayInfo.specialCourseId = undefined
      state.videoState.videoPlayInfo.clipId = undefined
    }
  })
  emits('onPreviewClick')
}

const handleSelfClipShow = (
  data: VideoPlayType,
  type: 'systemLesson' | 'specialCourse',
  item: TopicDetail,
) => {
  store.$patch((state) => {
    if (data.specialCourseId) {
      state.videoState.videoPlayInfo = data
    } else {
      state.videoState.videoPlayInfo.topicId = data.topicId
      state.videoState.videoPlayInfo.specialCourseId = undefined
      state.videoState.videoPlayInfo.clipId = undefined
    }
  })
  emits('onSelfClipClick', type, { ...item, ...csv.value })
}
</script>

<style scoped lang="scss">
.search-chapters-box {
  margin-top: 20px;
  margin-bottom: 32px;
  box-shadow: inset 0px -1px 0px 0px #dcd8e7;

  .search-chapters-content {
    padding-bottom: 16px;

    .search-chapters-list {
      display: flex;
      align-items: center;
      padding: 16px;

      &:hover {
        background: #f4f6ff;
        border-radius: 12px;
      }

      &.search-chapters-null {
        padding-top: 20px;
        padding-bottom: 20px;

        &:hover {
          background: #fff;
          border-radius: 0;
        }
      }

      .chapters-name {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #5e80ff;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }

      .chapters-count {
        margin-left: 4px;
        font-size: 16px;
        line-height: 24px;
        color: #393548;
      }
    }
  }
}

.search-title-box {
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: #393548;

  &.mb-24px {
    margin-bottom: 24px;
  }

  .grey-text {
    font-size: 14px;
    font-weight: 400;
    color: #9792ac;
  }
}

.micro-video-main {
  min-height: 90%;
  padding-top: 16px;
  margin: 0 auto;

  .search-input {
    width: 240px;

    .oiw-input_default.n-input {
      height: 44px;
      font-size: 15px;

      ::v-deep(.n-input__border) {
        border: 1px solid rgb(224, 224, 230);
        border-radius: 12px;
      }
    }

    ::v-deep(
        .oiw-input_default.n-input:not(.n-input--disabled):hover
          .n-input__state-border
      ) {
      border-radius: 12px;
    }
  }

  .content-grid {
    position: relative;
    height: 100%;
    padding-bottom: 91px;
    margin-top: 24px;
  }
}
</style>
