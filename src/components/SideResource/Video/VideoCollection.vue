<template>
  <div class="mt-16px">
    <div class="flex items-center justify-between">
      <div class="w-240px">
        <OIWInput
          v-model:value="searchValue"
          class="h-40px"
          placeholder="输入搜索关键词搜索资源"
          :maxlength="50"
        >
          <template #suffix>
            <Search theme="outline" size="16" fill="#C5C1D4" />
          </template>
        </OIWInput>
      </div>
      <div class="w-240px">
        <OIWSelect
          v-model:value="selectValue"
          :options="selectOptions"
          :consistent-menu-width="false"
          placeholder="选择类型"
        ></OIWSelect>
      </div>
    </div>
    <CollectionList
      v-if="selectValue === 'myCollection'"
      :topic-list="collectionList"
      :show-self-clip="showSelfClip"
      :total="total"
      @on-insert-click="handelInsertClick"
      @on-preview-click="handelPreviewClick"
      @on-self-clip="handelSelfClip"
    />
    <UpdateList
      v-if="selectValue === 'myUpload'"
      :topic-list="userUpdateList"
      :show-self-clip="showSelfClip"
      :total="total"
      @on-insert-click="handelInsertClick"
      @on-preview-click="handelPreviewClick"
      @on-self-clip="handelSelfClip"
    />
    <VideoModal
      v-model:show="showVideoModal"
      :clip-id="videoPlayInfo.clipId"
      :special-course-id="videoPlayInfo.specialCourseId"
      :topic-id="videoPlayInfo.topicId"
      :to="to"
    />
    <VideoModalMp4
      v-model:show="schoolPreviewModalShow"
      :to="to"
      :video-id="schoolVideoPlayInfo?.id || ''"
    />
  </div>
</template>

<script setup lang="ts">
import { Search } from '@icon-park/vue-next'
import CollectionList from './CollectionList.vue'
import UpdateList from './UpdateList.vue'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import type {
  TopicDetail,
  ExamPreviewProblemProblem,
} from '@/pages/microVideo/service'
import { getTeacherDeskFavoriteList } from '@/pages/microVideo/service'
import { getSchoolVideoListApi } from '@/pages/schoolResource/schoolVideo/service'
import type { InsertParams } from '../types'
import type { VideoPlayType } from '../store'
import { OIWInput, OIWSelect } from '@guanghe-pub/onion-ui-web'
import { debounce } from 'lodash-es'
import VideoModal from './VideoModal.vue'
import VideoModalMp4 from '@/components/VideoPlayer/VideoModalMp4.vue'
import { useAuth } from '@/hooks/useAuth'
import useSideResourceStore from '@/components/SideResource/store'

defineProps<{
  showSelfClip: boolean
  to?: string
}>()

const emits = defineEmits<{
  (e: 'insertVideo', val: InsertParams): void
  (
    e: 'onSelfClip',
    type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
    item: TopicDetail | SchoolVideoDetail,
  ): void
}>()
const store = useSideResourceStore()
const { userId } = useAuth()
const searchValue = ref<string>('')
const selectValue = ref<'myCollection' | 'myUpload'>('myCollection')
const selectOptions: {
  label: string
  value: 'myCollection' | 'myUpload'
}[] = [
  {
    label: '我的收藏',
    value: 'myCollection',
  },
  {
    label: '我的上传',
    value: 'myUpload',
  },
]

const collectionList = ref<ExamPreviewProblemProblem[]>([])
const userUpdateList = ref<SchoolVideoDetail[]>([])
const topicList = computed(() => {
  return selectValue.value === 'myCollection'
    ? collectionList.value
    : userUpdateList.value
})
const total = computed(() => {
  return selectValue.value === 'myCollection'
    ? collectionList.value.length
    : topicList.value.length
})

const showVideoModal = ref(false)
const schoolPreviewModalShow = ref(false)

const handelInsertClick = (data: InsertParams) => {
  emits('insertVideo', data)
}

const videoPlayInfo = ref<VideoPlayType>({
  topicId: '',
  specialCourseId: undefined,
  clipId: undefined,
})

const schoolVideoPlayInfo = ref<SchoolVideoDetail | undefined>()

// 预览
const handelPreviewClick = async (
  data: VideoPlayType | SchoolVideoDetail,
  type: 'school' | 'cb',
) => {
  if (type === 'cb') {
    videoPlayInfo.value = data as VideoPlayType
    showVideoModal.value = true
  }
  if (type === 'school') {
    schoolVideoPlayInfo.value = data as SchoolVideoDetail
    schoolPreviewModalShow.value = true
  }
}

const handelSelfClip = async (
  data: TopicDetail | SchoolVideoDetail,
  type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
) => {
  store.$patch((state) => {
    if (data.resourceSubType === 'school') {
      state.schoolVideoState.videoId = (data as SchoolVideoDetail).id
      state.schoolVideoState.videoInfo = data as SchoolVideoDetail
    } else if (data.resourceSubType === 'special') {
      state.videoState.videoPlayInfo.topicId = data.resourceId as string
      state.videoState.videoPlayInfo.specialCourseId = data.resourceSubId
      state.videoState.videoPlayInfo.clipId = undefined
    } else {
      state.videoState.videoPlayInfo.topicId = data.resourceId as string
      state.videoState.videoPlayInfo.specialCourseId = undefined
      state.videoState.videoPlayInfo.clipId = undefined
    }
  })
  emits('onSelfClip', type, data)
}

const getCollectionList = async () => {
  const res = await getTeacherDeskFavoriteList({
    resourceType: 'preview,school_preview',
    resourceName: searchValue.value,
    pageSize: 10000,
    page: 1,
  })
  if (res) {
    collectionList.value = res.data || []
  }
}

const getUserUpdateList = async () => {
  const res = await getSchoolVideoListApi({
    createdBy: userId.value,
    name: searchValue.value,
    pageSize: 10000,
    page: 1,
  })
  if (res) {
    userUpdateList.value = res.schoolVideos || []
  }
}

const fetch = () => {
  if (selectValue.value === 'myCollection') {
    getCollectionList()
  } else {
    getUserUpdateList()
  }
}
const throttledGetCollectionList = debounce(fetch, 500)
watch(
  () => searchValue.value,
  () => {
    collectionList.value = []
    userUpdateList.value = []
    throttledGetCollectionList()
  },
  {
    immediate: true,
  },
)
watch(
  () => selectValue.value,
  () => {
    collectionList.value = []
    userUpdateList.value = []
    throttledGetCollectionList()
  },
)
</script>
