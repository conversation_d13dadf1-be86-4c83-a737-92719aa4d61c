<template>
  <div class="bg-#F4F6FF rounded-16px">
    <div
      class="brief-desc-header group relative"
      :style="{
        backgroundImage: `url(${videoCover})`,
      }"
    >
      <div
        class="hidden group-hover:block left-0 absolute top-0 right-0 bottom-0 rounded-12px"
      >
        <div
          class="rounded-12px h-100% w-100% px-31px flex *:min-w-45px *:inline-flex *:flex-col *:items-center *:cursor-pointer bg-#12254d/60 pt-40px backdrop-blur-4px"
          :class="{
            'justify-center': !showSelfClip,
            'gap-32px': !showSelfClip,
            'justify-between': showSelfClip,
          }"
        >
          <div @click="previewClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <EyeIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">预览</div>
          </div>
          <div @click="insertClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <AddIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">插入</div>
          </div>
          <div v-if="showSelfClip" @click="onSelfClipClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <FolderIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">自定义插入</div>
          </div>
        </div>
      </div>
    </div>
    <div class="brief-desc-content">
      <div
        class="font-600 font-size-16px color-#393548 ml-8px text-ellipsis w-208px"
      >
        {{
          videoType === 'system-point'
            ? slice?.content
            : videoDetail?.name || schoolVideo?.name || ''
        }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useVideoCover } from '@/hooks/useVideoCover'
import FolderIcon from '~icons/yc/folder-add'
import EyeIcon from '~icons/yc/eye'
import AddIcon from '~icons/yc/self-add'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'

const props = defineProps<{
  videoDetail?: YcType.VideoType
  schoolVideo?: SchoolVideoDetail
  slice?: YcType.KeyPoint
  videoType: 'system-point' | 'system' | 'special' | 'school'
  showSelfClip: boolean
}>()
const emits = defineEmits<{
  (e: 'onPreviewClick', isClip: boolean, clipId?: string, time?: number): void
  (
    e: 'onInsertClick',
    isClip: boolean,
    clipId: string,
    cover: string,
    name: string,
  ): void
  (e: 'onSelfClipClick', item?: YcType.VideoType | SchoolVideoDetail): void
}>()

const { videoCover } = useVideoCover({
  video: props.videoDetail as YcType.VideoType,
  resourceSubId: props.slice?.id || '',
  resourceType: props.videoType,
  schoolVideo: props.schoolVideo,
})

const previewClick = () => {
  if (
    props.videoType === 'system' ||
    props.videoType === 'special' ||
    props.videoType === 'school'
  ) {
    emits('onPreviewClick', false)
  } else if (props.videoType === 'system-point') {
    emits('onPreviewClick', true, props.slice?.id, props.slice?.time)
  }
}

const insertClick = () => {
  if (
    props.videoType === 'system' ||
    props.videoType === 'special' ||
    props.videoType === 'school'
  ) {
    emits(
      'onInsertClick',
      false,
      '',
      props.videoDetail?.cover || '',
      '点击播放：' + props.videoDetail?.name,
    )
  } else if (props.videoType === 'system-point') {
    emits(
      'onInsertClick',
      true,
      props.slice?.id || '',
      videoCover.value,
      '点击播放：' + props.videoDetail?.name + '-' + props.slice?.content,
    )
  }
}

const onSelfClipClick = () => {
  emits('onSelfClipClick', props.videoDetail || props.schoolVideo)
}
</script>

<style lang="scss" scoped>
.brief-desc-header {
  width: 240px;
  height: 135px;
  padding: 12px;
  background-size: cover;
  border-radius: 16px;

  .head-info {
    .label {
      padding: 6px 10px;
      font-size: 12px;
      font-weight: 500;
      line-height: 12px;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px;
    }

    .collect-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: rgba(0, 0, 0, 0.5)
        url('https://fp.yangcong345.com/onion-extension/111-0c2ec3eea9dc435a0f02c6a5c58ee85d.png')
        no-repeat;
      background-position: 4px 3px;
      background-size: 16px 16px;
      border-radius: 8px;

      &.active {
        background: rgba(0, 0, 0, 0.5)
          url('https://fp.yangcong345.com/onion-extension/333-f996430c0cc4ec2527195dd525f2beb5.png')
          no-repeat;
        background-position: 4px 3px;
        background-size: 16px 16px;
      }
    }
  }
}

.brief-desc-content {
  padding: 12px;

  .title {
    overflow: hidden;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    color: #393548;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.grey {
      font-size: 16px;
      font-weight: 500;
      line-height: 18px;
      color: #c5c1d4;
    }
  }

  .info-list {
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
    color: #8a869e;
  }
}
</style>
