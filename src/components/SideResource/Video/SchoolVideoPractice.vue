<template>
  <div class="text-size-14px">
    <OIWLoading v-if="loading" :show="loading" width="200px" height="200px" />
    <div v-else>
      <template v-if="problems.length">
        <ProblemItem
          v-for="(problem, index) in problems"
          :key="problem.id"
          :problem="problem"
          :index="index + 1 + (page - 1) * 10"
          class="border-b border-gray-200 last:border-none"
          :showSchoolAiExplainButton="
            problem.problemType === 'ItemTypeSchoolProblem'
          "
          :showInsertButton="!isShowInsertSelect"
          :insertCount="problemsCount[problem.id]"
          @on-insert="clickInsert(problem)"
        >
          <template #insert>
            <n-popselect
              v-if="isShowInsertSelect"
              v-model:value="insertVal"
              :options="insertOptions"
              trigger="click"
              @update:value="(value) => clickInsert(problem, value)"
            >
              <span
                class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
                @click="clickInsertTip"
              >
                <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
                插入
              </span>
            </n-popselect>
          </template>
        </ProblemItem>
        <div class="mt-59px flex justify-end">
          <n-pagination
            class="custom-pagination"
            :page="page"
            :page-size="10"
            :item-count="total"
            @update:page="onPageChange"
          />
        </div>
      </template>
      <template v-else>
        <OIWStateBlock
          type="empty"
          style="margin-top: 200px"
          title="暂无资源"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="tsx">
import ProblemItem from '@/components/ProblemSingle/index.vue'
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'
import { OIWLoading, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import useSideResourceStore from '@/components/SideResource/store'
import AddIcon from '~icons/yc/add'
import { useOIWDialog } from '@guanghe-pub/onion-ui-web'
import { useProblem } from '@/hooks/useProblem'

defineProps<{
  isShowInsertSelect?: boolean
}>()

const emits =
  defineEmits<
    (e: 'insertProblem', problem: ProblemDetailType, value?: number) => void
  >()

const { fetchDetails } = useProblem()

const loading = ref(false)
const store = useSideResourceStore()
const { schoolVideoDetail, problemsCount } = storeToRefs(store)
const problems = ref<ProblemDetailType[]>([])
const page = ref(1)
const total = computed(() => schoolVideoDetail.value?.problems?.length || 0)

const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])
onMounted(() => {
  getProblemDetail()
})

async function getProblemDetail() {
  if (
    !schoolVideoDetail.value?.problems ||
    !schoolVideoDetail.value?.problems.length
  ) {
    return
  }
  loading.value = true
  problems.value = []

  // 计算当前页的起始和结束索引
  const startIndex = (page.value - 1) * 10
  const endIndex = startIndex + 10

  // 获取当前页的数据
  const currentPageItems = schoolVideoDetail.value?.problems.slice(
    startIndex,
    endIndex,
  )
  problems.value = (await fetchDetails(currentPageItems)) as ProblemDetailType[]
  loading.value = false
}

const onPageChange = (index: number) => {
  page.value = index
  getProblemDetail()
}

const clickInsert = (problem: ProblemDetailType, value?: number) => {
  emits('insertProblem', problem, value)
}
const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}
</script>
