<template>
  <div class="">
    <div class="mt-16px">
      <OIWInput
        v-model:value="searchValue"
        class="h-40px"
        placeholder="输入搜索关键词搜索资源"
        :maxlength="50"
        clearable
      >
        <template #suffix>
          <Search theme="outline" size="16" fill="#C5C1D4" />
        </template>
      </OIWInput>
    </div>
    <div class="mt-12px">
      <Tree
        show-all-label
        local-sync-key="csvMicroVideo"
        is-school-resource
        @change="handleNodeChange"
      />
    </div>
    <div class="mt-12px w-100% flex items-center justify-between mb-12px">
      <div class="flex items-center w-49%">
        <span class="flex-shrink-0 mr-8px">来自:</span>
        <OIWSelect
          :value="createdBy"
          :options="createdByOptions"
          filterable
          :consistent-menu-width="false"
          placeholder="选择分享人"
          @update:value="onCreatedByChange"
        ></OIWSelect>
      </div>
    </div>
    <OIWLoading width="200px" height="200px" :show="loading">
      <template v-if="videoList.length">
        <div class="micro-video-content mt-16px">
          <template v-for="video of videoList" :key="video.id">
            <SchoolVideoCard
              :videoData="video"
              :show-self-clip="showSelfClip"
              @insert="() => onInsert(video)"
              @preview="() => onPreview(video)"
              @self-clip="() => onSelfClip(video)"
            />
          </template>
        </div>
      </template>
      <OIWStateBlock
        v-else
        class="mt-120px text-center"
        type="empty"
        title="暂无资源"
      />
    </OIWLoading>
    <n-space v-if="totalPage > 1" justify="end" style="margin-top: 20px">
      <n-pagination
        v-model:page="page"
        v-model:page-size="pageSize"
        class="custom-pagination"
        :page-count="totalPage"
      />
    </n-space>
  </div>
</template>

<script setup lang="ts">
import Tree from '../components/SystemTree.vue'

import {
  OIWInput,
  OIWLoading,
  OIWStateBlock,
  OIWSelect,
} from '@guanghe-pub/onion-ui-web'
import { useLoading } from '@/hooks/useLoading'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import { getSchoolVideoListApi } from '@/pages/schoolResource/schoolVideo/service'
import SchoolVideoCard from './SchoolVideoCard.vue'
import { debounce } from 'lodash-es'
import { Search } from '@icon-park/vue-next'
import type { ChangeParams } from '@/hooks/useTree'
import useSideResourceStore from '@/components/SideResource/store'
import { useSchoolTeachers } from '@/hooks/useSchoolTeachers'

defineProps<{
  showSelfClip: boolean
}>()
const emits = defineEmits<{
  (e: 'onInsert'): void
  (e: 'onPreview'): void
  (e: 'onSelfClip', type: 'schoolVideo', item: SchoolVideoDetail): void
}>()

const store = useSideResourceStore()
const { createdBy, createdByOptions } = useSchoolTeachers()
const searchValue = ref('')
const { schoolVideoState, schoolCsv } = storeToRefs(store)

const curNodeParams = computed(() => {
  const { schoolNode } = schoolVideoState.value
  if (schoolNode && !searchValue.value) {
    return schoolNode?.type === 'chapter'
      ? {
          chapterId: schoolNode.id,
        }
      : schoolNode?.type === 'section'
        ? {
            sectionId: schoolNode.id,
          }
        : schoolNode?.type === 'subSection'
          ? {
              subsectionId: schoolNode.id,
            }
          : {}
  }
  return {}
})

const handleNodeChange = async (val: ChangeParams) => {
  searchValue.value = ''
  await store.$patch((state) => {
    state.schoolVideoState.schoolNode = val.node
    state.schoolCsv = val.csv
  })
  fetch()
}

const onCreatedByChange = (val: string) => {
  searchValue.value = ''
  createdBy.value = val
  fetch()
}

const { loading } = useLoading()
const videoList = ref<SchoolVideoDetail[]>([])
const totalPage = ref(0)
const page = ref(1)
const pageSize = ref(10)

async function fetch() {
  loading.value = true
  try {
    const data = await getSchoolVideoListApi({
      publisherId: schoolCsv.value?.publisherId,
      semesterId: schoolCsv.value?.semesterId,
      subjectId: schoolCsv.value?.subjectId,
      stageId: schoolCsv.value?.stageId,
      name: searchValue.value,
      page: page.value,
      pageSize: pageSize.value,
      createdBy: createdBy.value,
      ...curNodeParams.value,
    })
    if (data) {
      videoList.value = data.schoolVideos
      totalPage.value = data.totalPage
    }
  } finally {
    loading.value = false
  }
}

const debounceFetch = debounce(fetch, 500)
watch(searchValue, () => {
  page.value = 1
  debounceFetch()
})

const onInsert = async (video: SchoolVideoDetail) => {
  await store.$patch((state) => {
    state.schoolVideoState.videoId = video.id
    state.schoolVideoState.videoInfo = video
  })
  emits('onInsert')
}

const onPreview = async (video: SchoolVideoDetail) => {
  await store.$patch((state) => {
    state.schoolVideoState.videoId = video.id
    state.schoolVideoState.videoInfo = video
  })
  emits('onPreview')
}

const onSelfClip = async (video: SchoolVideoDetail) => {
  await store.$patch((state) => {
    state.schoolVideoState.videoId = video.id
    state.schoolVideoState.videoInfo = video
  })
  emits('onSelfClip', 'schoolVideo', video)
}

watch(page, () => {
  fetch()
})
</script>

<style lang="scss" scoped>
.micro-video-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
</style>
