<template>
  <n-modal
    v-model:show="modalShow"
    preset="card"
    class="video-play-modal"
    closable
    :mask-closable="false"
    style="width: 920px; min-height: 538px"
    :to="to"
  >
    <div class="video-play-box">
      <SimpleVideo
        :topicId="topicId"
        :specialCourseId="specialCourseId"
        :clipId="clipId"
        :buryPointParams="videoBuryPointParams"
        :videoClip="videoClip"
      />
      <CloseIcon
        class="close-round-icon cursor-pointer"
        @click="modalShow = false"
      />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import SimpleVideo from '@/components/VideoPlayer/simpleVideo.vue'
import CloseIcon from '~icons/yc/close-round'
import type { VideoClip } from '@/components/VideoPlayer/service.ts'

const props = defineProps<{
  show: boolean
  topicId: string
  to?: string
  specialCourseId?: string
  clipId?: string
  videoBuryPointParams?: Record<string, any> // 播放器埋点参数
  videoClip?: VideoClip
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    const fullscreenElement = document.fullscreenElement

    if (fullscreenElement) {
      const closeButton = document.createElement('div')
      closeButton.classList.add('video-fullscreen-close-btn')
      closeButton.onclick = () => {
        document.exitFullscreen()
        modalShow.value = false
      }
      fullscreenElement.appendChild(closeButton)
    }
  })
})
</script>

<style lang="scss">
.video-play-modal {
  position: relative;

  .n-base-close {
    display: none;
  }

  .n-card-header {
    padding: 10px !important;
  }
}
</style>

<style lang="scss" scoped>
::v-deep(.video-fullscreen-close-btn) {
  position: absolute;
  right: 20px;
  bottom: 6px;
  width: 40px;
  height: 40px;
  background: url('https://fp.yangcong345.com/middle/1.0.0/40关闭icon@2x-f9c60db4752967db1e7424df240bfd16__w.png')
    no-repeat;
  background-size: 40px 40px;
}

.video-play-box {
  width: 100%;
  max-width: 1200px;
  height: 100%;
  max-height: 675px;
  margin: 10px auto;

  .close-round-icon {
    position: absolute;
    right: -70px;
    bottom: 0px;
  }
}
</style>
