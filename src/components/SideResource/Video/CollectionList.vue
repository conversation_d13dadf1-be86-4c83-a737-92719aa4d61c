<template>
  <OIWLoading v-if="load" :show="load" width="200px" height="200px" />
  <div v-else-if="videoList && videoList.length" class="micro-video-list">
    <div class="micro-video-content">
      <div v-for="(item, index) in videoList" :key="index">
        <BriefDesc
          v-if="item.resourceSubType !== 'school'"
          :briefData="item as TopicDetail"
          :showSelfClip="showSelfClip"
          :auth="item.auth"
          type="collection"
          @preview="handlePreview"
          @insert="handleInsert"
          @self-clip="handleSelfClip"
        />
        <SchoolVideoCard
          v-else
          :showSelfClip="showSelfClip"
          :videoData="item as SchoolVideoDetail"
          @preview="() => handleSchoolPreview(item as SchoolVideoDetail)"
          @insert="() => handleSchoolInsert(item as SchoolVideoDetail)"
          @self-clip="() => handleSchoolSelfClip(item as SchoolVideoDetail)"
        />
      </div>
    </div>
  </div>
  <div v-else class="micro-empty-box">
    <OIWStateBlock type="empty" title="暂无内容" />
  </div>
  <n-space
    v-if="topicList && topicList.length"
    justify="end"
    style="margin-top: 20px"
  >
    <n-pagination
      v-model:page="page"
      v-model:page-size="pageSize"
      class="custom-pagination"
      :item-count="total"
    />
  </n-space>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  OIWLoading,
  OIWStateBlock,
  useOIWDialog,
} from '@guanghe-pub/onion-ui-web'
import type {
  TopicDetail,
  ExamPreviewProblemProblem,
} from '@/pages/microVideo/service'
import {
  systemLessonPermission,
  specialCoursePermission,
} from '@/hooks/usePermission'
import BriefDesc from '../components/BriefDesc.vue'
import type { InsertParams } from '../types'
import { getKeyPointEntryFromId } from '@/utils/videoKeypoint'
import { useVideo } from '@/hooks/useVideo'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import SchoolVideoCard from './SchoolVideoCard.vue'

interface VideoPlay {
  topicId: string
  specialCourseId: string | undefined
  clipId: string | undefined
}
const props = defineProps<{
  topicList: ExamPreviewProblemProblem[]
  total: number
  showSelfClip: boolean
}>()
const emits = defineEmits<{
  (
    e: 'onPreviewClick',
    data: VideoPlay | SchoolVideoDetail,
    type: 'school' | 'cb',
  ): void
  (e: 'onInsertClick', data: InsertParams): void
  (
    e: 'onSelfClip',
    data: TopicDetail | SchoolVideoDetail,
    type: 'systemLesson' | 'specialCourse' | 'schoolVideo',
  ): void
}>()
const { fetchVideoDetails } = useVideo()
const dialog = useOIWDialog()
const load = ref(true)
const videoList = ref<(TopicDetail | SchoolVideoDetail)[]>([])
const pageSize = ref(12)
const page = ref(1)

const initData = async () => {
  try {
    load.value = true
    if (props.topicList && !props.topicList.length) {
      return
    }
    const tempListData = props.topicList.filter(
      (item, index) =>
        index < page.value * pageSize.value &&
        index >= (page.value - 1) * pageSize.value,
    )
    videoList.value = await fetchVideoDetails(tempListData, 'collectionId')
  } finally {
    load.value = false
  }
}

const handleInsert = async (item: TopicDetail, cover: string) => {
  if (
    item.resourceSubType === 'system' ||
    item.resourceSubType === 'system-point'
  ) {
    const auth = await systemLessonPermission({
      subjectId: item.video.subjectId,
      stageId: item.video.stageId,
    })
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  } else {
    const auth = await specialCoursePermission(item.resourceSubId as string)
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  }
  let data: VideoPlay = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (item.resourceSubType === 'special') {
    // 培优课
    data.specialCourseId = item.resourceSubId
  }
  if (item.resourceSubType === 'system-point') {
    // 微课 切片
    data.clipId = item.resourceSubId
  }
  const keypointEntity =
    item.resourceSubType === 'system-point'
      ? getKeyPointEntryFromId(item.resourceSubId!, item.video)
      : undefined
  const chapterId =
    item.extra.chapterId !== undefined ? item.extra.chapterId : undefined
  const sectionId =
    item.extra.sectionId !== undefined ? item.extra.sectionId : undefined
  const subSectionId =
    item.extra.subSectionId !== undefined ? item.extra.subSectionId : undefined

  emits('onInsertClick', {
    sourceType: item.resourceSubType === 'system-point' ? 'Clip' : 'Video',
    sourceId: item.video.id,
    publisherId: item.publisher,
    semesterId: item.semester,
    stageId: item.stage,
    topicId: item.id,
    subjectId: item.subject,
    chapterId,
    sectionId,
    subSectionId,
    specialCourseId:
      item.resourceSubType === 'special' ? item.resourceSubId : undefined,
    duration: keypointEntity ? keypointEntity.duration : item.video.duration,
    name: keypointEntity
      ? `${keypointEntity.content} - ${item.name}`
      : item.name,
    videoCover: cover,
    videoClipExtra: keypointEntity
      ? {
          clipId: keypointEntity.id,
          playRangeStart: keypointEntity.offset,
          playRangeEnd: keypointEntity.offset + keypointEntity.duration,
        }
      : undefined,
  })
}
const handleSchoolInsert = (item: SchoolVideoDetail) => {
  const chapterId = item.chapterId !== undefined ? item.chapterId : undefined
  const sectionId = item.sectionId !== undefined ? item.sectionId : undefined
  const subSectionId =
    item.subsectionId !== undefined ? item.subsectionId : undefined
  emits('onInsertClick', {
    sourceType: 'SchoolVideo',
    sourceId: item.id || '',
    name: item.name || '',
    duration: item.duration || 0,
    topicId: '',
    chapterId,
    sectionId,
    subSectionId,
    specialCourseId: '',
    subjectId: item.subjectId || '-1',
    stageId: item.stageId || '-1',
    publisherId: item.publisherId || '-1',
    semesterId: item.semesterId || '-1',
    importFrom: 'School',
  })
}

const handleSchoolSelfClip = (item: SchoolVideoDetail) => {
  emits('onSelfClip', item, 'schoolVideo')
}

watch(
  () => props.topicList,
  () => {
    page.value = 1
    initData()
  },
  {
    immediate: true,
  },
)
watch(
  () => page.value,
  () => {
    initData()
  },
)

const handlePreview = async (item: TopicDetail) => {
  if (
    item.resourceSubType === 'system' ||
    item.resourceSubType === 'system-point'
  ) {
    const auth = await systemLessonPermission({
      subjectId: item.video.subjectId,
      stageId: item.video.stageId,
    })
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  } else {
    const auth = await specialCoursePermission(item.resourceSubId as string)
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  }
  let data: VideoPlay = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (item.resourceSubType === 'special') {
    // 培优课
    data.specialCourseId = item.resourceSubId
  }
  if (item.resourceSubType === 'system-point') {
    // 微课 切片
    data.clipId = item.resourceSubId
  }
  emits('onPreviewClick', data, 'cb')
}

const handleSchoolPreview = (item: SchoolVideoDetail) => {
  emits('onPreviewClick', item, 'school')
}

const handleSelfClip = async (item: TopicDetail) => {
  if (
    item.resourceSubType === 'system' ||
    item.resourceSubType === 'system-point'
  ) {
    const auth = await systemLessonPermission({
      subjectId: item.video.subjectId,
      stageId: item.video.stageId,
    })
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  } else {
    const auth = await specialCoursePermission(item.resourceSubId as string)
    if (!item.isFreeTime && item.pay && !auth) {
      dialog.create({
        showIcon: false,
        title: '提示',
        content:
          '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
        positiveText: '确认',
      })
      return
    }
  }
  let data: VideoPlay = {
    topicId: item.id,
    specialCourseId: undefined,
    clipId: undefined,
  }
  if (item.resourceSubType === 'special') {
    // 培优课
    data.specialCourseId = item.resourceSubId
  }
  if (item.resourceSubType === 'system-point') {
    // 微课 切片
    data.clipId = item.resourceSubId
  }
  emits(
    'onSelfClip',
    item,
    item.resourceSubType === 'special' ? 'specialCourse' : 'systemLesson',
  )
}
</script>

<style lang="scss" scoped>
.micro-empty-box {
  margin-top: 100px;
}

.micro-video-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
</style>
