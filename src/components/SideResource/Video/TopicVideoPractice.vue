<template>
  <div class="text-size-14px">
    <OIWLoading v-if="loading" :show="loading" width="200px" height="200px" />
    <div v-else>
      <template v-if="problems.length">
        <ProblemItem
          v-for="(problem, index) of isShowAll ? allProblems : problems"
          :key="problem.id"
          :problem="problem"
          :index="index + 1"
          class="border-b border-gray-200 last:border-none"
          isHideFavorite
          :showInsertButton="!isShowInsertSelect"
          :insertCount="problemsCount[problem.id]"
          @on-insert="clickInsert(problem)"
        >
          <template #insert>
            <n-popselect
              v-if="isShowInsertSelect"
              v-model:value="insertVal"
              :options="insertOptions"
              trigger="click"
              @update:value="(value) => clickInsert(problem, value)"
            >
              <span
                class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
                @click="clickInsertTip"
              >
                <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
                插入
              </span>
            </n-popselect>
          </template>
        </ProblemItem>
      </template>
      <template v-else>
        <OIWStateBlock
          type="empty"
          style="margin-top: 200px"
          title="暂无资源"
        />
      </template>
    </div>
    <div class="w-100% p-12px flex justify-center">
      <OIWButton
        v-if="problems.length && !isShowAll"
        round
        type="info"
        ghost
        @click="isShowAll = !isShowAll"
      >
        查看更多题目
      </OIWButton>
    </div>
  </div>
</template>

<script setup lang="tsx">
import {
  getSystemLessonTopicDetail,
  getSpicialCourseDetail,
} from '@/service/common.ts'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'
import { OIWLoading, OIWStateBlock, OIWButton } from '@guanghe-pub/onion-ui-web'
import useSideResourceStore from '@/components/SideResource/store'
import { useOIWDialog } from '@guanghe-pub/onion-ui-web'
import AddIcon from '~icons/yc/add'

const props = defineProps<{
  type: 'systemLesson' | 'specialCourse'
  topicId: string
  isShowInsertSelect?: boolean
  csv:
    | {
        publisherId?: YcType.CsvId
        semesterId?: YcType.CsvId
        subjectId: YcType.CsvId
        stageId: YcType.CsvId
      }
    | undefined
}>()

const emits =
  defineEmits<
    (e: 'insertProblem', problem: ProblemDetailType, value?: number) => void
  >()

const loading = ref(true)
const isShowAll = ref(false)
const store = useSideResourceStore()
const { problemsCount } = storeToRefs(store)
const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])
const problems = ref<ProblemDetailType[]>([]) // 每层下的第一题
const allProblems = ref<ProblemDetailType[]>([]) // 每层下的所有题
onMounted(() => {
  if (props.type === 'systemLesson') {
    getSystemLessonTopicDetail({
      topicId: props.topicId,
      publisherId: props.csv?.publisherId || '-1',
      semesterId: props.csv?.semesterId || '-1',
      subjectId: props.csv?.subjectId || '-1',
      stageId: props.csv?.stageId || '-1',
      doneLevel: '0',
    }).then((res) => {
      let allTemp: ProblemDetailType[] = []
      let temp: ProblemDetailType[] = []
      res.practices?.forEach((arr) => {
        const arrTemp = arr.map((item) => {
          item.id = item.problemId
          return item
        })
        temp.push({ ...arrTemp[0], id: arrTemp[0].problemId })
        allTemp = [...allTemp, ...arrTemp]
      })
      problems.value = temp
      allProblems.value = allTemp
      loading.value = false
    })
  } else {
    let temp: ProblemDetailType[] = []
    let allTemp: ProblemDetailType[] = []
    getSpicialCourseDetail(props.topicId).then((res) => {
      res.practices?.forEach((arr) => {
        const arrTemp = arr.map((item) => {
          item.id = item.problemId
          return item
        })
        temp.push(arrTemp[0])
        allTemp = [...allTemp, ...arrTemp]
      })
      problems.value = temp
      allProblems.value = allTemp
      loading.value = false
    })
  }
})

const clickInsert = (problem: ProblemDetailType, value?: number) => {
  emits('insertProblem', problem, value)
}

const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}
</script>
