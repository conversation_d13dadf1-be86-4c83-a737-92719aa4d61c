import request from '@/utils/request'

interface SchoolVideoListParams {
  page: number
  pageSize: number
  stageId: YcType.CsvId
  subjectId: YcType.CsvId
  semesterId: YcType.CsvId
  publisherId: YcType.CsvId
  chapterId: YcType.CsvId
  sectionId: string
  subsectionId: string
  name: string
}

export const getSchoolVideoListApi = async (params: SchoolVideoListParams) => {
  return request.get('/teacher-desk/school-resource/school-video/list', {
    params,
  })
}

export interface GetSchoolTeachersRes {
  teachers?: {
    id?: string
    name?: string
    nickname?: string
    role?: string
    jobTitle?: {
      id?: number
      name?: string
      [k: string]: unknown
    }
    stageId?: number
    subjectId?: number
    stageSubject?: string
    [k: string]: unknown
  }[]
  [k: string]: unknown
}

/**
 * @description 获取当前教师所在学校的所有确认教师
 * https://yapi.yc345.tv/project/2531/interface/api/122789
 * <AUTHOR>
 * @date 2025-02-27
 * @export
 * @returns {Promise<GetSchoolTeachersRes>}
 */
export const getSchoolTeachersUserApi = async () => {
  return request.get<GetSchoolTeachersRes>(
    '/teacher-common/teacher-school/school/teachers',
  )
}

export interface FilterOptionItem {
  id: string
  name: string
  value: string
}

export interface GetFilterOptionsRes {
  problemExamTypeOption: {
    key: number
    items: FilterOptionItem[]
  }
  gradeOption: {
    key: number
    items: FilterOptionItem[]
  }
  termOption: {
    key: number
    items: FilterOptionItem[]
  }
  yearOption: {
    key: number
    items: FilterOptionItem[]
  }
  paperTypeOption: {
    key: number
    items: FilterOptionItem[]
  }
  paperLevelOption: {
    key: number
    items: FilterOptionItem[]
  }
  provinceCodeOption: {
    key: number
    items: FilterOptionItem[]
  }
}

export const getFilterOptionsApi = async () => {
  return request.get<GetFilterOptionsRes>(
    '/teacher-desk/paper-problem-pool/get-filter-options',
  )
}

export interface GetPaperProblemPool {
  subjectId: number // 学科id.
  stageId: number // 学段id.
  problemExamType: string // 题型.
  grade: number // 年级.
  term: string // 学期.
  year: number // 年份.
  paperType: string // 试卷类型.
  paperLevel: number // 试卷难度.
  provinceCode: number // 省份编码.
  publisherId: number // 出版社id.
  teachingTagIds?: string[] // 知识点标签ID数组
}

export interface GetPaperProblemPoolListParams extends GetPaperProblemPool {
  page: number // 页码.
  pageSize: number // 每页条数.
}

export const getPaperProblemPoolListApi = async (
  data: GetPaperProblemPoolListParams,
) => {
  return request.post<{
    paperProblems: {
      problemId: string
      problemExamType: string
      problemDifficulty: string
      paperId: number
      paperName: number
      favoriteCount: number
    }[]
    total: number
  }>('/teacher-desk/paper-problem-pool/list', data)
}
