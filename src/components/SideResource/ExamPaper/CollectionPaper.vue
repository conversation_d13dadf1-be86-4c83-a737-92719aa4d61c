<template>
  <section class="mt-16px">
    <div class="flex md-16px">
      <OIWInput
        class="h-40px"
        :value="filterModel.resourceName"
        placeholder="输入搜索关键词搜索资源"
        clearable
        @update:value="onNameChange"
      >
        <template #suffix>
          <SearchIcon />
        </template>
      </OIWInput>
    </div>
    <OIWLoading
      v-if="showLoading"
      :show="showLoading"
      width="200px"
      height="200px"
    />
    <div v-else-if="papers.length">
      <PaperItem
        v-for="paper of papers"
        :key="paper.name"
        :paper="paper"
        type="public"
        @favorite-item="favoriteItem"
      />
      <n-space v-if="total > 10" justify="end" style="margin-top: 20px">
        <n-pagination
          class="custom-pagination"
          :page="page"
          :item-count="total"
          @update:page="onPageChange"
        />
      </n-space>
    </div>
    <OIWStateBlock v-else type="empty" title="暂无资源" />
  </section>
</template>

<script setup lang="ts">
import { OIWLoading, OIWInput, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import SearchIcon from '~icons/yc/search'
import { useAddFavorite, useFavorite } from '@/hooks/useFavorite'
import type { ExamPaperItem } from '@/pages/paper/service'
import { getExamPaperListApi } from '@/pages/paper/service'
import { useLoading } from '@/hooks/useLoading'
import PaperItem from '../components/PaperItem.vue'

const filterModel = ref<{
  resourceName: string
  resourceType: 'exam_paper'
}>({
  resourceName: '',
  resourceType: 'exam_paper',
})

const {
  fetchLoading,
  favorites,
  debounceFetch,
  page,
  resetPage,
  pageChange,
  total,
} = useFavorite(filterModel, {
  initFetch: true,
})
const onNameChange = (val: string) => {
  filterModel.value.resourceName = val
  resetPage()
  debounceFetch()
}
const onPageChange = (val: number) => {
  pageChange(val)
}

const currentPaperIds = computed(() => {
  return favorites.value.map((el) => el.resourceId)
})

const papers = ref<ExamPaperItem[]>([])

const showLoading = computed(() => {
  return loading.value || fetchLoading.value
})
const { loading, toggleLoading } = useLoading()
async function fetchPaperList() {
  toggleLoading()
  const res = await getExamPaperListApi({
    ids: currentPaperIds.value,
  }).finally(() => toggleLoading())
  papers.value = res.rows
}
watch(
  currentPaperIds,
  (val) => {
    if (val.length) {
      fetchPaperList()
    } else {
      papers.value = []
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

const { addFavorite, deleteFavorite } = useAddFavorite()
const favoriteItem = async (paper: ExamPaperItem) => {
  if (!paper.isFavorite) {
    await addFavorite({
      resourceType: 'exam_paper',
      resourceId: paper.id,
      publisher: paper.publisherId,
      subject: paper.subjectId,
      stage: paper.stageId,
    })
    paper.isFavorite = true
    paper.favoriteCount++
  } else {
    await deleteFavorite({
      resourceId: paper.id,
    })
    paper.isFavorite = false
    const newCount = paper.favoriteCount - 1
    paper.favoriteCount = newCount < 0 ? 0 : newCount
  }
}
</script>

<style lang="scss">
.cascader {
  .n-base-selection {
    border-radius: 12px;
  }
}

.medium-select {
  height: 34px;
}

.select-40 {
  height: 40px;
}
</style>
