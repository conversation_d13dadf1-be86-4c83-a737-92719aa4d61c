<template>
  <div class="p-24px">
    <div>
      <div class="flex justify-between">
        <OIWRadioButtonGroup
          v-if="isPageTypeList"
          :value="examPaperState.sourceType"
          :on-update-value="store.onSwitchExamPaperType"
        >
          <OIWRadioButton value="common">公共资源</OIWRadioButton>
          <OIWRadioButton value="school"> 校本资源 </OIWRadioButton>
          <OIWRadioButton value="collection">我的收藏</OIWRadioButton>
        </OIWRadioButtonGroup>
        <div
          v-else
          class="font-14px font-600 flex items-center cursor-pointer color-#9792AC"
          @click="onBackList"
        >
          <BackIcon
            class="w-14px cursor-pointer h-14px font-600 font-size-18px color-#9792AC"
          />
          返回
        </div>

        <CloseIcon class="cursor-pointer ml-auto" @click="emits('close')" />
      </div>
    </div>
    <div class="w-100%">
      <div v-show="isPageTypeList">
        <CommonPaper v-if="examPaperState.sourceType === 'common'" />
        <SchoolPaper v-if="examPaperState.sourceType === 'school'" />
        <CollectionPaper v-if="examPaperState.sourceType === 'collection'" />
      </div>
      <ExamPaperDetail
        v-if="!isPageTypeList"
        @add-problem="addProblems"
        @problem-all="addProblemAll"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import BackIcon from '~icons/yc/back'
import CloseIcon from '~icons/yc/close-round-gray'
import useSideResourceStore from '../store'
import CommonPaper from './CommonPaper.vue'
import SchoolPaper from './SchoolPaper.vue'
import CollectionPaper from './CollectionPaper.vue'
import ExamPaperDetail from './ExamPaperDetail.vue'
import { OIWRadioButtonGroup, OIWRadioButton } from '@guanghe-pub/onion-ui-web'
import type { InsertParams } from '../types'

const emits = defineEmits<{
  (e: 'close'): void
  (e: 'insertProblem', val: InsertParams): void
}>()

const store = useSideResourceStore()
const { examPaperState } = storeToRefs(store)

const isPageTypeList = computed(() => {
  return examPaperState.value.pageType === 'list'
})

const onBackList = () => {
  store.onChangeExamPaperPageType('list')
}

const addProblems = (val: InsertParams) => {
  emits('insertProblem', {
    ...val,
    importFrom:
      examPaperState.value.sourceType === 'common'
        ? 'Public'
        : examPaperState.value.sourceType === 'school'
          ? 'School'
          : 'MyFavorite',
  })
}

const addProblemAll = (all: InsertParams[]) => {
  if (all.length) {
    all.forEach((val) => {
      emits('insertProblem', {
        ...val,
        importFrom:
          examPaperState.value.sourceType === 'common'
            ? 'Public'
            : examPaperState.value.sourceType === 'school'
              ? 'School'
              : 'MyFavorite',
      })
    })
  }
}
</script>
