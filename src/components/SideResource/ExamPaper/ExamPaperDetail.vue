<template>
  <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
  <div v-else class="exam-paper-detail">
    <div class="flex justify-end mt-14px">
      <OIWButton round :disabled="!problems.length" @click="onAllInsert"
        >全部插入</OIWButton
      >
    </div>
    <div class="mt-24px">
      <div class="color-#393548 text-20px font-600 text-center">
        <img
          v-if="
            examPaper &&
            (examPaper as ExamPaperItem).level === 'EXAM_PAPER_LEVEL_BEST'
          "
          class="w-32px h-20px mr-8px inline-block"
          src="https://fp.yangcong345.com/onion-extension/%<EMAIL>"
          alt=""
        />
        <span>{{ examPaper?.name }}</span>
      </div>
      <div
        v-if="examPaperState.detailType === 'public'"
        class="flex items-center justify-center mt-16px text-14px color-#9792AC"
      >
        <span
          >下载量：{{
            examPaper && (examPaper as ExamPaperItem).downloadCount
          }}</span
        >
        <span>｜</span>
        <span
          >收藏量：{{
            examPaper && (examPaper as ExamPaperItem).favoriteCount
          }}</span
        >
      </div>
      <div v-if="problems.length" class="mb-40px">
        <ProblemItem
          v-for="(problem, index) in problems"
          :key="problem.id"
          :problem="problem"
          :index="index + 1"
          class="border-b border-gray-200 last:border-none"
          isHideClassMode
          isHideFavorite
          :showSchoolAiExplainButton="examPaperState.detailType === 'school'"
          :tagConfig="{
            isHideBaseTags: true,
            isHideUsageCount: true,
            isHideAccuracy: true,
          }"
          showInsertButton
          :insertCount="problemsCount[problem.id]"
          @on-insert="clickInsert(problem)"
        />
      </div>
      <OIWStateBlock v-else class="mt-120px text-center" type="empty" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { OIWButton, OIWStateBlock, OIWLoading } from '@guanghe-pub/onion-ui-web'
import useSideResourceStore from '../store'
import type { ProblemPoolSimple } from '@/pages/problem/service'
import { getPaperDetailApi } from '@/pages/paper/service'
import type { SchoolExamPaperItem } from '@/pages/schoolResource/schoolPaper/service'
import { getSchoolPaperDetailApi } from '@/pages/schoolResource/schoolPaper/service'
import type { ExamPaperItem } from '@/pages/paper/service'
import { getProblemDetailsByIdApi } from '@/pages/problem/service'
import { getSchoolProblemDetailApi } from '@/pages/schoolResource/schoolProblem/service.ts'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import { useLoading } from '@/hooks/useLoading'
import type { InsertParams } from '../types'
import { getDurationFromProblemDifficulty, getProblemTypeName } from '../utils'

const emits = defineEmits<{
  (e: 'addProblem', val: InsertParams): void
  (e: 'problemAll', val: InsertParams[]): void
}>()
const store = useSideResourceStore()
const { examPaperState, problemsCount } = storeToRefs(store)
const problemPool = shallowRef<ProblemPoolSimple[]>([])
const problems = shallowRef<YcType.ProblemType[]>([])
const examPaper = ref<ExamPaperItem | SchoolExamPaperItem | undefined>()
const { toggleLoading, loading } = useLoading()
const dataFetch = async () => {
  const res = await getPaperDetailApi(examPaperState.value.paperId)
  problemPool.value = res.problems
  examPaper.value = res.examPaper
  if (!problemPool.value.length) return
  const problemDetailsRes = await getProblemDetailsByIdApi(
    problemPool.value.map((el) => el.problemId),
  )
  problems.value = problemDetailsRes
}
const schoolDataFetch = async () => {
  const res = await getSchoolPaperDetailApi(examPaperState.value.paperId)
  examPaper.value = res.detail
  if (!res.problemIds.length) return
  const problemDetailsRes = await getSchoolProblemDetailApi({
    problemsId: res.problemIds,
  })
  problems.value = problemDetailsRes.data
}

const initData = async () => {
  try {
    toggleLoading()
    if (examPaperState.value.detailType === 'school') {
      await schoolDataFetch()
    } else {
      await dataFetch()
    }
  } finally {
    toggleLoading()
  }
}
initData()

const clickInsert = (problem: YcType.ProblemType) => {
  emits('addProblem', {
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType:
      examPaperState.value.detailType === 'public'
        ? 'Problem'
        : 'SchoolProblem',
    publisherId: Number(examPaperState.value.publisherId),
    subjectId: Number(examPaperState.value.subjectId),
    stageId: Number(examPaperState.value.stageId),
    semesterId: '',
    chapterId: '',
    sectionId: '',
    subSectionId: '',
    paperId: examPaperState.value.paperId,
  })
}

const onAllInsert = () => {
  emits(
    'problemAll',
    problems.value.map((problem) => {
      return {
        sourceId: problem.id,
        name: getProblemTypeName(problem),
        duration: getDurationFromProblemDifficulty(
          problem.difficulty as string,
        ),
        sourceType:
          examPaperState.value.detailType === 'public'
            ? 'Problem'
            : 'SchoolProblem',
        publisherId: Number(examPaperState.value.publisherId),
        subjectId: Number(examPaperState.value.subjectId),
        stageId: Number(examPaperState.value.stageId),
        semesterId: '',
        chapterId: '',
        sectionId: '',
        subSectionId: '',
        paperId: examPaperState.value.paperId,
      }
    }),
  )
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
}
</style>
