<template>
  <div class="common-paper-type">
    <div class="pt-16px relative border-b-1px border-#DFDCE8 pb-44px">
      <div class="flex justify-between items-center">
        <OIWInput
          v-model:value="formModel.name"
          :maxlength="50"
          class="h-40px"
          placeholder="输入搜索关键词搜索资源"
          clearable
        >
          <template #suffix>
            <SearchIcon />
          </template>
        </OIWInput>
      </div>
      <div class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">学科:</span>
          <n-cascader
            v-model:value="stageAndSubject"
            class="oiw-cascader"
            label-field="name"
            value-field="treeId"
            placeholder="选择学科"
            expand-trigger="click"
            :options="csvOptions"
            check-strategy="child"
            filterable
            :disabled="disabled"
            @update:value="onCsvSelectChange"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">来自:</span>
          <OIWSelect
            v-model:value="formModel.creatorId"
            :options="createdByOptions"
            placeholder="选择分享人"
            :disabled="disabled"
          />
        </div>
      </div>
    </div>
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else-if="paperList.length">
      <PaperItem
        v-for="paper of paperList"
        :key="paper.name"
        type="school"
        :paper="paper"
      />
      <n-space v-if="total > 10" justify="end" style="margin-top: 20px">
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          class="custom-pagination"
          :item-count="total"
        />
      </n-space>
    </div>
    <OIWStateBlock
      v-else
      class="mt-120px text-center"
      type="empty"
      title="暂无资源"
    />
  </div>
</template>

<script setup lang="ts">
import {
  OIWSelect,
  OIWStateBlock,
  OIWLoading,
  OIWInput,
} from '@guanghe-pub/onion-ui-web'
import type { SchoolExamPaperItem } from '@/pages/schoolResource/schoolPaper/service'
import { getSchoolExamPaperListApi } from '@/pages/schoolResource/schoolPaper/service'
import SearchIcon from '~icons/yc/search'
import { cloneDeep } from 'lodash-es'
import type { CascaderOption } from 'naive-ui'
import { useLoading } from '@/hooks/useLoading'
import { useCvs } from '@/hooks/useCvs'
import PaperItem from '../components/PaperItem.vue'
import { debounce } from 'lodash-es'
import { useCurrentBook } from '@/hooks/useCvs'
import { useSchoolTeachers } from '@/hooks/useSchoolTeachers'

const { toggleLoading, loading } = useLoading()
const { createdBy, createdByOptions } = useSchoolTeachers()
const csv = useCvs()
const formModel = reactive<{
  stageId: number
  subjectId: number
  creatorId: string
  name: string
  orderBy: 'name' | 'created_at'
  orderDirection: 'asc' | 'desc'
}>({
  subjectId: -1,
  stageId: -1,
  creatorId: createdBy.value || '',
  name: '',
  orderBy: 'created_at',
  orderDirection: 'desc',
})
const stageAndSubject = computed(() => {
  if (currentBookLoading.value) {
    return null
  }
  return `${formModel.stageId}-${formModel.subjectId}`
})
const { currentBookLoading } = useCurrentBook(({ subjectId, stageId }) => {
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
})
const csvOptions = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = item.subjects as CascaderOption[]
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})


const paperList = ref<SchoolExamPaperItem[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const disabled = ref(false)

// 防抖处理的搜索函数
const debounceFetchData = debounce(() => {
  disabled.value = true
  paperList.value = []
  total.value = 0
  page.value = 1
  fetchData()
}, 300) as () => void

watch(
  formModel,
  () => {
    debounceFetchData()
  },
  {
    immediate: true,
  },
)

watch(
  () => page.value,
  () => {
    fetchData()
  },
)

async function fetchData() {
  try {
    toggleLoading()
    const res = await getSchoolExamPaperListApi({
      ...formModel,
      page: page.value,
      pageSize: pageSize.value,
    })
    paperList.value = res.rows
    total.value = res.count
  } catch (error) {
    console.error(error)
  } finally {
    toggleLoading()
    disabled.value = false
  }
}

const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
}
</script>
