<template>
  <div class="common-paper-type">
    <div class="pt-16px relative border-b-1px border-#DFDCE8 pb-44px">
      <div class="flex justify-between items-center">
        <OIWInput
          v-model:value="formModel.name"
          :maxlength="50"
          class="h-40px"
          placeholder="输入搜索关键词搜索资源"
          clearable
        >
          <template #suffix>
            <SearchIcon />
          </template>
        </OIWInput>
      </div>
      <div class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">学科:</span>
          <n-cascader
            :value="stageAndSubject"
            class="oiw-cascader"
            label-field="name"
            value-field="treeId"
            placeholder="选择学科"
            expand-trigger="click"
            :options="csvOptions"
            check-strategy="child"
            filterable
            :disabled="disabled"
            @update:value="onCsvSelectChange"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">年级:</span>
          <OIWSelect
            v-model:value="formModel.gradeId"
            :options="gradeFilters"
            placeholder="选择年级"
            :disabled="disabled"
          />
        </div>
      </div>
      <div class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">学期:</span>
          <OIWSelect
            v-model:value="formModel.term"
            :options="termFilters"
            placeholder="选择学期"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">年份:</span>
          <OIWSelect
            v-model:value="formModel.year"
            :options="yearFilters"
            placeholder="选择年份"
            :disabled="disabled"
          />
        </div>
      </div>
      <div v-if="isExpand" class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">类型:</span>
          <OIWSelect
            v-model:value="formModel.paperType"
            :options="paperTypeFilters"
            placeholder="选择类型"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">等级:</span>
          <OIWSelect
            v-model:value="formModel.level"
            :options="levelFilters"
            placeholder="选择等级"
            :disabled="disabled"
          />
        </div>
      </div>
      <div v-if="isExpand" class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">地区:</span>
          <OIWSelect
            v-model:value="formModel.regionCode"
            :options="regionFilters"
            placeholder="选择地区"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">版本:</span>
          <OIWSelect
            v-model:value="formModel.publisherId"
            :options="publisherIdOption"
            placeholder="选择版本"
            :disabled="disabled"
          />
        </div>
      </div>
      <div
        class="absolute right-16px flex items-center leading-20px color-#5E80FF text-14px font-600 cursor-pointer"
        :class="{
          'bottom-12px': !isExpand,
          'bottom-16px': isExpand,
        }"
        @click="isExpand = !isExpand"
      >
        <span class="mr-2px">{{ isExpand ? '收起' : '展开' }}</span>
        <ArrowIcon
          class="w-18px h-18px color-#5E80FF transition-all duration-300 ease-in-out"
          :class="{
            'rotate-0': isExpand,
            'rotate-180': !isExpand,
          }"
        />
      </div>
    </div>
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else-if="paperList.length">
      <PaperItem
        v-for="paper of paperList"
        :key="paper.name"
        type="public"
        :paper="paper"
        @favorite-item="favoriteItem"
      />
      <n-space v-if="total > 10" justify="end" style="margin-top: 20px">
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          class="custom-pagination"
          :item-count="total"
        />
      </n-space>
    </div>
    <OIWStateBlock
      v-else
      class="mt-120px text-center"
      type="empty"
      title="暂无资源"
    />
  </div>
</template>

<script setup lang="ts">
import {
  OIWSelect,
  OIWStateBlock,
  OIWLoading,
  OIWInput,
} from '@guanghe-pub/onion-ui-web'
import { useCurrentBook } from '@/hooks/useCvs'
import SearchIcon from '~icons/yc/search'
import { cloneDeep } from 'lodash-es'
import type { CascaderOption } from 'naive-ui'
import { useLoading } from '@/hooks/useLoading'
import ArrowIcon from '~icons/yc/arrowIcon'
import { useAddFavorite } from '@/hooks/useFavorite'
import { useCvs, useCvsEnum } from '@/hooks/useCvs'
import type {
  ExamFilterItem,
  ExamPaperItem,
  ExamPaperSortBy,
} from '@/pages/paper/service'
import {
  getExamPaperListApi,
  getExamPaperFiltersApi,
} from '@/pages/paper/service'
import PaperItem from '../components/PaperItem.vue'
import { debounce } from 'lodash-es'

const { addFavorite, deleteFavorite } = useAddFavorite()
const { toggleLoading, loading } = useLoading()
const isExpand = ref(false)
const { PublisherEnum } = useCvsEnum()
const paperList = ref<ExamPaperItem[]>([])
const csv = useCvs()
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const disabled = ref(false)
const formModel = reactive<{
  stageId: number
  subjectId: number
  gradeId: number
  year: number
  paperType: string
  regionCode?: string
  level: number
  sortBy: ExamPaperSortBy
  name: string
  term: string
  publisherId: number
}>({
  stageId: 0,
  subjectId: 0,
  gradeId: 0,
  year: 0,
  paperType: '',
  regionCode: '',
  sortBy: 'YEAR',
  level: 0,
  name: '',
  term: '',
  publisherId: 0,
})
const stageAndSubject = computed(() => {
  if (currentBookLoading.value) {
    return null
  }
  return `${formModel.stageId}-${formModel.subjectId}`
})
const { currentBookLoading } = useCurrentBook(({ subjectId, stageId }) => {
  resetFormModel()
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
  fetchExamPaperFilters(Number(subjectId), Number(stageId))
})
const csvOptions = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = item.subjects as CascaderOption[]
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})

const filters = ref<ExamFilterItem[]>([])
const gradeFilters = computed(() => {
  return [
    {
      label: '全部',
      value: 0,
    },
    ...(filters.value
      .find((el) => el.name === 'gradeId')
      ?.enums.map((el) => {
        return {
          label: el.showName,
          value: el.value,
        }
      }) || []),
  ]
})
const yearFilters = computed(() => {
  return [
    {
      label: '全部',
      value: 0,
    },
    ...(filters.value
      .find((el) => el.name === 'year')
      ?.enums.map((item) => {
        return {
          label: item.showName,
          value: item.value,
        }
      }) || []),
  ]
})
const levelFilters = computed(() => {
  return [
    {
      label: '全部',
      value: 0,
    },
    ...(filters.value
      .find((el) => el.name === 'level')
      ?.enums.map((item) => {
        return {
          label: item.showName,
          value: item.value,
        }
      }) || []),
  ]
})
const termFilters = computed(() => {
  return [
    {
      label: '全部',
      value: '',
    },
    ...(filters.value
      .find((el) => el.name === 'term')
      ?.enums.map((item) => {
        return {
          label: item.showName,
          value: item.value,
        }
      }) || []),
  ]
})
const paperTypeFilters = computed(() => {
  return [
    {
      label: '全部',
      value: '',
    },
    ...(filters.value
      .find((el) => el.name === 'paperType')
      ?.enums.map((item) => {
        return {
          label: item.showName,
          value: item.value,
        }
      }) || []),
  ]
})

const publisherIdOption = computed(() => {
  return PublisherEnum.value.map((item, index) => ({
    id: index,
    label: item || '全部',
    value: index,
  }))
})
const regionFilters = computed(() => {
  return [
    {
      label: '全部',
      value: '',
    },
    ...(filters.value
      .find((el) => el.name === 'regionCode')
      ?.enums.map((item) => {
        return {
          label: item.showName,
          value: item.value,
        }
      }) || []),
  ]
})
// 防抖处理的搜索函数
const debounceFetchData = debounce(() => {
  disabled.value = true
  paperList.value = []
  total.value = 0
  page.value = 1
  fetchData()
}, 300) as () => void
watch(
  formModel,
  () => {
    debounceFetchData()
  },
  {
    immediate: true,
  },
)
watch(
  () => page.value,
  () => {
    fetchData()
  },
)

async function fetchExamPaperFilters(
  subjectId: YcType.CsvId,
  stageId: YcType.CsvId,
) {
  const res = await getExamPaperFiltersApi({
    subjectId,
    stageId,
  })
  filters.value = res.filters
}

async function fetchData() {
  try {
    toggleLoading()
    const res = await getExamPaperListApi({
      ...formModel,
      page: page.value,
      pageSize: pageSize.value,
    })
    paperList.value = res.rows
    total.value = res.total
  } catch (error) {
    console.error(error)
  } finally {
    toggleLoading()
    disabled.value = false
  }
}

function resetFormModel() {
  formModel.gradeId = 0
  formModel.year = 0
  formModel.level = 0
  formModel.paperType = ''
  formModel.regionCode = ''
  formModel.sortBy = 'YEAR'
  formModel.name = ''
  formModel.term = ''
  formModel.publisherId = 0
}

const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
  resetFormModel()
  fetchExamPaperFilters(subjectId, stageId)
}

const favoriteItem = async (paper: ExamPaperItem) => {
  if (!paper.isFavorite) {
    await addFavorite({
      resourceType: 'exam_paper',
      resourceId: paper.id,
      resourceName: paper.name,
      publisher: paper.publisherId,
      subject: paper.subjectId,
      stage: paper.stageId,
    })
    paper.isFavorite = true
  } else {
    await deleteFavorite({
      resourceId: paper.id,
    })
    paper.isFavorite = false
  }
}
</script>
