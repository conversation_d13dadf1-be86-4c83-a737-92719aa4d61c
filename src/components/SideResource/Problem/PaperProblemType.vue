<template>
  <div class="paper-problem-type">
    <div
      class="pt-16px pb-16px relative border-b-1px border-#DFDCE8"
      :class="!isExpand ? 'pb-44px' : 'pb-64px'"
    >
      <div class="flex justify-between items-center">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">学科:</span>
          <n-cascader
            v-model:value="csvValue"
            class="oiw-cascader"
            label-field="name"
            value-field="treeId"
            placeholder="选择学科"
            expand-trigger="click"
            :options="csvOptions"
            check-strategy="child"
            filterable
            :disabled="disabled"
            @update:value="onCsvSelectChange"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">题型:</span>
          <OIWSelect
            v-model:value="formModel.problemExamType"
            :options="options.problemExamTypeOption"
            placeholder="选择题型"
            label-field="name"
            :disabled="disabled"
          />
        </div>
      </div>
      <div class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">年级:</span>
          <OIWSelect
            v-model:value="formModel.grade"
            :options="options.gradeOption"
            placeholder="选择年级"
            label-field="name"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">学期:</span>
          <OIWSelect
            v-model:value="formModel.term"
            :options="options.termOption"
            placeholder="选择学期"
            label-field="name"
            :disabled="disabled"
          />
        </div>
      </div>
      <div class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">年份:</span>
          <OIWSelect
            v-model:value="formModel.year"
            :options="options.yearOption"
            placeholder="选择年份"
            label-field="name"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">类型:</span>
          <OIWSelect
            v-model:value="formModel.paperType"
            :options="options.paperTypeOption"
            placeholder="选择类型"
            label-field="name"
            :disabled="disabled"
          />
        </div>
      </div>
      <div v-if="isExpand" class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">等级:</span>
          <OIWSelect
            v-model:value="formModel.paperLevel"
            :options="options.paperLevelOption"
            placeholder="选择等级"
            label-field="name"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">地区:</span>
          <OIWSelect
            v-model:value="formModel.provinceCode"
            :options="options.provinceCodeOption"
            placeholder="选择地区"
            label-field="name"
            :disabled="disabled"
          />
        </div>
      </div>
      <div v-if="isExpand" class="flex justify-between items-center mt-16px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">版本:</span>
          <OIWSelect
            v-model:value="formModel.publisherId"
            :options="options.publisherIdOption"
            placeholder="选择版本"
            label-field="name"
            :disabled="disabled"
          />
        </div>
        <div class="flex items-center w-49% teachingTagSelect">
          <span class="flex-shrink-0 mr-8px">知识点:</span>
          <n-select
            :value="teachingTagIds"
            multiple
            clearable
            filterable
            virtual-scroll
            :style="
              teachingTagIds.length > 1
                ? {
                    'min-height': '40px',
                    height: 'auto',
                    width: '182px',
                  }
                : { height: '40px', width: '182px' }
            "
            :options="tagsWithAll"
            placeholder="选择知识点"
            :render-option="renderOption"
            @update:value="changeTeachingTagIds"
          ></n-select>
        </div>
      </div>
      <div
        class="absolute right-16px flex items-center leading-20px color-#5E80FF text-14px font-600 cursor-pointer"
        :class="{
          'bottom-12px': !isExpand,
          'bottom-16px': isExpand,
        }"
        @click="isExpand = !isExpand"
      >
        <span class="mr-2px">{{ isExpand ? '收起' : '展开' }}</span>
        <ArrowIcon
          class="w-18px h-18px color-#5E80FF transition-all duration-300 ease-in-out"
          :class="{
            'rotate-0': isExpand,
            'rotate-180': !isExpand,
          }"
        />
      </div>
    </div>
    <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
    <div v-else-if="problemList.length">
      <div class="flex mt-24px justify-between items-center">
        <div>筛选到题目总数：{{ total }}</div>
      </div>
      <ProblemItem
        v-for="(problem, index) in problemList"
        :key="problem.problemId"
        :problem="problem"
        isHideClassMode
        :index="index + 1 + (page - 1) * 10"
        :is-favorite="isFavorite(problem.id)"
        :show-insert-button="type !== 'courseware'"
        class="border-b border-gray-200 last:border-none"
        :insertCount="problemsCount[problem.id]"
        @on-favorite="handleFavoriteClick(problem.id)"
        @on-insert="clickInsert(problem)"
      >
        <template #insert>
          <n-popselect
            v-if="type === 'courseware'"
            v-model:value="insertVal"
            :options="insertOptions"
            trigger="click"
            @update:value="(value) => clickInsertCourseware(problem, value)"
          >
            <span
              class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
              @click="clickInsertTip"
            >
              <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
              插入
            </span>
          </n-popselect>
        </template>
      </ProblemItem>
      <n-space v-if="total > 10" justify="end" style="margin-top: 20px">
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          class="custom-pagination"
          :item-count="total"
        />
      </n-space>
    </div>
    <OIWStateBlock
      v-else
      class="mt-120px text-center"
      type="empty"
      title="暂无资源"
    />
  </div>
</template>

<script setup lang="tsx">
import {
  OIWSelect,
  OIWStateBlock,
  OIWLoading,
  useOIWDialog,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import { NTooltip } from 'naive-ui'
import type { VNode } from 'vue'
import type { CascaderOption, SelectOption } from 'naive-ui'
import { cloneDeep, debounce } from 'lodash-es'
import { useCvs, useCvsEnum } from '@/hooks/useCvs'
import { useAuth } from '@/hooks/useAuth.ts'
import type {
  FilterOptionItem,
  GetPaperProblemPool,
} from '@/components/SideResource/service'
import {
  getFilterOptionsApi,
  getPaperProblemPoolListApi,
} from '@/components/SideResource/service'
import type { ProblemDetailType } from '@/pages/problem/utils'
import ArrowIcon from '~icons/yc/arrowIcon'
import { postProblemDetail } from '@/pages/problem/service'
import { useLoading } from '@/hooks/useLoading'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import type { InsertParams } from '../types'
import { getDurationFromProblemDifficulty, getProblemTypeName } from '../utils'
import useSideResourceStore from '@/components/SideResource/store'
import { getFavoriteIdsApi } from '@/service/common.ts'
import { useAddFavorite } from '@/hooks/useFavorite.ts'
import { usePaperTeachingTags } from '@/hooks/usePaperTeachingTags.ts'
import AddIcon from '~icons/yc/add'

defineProps<{
  type?: 'insert' | 'courseware'
}>()

const emits = defineEmits<{
  (e: 'insertProblem', val: InsertParams): void
  (
    e: 'insertCoursewareProblem',
    val: YcType.ProblemType,
    insertVal: number,
  ): void
}>()

const { toggleLoading, loading } = useLoading()
const isExpand = ref(false)
const { subjectId, stageId } = useAuth()
const { PublisherEnum } = useCvsEnum()
const csv = useCvs()
const sideResourceStore = useSideResourceStore()
const { problemsCount } = storeToRefs(sideResourceStore)
const csvValue = ref<string>(`${stageId.value}-${subjectId.value}`)
const csvOptions = computed(() => {
  const csvData: CascaderOption[] = cloneDeep(csv.value)
  return csvData.map((item) => {
    item.treeId = `${item.id}`
    item.children = item.subjects as CascaderOption[]
    item.children.forEach((subject) => {
      subject.treeId = `${item.treeId}-${subject.id}`
    })
    return item
  })
})

const options = reactive<{
  problemExamTypeOption: FilterOptionItem[]
  gradeOption: FilterOptionItem[]
  termOption: FilterOptionItem[]
  yearOption: FilterOptionItem[]
  paperTypeOption: FilterOptionItem[]
  paperLevelOption: FilterOptionItem[]
  provinceCodeOption: FilterOptionItem[]
  publisherIdOption: FilterOptionItem[]
}>({
  problemExamTypeOption: [],
  gradeOption: [],
  termOption: [],
  yearOption: [],
  paperTypeOption: [],
  paperLevelOption: [],
  provinceCodeOption: [],
  publisherIdOption: [],
})
const formModel = reactive<GetPaperProblemPool>({
  subjectId: subjectId.value || 1,
  stageId: stageId.value || 2,
  problemExamType: '',
  grade: 0,
  term: '',
  year: 0,
  paperType: '',
  paperLevel: 0,
  provinceCode: 0,
  publisherId: 0,
})

const problemList = ref<ProblemDetailType[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const disabled = ref(false)

const teachingTagIds = ref<string[]>(['all']) // 选择的知识点标签
const { tagsWithAll, getPaperTeachingTags } = usePaperTeachingTags()

const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])
// 渲染知识点标签Option
const renderOption = ({
  node,
  option,
}: {
  node: VNode
  option: SelectOption
}) =>
  h(NTooltip, null, {
    trigger: () => node,
    default: () => option.label,
  })

const debounceFetch = debounce(fetchList, 1000)
const message = useOIWMessage()
const changeTeachingTagIds = (ids: string[]) => {
  if (ids.length === 0 || ids[ids.length - 1] === 'all') {
    teachingTagIds.value = ['all']
  } else {
    // 判断是否超过5个
    const delAllIds = ids.filter((item) => item !== 'all')
    if (delAllIds.length > 5) {
      message.warning('最多选择5个知识点')
      return
    }
    teachingTagIds.value = delAllIds
  }
  disabled.value = true
  page.value = 1
  debounceFetch()
}

watch(
  formModel,
  () => {
    disabled.value = true
    page.value = 1
    fetchList()
  },
  {
    immediate: true,
  },
)
watch(
  () => page.value,
  () => {
    fetchList()
  },
)

onMounted(() => {
  optionsInit()
  getPaperTeachingTags({
    stageId: stageId.value ? Number(stageId.value) : 2,
    subjectId: subjectId.value ? Number(subjectId.value) : 1,
    scene: 'course',
  })
})

function resetOption() {
  formModel.problemExamType = ''
  formModel.grade = 0
  formModel.term = ''
  formModel.year = 0
  formModel.paperType = ''
  formModel.paperLevel = 0
  formModel.provinceCode = 0
  formModel.publisherId = 0
  teachingTagIds.value = ['all']
}

const optionsInit = async () => {
  const res = await getFilterOptionsApi()
  if (res) {
    Object.assign(options, {
      problemExamTypeOption: res.problemExamTypeOption?.items || [],
      gradeOption: res.gradeOption?.items || [],
      termOption: res.termOption?.items || [],
      yearOption: res.yearOption?.items || [],
      paperTypeOption: res.paperTypeOption?.items || [],
      paperLevelOption: res.paperLevelOption?.items || [],
      provinceCodeOption: res.provinceCodeOption?.items || [],
      publisherIdOption: PublisherEnum.value.map((item, index) => ({
        id: index,
        name: item || '全部',
        value: index,
      })),
    })
  }
}

const onCsvSelectChange = (value: string) => {
  const [stageId, subjectId] = value.split('-')
  formModel.subjectId = Number(subjectId)
  formModel.stageId = Number(stageId)
  resetOption()
  // 重新获取知识点标签
  getPaperTeachingTags({
    stageId: stageId ? Number(stageId) : 2,
    subjectId: subjectId ? Number(subjectId) : 1,
    scene: 'course',
  })
}

async function fetchList() {
  try {
    toggleLoading()
    const realTeachingTagIds =
      teachingTagIds.value?.length === 1 && teachingTagIds.value[0] === 'all'
        ? []
        : teachingTagIds.value
    const res = await getPaperProblemPoolListApi({
      ...formModel,
      teachingTagIds: realTeachingTagIds,
      page: page.value,
      pageSize: pageSize.value,
    })
    if (res) {
      total.value = res.total
      const problem = await postProblemDetail({
        problemsId: res.paperProblems.map((item) => item.problemId),
      })
      problemList.value = res.paperProblems.map((item) => {
        const temp = problem.find((p) => p.id === item.problemId)
        return {
          ...item,
          ...temp,
        }
      }) as ProblemDetailType[]
      await fetchFavorites()
    }
  } catch (error) {
    console.error(error)
  } finally {
    toggleLoading()
    disabled.value = false
  }
}

const clickInsert = (problem: ProblemDetailType) => {
  emits('insertProblem', {
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType: 'Problem',
    publisherId: Number(formModel.publisherId),
    subjectId: Number(formModel.subjectId),
    stageId: Number(formModel.stageId),
    semesterId: '',
    chapterId: '',
    sectionId: '',
    subSectionId: '',
    paperId: problem.paperId,
  })
}

const clickInsertCourseware = (problem: ProblemDetailType, value: number) => {
  emits('insertCoursewareProblem', problem, value)
}

// 收藏
const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem',
    stage: Number(formModel.stageId),
    subject: Number(formModel.subjectId),
  })
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()
const handleFavoriteClick = async (problemId: string) => {
  const updateProblemFavoriteCount = (id: string, add = true) => {
    const problem = problemList.value.find((el) => el.id === id)
    if (problem) {
      if (add) {
        const newCount = (problem.favoriteCount as number) + 1
        problem.favoriteCount = newCount
      } else {
        const newCount = (problem.favoriteCount as number) - 1
        problem.favoriteCount = newCount
      }
    }
  }
  if (!isFavorite(problemId)) {
    const problem = problemList.value.find((el) => el.id === problemId)
    await addFavorite({
      resourceId: problemId,
      resourceType: 'problem',
      stage: Number(formModel.stageId),
      subject: Number(formModel.subjectId),
      extra: {
        examType: problem?.examType,
        type: problem?.type,
        region: problem?.region,
        difficulty: problem?.difficulty,
        accuracy: problem?.accuracy,
      },
    })
    updateProblemFavoriteCount(problemId, true)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    updateProblemFavoriteCount(problemId, false)
    favoriteIds.value.delete(problemId)
  }
}
const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}
</script>

<style lang="scss" scoped>
.paper-problem-type {
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.teachingTagSelect {
  ::v-deep(.n-base-selection-tag-wrapper) {
    display: block !important;
    width: 100%;
  }

  ::v-deep(.n-base-selection--selected) {
    min-height: 40px;
    border-radius: 12px;
  }

  ::v-deep(.n-base-selection-tags) {
    min-height: 40px;
  }

  ::v-deep(.n-base-selection__border) {
    border: 1px solid #c5c1d4;
  }
}
</style>
