<template>
  <div class="">
    <div class="mt-12px">
      <Tree layout="horizontal" @on-tree-node-select="handleNodeSelect" />
    </div>
    <div class="mt-12px w-100% flex items-center justify-between mb-12px">
      <div class="flex items-center w-49%">
        <span class="flex-shrink-0 mr-8px">题型:</span>
        <OIWSelect
          :value="filterModel.examType"
          :options="examTypeOptions"
          :consistent-menu-width="false"
          placeholder="选择题型"
          @update:value="onExamTypeChange"
        ></OIWSelect>
      </div>
      <div class="flex items-center w-48%">
        <span class="flex-shrink-0 mr-8px">难度:</span>
        <OIWSelect
          :value="filterModel.difficulty"
          :options="difficultyOptions"
          :consistent-menu-width="false"
          placeholder="选择难度"
          @update:value="onDifficultyChange"
        ></OIWSelect>
      </div>
    </div>
    <div class="mt-12px w-100% flex items-center justify-between mb-12px">
      <div class="flex items-center w-49%">
        <span class="flex-shrink-0 mr-8px">来自:</span>
        <OIWSelect
          :value="filterModel.createdBy"
          :options="createdByOptions"
          filterable
          :consistent-menu-width="false"
          placeholder="选择分享人"
          @update:value="onCreatedByChange"
        ></OIWSelect>
      </div>
    </div>
    <OIWLoading :show="loading" width="200px" height="200px">
      <template v-if="problems.length">
        <div>筛选到题目总数：{{ total }}</div>
        <div class="divide-y-[1px] divide-[#DCD8E7]">
          <ProblemItem
            v-for="(problem, index) of problems"
            :key="problem.id"
            :problem="problem"
            is-hide-class-mode
            :index="index + 1"
            from="schoolResource"
            :tagConfig="{
              isShowCreatedName: true,
            }"
            :problemTypeConfig="{
              isSchool: true,
            }"
            showSchoolAiExplainButton
            :showInsertButton="type !== 'related' && type !== 'courseware'"
            :insertCount="problemsCount[problem.id]"
            @on-insert="clickInsert(problem)"
          >
            <template #insert>
              <span
                v-if="type === 'related'"
                :class="{
                  'color-#FA5A65':
                    relatedIds && relatedIds.includes(problem.id),
                  'bg-#5E80FF !border-[#5E80FF] color-#fff':
                    !relatedIds || !relatedIds.includes(problem.id),
                }"
                class="problem-operation cursor-pointer ml-8px flex items-center"
                @click="relatedInsert(problem)"
              >
                <DeleteIcon
                  v-if="relatedIds && relatedIds.includes(problem.id)"
                  class="w-16px h-16px mr-4px"
                  color="#FA5A65"
                />
                <AddIcon v-else class="w-16px h-16px mr-4px" color="#ffffff" />
                {{
                  relatedIds && relatedIds.includes(problem.id)
                    ? '取消关联'
                    : '添加关联'
                }}
              </span>
              <n-popselect
                v-if="type === 'courseware'"
                v-model:value="insertVal"
                :options="insertOptions"
                trigger="click"
                @update:value="(value) => clickInsertCourseware(problem, value)"
              >
                <span
                  class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
                  @click="clickInsertTip"
                >
                  <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
                  插入
                </span>
              </n-popselect>
            </template>
          </ProblemItem>
        </div>

        <BackTop />
      </template>
      <template v-else>
        <div class="pt-16%">
          <OIWStateBlock type="empty" title="暂无资源" />
        </div>
      </template>
    </OIWLoading>
    <div>
      <n-space v-if="total > 10" justify="end" style="margin-top: 20px">
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          class="custom-pagination"
          :item-count="total"
        />
      </n-space>
    </div>
  </div>
</template>

<script setup lang="tsx">
import {
  OIWSelect,
  OIWStateBlock,
  OIWLoading,
  useOIWDialog,
} from '@guanghe-pub/onion-ui-web'
import Tree from '@/pages/schoolResource/components/SystemTree.vue'
import type { TreeType } from '@/components/TeacherTree/types'
import { useLoading } from '@/hooks/useLoading'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import {
  DifficultyEnums,
  ProblemTypesEnums,
} from '@guanghe-pub/onion-problem-render'
import type { ProblemDetailType } from '@/pages/problem/utils'
import type { SchoolProblemTypeSimple } from '@/pages/schoolResource/schoolProblem/service'
import {
  getSchoolProblemDetailApi,
  getSchoolProblemsApi,
} from '@/pages/schoolResource/schoolProblem/service'
import AddIcon from '~icons/yc/add'
import DeleteIcon from '~icons/yc/minus'
import type { InsertParams } from '../types'
import { getDurationFromProblemDifficulty } from '../utils'
import { useSchoolTeachers } from '@/hooks/useSchoolTeachers'
import useSideResourceStore from '@/components/SideResource/store'

defineProps<{
  type?: 'insert' | 'related' | 'courseware'
  relatedIds?: string[]
}>()

const emits = defineEmits<{
  (e: 'insertProblem', val: InsertParams): void
  (e: 'relatedProblem', val: InsertParams): void
  (
    e: 'insertCoursewareProblem',
    val: YcType.ProblemType,
    insertVal: number,
    isSchool?: boolean,
  ): void
}>()

const { createdBy, createdByOptions } = useSchoolTeachers()
const sideResourceStore = useSideResourceStore()
const { problemsCount } = storeToRefs(sideResourceStore)

const filterModel = reactive<{
  examType?: string
  difficulty?: string | number
  createdBy?: string
}>({
  examType: '-1',
  difficulty: '-1',
  createdBy: createdBy.value || '',
})

const examTypeOptions = [
  {
    label: '全部',
    value: '-1',
  },
  {
    label: '单选题',
    value: 'single_choice',
  },
  {
    label: '简答题',
    value: 'shortAnswer',
  },
]

const difficultyOptions = [
  {
    label: '全部',
    value: '-1',
  },
  ...DifficultyEnums.map((el) => {
    return {
      label: el.name.toString(),
      value: `${el.score}`,
    }
  }),
]

const onExamTypeChange = (examType: string) => {
  filterModel.examType = examType
  page.value = 1
  fetchProblemAndDetails()
}

const onDifficultyChange = (difficulty: string) => {
  filterModel.difficulty = difficulty
  page.value = 1
  fetchProblemAndDetails()
}

const onCreatedByChange = (createdBy: string) => {
  filterModel.createdBy = createdBy
  page.value = 1
  fetchProblemAndDetails()
}

const curNode = ref<TreeType>()

// 左侧树切换
const csv = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}>()
const handleNodeSelect = (
  node: TreeType,
  csvData: {
    publisherId: YcType.CsvId
    semesterId: YcType.CsvId
    subjectId: YcType.CsvId
    stageId: YcType.CsvId
  },
) => {
  csv.value = csvData
  curNode.value = node
  page.value = 1
  resetOption()
  fetchProblemAndDetails()
}

const { loading, startLoading, endLoading } = useLoading(true)

const problemPoolParams = computed(() => {
  if (!csv.value)
    return {
      publisherId: -1,
      semesterId: -1,
      subjectId: -1,
      stageId: -1,
    }
  const baseParams = {
    ...csv.value,
    difficulty: filterModel.difficulty,
    examType: filterModel.examType === '-1' ? undefined : filterModel.examType,
    createdBy: filterModel.createdBy,
    page: page.value,
    pageSize: pageSize.value,
  }
  switch (curNode.value?.type) {
    case 'chapter':
      return { ...baseParams, chapterId: curNode.value.id }
    case 'section':
      return { ...baseParams, sectionId: curNode.value.id }
    case 'subSection':
      return { ...baseParams, subSectionId: curNode.value.id }
    default:
      return baseParams // 处理 'all' 和其他情况
  }
})

const getProblemTypeText = (type: string) => {
  if (type === 'exam') {
    return '简答题'
  }
  if (type === 'single_choice') {
    return '单选题'
  }
  const text = ProblemTypesEnums[type] || ''
  return text.endsWith('题') ? text : `${text}题`
}

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

const problems = ref<ProblemDetailType[]>([])
function resetOption() {
  filterModel.examType = '-1'
  filterModel.difficulty = '-1'
  filterModel.createdBy = createdBy.value || ''
}

async function fetchProblemDetails(simpleProblems: SchoolProblemTypeSimple[]) {
  const problemIds = simpleProblems.map((item) => item.problemId)
  if (!problemIds.length) return
  const res = await getSchoolProblemDetailApi({
    problemsId: problemIds,
  })
  const enhancedDetails = res.data.map((detail) => {
    // 在 problemPool 中查找匹配项
    const sourceItem = simpleProblems.find((p) => p.problemId === detail.id)
    return {
      ...detail,
      createdName: sourceItem?.createdName || '未知创建者', // 添加兜底值
      isShowProblemType: false,
      isHideExamType: true,
      isFavorite: false,
      createdBy: sourceItem?.createdBy,
    }
  })
  problems.value = enhancedDetails
}

async function fetchProblemPool() {
  problems.value = []
  const res = await getSchoolProblemsApi(problemPoolParams.value)
  await fetchProblemDetails(res.data)
  total.value = res.total
}

async function fetchProblemAndDetails() {
  startLoading()
  await fetchProblemPool()
  endLoading()
}

watch(page, async () => {
  startLoading()
  await fetchProblemPool()
  endLoading()
})

const clickInsert = (problem: ProblemDetailType) => {
  emits('insertProblem', {
    sourceId: problem.id,
    name: getProblemTypeText(problem.type),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType: 'SchoolProblem',
    publisherId: Number(csv.value?.publisherId),
    subjectId: Number(csv.value?.subjectId),
    stageId: Number(csv.value?.stageId),
    semesterId: Number(csv.value?.semesterId),
  })
}

const relatedInsert = (problem: ProblemDetailType) => {
  emits('relatedProblem', {
    sourceId: problem.id,
    name: getProblemTypeText(problem.type),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType: 'SchoolProblem',
    publisherId: Number(csv.value?.publisherId),
    subjectId: Number(csv.value?.subjectId),
    stageId: Number(csv.value?.stageId),
    semesterId: Number(csv.value?.semesterId),
  })
}

const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])

const clickInsertCourseware = (problem: ProblemDetailType, value: number) => {
  emits('insertCoursewareProblem', problem, value, true)
}

const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}
</script>

<style lang="scss" scoped>
.micro-video-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
</style>
