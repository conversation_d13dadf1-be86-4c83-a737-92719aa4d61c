<template>
  <div title="题目" closable class="w-100%">
    <CsvTreeSelect layout="horizontal" @change="onSelectChange" />
    <section style="border-bottom: 1px solid #dfdce8">
      <div class="w-100% flex items-center justify-between mb-12px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">题型:</span>
          <OIWSelect
            :value="filterModel.examType"
            :options="examTypeFilters"
            :consistent-menu-width="false"
            placeholder="选择题型"
            @update:value="onExamTypeChange"
          ></OIWSelect>
        </div>
        <div class="flex items-center w-48%">
          <span class="flex-shrink-0 mr-8px">正确率:</span>
          <OIWSelect
            :value="filterModel.accuracy"
            :options="accuracyOptions"
            :consistent-menu-width="false"
            placeholder="选择正确率"
            @update:value="onAccuracyChange"
          ></OIWSelect>
        </div>
      </div>

      <div class="w-100% flex items-center justify-between mb-24px">
        <div class="flex items-center w-49%">
          <span class="flex-shrink-0 mr-8px">难度:</span>
          <OIWSelect
            :value="filterModel.difficulty"
            :options="difficultyOptions"
            :consistent-menu-width="false"
            placeholder="选择难度"
            @update:value="onDifficultyChange"
          ></OIWSelect>
        </div>
        <div class="flex items-center w-48%">
          <span class="flex-shrink-0 mr-8px">地区:</span>
          <OIWSelect
            :value="filterModel.region"
            :options="regionFilters"
            :consistent-menu-width="false"
            placeholder="选择地区"
            @update:value="onRegionCodeChange"
          ></OIWSelect>
        </div>
      </div>
    </section>
    <section class="mx-auto flex pb-60px">
      <div class="flex-1">
        <div class="flex mt-24px justify-between items-center">
          <div>筛选到题目总数：{{ total }}</div>
          <div class="button-group">
            <div
              v-for="i in sortByOptions"
              :key="i.value"
              class="button-item"
              :class="{ active: i.value === filterModel.sortBy }"
              @click="onSortByChange(i.value)"
            >
              {{ i.label }}
            </div>
          </div>
        </div>
        <OIWLoading :show="loading" height="200px">
          <template v-if="problems.length">
            <div class="divide-y-[1px] divide-[#DCD8E7]">
              <ProblemItem
                v-for="(problem, index) of problems"
                :key="problem.id"
                :problem="problem"
                :index="index + 1"
                is-hide-class-mode
                :is-favorite="isFavorite(problem.id)"
                :showInsertButton="type !== 'related' && type !== 'courseware'"
                :insertCount="problemsCount[problem.id]"
                @on-favorite="favoriteItem(problem.id)"
                @on-insert="clickInsert(problem)"
              >
                <template #insert>
                  <span
                    v-if="type === 'related'"
                    :class="{
                      'color-#FA5A65':
                        relatedIds && relatedIds.includes(problem.id),
                      'bg-#5E80FF !border-[#5E80FF] color-#fff':
                        !relatedIds || !relatedIds.includes(problem.id),
                    }"
                    class="problem-operation cursor-pointer ml-8px flex items-center"
                    @click="relatedInsert(problem)"
                  >
                    <DeleteIcon
                      v-if="relatedIds && relatedIds.includes(problem.id)"
                      class="w-16px h-16px mr-4px"
                      color="#FA5A65"
                    />
                    <AddIcon
                      v-else
                      class="w-16px h-16px mr-4px"
                      color="#ffffff"
                    />
                    {{
                      relatedIds && relatedIds.includes(problem.id)
                        ? '取消关联'
                        : '添加关联'
                    }}
                  </span>
                  <n-popselect
                    v-if="type === 'courseware'"
                    v-model:value="insertVal"
                    :options="insertOptions"
                    trigger="click"
                    @update:value="
                      (value) => clickInsertCourseware(problem, value)
                    "
                  >
                    <span
                      class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
                      @click="clickInsertTip"
                    >
                      <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
                      插入
                    </span>
                  </n-popselect>
                </template>
              </ProblemItem>
            </div>
            <div class="mt-59px flex justify-end">
              <n-pagination
                class="custom-pagination"
                :page="page"
                :page-size="10"
                :item-count="total"
                @update:page="onPageChange"
              />
            </div>
            <BackTop />
          </template>
          <template v-else>
            <div class="mt-120px">
              <OIWStateBlock type="empty" title="暂无资源" />
            </div>
          </template>
        </OIWLoading>
      </div>
    </section>
  </div>
</template>

<script setup lang="tsx">
import { useAddFavorite } from '@/hooks/useFavorite'
import { useLoading } from '@/hooks/useLoading'
import type {
  ProblemFilterItem,
  ProblemPoolSimple,
} from '@/pages/problem/service'
import {
  getProblemDetailsByIdApi,
  getProblemFilterApi,
  getProblemPoolListApi,
} from '@/pages/problem/service'
import type { ProblemDetailType } from '@/pages/problem/utils'
import { getFavoriteIdsApi } from '@/service/common'
import { getDurationFromProblemDifficulty, getProblemTypeName } from '../utils'
import { DifficultyEnums } from '@guanghe-pub/onion-problem-render'
import type { InsertParams } from '../types'
import {
  OIWSelect,
  OIWStateBlock,
  useOIWDialog,
} from '@guanghe-pub/onion-ui-web'
import CsvTreeSelect from '@/pages/problem/components/Tree.vue'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import AddIcon from '~icons/yc/add'
import DeleteIcon from '~icons/yc/minus'
import { OIWLoading } from '@guanghe-pub/onion-ui-web'
import useSideResourceStore from '../store'

defineProps<{
  type?: 'insert' | 'related' | 'courseware'
  relatedIds?: string[]
}>()

const emits = defineEmits<{
  (e: 'insertProblem', val: InsertParams): void
  (
    e: 'insertCoursewareProblem',
    val: YcType.ProblemType,
    insertVal: number,
  ): void
  (e: 'relatedProblem', val: InsertParams): void
  (e: 'close'): void
}>()
const sideResourceStore = useSideResourceStore()
const { problemsCount } = storeToRefs(sideResourceStore)

const filterModel = reactive<{
  examType?: string
  accuracy?: string
  difficulty?: string
  region?: string
  sortBy: string
}>({
  examType: '',
  accuracy: '',
  difficulty: '',
  region: '',
  sortBy: 'usageCount',
})
const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])
const onExamTypeChange = (value: any) => {
  filterModel.examType = value
  onFilterChange()
}
const onAccuracyChange = (value: any) => {
  filterModel.accuracy = value
  onFilterChange()
}
const onDifficultyChange = (value: any) => {
  filterModel.difficulty = value
  onFilterChange()
}
const onRegionCodeChange = (value: any) => {
  filterModel.region = value
  onFilterChange()
}
const onSortByChange = (value: any) => {
  filterModel.sortBy = value
  onFilterChange()
}

const resetFilterModel = () => {
  filterModel.examType = ''
  filterModel.accuracy = ''
  filterModel.difficulty = ''
  filterModel.region = ''
  filterModel.sortBy = 'usageCount'
}

const accuracyOptions = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '20%以下',
    value: '0-20',
  },
  {
    label: '20%-40%',
    value: '20-40',
  },
  {
    label: '40%-60%',
    value: '40-60',
  },
  {
    label: '60%-80%',
    value: '60-80',
  },
  {
    label: '80%以上',
    value: '80-101',
  },
]

const difficultyOptions = [
  {
    label: '全部',
    value: '',
  },
  ...DifficultyEnums.map((el) => {
    return {
      label: `${el.name}`,
      value: `${el.score}`,
    }
  }),
]

const sortByOptions = [
  {
    label: '使用数从高到低',
    value: 'usageCount',
  },
  {
    label: '收藏数从高到低',
    value: 'favoriteCount',
  },
]

interface ChangeEvent {
  id: string
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  resourceType?: 'new'
  ids?: string[]
}

const currentIds = ref<string[]>([])
const currentCsv = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}>()
const onSelectChange = ({
  id,
  resourceType,
  subjectId,
  stageId,
  publisherId,
  semesterId,
  ids,
}: ChangeEvent) => {
  currentCsv.value = {
    subjectId,
    stageId,
    publisherId,
    semesterId,
  }
  currentIds.value = ids || []
  fetchFavorites()
  fetch(subjectId, stageId, id, resourceType)
}

const filters = ref<ProblemFilterItem[]>([])
const regionFilters = computed(() => {
  const regions = (
    filters.value.find((el) => el.name === 'region')?.enums || []
  ).map((el) => {
    return {
      label: el.showName,
      value: el.value,
    }
  })
  return [
    {
      label: '全部',
      value: '',
    },
    ...regions,
  ]
})
const examTypeFilters = computed(() => {
  const examFilters = (filters.value.find((el) => el.name === 'examType')
    ?.enums || []) as {
    showName: string
    value: string
  }[]
  return [
    {
      label: '全部',
      value: '',
    },
    ...examFilters.map((el) => {
      return {
        label: el.showName,
        value: el.value,
      }
    }),
  ]
})

const allProblemPool = shallowRef<ProblemPoolSimple[]>([])
const filterProblemPool = shallowRef<ProblemPoolSimple[]>([])
const page = ref(1)
const currentPageProblems = computed(() => {
  return filterProblemPool.value.slice((page.value - 1) * 10, page.value * 10)
})
const total = computed(() => filterProblemPool.value.length)
const { loading, startLoading, endLoading } = useLoading(true)

const fetch = async (
  subjectId: YcType.CsvId,
  stageId: YcType.CsvId,
  subSectionId: string,
  resourceType?: 'new',
) => {
  startLoading()
  if (subSectionId === '') {
    endLoading()
    allProblemPool.value = []
    filterProblemPool.value = []
    problems.value = []
    return
  }
  const res = await getProblemPoolListApi({
    subSectionId,
    resourceType,
  })
  const filtersRes = await getProblemFilterApi({
    subjectId,
    stageId,
    subSectionId,
    resourceType,
  })
  filters.value = filtersRes.filters
  allProblemPool.value = res.data
  filterProblemPool.value = res.data
  resetFilterModel()
  await filterCurrentProblemPool()
  endLoading()
}

async function onFilterChange() {
  startLoading()
  await filterCurrentProblemPool()
  endLoading()
}

async function filterCurrentProblemPool() {
  let filterProblems: ProblemPoolSimple[] = []
  filterProblems = allProblemPool.value.filter((el) => {
    let regionFilter = true
    let examTypeFilter = true
    let difficultyFilter = true
    let accuracyFilter = true
    if (filterModel.region) {
      regionFilter = el.region === filterModel.region
    }
    if (filterModel.examType) {
      examTypeFilter =
        (el.examType && el.examType === filterModel.examType) ||
        el.type === filterModel.examType
    }
    if (filterModel.accuracy) {
      const [min, max] = filterModel.accuracy.split('-')
      accuracyFilter =
        Number(el.accuracy) >= Number(min) && Number(el.accuracy) < Number(max)
    }
    if (filterModel.difficulty) {
      difficultyFilter = el.difficulty === Number(filterModel.difficulty)
    }
    return regionFilter && examTypeFilter && difficultyFilter && accuracyFilter
  })
  if (filterModel.sortBy) {
    if (filterModel.sortBy === 'usageCount') {
      filterProblems = filterProblems.sort((a, b) => {
        return Number(b.usageCount) - Number(a.usageCount)
      })
    } else {
      filterProblems = filterProblems.sort((a, b) => {
        return Number(b.favoriteCount) - Number(a.favoriteCount)
      })
    }
  }
  filterProblemPool.value = filterProblems
  page.value = 1
  if (filterProblemPool.value.length > 0) {
    await fetchProblemDetails()
  } else {
    problems.value = []
  }
}

const problems = ref<ProblemDetailType[]>([])

async function fetchProblemDetails() {
  const currentPageProblemIds = currentPageProblems.value.map(
    (el) => el.problemId,
  )
  if (currentPageProblemIds.length === 0) return
  const res = await getProblemDetailsByIdApi(currentPageProblemIds)
  problems.value = res.map((el) => {
    return {
      ...el,
      accuracy:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.accuracy || 0,
      favoriteCount:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.favoriteCount || '0',
      usageCount:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.usageCount || '0',
      region:
        allProblemPool.value.find(
          (simpleProblem) => simpleProblem.problemId === el.id,
        )?.region || '',
    }
  })
}
const onPageChange = (val: number) => {
  page.value = val
  fetchProblemDetails()
}

const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem',
    stage: currentCsv.value?.stageId,
    subject: currentCsv.value?.subjectId,
    semester: currentCsv.value?.semesterId,
    publisher: currentCsv.value?.publisherId,
  })
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()

const updateProblemFavoriteCount = (id: string, add = true) => {
  const simpleProblem = allProblemPool.value.find((el) => el.problemId === id)
  const problem = problems.value.find((el) => el.id === id)
  if (simpleProblem) {
    if (add) {
      const newCount = parseInt(simpleProblem.favoriteCount) + 1
      simpleProblem.favoriteCount = newCount.toString()
      if (problem) {
        problem.favoriteCount = newCount.toString()
      }
    } else {
      let newCount = parseInt(simpleProblem.favoriteCount) - 1
      newCount = newCount < 0 ? 0 : newCount
      simpleProblem.favoriteCount = newCount.toString()
      if (problem) {
        problem.favoriteCount = newCount.toString()
      }
    }
  }
}
const favoriteItem = async (problemId: string) => {
  if (!isFavorite(problemId)) {
    await addFavorite({
      resourceType: 'problem',
      resourceId: problemId,
      publisher: currentCsv.value?.publisherId,
      subject: currentCsv.value?.subjectId,
      stage: currentCsv.value?.stageId,
      semester: currentCsv.value?.semesterId,
      extra: {
        accuracy: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.accuracy,
        difficulty: allProblemPool.value.find(
          (el) => el.problemId === problemId,
        )?.difficulty,
        examType: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.examType,
        type: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.type,
        region: allProblemPool.value.find((el) => el.problemId === problemId)
          ?.region,
      },
    })
    updateProblemFavoriteCount(problemId)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    favoriteIds.value.delete(problemId)
    updateProblemFavoriteCount(problemId, false)
  }
}

const clickInsert = (problem: ProblemDetailType) => {
  const [chapterId, sectionId, subSectionId] = currentIds.value
  emits('insertProblem', {
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType: 'Problem',
    publisherId: Number(currentCsv.value?.publisherId),
    subjectId: Number(currentCsv.value?.subjectId),
    stageId: Number(currentCsv.value?.stageId),
    semesterId: Number(currentCsv.value?.semesterId),
    chapterId,
    sectionId,
    subSectionId,
  })
}

const clickInsertCourseware = (problem: ProblemDetailType, value: number) => {
  emits('insertCoursewareProblem', problem, value)
}

const relatedInsert = (problem: ProblemDetailType) => {
  const [chapterId, sectionId, subSectionId] = currentIds.value
  emits('relatedProblem', {
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    sourceType: 'Problem',
    publisherId: Number(currentCsv.value?.publisherId),
    subjectId: Number(currentCsv.value?.subjectId),
    stageId: Number(currentCsv.value?.stageId),
    semesterId: Number(currentCsv.value?.semesterId),
    chapterId,
    sectionId,
    subSectionId,
  })
}
const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}
</script>

<style lang="scss" scoped>
::v-deep(.tree.horizontal) {
  padding-bottom: 12px !important;
}

.button-group {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;

  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.medium-select {
  width: 150px;
}
</style>
