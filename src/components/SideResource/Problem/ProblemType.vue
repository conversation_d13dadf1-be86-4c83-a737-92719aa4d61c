<template>
  <div class="p-24px">
    <div class="flex justify-between font-semibold">
      <div class="flex justify-between">
        <OIWRadioButtonGroup
          :value="questionBankState.questionBankSource"
          :on-update-value="store.onSwitchQuestionBankSource"
        >
          <OIWRadioButton value="common">公共资源</OIWRadioButton>
          <OIWRadioButton v-if="isSchoolResourcesAuth" value="school">
            校本资源
          </OIWRadioButton>
          <OIWRadioButton value="collection">我的收藏</OIWRadioButton>
        </OIWRadioButtonGroup>
      </div>
      <CloseIcon class="cursor-pointer" @click="handleClose" />
    </div>
    <div class="w-100%">
      <div
        v-if="questionBankState.questionBankSource === 'common'"
        class="mt-16px"
      >
        <OIWRadioButtonGroup
          :value="questionBankState.questionCommon"
          :on-update-value="store.onSwitchQuestionCommon"
        >
          <OIWRadioButton value="problem">同步习题</OIWRadioButton>
          <OIWRadioButton value="paper">试卷真题</OIWRadioButton>
        </OIWRadioButtonGroup>
        <CommonProblemType
          v-if="questionBankState.questionCommon === 'problem'"
          @insert-problem="(e) => handleInsertProblem(e, 'problemList')"
        />
        <PaperProblemType
          v-if="questionBankState.questionCommon === 'paper'"
          @insert-problem="handleInsertPaperProblem"
        />
      </div>
      <SchoolProblem
        v-if="questionBankState.questionBankSource === 'school'"
        @insert-problem="(e) => handleInsertProblem(e, 'schoolList')"
      />
      <ProblemCollection
        v-if="questionBankState.questionBankSource === 'collection'"
        @insert-problem="(e) => handleInsertProblem(e, 'collection')"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { OIWRadioButton, OIWRadioButtonGroup } from '@guanghe-pub/onion-ui-web'
import CloseIcon from '~icons/yc/close-round-gray'
import type { InsertParams } from '../types'
import CommonProblemType from './CommonProblemType.vue'
import SchoolProblem from './SchoolProblem.vue'
import ProblemCollection from './ProblemCollection.vue'
import PaperProblemType from './PaperProblemType.vue'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useDurationTimer } from '@/hooks/useDurationTimer'
import { convertToNumber } from '@/components/SideResource/utils.ts'
import useSideResourceStore from '../store'
import { useAuth } from '@/hooks/useAuth.ts'

const props = defineProps<{
  drawerShow: boolean
}>()
const emits = defineEmits<{
  (e: 'insertProblem', val: InsertParams): void
  (e: 'close'): void
}>()
const { isSchoolResourcesAuth } = useAuth()
const store = useSideResourceStore()
const { questionBankState } = storeToRefs(store)
const handleInsertPaperProblem = (e: InsertParams) => {
  emits('insertProblem', {
    ...e,
    importFrom: 'Public',
  })
  const { stageId, subjectId, paperId } = e
  // 埋点
  buryPoint(
    'clickCourseDrawerProbelmInsert',
    {
      subjectId: convertToNumber(subjectId),
      stageId: convertToNumber(stageId),
      paperId,
      fromPageName: 'exam',
    },
    'course',
  )
}
const handleInsertProblem = (
  e: InsertParams,
  fromPageName: 'problemList' | 'schoolList' | 'collection',
) => {
  emits('insertProblem', {
    ...e,
    importFrom:
      fromPageName === 'problemList'
        ? 'Public'
        : fromPageName === 'schoolList'
          ? 'School'
          : 'MyFavorite',
  })
  // 埋点
  const {
    specialCourseId,
    stageId,
    subjectId,
    publisherId,
    semesterId,
    chapterId,
    sectionId,
    subSectionId,
    topicId,
  } = e
  const baseParams = {
    subjectId: convertToNumber(subjectId),
    stageId: convertToNumber(stageId),
    publisherId: convertToNumber(publisherId),
    semesterId: convertToNumber(semesterId),
    courseId: specialCourseId,
    chapterId,
    sectionId,
    subSectionId,
    topicId,
    fromPageName,
  }
  if (fromPageName === 'schoolList') {
    buryPoint('clickCourseDrawerProbelmInsert', baseParams, 'course')
  } else {
    buryPoint(
      'clickCourseDrawerProbelmInsert',
      {
        status: specialCourseId ? '培优课' : '同步课',
        ...baseParams,
      },
      'course',
    )
  }
}
// 埋点--停留时长
const { duration, startTime, closeTime } = useDurationTimer()

const buryPointDuration = (duration: number, fromPageName: string) => {
  if (duration > 0) {
    buryPoint(
      'getProblemListBrowseDuration',
      {
        pageName: 'drawer',
        fromPageName,
        duration,
      },
      'course',
    )
    closeTime()
  }
}

const getFromPageName = (type: string) => {
  return type === 'common'
    ? 'problemList'
    : type === 'school'
      ? 'schoolList'
      : 'collection'
}
watch(
  () => questionBankState.value.questionBankSource,
  (val, oldVal) => {
    if (val) {
      buryPoint(
        'enterProblemListBrowsePage',
        {
          pageName: 'drawer',
          fromPageName: getFromPageName(val),
        },
        'course',
      )

      startTime()
      if (oldVal && oldVal !== val && duration.value > 0) {
        const fromPage = getFromPageName(oldVal)
        buryPointDuration(duration.value, fromPage)
      }
    }
  },
  {
    immediate: true,
  },
)

const handleClose = () => {
  if (duration.value > 0) {
    const fromPage = getFromPageName(questionBankState.value.questionBankSource)
    buryPointDuration(duration.value, fromPage)
  }
  emits('close')
}

watch(
  () => props.drawerShow,
  (val) => {
    if (!val && duration.value > 0) {
      const fromPage = getFromPageName(
        questionBankState.value.questionBankSource,
      )
      buryPointDuration(duration.value, fromPage)
    }
  },
)

onBeforeUnmount(() => {
  closeTime()
})
</script>
