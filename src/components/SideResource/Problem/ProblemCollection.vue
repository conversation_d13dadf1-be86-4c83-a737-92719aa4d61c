<template>
  <section class="mt-16px flex pb-60px w-100%">
    <div class="flex-1">
      <section style="border-bottom: 1px solid #dfdce8">
        <CsvSelect
          publisherSemesterCanAll
          class="mb-12px"
          @change="onCsvSelectChange"
        />
        <div class="w-100% flex items-center justify-between mb-12px">
          <div class="flex items-center w-49%">
            <span class="flex-shrink-0 mr-8px">题型:</span>
            <OIWSelect
              :value="filterModel.examType"
              :options="examTypeFilters"
              :consistent-menu-width="false"
              placeholder="选择题型"
              @update:value="onExamTypeChange"
            ></OIWSelect>
          </div>
          <div class="flex items-center w-48%">
            <span class="flex-shrink-0 mr-8px">来源:</span>
            <OIWSelect
              :value="source"
              :options="sourceOptions"
              :consistent-menu-width="false"
              placeholder="选择题型"
              @update:value="onSourceChange"
            ></OIWSelect>
          </div>
        </div>
      </section>
      <div class="flex mt-24px justify-between items-center">
        <div>筛选到题目总数：{{ totalNumber }}</div>
      </div>
      <OIWLoading :show="loading" width="200px" height="200px">
        <div v-if="problems.length">
          <div class="divide-y-[1px] divide-[#DCD8E7]">
            <ProblemItem
              v-for="(problem, index) of problems"
              :key="problem.id"
              :problem="problem"
              :index="index + 1"
              isHideClassMode
              :is-favorite="isFavorite(problem.id)"
              :isHideFavorite="source === 'myUpload'"
              :tagConfig="{
                isShowCreatedName: true,
                isHideUsageCount: problem.resourceType === 'school_problem',
                isHideAccuracy: problem.resourceType === 'school_problem',
              }"
              :problemTypeConfig="{
                isSchool: true,
              }"
              :showSchoolAiExplainButton="
                problem.resourceType === 'school_problem'
              "
              :showInsertButton="type !== 'related' && type !== 'courseware'"
              :insertCount="problemsCount[problem.id]"
              @on-insert="clickInsert(problem)"
              @on-favorite="favoriteItem(problem)"
            >
              <template #insert>
                <span
                  v-if="type === 'related'"
                  :class="{
                    'color-#FA5A65':
                      relatedIds && relatedIds.includes(problem.id),
                    'bg-#5E80FF !border-[#5E80FF] color-#fff':
                      !relatedIds || !relatedIds.includes(problem.id),
                  }"
                  class="problem-operation cursor-pointer ml-8px flex items-center"
                  @click="relatedInsert(problem)"
                >
                  <DeleteIcon
                    v-if="relatedIds && relatedIds.includes(problem.id)"
                    class="w-16px h-16px mr-4px"
                    color="#FA5A65"
                  />
                  <AddIcon
                    v-else
                    class="w-16px h-16px mr-4px"
                    color="#ffffff"
                  />
                  {{
                    relatedIds && relatedIds.includes(problem.id)
                      ? '取消关联'
                      : '添加关联'
                  }}
                </span>
                <n-popselect
                  v-if="type === 'courseware'"
                  v-model:value="insertVal"
                  :options="insertOptions"
                  trigger="click"
                  @update:value="
                    (value) => clickInsertCourseware(problem, value)
                  "
                >
                  <span
                    class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
                    @click="clickInsertTip"
                  >
                    <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
                    插入
                  </span>
                </n-popselect>
              </template>
            </ProblemItem>
          </div>
          <div class="mt-59px flex justify-end">
            <n-pagination
              class="custom-pagination"
              :page="page"
              :page-size="pageSize"
              :item-count="totalNumber"
              @update:page="onPageChange"
            />
          </div>
        </div>
        <div v-else>
          <div class="pt-200px">
            <OIWStateBlock type="empty" title="暂无资源" />
          </div>
        </div>
      </OIWLoading>
    </div>
  </section>
</template>

<script setup lang="tsx">
import {
  OIWLoading,
  OIWStateBlock,
  OIWSelect,
  useOIWDialog,
} from '@guanghe-pub/onion-ui-web'
import ProblemItem from '@/components/ProblemSingle/index.vue'
import { ExamProblemTypeEnums } from '@guanghe-pub/onion-problem-render'
import CsvSelect from '@/pages/problem/components/CsvSelect.vue'
import { useLoading } from '@/hooks/useLoading'
import type { ProblemDetailType } from '@/pages/problem/utils'
import type { FavoriteItem, FavoriteType } from '@/service/common'
import { getFavoriteIdsApi } from '@/service/common'
import { useAddFavorite, useFavorite } from '@/hooks/useFavorite'
import { useProblem } from '@/hooks/useProblem.ts'
import { groupBy } from 'lodash-es'
import type { ProblemSimpleType } from '@/pages/problem/utils.ts'
import type {
  SchoolProblemParams,
  SchoolProblemTypeSimple,
} from '@/pages/schoolResource/schoolProblem/service'
import {
  getSchoolProblemDetailApi,
  getSchoolProblemsApi,
} from '@/pages/schoolResource/schoolProblem/service'
import { useAuth } from '@/hooks/useAuth.ts'
import useBooksProblem from '@/store/booksProblem'
import DeleteIcon from '~icons/yc/minus'
import AddIcon from '~icons/yc/add'
import type { InsertParams } from '../types'
import { getDurationFromProblemDifficulty, getProblemTypeName } from '../utils'
import useSideResourceStore from '@/components/SideResource/store'

defineProps<{
  type?: 'insert' | 'related' | 'courseware'
  relatedIds?: string[]
}>()
const emits = defineEmits<{
  (e: 'insertProblem', val: InsertParams): void
  (e: 'relatedProblem', val: InsertParams): void
  (
    e: 'insertCoursewareProblem',
    val: YcType.ProblemType,
    insertVal: number,
    isSchool: boolean,
  ): void
}>()
type ValueType = string | number | undefined | null

const booksProblemStore = useBooksProblem()
const { fetchDetails } = useProblem()
const sideResourceStore = useSideResourceStore()
const { problemsCount } = storeToRefs(sideResourceStore)
// 来源
const sourceOptions = [
  {
    label: '我的收藏',
    value: 'myCollect',
  },
  {
    label: '我的上传',
    value: 'myUpload',
  },
]
const source = ref('myCollect')

onMounted(() => {
  booksProblemStore.getBookProblems()
})

const onSourceChange = async (value: ValueType) => {
  source.value = value as string
  startLoading()
  page.value = 1
  await fetchList()
  endLoading()
}

// 题型
const filterModel = reactive<{
  examType?: string
}>({
  examType: '',
})
const onExamTypeChange = (value: ValueType) => {
  filterModel.examType = value as string
  onFilterChange()
}
const resetFilterModel = () => {
  filterModel.examType = ''
}

// 列表
const page = ref(1)
const pageSize = ref(10) // 我的上传列表使用
const total = ref(0) // 我的上传列表使用
const myUploadList = ref<SchoolProblemTypeSimple[]>([])
const problems = shallowRef<ProblemDetailType[]>([])

const { userId } = useAuth()

// 我的上传获取题目详情
async function fetchSchoolProblemDetails(
  simpleProblems: SchoolProblemTypeSimple[],
) {
  const problemIds = simpleProblems.map((item) => item.problemId)
  if (!problemIds.length) {
    problems.value = []
    return
  }
  const res = await getSchoolProblemDetailApi({
    problemsId: problemIds,
  })
  problems.value = res.data.map((detail) => {
    const sourceItem = simpleProblems.find((p) => p.problemId === detail.id)
    return {
      ...detail,
      ...sourceItem,
      createdName: sourceItem?.createdName || '未知创建者', // 添加兜底值
      isShowProblemType: false,
      isHideExamType: true,
      resourceType: 'school_problem', // 我的上传列表都是校本题
    }
  })
}

const fetchMyUpload = async () => {
  const params: SchoolProblemParams = {
    difficulty: -1,
    stageId: currentCsv.value?.stageId || '-1',
    subjectId: currentCsv.value?.subjectId || '-1',
    type: filterModel.examType,
    createdBy: userId.value,
    page: page.value,
    pageSize: pageSize.value,
  }
  if (currentCsv.value?.publisherId && currentCsv.value?.publisherId !== '0') {
    params.publisherId = currentCsv.value.publisherId
  }
  if (currentCsv.value?.semesterId && currentCsv.value?.semesterId !== '0') {
    params.semesterId = currentCsv.value.semesterId
  }
  const res = await getSchoolProblemsApi(params)
  if (res.data) {
    myUploadList.value = res.data
    total.value = res.total
    await fetchSchoolProblemDetails(res.data)
  }
}

const fetchList = async () => {
  await resetFilterModel()
  if (source.value === 'myCollect') {
    // 我的收藏列表，前端分页
    await fetch()
    filterProblemPool.value = favorites.value
    await filterCurrentProblemPool()
  } else {
    // 我的上传获取列表，后端分页
    await fetchMyUpload()
  }
  getFiltersFromSimpleProblem()
  await fetchFavorites()
}

const currentCsv = ref<{
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}>()
const onCsvSelectChange = async ({
  publisherId,
  stageId,
  subjectId,
  semesterId,
}: {
  publisherId: YcType.CsvId
  stageId: YcType.CsvId
  subjectId: YcType.CsvId
  semesterId: YcType.CsvId
}) => {
  loading.value = true
  currentCsv.value = {
    publisherId,
    semesterId,
    subjectId,
    stageId,
  }
  page.value = 1
  await fetchList()
  endLoading()
}

// 查询到的题目总数
const totalNumber = computed(() => {
  return source.value === 'myCollect'
    ? filterProblemPool.value.length
    : total.value
})
// 当前页面的习题简单结构
const currentPageProblems = computed(() => {
  if (source.value === 'myCollect') {
    return filterProblemPool.value.slice((page.value - 1) * 10, page.value * 10)
  }
  return myUploadList.value
})

// 题型的筛选项，我的收藏与我的上传来源不一样
interface Option {
  label: string
  value: string | number | undefined
}
const examTypeFilters = ref<Option[]>([])
function getFiltersFromSimpleProblem() {
  if (source.value === 'myCollect') {
    examTypeFilters.value = [
      {
        label: '全部',
        value: '',
      },
      ...Object.keys(groupBy(favorites.value, 'extra.examType'))
        .map((el) => {
          return {
            value: el,
            label: ExamProblemTypeEnums[el],
          }
        })
        .filter((el) => !!el.value && !!el.label),
    ]
  } else {
    examTypeFilters.value = [
      {
        label: '全部',
        value: '',
      },
      {
        label: '单选题',
        value: 'single_choice',
      },
      {
        label: '简答题',
        value: 'exam',
      },
    ]
  }
}

const favoriteFilter = computed(() => {
  const baseParams: {
    resourceType: FavoriteType
    stage: YcType.CsvId
    subject: YcType.CsvId
    publisher?: YcType.CsvId
    semester?: YcType.CsvId
  } = {
    resourceType: 'problem,school_problem' as FavoriteType,
    stage: currentCsv.value?.stageId || '-1',
    subject: currentCsv.value?.subjectId || '-1',
  }
  if (currentCsv.value?.publisherId && currentCsv.value?.publisherId !== '0') {
    baseParams.publisher = currentCsv.value.publisherId
  }
  if (currentCsv.value?.semesterId && currentCsv.value?.semesterId !== '0') {
    baseParams.semester = currentCsv.value.semesterId
  }
  return {
    ...baseParams,
  }
})

const { favorites, fetch } = useFavorite(favoriteFilter, {
  pageSize: 99999,
})

const { loading, startLoading, endLoading } = useLoading(true)
const filterProblemPool = shallowRef<FavoriteItem[]>([])

async function onFilterChange() {
  page.value = 1
  startLoading()
  if (source.value === 'myCollect') {
    await filterCurrentProblemPool()
  } else {
    await fetchMyUpload()
  }
  endLoading()
}

// 我的收藏题型筛选逻辑
async function filterCurrentProblemPool() {
  let filterProblems: FavoriteItem[] = []
  filterProblems = favorites.value.filter((el) => {
    let examTypeFilter = true
    if (filterModel.examType) {
      examTypeFilter =
        el.extra?.examType === filterModel.examType ||
        el.extra?.type === filterModel.examType
    }
    return examTypeFilter
  })
  filterProblemPool.value = filterProblems
  if (filterProblemPool.value.length > 0) {
    await fetchProblemDetails()
  } else {
    problems.value = []
  }
}

// 获取我的收藏题目详情
async function fetchProblemDetails() {
  if (source.value === 'myUpload') {
    return
  }
  const currentPageSimpleProblem: ProblemSimpleType[] =
    currentPageProblems.value.map((el) => ({
      problemId: (el as any).resourceId,
      resourceType: (el as any).resourceType,
    }))
  if (currentPageSimpleProblem.length === 0) return
  const res = await fetchDetails(currentPageSimpleProblem)
  problems.value = res.map((el) => {
    return {
      ...el,
      accuracy:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.extra?.accuracy || 0,
      favoriteCount:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.favoriteCount || 0,
      usageCount:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.usageCount || 0,
      region:
        favorites.value.find(
          (simpleProblem) => simpleProblem.resourceId === el.id,
        )?.extra?.region || '',
    }
  })
}

const onPageChange = (val: number) => {
  page.value = val
  if (source.value === 'myCollect') {
    fetchProblemDetails()
  } else {
    fetchMyUpload()
  }
}

// 收藏
const favoriteIds = ref<Set<string>>(new Set())
async function fetchFavorites() {
  const res = await getFavoriteIdsApi({
    resourceType: 'problem,school_problem' as FavoriteType,
    stage: currentCsv.value?.stageId,
    subject: currentCsv.value?.subjectId,
    semester: currentCsv.value?.semesterId,
    publisher: currentCsv.value?.publisherId,
  })
  favoriteIds.value = new Set(res.data)
}
const isFavorite = (problemId: string) => {
  return favoriteIds.value.has(problemId)
}
const { addFavorite, deleteFavorite } = useAddFavorite()

const updateProblemFavoriteCount = (problem: ProblemDetailType, add = true) => {
  if (!problem) return
  if (add) {
    const newCount = Number(problem.favoriteCount) + 1
    problem.favoriteCount = newCount.toString()
  } else {
    let newCount = Number(problem.favoriteCount) - 1
    newCount = newCount < 0 ? 0 : newCount
    problem.favoriteCount = newCount.toString()
  }
}

const favoriteItem = async (problem: ProblemDetailType) => {
  const { problemId, resourceType } = problem
  if (!isFavorite(problemId)) {
    await addFavorite({
      resourceType: resourceType,
      resourceId: problemId,
      publisher: currentCsv.value?.publisherId,
      subject: currentCsv.value?.subjectId,
      stage: currentCsv.value?.stageId,
      semester: currentCsv.value?.semesterId,
      extra: {
        accuracy: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.accuracy,
        difficulty: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.difficulty,
        examType: favorites.value.find((el) => el.resourceId === problemId)
          ?.extra?.examType,
        type: favorites.value.find((el) => el.resourceId === problemId)?.extra
          ?.type,
        region: favorites.value.find((el) => el.resourceId === problemId)?.extra
          ?.region,
      },
    })
    updateProblemFavoriteCount(problem)
    favoriteIds.value.add(problemId)
  } else {
    await deleteFavorite({
      resourceId: problemId,
    })
    favoriteIds.value.delete(problemId)
    updateProblemFavoriteCount(problem, false)
  }
}

const clickInsert = (problem: ProblemDetailType) => {
  emits('insertProblem', {
    sourceType:
      problem.resourceType === 'school_problem' ? 'SchoolProblem' : 'Problem',
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    publisherId: Number(currentCsv.value?.publisherId),
    semesterId: Number(currentCsv.value?.semesterId),
    subjectId: Number(currentCsv.value?.subjectId),
    stageId: Number(currentCsv.value?.stageId),
  })
}

const relatedInsert = (problem: ProblemDetailType) => {
  emits('relatedProblem', {
    sourceType:
      problem.resourceType === 'school_problem' ? 'SchoolProblem' : 'Problem',
    sourceId: problem.id,
    name: getProblemTypeName(problem),
    duration: getDurationFromProblemDifficulty(problem.difficulty as string),
    publisherId: Number(currentCsv.value?.publisherId),
    semesterId: Number(currentCsv.value?.semesterId),
    subjectId: Number(currentCsv.value?.subjectId),
    stageId: Number(currentCsv.value?.stageId),
  })
}

const insertVal = ref()
const insertOptions = ref([
  {
    label: '仅题干',
    value: 1,
  },
  {
    label: '仅答案',
    value: 2,
  },
  {
    label: '题干+答案',
    value: 3,
  },
])

const dialog = useOIWDialog()
const clickInsertTip = () => {
  if (window.localStorage.getItem('insertGifTip') === 'true') return
  window.localStorage.setItem('insertGifTip', 'true')
  dialog.create({
    title: '教学提示',
    style: {
      width: '720px',
      paddingBottom: 0,
    },
    content: () => {
      return (
        <div>
          <img
            class="rounded-12px"
            src="https://fp.yangcong345.com/middle/1.0.0/20241127195158_rec_ (2).gif"
            alt=""
          />
          <div class="mt-16px pb-32px color-#57526C">
            插入题目会同时插入图片+链接，授课时，点击题目下方链接「课堂模式」，可以全屏展示题目，调整显示效果，支持展开收起题目正确答案和解析
          </div>
        </div>
      )
    },
  })
}

const clickInsertCourseware = (problem: ProblemDetailType, value: number) => {
  emits(
    'insertCoursewareProblem',
    problem,
    value,
    problem.resourceType === 'school_problem',
  )
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 3px 4px;

  border: 1px solid #c5c1d4;
  border-radius: 343px;

  .button-item {
    padding: 5px 16px;
    font-size: 14px;
    color: #8a869e;
    cursor: pointer;
    border-radius: 350px;

    &.active {
      font-weight: 600;
      color: #5e80ff;
      background: #f4f6ff;
    }
  }
}

.medium-select {
  width: 150px;
}

.cascader-collection {
  ::v-deep(.n-base-selection) {
    height: 34px !important;
    font-size: 12px;
    border-radius: 12px;
    --n-height: 32px !important;
  }
}
</style>
