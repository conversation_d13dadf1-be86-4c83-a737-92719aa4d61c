import { acceptHMRUpdate, defineStore } from 'pinia'
import type { TopicDetail } from '@/pages/microVideo/service'
import {
  postSystemTopicDetails,
  postSpecialTopicDetails,
} from '@/pages/microVideo/service'
import { useLocalSpecialCourse } from '@/hooks/useLocalSpecialCourse'
import { debounce } from 'lodash-es'
import { formattedDuration } from '@/pages/microVideo/service'
import type { ChangeParams, CustomTreeType } from '@/hooks/useTree'
import type { SchoolVideoDetail } from '@/pages/schoolResource/schoolVideo/service'
import { useAuth } from '@/hooks/useAuth'
interface TopicSimple {
  id: string
  name: string
  pay?: boolean
}
// 从叶子节点收集所有话题，返回话题列表
const collectTopicsFromLeafNodes = (
  nodes: CustomTreeType[],
  topics: (CustomTreeType | YcType.TopicDetail)[],
) => {
  nodes.forEach((node) => {
    if (!node.children || node.children.length === 0) {
      if (node.topics) {
        topics.push(...node.topics)
      } else {
        topics.push(node)
      }
    } else {
      collectTopicsFromLeafNodes(node.children, topics)
    }
  })
  return topics
}

// 从叶子节点搜索话题，返回搜索列表
const searchTopicsFromLeafNodes = (
  nodes: CustomTreeType[],
  searchList: CustomTreeType[],
) => {
  nodes.forEach((node) => {
    if (!node.children || node.children.length === 0) {
      searchList.push(node)
    } else {
      searchTopicsFromLeafNodes(node.children, searchList)
    }
  })
  return searchList
}

// 资源来源类型定义
export type SourceType = 'common' | 'school' | 'collection'

// 视频播放信息接口定义
export interface VideoPlayType {
  topicId: string // 话题ID
  specialCourseId: string | undefined // 特殊课程ID
  clipId: string | undefined // 片段ID
}

// 视频状态管理接口
export interface VideoState {
  sourceType: SourceType
  searchValue?: string
  videoType: 'systemLesson' | 'specialCourse'
  pageType: 'list' | 'detail'
  videoTopicDetail?: TopicDetail
  detailType: 'video' | 'practice'
  topicNode?: CustomTreeType
  searchSelectId?: string
  currentTrees: CustomTreeType[]
  searchList: {
    id: string
    name: string
    coursewareCount: number
  }[]
  topicList: string[]
  videoPlayInfo: VideoPlayType
  listLoading: boolean
  videoList: TopicDetail[]
  pageSize: number
  page: number
}

// 题库状态管理接口
export interface QuestionBankState {
  questionBankSource: SourceType
  questionCommon: 'problem' | 'paper'
}

// 校本资源
export interface SchoolVideoState {
  pageType: 'list' | 'detail'
  detailType: 'video' | 'practice'
  videoId: string
  videoInfo: SchoolVideoDetail
  listLoading: boolean
  schoolNode?: CustomTreeType
}

export interface ExamPaperState {
  pageType: 'list' | 'detail'
  sourceType: SourceType
  detailType: 'public' | 'school'
  paperId: string
  subjectId: number
  stageId: number
  publisherId: number
}

// 侧边栏资源管理 Store
const useSideResourceStore = defineStore('SideResource', () => {
  // 当前激活的标签页（视频/问题）
  const activeTab = ref<'video' | 'problem' | 'examPaper' | 'wrongBook'>(
    'video',
  )

  // 选中的题目数量
  const problemsCount = ref<Record<string, number>>({})
  // 选中的题目IDs
  const selectProblemIds = ref<string[]>([])

  const groupIds = ref<string[]>([])

  // CSV相关配置（出版商、学期、学科、阶段）
  const csv = ref<{
    publisherId?: YcType.CsvId
    semesterId?: YcType.CsvId
    subjectId: YcType.CsvId
    stageId: YcType.CsvId
  }>()

  const schoolCsv = ref<{
    publisherId?: YcType.CsvId
    semesterId?: YcType.CsvId
    subjectId: YcType.CsvId
    stageId: YcType.CsvId
  }>()

  // 获取特殊课程ID
  const { specialCourseId } = useLocalSpecialCourse(csv)
  const { isFromDingDing } = useAuth()

  // 更新特殊课程ID
  const videoTreeSpecialIdChange = (val: YcType.CsvId) => {
    specialCourseId.value = `${val}`
  }

  // 题库初始化
  const questionBankState = reactive<QuestionBankState>({
    questionBankSource: 'common',
    questionCommon: 'problem',
  })

  // 视频状态初始化
  const videoState = reactive<VideoState>({
    sourceType: 'common',
    videoType: 'systemLesson',
    videoTopicDetail: undefined,
    detailType: 'video',
    searchSelectId: undefined,
    currentTrees: [],
    searchValue: undefined,
    searchList: [],
    topicList: [],
    pageType: 'list',
    videoPlayInfo: {
      topicId: '',
      specialCourseId: undefined,
      clipId: undefined,
    },
    listLoading: true,
    videoList: [],
    pageSize: 8,
    page: 1,
  })

  // 校本资源
  const schoolVideoState = reactive<SchoolVideoState>({
    videoId: '',
    pageType: 'list',
    detailType: 'video',
    videoInfo: {
      id: '',
      name: '',
      createdBy: '',
      createdName: '',
      duration: 0,
      isEdit: false,
      chapterId: '',
      sectionId: '',
      subsectionId: '',
      problems: [],
    },
    listLoading: true,
  })

  const examPaperState = reactive<ExamPaperState>({
    pageType: 'list',
    sourceType: 'common',
    detailType: 'public',
    paperId: '',
    subjectId: -1,
    stageId: -1,
    publisherId: -1,
  })

  // 重置视频状态到初始值
  const resetVideoState = () => {
    videoState.sourceType = 'common'
    videoState.videoType = 'systemLesson'
    videoState.videoTopicDetail = undefined
    videoState.detailType = 'video'
    videoState.searchSelectId = undefined
    videoState.currentTrees = []
    videoState.searchValue = undefined
    videoState.searchList = []
    videoState.pageType = 'list'
    videoState.videoList = []
    videoState.pageSize = 8
    videoState.page = 1
    videoState.listLoading = true
  }

  // 切换资源类型（普通/学校/收藏）
  const onSwitchSourceType = (type: SourceType) => {
    resetVideoState()
    videoState.sourceType = type
  }

  // 切换试卷资源类型（普通/学校/收藏）
  const onSwitchExamPaperType = (type: SourceType) => {
    examPaperState.sourceType = type
  }

  // 处理视频详情，特别是关键点时间线
  const videoDetail = computed(() => {
    if (!videoState.videoTopicDetail) return
    const temp = videoState.videoTopicDetail.video
    temp.topicName = videoState.videoTopicDetail.name || ''
    temp.topicId = videoState.videoTopicDetail.id || ''
    if (temp.keyPoints && temp.keyPoints.length) {
      const points =
        temp.keyPoints[0].content === '片头' && temp.keyPoints[0].time === 0
          ? temp.keyPoints.slice(1)
          : temp.keyPoints
      temp.keyPoints = points.map((point, index, array) => {
        let time
        let startTime
        let endTime
        if (index !== array.length - 1) {
          time = array[index + 1].time - point.time
          startTime = point.time
          endTime = array[index + 1].time
        } else {
          time = temp.duration - point.time
          startTime = point.time
          endTime = temp.duration
        }
        const durationFormat = `${formattedDuration(time)}`
        return {
          ...point,
          offset: time,
          durationFormat,
          timeline: `${formattedDuration(startTime)} - ${formattedDuration(
            endTime,
          )}`,
        }
      })
    }
    return temp
  })

  const schoolVideoDetail = computed(() => {
    if (!schoolVideoState.videoInfo) return
    return schoolVideoState.videoInfo
  })

  // 获取当前树的所有话题
  const currentTreeAllTopics = computed(() => {
    const topics: CustomTreeType[] = []
    return collectTopicsFromLeafNodes(videoState.currentTrees, topics)
  })

  // 获取当前树的所有可搜索项
  const currentTreeAllSearchList = computed(() => {
    const allSearchList: CustomTreeType[] = []
    return searchTopicsFromLeafNodes(videoState.currentTrees, allSearchList)
  })

  // 更新激活标签
  const updateActiveTab = (
    tab: 'video' | 'problem' | 'examPaper' | 'wrongBook',
  ) => {
    activeTab.value = tab
  }

  // 更新视频类型并重置相关状态
  const updateVideoType = (type: 'systemLesson' | 'specialCourse') => {
    videoState.listLoading = true
    videoState.videoType = type
    videoState.topicList = []
    videoState.videoList = []
    videoState.currentTrees = []
    videoState.searchValue = undefined
  }

  // 更新树数据
  const videoTreeDataChange = (tree: CustomTreeType[]) => {
    videoState.currentTrees = tree
  }

  // 处理树变化
  const videoTreeTopicChange = (val: ChangeParams) => {
    videoState.searchValue = ''
    videoState.searchSelectId = ''
    csv.value = val.csv
    videoState.topicNode = val.node
    if (videoState.videoType === 'systemLesson') {
      const filterTopics = isFromDingDing.value
        ? (val.nodeAllTopics as YcType.TopicDetail[])?.filter(
            (item: TopicSimple) => !item.pay,
          )
        : (val.nodeAllTopics as YcType.TopicDetail[])
      videoState.topicList = filterTopics?.map((item) => item.id) || []
    } else {
      videoState.topicList =
        (val.nodeAllTopics as string[])?.map((item) => item) || []
    }
  }

  // 过滤收藏列表
  const filterCollectionList = () => {
    const { searchValue } = videoState
    if (searchValue && currentTreeAllTopics.value?.length) {
      videoState.topicList =
        currentTreeAllTopics.value
          .filter((item: { id: string; name: string }) =>
            item.name.includes(searchValue),
          )
          .map((item: { id: string; name: string }) => item.id) || []
    }
  }

  // 防抖处理的过滤收藏列表
  const throttledFilterCollectionList = debounce(
    filterCollectionList,
    300,
  ) as () => void

  // 搜索处理函数
  function searchHandler() {
    videoState.searchList = currentTreeAllSearchList.value
      .filter((item: any) => item.name.includes(videoState.searchValue))

      .map((item) => {
        // 钉钉需要过滤同步课付费微课
        const filterTopics = isFromDingDing.value
          ? item.topics?.filter((item: TopicSimple) => !item.pay)
          : item.topics
        return {
          id: item.id,
          name: item.name,
          coursewareCount: (filterTopics && filterTopics.length) || 0,
        }
      })
      .sort((a, b) => b.coursewareCount - a.coursewareCount)
      .slice(0, 5)
  }

  // 防抖处理的搜索函数
  const throttledSearchHandler = debounce(searchHandler, 300) as () => void

  // 监听搜索值变化
  watch(
    () => videoState.searchValue,
    () => {
      throttledFilterCollectionList()
      throttledSearchHandler()
    },
  )

  // 更新搜索选中状态
  const changeSearch = (id: string) => {
    videoState.searchValue = ''
    videoState.searchSelectId = id
  }

  // 获取话题详情数据
  const fetchData = async (topicIds: string[]) => {
    try {
      let response
      if (videoState.videoType === 'systemLesson') {
        response = await postSystemTopicDetails({ topicIds })
      } else if (videoState.videoType === 'specialCourse') {
        response = await postSpecialTopicDetails({ topicIds })
      }
      return response && response.data
    } catch (error) {
      console.error('Error fetching data:', error)
      return null
    }
  }

  // 获取视频列表数据
  const fetchVideoList = async (isNeed = true) => {
    const fetchTopicIds = videoState.topicList.filter(
      (item, index) =>
        index < videoState.page * videoState.pageSize &&
        index >= (videoState.page - 1) * videoState.pageSize,
    )
    if (isNeed) {
      videoState.listLoading = true
    }
    if (!fetchTopicIds.length) {
      videoState.videoList = []
      videoState.listLoading = false
      return
    }
    const data = await fetchData(fetchTopicIds).finally(() => {
      videoState.listLoading = false
    })
    if (data) {
      const filterTopics = isFromDingDing.value
        ? data?.filter((item: TopicSimple) => !item.pay)
        : data
      videoState.videoList = filterTopics
    }
  }

  // 切换资源来源类型
  const onSwitchQuestionBankSource = (type: SourceType) => {
    questionBankState.questionBankSource = type
  }

  // 切换资源类型
  const onSwitchQuestionCommon = (type: 'problem' | 'paper') => {
    questionBankState.questionCommon = type
  }

  const onChangeExamPaperPageType = (
    type: 'list' | 'detail',
    id?: string,
    detailType?: 'public' | 'school',
    stageId?: number,
    subjectId?: number,
    publisherId?: number,
  ) => {
    examPaperState.pageType = type
    if (type === 'detail') {
      examPaperState.detailType = detailType || 'public'
      examPaperState.paperId = id || ''
      examPaperState.stageId = stageId || -1
      examPaperState.subjectId = subjectId || -1
      examPaperState.publisherId = publisherId || -1
    } else {
      examPaperState.detailType = 'public'
      examPaperState.paperId = ''
      examPaperState.stageId = -1
      examPaperState.subjectId = -1
      examPaperState.publisherId = -1
    }
  }

  // 设置班级ID
  const setGroupIds = (ids: string[]) => {
    groupIds.value = ids
  }

  // 监听话题列表变化
  watch(
    () => videoState.topicList,
    () => {
      videoState.page = 1
      fetchVideoList()
    },
  )

  // 监听页码变化
  watch(
    () => videoState.page,
    () => {
      fetchVideoList()
    },
  )

  // 设置选中的题目ID数量
  const setSelectProblemIds = (ids: string[]) => {
    const obj: Record<string, number> = {}
    if (ids.length) {
      ids.reduce((acc, cur) => {
        if (acc[cur]) {
          acc[cur] = acc[cur] + 1
        } else {
          acc[cur] = 1
        }
        return acc
      }, obj)
    }
    selectProblemIds.value = ids
    problemsCount.value = obj
  }

  // 返回store暴露的属性和方法
  return {
    activeTab, // 当前激活的标签页
    problemsCount, // 选中的题目数量
    selectProblemIds, // 选中的题目ID
    videoState, // 视频状态管理
    csv, // CSV相关配置
    videoDetail, // 视频详情
    specialCourseId, // 特殊课程ID
    currentTreeAllTopics, // 当前树的所有话题
    currentTreeAllSearchList, // 当前树的所有可搜索项
    onSwitchSourceType, // 切换资源类型
    updateActiveTab, // 更新激活标签
    updateVideoType, // 更新视频类型
    videoTreeDataChange, // 更新视频树数据
    videoTreeTopicChange, // 处理视频树话题变化
    videoTreeSpecialIdChange, // 更新特殊课程ID
    throttledSearchHandler, // 防抖处理的搜索函数
    throttledFilterCollectionList, // 防抖处理的过滤收藏列表
    changeSearch, // 更新搜索选中状态
    fetchVideoList, // 获取视频列表数据
    schoolVideoState, // 校本资源
    schoolVideoDetail, // 校本资源详情
    schoolCsv, // 校本资源CSV配置
    questionBankState,
    onSwitchQuestionBankSource, // 切换资源来源类型
    onSwitchQuestionCommon, // 切换资源类型
    setGroupIds,
    groupIds,
    examPaperState, // 试卷资源
    onSwitchExamPaperType, // 切换试卷资源类型
    onChangeExamPaperPageType, // 切换试卷展示
    setSelectProblemIds, // 设置选中的题目ID
  }
})

// 启用热更新
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useSideResourceStore, import.meta.hot))
}

export default useSideResourceStore
