import type { ProblemDetailType } from '@/pages/problem/utils'
import {
  ExamProblemTypeEnums,
  ProblemTypesEnums,
} from '@guanghe-pub/onion-problem-render'

const difficultyMap: Record<string, number> = {
  '0': 60,
  '55': 60,
  '65': 60,
  '75': 180,
  '85': 360,
  '95': 360,
}

export const getDurationFromProblemDifficulty = (difficulty: string) => {
  return difficultyMap[difficulty] || 60
}

export const getProblemTypeName = (problem: ProblemDetailType) => {
  const type =
    ExamProblemTypeEnums[problem.examType] || ProblemTypesEnums[problem.type]
  return type.endsWith('题') ? type : `${type}题`
}

// 判断传入的参数存在且是一个字符串，则转为数字
export function convertToNumber(
  value: string | number | undefined,
): number | undefined {
  if (typeof value === 'string') {
    return Number(value)
  } else {
    return value
  }
}
