<template>
  <n-drawer
    v-model:show="drawerShow"
    :show-mask="false"
    width="614px"
    placement="right"
    :block-scroll="false"
  >
    <div class="flex leading-normal relative side-resource-drawer">
      <div class="w-544px">
        <n-scrollbar class="h-100vh">
          <VideoType
            v-if="activeTab === 'video'"
            :drawerShow="show"
            :scene="scene"
            @insert-video="onInsert"
            @insert-problem="onInsert"
            @close="onClose"
          />
          <ProblemType
            v-if="activeTab === 'problem'"
            :drawerShow="show"
            @insert-problem="onInsert"
            @close="onClose"
          />
          <WrongBookType
            v-if="activeTab === 'wrongBook'"
            @insert-problem="onInsert"
            @close="onClose"
          />
          <ExamPaperType
            v-if="activeTab === 'examPaper'"
            @insert-problem="onInsert"
            @close="onClose"
          />
        </n-scrollbar>
      </div>
      <div
        class="select-none w-70px px-8px pt-16px h-100vh space-y-8px text-14px border-l-1px border-#dcd8e7 border-solid border- [&_.active]:color-#5e80ff [&_.active]:bg-#f4f6ff [&_div]:rounded-12px [&_div]:w-56px [&_div]:h-60px [&_div]:flex [&_div]:flex-col [&_div]:justify-center [&_div]:items-center"
      >
        <div
          class="color-#9792ac cursor-pointer"
          :class="[activeTab === 'video' ? 'active' : '']"
          @click="onSwitchActiveType('video')"
        >
          <VideoTypeIcon />
          <span>微课</span>
        </div>
        <div
          class="color-#9792ac cursor-pointer"
          :class="[activeTab === 'problem' ? 'active' : '']"
          @click="onSwitchActiveType('problem')"
        >
          <ProblemTypeIcon />
          <span>题库</span>
        </div>
        <div
          class="color-#9792ac cursor-pointer"
          :class="[activeTab === 'examPaper' ? 'active' : '']"
          @click="onSwitchActiveType('examPaper')"
        >
          <ExamPaperIcon />
          <span>试卷</span>
        </div>
        <div
          class="color-#9792ac cursor-pointer relative"
          :class="[activeTab === 'wrongBook' ? 'active' : '']"
          @click="onSwitchActiveType('wrongBook')"
        >
          <WrongBookTypeIcon />
          <span>错题本</span>
          <span
            v-if="isNewWrongBook"
            class="text-12px font-bold text-#fff bg-#FA5A65 rounded-4px px-4px absolute top-0 right-0"
            >新!</span
          >
        </div>
      </div>
    </div>
  </n-drawer>
</template>

<script setup lang="ts">
import VideoType from './Video/VideoType.vue'
import ProblemType from './Problem/ProblemType.vue'
import WrongBookType from './WrongBook/WrongBookType.vue'
import ExamPaperType from './ExamPaper/ExamPaperType.vue'
import VideoTypeIcon from '~icons/yc/video-type'
import ProblemTypeIcon from '~icons/yc/problem-type'
import WrongBookTypeIcon from '~icons/yc/wrong-book-type'
import ExamPaperIcon from '~icons/yc/exam-paper'
import type { InsertParams } from './types'
import useSideResourceStore from './store'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useDurationTimer } from '@/hooks/useDurationTimer'
import { useAuth } from '@/hooks/useAuth'

const props = defineProps<{
  show: boolean
  scene: 'edit' | 'create' | 'draft' // 当前页面场景 埋点用
}>()
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'add', val: InsertParams): void
}>()
const { userId } = useAuth()
const drawerShow = computed({
  get() {
    return props.show
  },
  set(val) {
    emits('update:show', val)
  },
})

const isNewWrongBook = ref(
  window.localStorage.getItem(`wrongBookSideResourceTab_${userId.value}`) !==
    '1',
)

const onClose = () => {
  drawerShow.value = false
}

const store = useSideResourceStore()
const { activeTab } = storeToRefs(store)

const onSwitchActiveType = (
  type: 'video' | 'problem' | 'examPaper' | 'wrongBook',
) => {
  store.updateActiveTab(type)
  if (type === 'wrongBook' && isNewWrongBook.value) {
    window.localStorage.setItem(`wrongBookSideResourceTab_${userId.value}`, '1')
    isNewWrongBook.value = false
  }
}

const onInsert = (data: InsertParams) => {
  emits('add', data)
}

// 埋点--抽屉停留时长
const { duration, startTime, closeTime } = useDurationTimer()
watch(
  () => drawerShow.value,
  (val) => {
    if (val) {
      startTime()
    }
    if (!val && duration.value > 0) {
      buryPoint(
        'getCourseDrawerStayDuration',
        {
          duration: duration.value,
        },
        'course',
      )
      closeTime()
    }
  },
)
onBeforeUnmount(() => {
  closeTime()
})
</script>
