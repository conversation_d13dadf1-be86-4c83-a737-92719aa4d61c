<template>
  <div
    :class="{ 'brief-desc-box': true, none: !isShow }"
    @click="handleClickDesc"
  >
    <div
      v-if="isShow"
      class="brief-desc-header group relative"
      :style="{
        backgroundImage: `url(${videoCover})`,
      }"
    >
      <n-space justify="space-between" class="head-info relative">
        <div class="flex">
          <div
            v-if="
              briefData.typeTags &&
              briefData.typeTags.length &&
              formattedTypeTags(briefData.typeTags)
            "
            class="label"
          >
            {{ formattedTypeTags(briefData.typeTags) }}
          </div>
          <n-popover
            v-if="
              briefData.video.keyPoints &&
              briefData.video.keyPoints.length &&
              keyPointCount &&
              !briefData.resourceId
            "
            trigger="hover"
          >
            <template #trigger>
              <span class="label ml-4px"> 片段{{ keyPointCount }} </span>
            </template>
            <span>当前微课支持选择片段插入课件</span>
          </n-popover>
          <div v-if="briefData.levelNum !== 0" class="label ml-4px">
            题目{{ briefData.levelNum }}
          </div>
        </div>
        <div v-if="type !== 'microVideo'" class="operate">
          <span
            :class="{ 'collect-icon': true, active: isFavorite }"
            @click.stop="handleClickCollect"
          />
        </div>
      </n-space>
      <div
        class="hidden group-hover:block left-0 absolute top-0 right-0 bottom-0 rounded-12px"
      >
        <div
          class="rounded-12px h-100% w-100% px-31px flex *:min-w-45px *:inline-flex *:flex-col *:items-center *:cursor-pointer bg-#12254d/60 pt-40px backdrop-blur-4px"
          :class="{
            'justify-center': !isShowSelfClip,
            'gap-32px': !isShowSelfClip,
            'justify-between': isShowSelfClip,
          }"
        >
          <div @click="onPreviewClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <EyeIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">预览</div>
          </div>
          <div @click="onInsertClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <Add class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">插入</div>
          </div>
          <div v-if="isShowSelfClip" @click="onSelfClipClick">
            <div
              class="hover:bg-#FFD633 bg-#c7c7c7 inline-flex rounded-50% justify-center items-center w-32px h-32px"
            >
              <FolderIcon class="w-20px h-20px" color="#393548" />
            </div>
            <div class="color-#EFEEF3 text-12px mt-8px">自定义插入</div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="brief-desc-header"
      :style="{
        backgroundImage: `url(https://fp.yangcong345.com/onion-extension/555-90f6ca756f41b5bad44b3f0516b98cb2.png)`,
      }"
    />
    <div class="brief-desc-content">
      <div class="flex items-center" :class="{ title: true, grey: !isShow }">
        <img
          v-if="briefData.pay && auth"
          class="w-46px h-20px mr-6px"
          src="https://fp.yangcong345.com/onion-extension/编组 3备份@3x-6d6bc77e63f598316b41fc1d6f7a584d.png"
          alt=""
        />
        <img
          v-if="briefData.pay && !auth"
          class="w-46px h-20px mr-6px"
          src="https://fp.yangcong345.com/onion-extension/编组 3备份@3x (1)-35218ebc2a64d210c91e217b197acdac.png"
          alt=""
        />
        {{ briefData.resourceName || briefData.name }}
      </div>
      <n-space v-if="isShow" justify="space-between" class="info-list">
        <div v-if="briefData.resourceSubType !== 'system-point'" class="data">
          <span>使用{{ briefData.usageCount }}</span>
          <span>｜收藏{{ briefData.favoriteCount }}</span>
        </div>
        <div class="time">
          {{ durationFormat }}
        </div>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import FolderIcon from '~icons/yc/folder-add'
import EyeIcon from '~icons/yc/eye'
import Add from '~icons/yc/self-add'
import type { TopicDetail } from '@/pages/microVideo/service'
import {
  formattedDuration,
  formattedTypeTags,
} from '@/pages/microVideo/service'
import { useVideoCover } from '@/hooks/useVideoCover'
import type { FavoriteSubType } from '@/service/common'

const props = defineProps<{
  briefData: TopicDetail
  type: 'microVideo' | 'collection'
  auth: boolean
  showSelfClip?: boolean
}>()

const emits = defineEmits<{
  (e: 'click', briefData: TopicDetail): void
  (e: 'preview', briefData: TopicDetail): void
  (e: 'collect', briefData: TopicDetail, isShow: boolean): void
  (e: 'insert', briefData: TopicDetail, cover: string): void
  (e: 'selfClip', briefData: TopicDetail): void
}>()

const { videoCover } = useVideoCover({
  video: props.briefData.video,
  resourceSubId: props.briefData.resourceSubId as string,
  resourceType: props.briefData.resourceSubType || 'system', // HACK: 由于部分资源没有resourceSubType字段
})
const isShow = computed(() => {
  if (props.briefData.resourceSubType !== 'system-point') {
    return !!props.briefData.name
  }
  return !!props.briefData.video.keyPoints?.some(
    (keyPoint: any) => keyPoint.id === props.briefData.resourceSubId,
  )
})

const isShowSelfClip = computed(() => {
  if (props.briefData.resourceSubType === 'system-point') {
    return false
  }
  if (props.showSelfClip === false) {
    return false
  }
  return props.briefData.resourceSubType !== ('system-point' as FavoriteSubType)
})

const keyPointCount = computed(() => {
  if (props.briefData.video) {
    return props.briefData.video.keyPoints?.filter(
      (keyPoint: any) => keyPoint.content !== '片头' && keyPoint.time !== 0,
    ).length
  }
  return 0
})

const durationFormat = computed(() => {
  if (props.briefData.resourceSubType !== 'system-point') {
    return formattedDuration(props.briefData.video.duration)
  }
  let time = 0
  if (props.briefData.video.keyPoints) {
    const keyIndex = props.briefData.video.keyPoints.findIndex(
      (keyPoint: any) => keyPoint.id === props.briefData.resourceSubId,
    )
    if (keyIndex > -1) {
      if (keyIndex === props.briefData.video.keyPoints.length - 1) {
        time =
          props.briefData.video.duration -
          props.briefData.video.keyPoints[keyIndex].time
      } else {
        time =
          props.briefData.video.keyPoints[keyIndex + 1].time -
          props.briefData.video.keyPoints[keyIndex].time
      }
    }
  }
  return formattedDuration(time)
})
const isFavorite = computed(() => {
  if (props.briefData.resourceSubType !== 'system-point') {
    return props.briefData.isFavorite
  }
  return props.briefData.favoriteKeyPointIds.some(
    (id: string) => id === props.briefData.resourceSubId,
  )
})

const handleClickCollect = () => {
  emits('collect', props.briefData, isShow.value)
}

const handleClickDesc = () => {
  // if (props.checkValue === 'specialCourse') return
  if (isShow.value) {
    emits('click', props.briefData)
  }
}

const onPreviewClick = () => {
  emits('preview', props.briefData)
}

const onInsertClick = () => {
  emits('insert', props.briefData, videoCover.value)
}

const onSelfClipClick = () => {
  emits('selfClip', props.briefData)
}
</script>

<style lang="scss" scoped>
.brief-desc-box {
  width: 240px;
  height: 207px;
  margin-top: 16px;
  cursor: pointer;
  background: #fff;
  border-radius: 16px;

  &:hover {
    background: #f4f6ff;
  }

  &.none {
    &:hover {
      background: #fff;
    }
  }

  .brief-desc-header {
    width: 240px;
    height: 135px;
    padding: 12px;
    background-size: cover;
    border-radius: 16px;

    .head-info {
      .label {
        padding: 6px 10px;
        font-size: 12px;
        font-weight: 500;
        line-height: 12px;
        color: #ffffff;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
      }

      .collect-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        cursor: pointer;
        background: rgba(0, 0, 0, 0.5)
          url('https://fp.yangcong345.com/onion-extension/111-0c2ec3eea9dc435a0f02c6a5c58ee85d.png')
          no-repeat;
        background-position: 4px 3px;
        background-size: 16px 16px;
        border-radius: 8px;

        &.active {
          background: rgba(0, 0, 0, 0.5)
            url('https://fp.yangcong345.com/onion-extension/333-f996430c0cc4ec2527195dd525f2beb5.png')
            no-repeat;
          background-position: 4px 3px;
          background-size: 16px 16px;
        }
      }
    }
  }

  .brief-desc-content {
    min-height: 72px;
    padding: 12px;

    .title {
      overflow: hidden;
      font-size: 16px;
      font-weight: 500;
      line-height: 18px;
      color: #393548;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.grey {
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        color: #c5c1d4;
      }
    }

    .info-list {
      margin-top: 8px;
      font-size: 14px;
      line-height: 22px;
      color: #8a869e;
    }
  }
}
</style>
