<template>
  <div
    :class="
      (paper as ExamPaperItem).state === 'unpublished' ? 'opacity-50' : ''
    "
    class="flex py-16px border-#E4E9F7 border-b-solid border-b-1px leading-24px relative"
  >
    <PaperIcon class="mt-8px" />
    <div
      v-if="(paper as ExamPaperItem).level === 'EXAM_PAPER_LEVEL_BEST'"
      class="best-tag"
    />
    <div class="ml-12px flex flex-col h-82px">
      <div
        class="color-#393548 inline-flex items-center text-16px"
        :class="{ 'mt-12px': type === 'school' }"
      >
        <n-ellipsis
          style="max-width: 350px; font-weight: 500; line-height: 24px"
        >
          {{ paper.name }}
        </n-ellipsis>
        <span v-if="type === 'public'">
          <StarIcon
            v-if="(paper as ExamPaperItem).isFavorite"
            class="ml-8px cursor-pointer"
            width="16px"
            height="16px"
            color="#FFD633"
            @click="emits('favoriteItem', paper as ExamPaperItem)"
          />
          <StarBlankIcon
            v-else
            class="ml-8px cursor-pointer"
            width="16px"
            height="16px"
            color="#FFD633"
            @click="emits('favoriteItem', paper as ExamPaperItem)"
          />
        </span>
      </div>
      <div
        v-if="type === 'public'"
        class="mt-8px leading-12px flex [&>span+span]:ml-8px"
      >
        <span
          v-for="item of paperItemTags"
          :key="item"
          class="text-12px rounded-4px px-6px py-4px border-1px border-#C5C1D4"
        >
          {{ item }}
        </span>
      </div>
      <div class="mt-8px color-#8A869E leading-22px text-14px">
        <span
          >题目数量：{{
            (paper as ExamPaperItem).totalProblems ||
            (paper as SchoolExamPaperItem).problemNum ||
            0
          }}</span
        >
      </div>
      <div
        v-if="(paper as ExamPaperItem).state !== 'unpublished'"
        class="absolute right-16px bottom-16px z-10"
      >
        <div
          class="rounded-12px w-76 h-32 text-14px text-center cursor-pointer bg-#5e80ff color-#fff leading-32px"
          @click="toPreview(paper)"
        >
          去选题
        </div>
      </div>
    </div>
    <div
      v-if="(paper as ExamPaperItem).state === 'unpublished'"
      class="ml-25px inline-flex items-center"
    >
      <ExpriedIcon />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ExamPaperItem } from '@/pages/paper/service'
import StarBlankIcon from '~icons/yc/star-blank'
import StarIcon from '~icons/yc/star'
import PaperIcon from '~icons/yc/paper'
import ExpriedIcon from '~icons/yc/expried'
import { getPaperItemTags } from '@/pages/paper/utils'
import { examBestAuth } from '@/hooks/usePermission'
import type { SchoolExamPaperItem } from '@/pages/schoolResource/schoolPaper/service'
import useSideResourceStore from '../store'
import { useOIWMessage } from '@guanghe-pub/onion-ui-web'

const props = defineProps<{
  paper: ExamPaperItem | SchoolExamPaperItem
  type: 'public' | 'school'
}>()

const emits = defineEmits<(e: 'favoriteItem', val: ExamPaperItem) => void>()
const store = useSideResourceStore()
const message = useOIWMessage()
const paperItemTags = computed(() => {
  return getPaperItemTags(props.paper as ExamPaperItem)
})

const toPreview = async (paper: ExamPaperItem | SchoolExamPaperItem) => {
  let auth = true
  if (
    (paper as ExamPaperItem).level &&
    (paper as ExamPaperItem).level === 'EXAM_PAPER_LEVEL_BEST'
  ) {
    auth = await examBestAuth(
      (paper as ExamPaperItem).pkgIds,
      paper.stageId,
      paper.subjectId,
    )
  }
  if (auth) {
    store.onChangeExamPaperPageType(
      'detail',
      paper.id,
      props.type,
      Number(paper.stageId) || -1,
      Number(paper.subjectId) || -1,
      Number((paper as ExamPaperItem).publisherId) || -1,
    )
  } else {
    message.error(
      '该试卷为精品试卷，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
    )
  }
}
</script>

<style scoped lang="scss">
.best-tag {
  position: absolute;
  top: 26px;
  left: 27px;
  display: inline-block;
  width: 33px;
  height: 26px;
  background: url('https://fp.yangcong345.com/onion-extension/切图 <EMAIL>')
    no-repeat;
  background-size: 100% 100%;
}
</style>
