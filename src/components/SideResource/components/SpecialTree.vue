<template>
  <div class="special-tree-main-wrapper">
    <CsvSelectSpecial
      class="csv-select"
      :specialCourseId="specialCourseId"
      @change="onCsvSelectChange"
    />
    <n-popover trigger="click">
      <template #trigger>
        <div class="option-name">
          <span class="text-ellipsis">{{ optionName }} </span>

          <i class="n-base-icon n-base-suffix__arrow">
            <svg
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z"
                fill="rgba(194, 194, 194, 1)"
              ></path>
            </svg>
          </i>
        </div>
      </template>
      <div class="special-tree-content">
        <Loading
          :loading="loading && selectId !== ''"
          :class="{ 'loading-padding': loading }"
        />
        <n-tree
          class="custom-tree"
          :default-expanded-keys="expandedKeys"
          accordion
          :selected-keys="selectKey"
          show-line
          :data="treeData"
          key-field="id"
          label-field="name"
          children-field="children"
          block-node
          @update:selected-keys="onSelectChange"
        >
          <template #empty>
            <n-empty
              description="当前学科和学段下暂无培优课"
              class="empty-tree-box"
            />
          </template>
        </n-tree>
      </div>
    </n-popover>
  </div>
</template>

<script setup lang="ts">
import CsvSelectSpecial from '@/components/CsvSelectSpecial/specialCsv.vue'
import type { ChangeParams, CustomTreeType } from '@/hooks/useTree'
import { useSepicalTreeData } from '@/hooks/useTree'

const props = defineProps<{
  // eslint-disable-next-line vue/no-unused-properties
  selectId: string
  specialCourseId: string
  localSyncKey: string
}>()

const emits = defineEmits<{
  (e: 'treeDataLoad', val: CustomTreeType[]): void
  (e: 'change', val: ChangeParams): void
  (e: 'specialIdChange', val: string): void
}>()

const onChange = (data: ChangeParams) => {
  emits('change', data)
}
const onTreeLoad = (data: CustomTreeType[]) => {
  emits('treeDataLoad', data)
}
const onSpecialIdChange = (specialId: string) => {
  emits('specialIdChange', specialId)
}

const {
  treeData,
  expandedKeys,
  selectKey,
  optionName,
  loading,
  onCsvSelectChange,
  onSelectChange,
} = useSepicalTreeData({
  selectId: toRef(props, 'selectId'),
  needLocalSync: true,
  localSyncKey: props.localSyncKey,
  onChange,
  onTreeLoad,
  onSpecialIdChange,
})
</script>

<style lang="scss" scoped>
.special-tree-main-wrapper {
  display: flex;
  justify-content: space-between;

  .csv-select {
    width: 240px;
  }

  .option-name {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 240px;
    height: 44px;
    padding: 0 16px;
    font-size: 15px;
    line-height: 44px;
    color: #393548;
    cursor: pointer;
    border: 1px solid rgb(224, 224, 230);
    border-radius: 12px;
    transition:
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border: 1px solid #5381fe;
    }
  }
}

.loading-padding {
  padding-top: 100px;
}

.special-tree-content {
  min-width: 200px;
  min-height: 200px;
  max-height: 70vh;
  margin-top: 16px;
  overflow-y: auto;
}
</style>
