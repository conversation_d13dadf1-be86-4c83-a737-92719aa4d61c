export interface InsertParams {
  topicId?: string
  specialCourseId?: string
  name: string
  duration: number
  sourceId: string
  id?: string
  sourceType:
    | 'ItemTypeNone'
    | 'Video'
    | 'Problem'
    | 'Clip'
    | 'SchoolVideo'
    | 'SchoolProblem'
    | 'SpecialTopic'
    | 'PPTCourseware'
  videoClipExtra?: {
    clipId?: string
    playRangeStart: number
    playRangeEnd: number
    type?: 'system_clip' | 'self_clip'
  }
  stageId?: YcType.CsvId
  subjectId?: YcType.CsvId
  publisherId?: YcType.CsvId
  semesterId?: YcType.CsvId
  chapterId?: string
  sectionId?: string
  subSectionId?: string
  videoCover?: string
  /**
   * 公共资源，校本资源，我的收藏，错题本
   * - ImportFromNone: 无.
   * - Public: 公共资源.
   * - School: 校本资源.
   * - MyFavorite: 我的收藏.
   * - WrongBook: 错题本.
   * - Recitation: 专题
   */
  importFrom?:
    | 'ImportFromNone'
    | 'Public'
    | 'School'
    | 'MyFavorite'
    | 'WrongBook'
    | 'Recitation'
    | 'MyCourseResource'
  /**
   * 试卷ID
   */
  paperId?: string
  /**
   * 题目body
   */
  problemBody?: string
  /**
   * 额外字段
   */
  extra?: {
    /**
     * 快背专题所属模块id
     */
    moduleId?: string
  }
}
