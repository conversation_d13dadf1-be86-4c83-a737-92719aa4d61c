# CsvSelect 学科选择器

## 概述

CsvSelect 是一个级联选择器组件，用于选择学科、学段、教材版本和学期。基于 Naive UI 的 Cascader 组件封装，提供了教育场景下的学科选择功能。

## 基本用法

```vue
<template>
  <div>
    <CsvSelect @change="handleChange" />
  </div>
</template>

<script setup>
import CsvSelect from '@/components/CsvSelect.vue'

const handleChange = (csvData, isInit) => {
  console.log('选择的学科信息:', csvData)
  console.log('是否为初始化:', isInit)
}
</script>
```

## 带默认值的用法

```vue
<template>
  <div>
    <CsvSelect 
      :defaultCsv="defaultCsvData"
      @change="handleChange" 
    />
  </div>
</template>

<script setup>
import CsvSelect from '@/components/CsvSelect.vue'

const defaultCsvData = {
  publisherId: 1,
  stageId: 2,
  subjectId: 1,
  semesterId: 13
}

const handleChange = (csvData, isInit) => {
  console.log('选择的学科信息:', csvData)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| defaultCsv | 默认选中的学科信息 | `CsvData` | `undefined` |
| publisherSemesterCanAll | 是否在教材版本和年级中包含"不限"选项 | `boolean` | `false` |
| notUpdateCurrentTextBook | 是否不更新当前教材缓存 | `boolean` | `false` |

### CsvData 类型定义

```typescript
interface CsvData {
  publisherId: YcType.CsvId  // 教材版本ID
  stageId: YcType.CsvId      // 学段ID
  subjectId: YcType.CsvId    // 学科ID
  semesterId: YcType.CsvId   // 学期ID
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选择变化时触发 | `(csvData: CsvData, isInit: boolean) => void` |

### 回调参数说明

- `csvData`: 选中的学科信息对象
- `isInit`: 是否为初始化触发（true 表示组件初始化时的默认选择）

## 特殊处理

### 科学学科处理

组件对科学学科（subjectId = 19）进行了特殊处理：
- 只有浙江省的教师才能选择科学学科
- 非浙江省教师如果有科学学科记录，会自动转换为数学学科

```javascript
// 特殊处理逻辑
if (subjectId === 19 && me.value?.school.provinceRegionCode !== '330000') {
  stage = 2      // 初中
  subject = 1    // 数学
  publisher = 1  // 人教版
  semester = 13  // 上册
}
```

## 样式定制

组件支持通过 CSS 类名进行样式定制：

```css
.oiw-cascader .n-base-selection {
  padding-top: 2px;
  padding-bottom: 2px;
  border-radius: 12px;
}
```

## 使用场景

1. **资源筛选**: 在题目、试卷、课件等资源页面进行学科筛选
2. **内容分类**: 按学科对教学内容进行分类展示
3. **权限控制**: 根据教师的学科权限显示相应内容

## 注意事项

1. 组件依赖全局的学科数据（useCvsTree）
2. 选择变化时会自动更新用户的当前教材缓存（除非设置 `notUpdateCurrentTextBook`）
3. 长文本会自动显示 Popover 提示
4. 支持搜索过滤功能

## 更新日志

### v1.0.0
- 初始版本，支持基本的学科选择功能
- 支持科学学科的特殊处理
- 支持默认值设置和缓存更新
