<template>
  <div class="filter-select">
    <div
      v-for="item of options"
      :key="item.label + item.value"
      class="filter-select_item"
      :class="[value === item.value ? 'active' : '']"
      @click="onSelect(item.value)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
type ValueType = string | number | undefined | null
interface FilterOptions {
  label: string
  value: ValueType
}

defineProps<{
  options: FilterOptions[]
  value: ValueType
}>()

const emits = defineEmits<(e: 'update:value', value: ValueType) => void>()

const onSelect = (value: ValueType) => {
  emits('update:value', value)
}
</script>

<style lang="scss">
.filter-select {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 0;
  width: 100%;

  .filter-select_item {
    padding: 3px 12px;
    font-size: 12px;
    font-weight: 500;
    color: #393548;
    cursor: pointer;
    user-select: none;

    &.active {
      color: #fff;
      background-color: #5E80FF;
      border-radius: 16px;
    }
  }
}
</style>
