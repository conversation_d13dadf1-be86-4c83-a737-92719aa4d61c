# FilterSelect 筛选选择器

## 概述

FilterSelect 是一个标签式的筛选选择器组件，以标签的形式展示选项，支持单选功能。常用于筛选条件的快速选择，提供直观的视觉反馈。

## 基本用法

```vue
<template>
  <div>
    <FilterSelect 
      v-model:value="selectedValue"
      :options="options"
    />
  </div>
</template>

<script setup>
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'

const selectedValue = ref('')
const options = [
  { label: '全部', value: '' },
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' },
  { label: '解答题', value: 'answer' }
]
</script>
```

## 与 FilterItem 组合使用

```vue
<template>
  <div class="filter-container">
    <FilterItem name="题型:" label-width="56">
      <FilterSelect 
        v-model:value="examType"
        :options="examTypeOptions"
      />
    </FilterItem>
    
    <FilterItem name="难度:" label-width="56" class="mt-12px">
      <FilterSelect 
        v-model:value="difficulty"
        :options="difficultyOptions"
      />
    </FilterItem>
  </div>
</template>

<script setup>
import FilterItem from '@/components/FilterItem/FilterItem.vue'
import FilterSelect from '@/components/FilterSelects/FilterSelect.vue'

const examType = ref('')
const difficulty = ref('')

const examTypeOptions = [
  { label: '全部', value: '' },
  { label: '单选题', value: 'single_choice' },
  { label: '多选题', value: 'multiple_choice' },
  { label: '判断题', value: 'judge' },
  { label: '填空题', value: 'blank' },
  { label: '解答题', value: 'answer' }
]

const difficultyOptions = [
  { label: '全部', value: '' },
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' }
]
</script>
```

## 数字类型值

```vue
<template>
  <div>
    <FilterSelect 
      v-model:value="level"
      :options="levelOptions"
    />
  </div>
</template>

<script setup>
const level = ref(0)
const levelOptions = [
  { label: '全部', value: 0 },
  { label: '初级', value: 1 },
  { label: '中级', value: 2 },
  { label: '高级', value: 3 }
]
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 当前选中的值 | `string \| number \| undefined \| null` | - |
| options | 选项数组 | `FilterOptions[]` | `[]` |

### FilterOptions 类型定义

```typescript
interface FilterOptions {
  label: string                                    // 显示文本
  value: string | number | undefined | null       // 选项值
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:value | 选择变化时触发 | `(value: string \| number \| undefined \| null) => void` |

## 样式说明

### 默认样式
- **未选中**: 灰色文本，无背景
- **选中**: 白色文本，蓝色背景，圆角边框
- **悬停**: 鼠标悬停时显示手型光标

### 样式变量

```css
.filter-select_item {
  --item-padding: 3px 12px;
  --item-font-size: 12px;
  --item-font-weight: 500;
  --item-color: #393548;
  --item-active-color: #fff;
  --item-active-bg: #5E80FF;
  --item-border-radius: 16px;
}
```

## 样式定制

### 自定义颜色主题

```vue
<template>
  <FilterSelect 
    v-model:value="value"
    :options="options"
    class="custom-filter-select"
  />
</template>

<style scoped>
.custom-filter-select .filter-select_item.active {
  background-color: #ff6b6b;
  color: white;
}

.custom-filter-select .filter-select_item:hover {
  background-color: #ff8e8e;
  color: white;
}
</style>
```

### 大尺寸样式

```vue
<template>
  <FilterSelect 
    v-model:value="value"
    :options="options"
    class="large-filter-select"
  />
</template>

<style scoped>
.large-filter-select .filter-select_item {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 20px;
}
</style>
```

### 紧凑布局

```vue
<template>
  <FilterSelect 
    v-model:value="value"
    :options="options"
    class="compact-filter-select"
  />
</template>

<style scoped>
.compact-filter-select {
  gap: 4px 0;
}

.compact-filter-select .filter-select_item {
  padding: 2px 8px;
  font-size: 11px;
}
</style>
```

## 使用场景

### 1. 题目筛选

```vue
<template>
  <div class="problem-filters">
    <FilterItem name="题型:" label-width="56">
      <FilterSelect 
        v-model:value="filters.examType"
        :options="examTypeOptions"
        @update:value="handleFilterChange"
      />
    </FilterItem>
    
    <FilterItem name="正确率:" label-width="56" class="mt-12px">
      <FilterSelect 
        v-model:value="filters.accuracy"
        :options="accuracyOptions"
        @update:value="handleFilterChange"
      />
    </FilterItem>
  </div>
</template>

<script setup>
const filters = reactive({
  examType: '',
  accuracy: ''
})

const examTypeOptions = [
  { label: '全部', value: '' },
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' }
]

const accuracyOptions = [
  { label: '全部', value: '' },
  { label: '80%以上', value: 'high' },
  { label: '60%-80%', value: 'medium' },
  { label: '60%以下', value: 'low' }
]

const handleFilterChange = () => {
  // 触发筛选逻辑
  fetchFilteredData(filters)
}
</script>
```

### 2. 状态筛选

```vue
<template>
  <div class="status-filter">
    <FilterSelect 
      v-model:value="status"
      :options="statusOptions"
      @update:value="handleStatusChange"
    />
  </div>
</template>

<script setup>
const status = ref('all')
const statusOptions = [
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' }
]

const handleStatusChange = (newStatus) => {
  console.log('状态变更为:', newStatus)
  // 处理状态变更逻辑
}
</script>
```

### 3. 多级筛选

```vue
<template>
  <div class="multi-level-filters">
    <FilterItem name="学段:" label-width="56">
      <FilterSelect 
        v-model:value="stage"
        :options="stageOptions"
        @update:value="handleStageChange"
      />
    </FilterItem>
    
    <FilterItem name="学科:" label-width="56" class="mt-12px">
      <FilterSelect 
        v-model:value="subject"
        :options="subjectOptions"
        @update:value="handleSubjectChange"
      />
    </FilterItem>
    
    <FilterItem name="版本:" label-width="56" class="mt-12px">
      <FilterSelect 
        v-model:value="version"
        :options="versionOptions"
      />
    </FilterItem>
  </div>
</template>

<script setup>
const stage = ref('')
const subject = ref('')
const version = ref('')

// 学段变化时更新学科选项
const handleStageChange = (newStage) => {
  subject.value = ''
  version.value = ''
  updateSubjectOptions(newStage)
}

// 学科变化时更新版本选项
const handleSubjectChange = (newSubject) => {
  version.value = ''
  updateVersionOptions(stage.value, newSubject)
}
</script>
```

## 与其他组件的对比

| 特性 | FilterSelect | Select | Radio Group | Button Group |
|------|-------------|--------|-------------|--------------|
| 视觉样式 | 标签式 | 下拉式 | 单选框 | 按钮式 |
| 空间占用 | 中等 | 小 | 大 | 中等 |
| 选项数量 | 适中(3-8个) | 大量 | 少量(2-5个) | 少量(2-5个) |
| 交互方式 | 点击标签 | 下拉选择 | 点击圆圈 | 点击按钮 |

## 最佳实践

### 1. 选项数量控制

```vue
<!-- 推荐：选项数量适中 -->
<FilterSelect :options="moderateOptions" />

<!-- 不推荐：选项过多会导致换行混乱 -->
<FilterSelect :options="tooManyOptions" />
```

### 2. 默认选项设计

```vue
<script setup>
// 推荐：提供"全部"选项作为默认值
const options = [
  { label: '全部', value: '' },  // 默认选项
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' }
]
</script>
```

### 3. 响应式处理

```vue
<template>
  <FilterSelect 
    v-model:value="value"
    :options="options"
    :class="{ 'mobile-style': isMobile }"
  />
</template>

<style scoped>
.mobile-style .filter-select_item {
  padding: 4px 10px;
  font-size: 11px;
}
</style>
```

## 注意事项

1. **选项数量**: 建议控制在 3-8 个选项，过多会影响布局
2. **文本长度**: 选项文本不宜过长，建议控制在 4-6 个字符
3. **默认值**: 通常提供"全部"或"不限"作为默认选项
4. **响应式**: 在小屏幕设备上考虑调整字体大小和间距

## 更新日志

### v1.0.0
- 初始版本，支持基本的标签式选择功能
- 支持字符串和数字类型的值
- 提供活跃状态的视觉反馈
