<template>
  <OIWModal
    v-model:show="modalShow"
    preset="card"
    title="布置给学生-填写练习信息"
    size="large"
    class="teacher-homework-modal"
  >
    <div class="flex gap-16px">
      <div class="w-70px font-500 font-size-14px text-#393548 mt-10px">
        选择班级：
      </div>
      <div class="border-1px border-#C5C1D4 rounded-12px overflow-hidden">
        <Loading :loading="roomsLoading" class="h-240px w-320px">
          <n-scrollbar class="h-240px w-320px">
            <div
              v-for="room of rooms"
              :key="room.id"
              class="h-40px box-border flex px-16px items-center cursor-pointer hover:bg-#F4F6FF"
              @click="onRoomClick(room)"
            >
              <div>
                <n-checkbox
                  :indeterminate="room.part"
                  :checked="room.checked"
                  @update:checked="
                    (val, e) => onRoomCheckedChange(room, val, e)
                  "
                >
                  <n-ellipsis style="max-width: 124px; margin-left: 8px">
                    {{ room.name }}
                  </n-ellipsis>
                  <span
                    v-if="room.type === 'admin'"
                    class="ml-4px pl-1px pr-5px text-center rounded-4px border-1px border-solid border-[rgba(254,163,69,0.6)] text-12px font-600 leading-20px text-#FEA345"
                  >
                    行政班
                  </span>
                </n-checkbox>
              </div>
              <div
                class="ml-auto text-12px leading-12px text-#5E80FF hover:decoration-underline"
              >
                {{
                  room.part
                    ? `部分小组(${partCount(room)})`
                    : `全部学生(${room.memberCount})`
                }}
              </div>
            </div>
          </n-scrollbar>
        </Loading>
      </div>
      <div class="border-1px border-#C5C1D4 rounded-12px">
        <Loading :loading="schemeLoading" class="h-240px w-262px">
          <div
            class="flex items-center mx-16px h-52px border-b-1px border-#E8E8E9"
          >
            <n-checkbox
              :checked="currentRoom?.checked"
              :indeterminate="currentRoom?.part"
              @update:checked="onSchemeAllCheckedChange"
            >
              <span class="text-#393548 font-size-14px line-height-20px ml-8px"
                >全部学生</span
              >
            </n-checkbox>
            <n-dropdown
              v-if="currentRoom?.schemes?.length"
              trigger="click"
              :options="currentRoomSchemaOptions"
              :render-label="renderLabel"
              @select="onSelectScheme"
            >
              <span
                class="ml-auto cursor-pointer text-#5E80FF font-size-12px line-height-12px hover:decoration-underline"
              >
                切换分组方案</span
              >
            </n-dropdown>
          </div>

          <n-scrollbar
            v-if="currentRoom?.schemes?.length"
            class="h-188px w-262px pl-20px"
          >
            <div
              v-for="group of currentScheme?.groups"
              :key="group.id"
              class="box-border h-40px flex pl-16px items-center"
            >
              <div>
                <n-checkbox
                  :checked="group.checked"
                  @update:checked="(val) => onGroupCheckChange(group, val)"
                >
                  <n-ellipsis
                    style="
                      max-width: 110px;
                      margin-left: 8px;
                      font-size: 14px;
                      line-height: 20px;
                      color: #393548;
                    "
                  >
                    {{ group.name }}({{ group.members.length }}人)
                  </n-ellipsis>
                </n-checkbox>
              </div>
            </div>
            <div class="h-40px flex pl-16px items-center">
              <n-checkbox
                :checked="!currentRoom.part && currentRoom.checked"
                @update:checked="onNoRoomCheckChange"
              >
                <span
                  class="text-#393548 font-size-14px line-height-20px ml-8px"
                  >未分组 ({{
                    currentScheme?.noGroupMembers.length || 0
                  }}人)</span
                >
              </n-checkbox>
            </div>
          </n-scrollbar>
          <div v-else class="w-262px px-16px box-border">
            <div class="h-40px flex pl-16px items-center">
              <n-checkbox
                :checked="currentRoom?.checked"
                @update:checked="onNoGroupRoomCheckChange"
              >
                <span
                  class="text-#393548 font-size-14px line-height-20px ml-8px"
                  >未分组 ({{ currentRoom?.memberCount || 0 }}人)</span
                >
              </n-checkbox>
            </div>
          </div>
        </Loading>
      </div>
    </div>
    <div class="flex items-center gap-16px mt-16px">
      <div class="w-70px font-500 font-size-14px text-#393548">定时发布：</div>
      <div>
        <n-switch
          v-model:value="homeworkForm.isCheckedSwitch"
          class="teacher-homework-modal-switch"
        />
      </div>
    </div>
    <div
      v-if="homeworkForm.isCheckedSwitch"
      class="flex items-center gap-16px mt-16px"
    >
      <div class="w-70px font-500 font-size-14px text-#393548">开始时间：</div>
      <div class="w-320px">
        <n-date-picker
          v-model:value="homeworkForm.startTime"
          type="datetime"
          clearable
          class="teacher-homework-modal-date-picker"
        />
      </div>
    </div>
    <div class="flex items-center gap-16px mt-16px">
      <div class="w-70px font-500 font-size-14px text-#393548">截止时间：</div>
      <div class="w-320px">
        <n-date-picker
          v-model:value="homeworkForm.endTime"
          type="datetime"
          clearable
          class="teacher-homework-modal-date-picker"
        />
      </div>
    </div>
    <div class="flex items-center gap-16px mt-16px">
      <div class="w-70px font-500 font-size-14px text-#393548">练习名称：</div>
      <div class="w-601px">
        <OIWInput
          v-if="canNote"
          v-model:value="homeworkForm.name"
          maxlength="15"
          show-count
          clearable
          placeholder="请输入练习名称"
          class="teacher-homework-modal-input"
        />
        <div v-else class="text-#c0c4cc cursor-pointer" @click="noAuthTips">
          {{ homeworkForm.name }}
        </div>
      </div>
    </div>
    <div class="flex items-center gap-16px mt-16px">
      <div class="w-70px font-500 font-size-14px text-#393548">教师留言：</div>
      <div class="w-601px">
        <OIWInput
          v-if="canNote"
          v-model:value="homeworkForm.remark"
          clearable
          show-count
          maxlength="50"
          class="teacher-homework-modal-input"
          placeholder="请输入留言（不超过50字）"
        />
        <div v-else class="text-#c0c4cc cursor-pointer" @click="noAuthTips">
          留言功能近期维护升级，暂不可用哦
        </div>
      </div>
    </div>
    <div class="flex items-center gap-16px mt-20px">
      <div class="w-70px font-500 font-size-14px text-#393548">练习内容：</div>
      <div
        class="w-601px font-600 text-14px text-#57526C flex items-center gap-4"
      >
        已选择<span class="text-#5E80FF">{{ topicsNumber }}</span
        >{{ isPractice ? '题' : '个知识点'
        }}<span v-if="payTopicsNumber && !isPractice">，包含</span
        ><span v-if="payTopicsNumber && !isPractice" class="text-#5E80FF">{{
          payTopicsNumber
        }}</span
        ><span v-if="payTopicsNumber && !isPractice">个付费知识点</span>
      </div>
    </div>
    <div class="flex items-center justify-between mt-32px">
      <div
        class="flex items-center font-t5 cursor-pointer"
        @click="onRouteToTips"
      >
        <WarningIcon /> <span class="ml-4px text-#9792AC">如何分层布置？</span>
      </div>
      <div class="flex gap-16px">
        <OIWButton ghost type="info" @click="onCancel"> 取消 </OIWButton>
        <OIWButton @click="onSubmit">确定</OIWButton>
      </div>
    </div>
  </OIWModal>
</template>

<script setup lang="tsx">
import {
  OIWModal,
  OIWButton,
  OIWInput,
  useOIWMessage,
} from '@guanghe-pub/onion-ui-web'
import WarningIcon from '~icons/yc/warning'
import { useAuth } from '@/hooks/useAuth'
import { getAdminRoomSchemesApi, getTeacherRoomListApi2 } from './service'
import type { RoomScheme } from './service'
import { useLoading } from '@/hooks/useLoading'
import Loading from '@/components/Loading.vue'
import dayjs from 'dayjs'
import { getQueryString } from '@guanghe-pub/onion-utils'
import { buryPoint } from '@/utils/buryPoint'

const props = defineProps<{
  show: boolean
  homeworkType?: string // 作业类型
  homeworkName?: string // 作业类型
  isPractice?: boolean // 同步练习只展示知识点总数
  payTopicsNumber: number
  topicsNumber: number
  specialCourseId?: string
  homeworkScene?: string
}>()

const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'success', val: any): void
}>()

const isEEData = getQueryString('pageFrom') === 'eeData'

const modalShow = computed({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

watch(
  () => props.show,
  (val) => {
    if (val) {
      buryPoint(
        'popupNNHTHPCTPHomeworkInformation',
        {
          courseId: props.specialCourseId || '',
          pageSource: props.homeworkType || '',
          scene: props.homeworkScene || 'none',
          fromPageName: isEEData ? '2' : '1',
          from: 'normal',
        },
        'course',
      )
    }
  },
  {
    immediate: true,
  },
)

const message = useOIWMessage()

const homeworkTypeName = computed(() => {
  if (props.homeworkType === 'review') {
    return '同步复习'
  }
  if (props.homeworkType === 'practice') {
    return '同步练习'
  }
  if (props.homeworkType === 'preview') {
    return '微课练习'
  }
  return '微课练习'
})

const homeworkForm = reactive({
  isCheckedSwitch: false,
  startTime: null,
  endTime: dayjs().add(1, 'day').hour(22).minute(0).second(0).valueOf(),
  name: dayjs().format(
    `MM月DD日${
      props.homeworkName ? props.homeworkName : homeworkTypeName.value + '任务'
    }`,
  ),
  remark: null,
})

interface HomeworkGroup extends YcType.TeacherRoom {
  hasGroup?: boolean
  schemes?: RoomScheme[]
  part?: boolean
  checked: boolean
  activeScheme: RoomScheme | null
}

const { loading: roomsLoading, toggleLoading: toggleRoomsLoading } =
  useLoading()
const rooms = ref<HomeworkGroup[]>([])

const { userId, isConfirm, isAuth } = useAuth()

const canNote = computed(() => {
  return isConfirm.value || isAuth.value
})

if (userId.value) {
  toggleRoomsLoading()
  getTeacherRoomListApi2(userId.value)
    .then((res) => {
      rooms.value = res.map((el) => {
        return {
          ...el,
          checked: false,
          activeScheme: null,
        }
      })
    })
    .finally(() => {
      toggleRoomsLoading()
    })
}

const currentRoom = ref<HomeworkGroup>()

const onRoomCheckedChange = async (
  room: HomeworkGroup,
  val: boolean,
  e: Event,
) => {
  e.stopPropagation()
  onSetCurrentRoom(room)
  if (val) {
    room.checked = true
    await onFetchScheme(room.id)

    room.activeScheme?.groups.forEach((group) => {
      group.checked = true
    })
  } else {
    room.checked = false
    room.part = false
    room.schemes?.forEach((scheme) => {
      scheme.groups.forEach((el) => {
        el.checked = false
      })
    })
  }
}

const onSetCurrentRoom = (room: HomeworkGroup) => {
  currentRoom.value = room
}

const onRoomClick = async (room: HomeworkGroup) => {
  onSetCurrentRoom(room)
  await onFetchScheme(room.id)
}

const { loading: schemeLoading, toggleLoading: toggleSchemeLoading } =
  useLoading()
const onFetchScheme = async (roomId: string) => {
  const room = rooms.value.find((el) => el.id === roomId)
  if (room && !room?.schemes) {
    toggleSchemeLoading()
    const res = await getAdminRoomSchemesApi(roomId).finally(() => {
      toggleSchemeLoading()
    })
    room.schemes = res
    if (res.length) {
      room.hasGroup = true
      room.activeScheme = res[0]
    } else {
      room.hasGroup = false
    }
  }
}

const currentScheme = computed(() => {
  return currentRoom.value?.schemes?.find(
    (el) => el.id === currentRoom.value?.activeScheme?.id,
  )
})
const currentRoomSchemaOptions = computed(() => {
  return currentRoom.value?.schemes?.map((el) => {
    return {
      label: el.name,
      key: el.id,
      scheme: el,
      props: {
        class: currentRoom.value?.activeScheme?.id === el.id ? 'is-active' : '',
      },
    }
  })
})
const onSelectScheme = (id: string, dropdown: any) => {
  if (currentRoom.value) {
    currentRoom.value.checked = true
    currentRoom.value.activeScheme = dropdown.scheme
    currentRoom.value.part = false
    dropdown.scheme?.groups.forEach((el: RoomScheme['groups'][number]) => {
      el.checked = true
    })
  }
}

const onNoRoomCheckChange = (val: boolean) => {
  if (!val) {
    if (currentRoom.value) {
      currentRoom.value.part = true
    }
  } else {
    message.info('未分组学生不能作为小组布置分层练习')
  }
}

const renderLabel = (dropdown: any) => {
  return h(
    'span',
    {
      class:
        currentRoom.value?.activeScheme?.id === dropdown.key ? 'is-active' : '',
    },
    dropdown.label,
  )
}

const partCount = (room: HomeworkGroup) => {
  return room.activeScheme?.groups.reduce((total, group) => {
    if (group.checked) {
      const newTotal = total + group.members.length
      return newTotal
    }
    return total
  }, 0)
}

const onSchemeAllCheckedChange = (val: boolean) => {
  if (currentRoom.value) {
    if (val) {
      currentRoom.value.checked = true
      currentRoom.value.part = false
      currentRoom.value.activeScheme?.groups.forEach((group) => {
        group.checked = true
      })
    } else {
      currentRoom.value.checked = false
      currentRoom.value.part = false
      currentRoom.value.activeScheme?.groups.forEach((group) => {
        group.checked = false
      })
    }
  }
}

const onGroupCheckChange = (
  group: RoomScheme['groups'][number],
  val: boolean,
) => {
  if (currentRoom.value) {
    if (val) {
      group.checked = true
      currentRoom.value.checked = true
      if (currentRoom.value.activeScheme?.groups.some((el) => !el.checked)) {
        currentRoom.value.part = true
      }
    } else {
      group.checked = false
      if (currentRoom.value.activeScheme?.groups.some((el) => el.checked)) {
        currentRoom.value.part = true
      } else {
        currentRoom.value.part = false
        currentRoom.value.checked = false
      }
    }
  }
}

const onNoGroupRoomCheckChange = (val: boolean) => {
  if (currentRoom.value) {
    if (val) {
      currentRoom.value.checked = true
    } else {
      currentRoom.value.checked = false
    }
  }
}

const onRouteToTips = () => {
  window.open(
    'http://mp.weixin.qq.com/s?__biz=MzA5NjE5Nzk5Mg==&mid=507311317&idx=1&sn=4da951ab85fb21deff40768a81883cee&chksm=0b7b98d23c0c11c44d239033312ba2fabc3829d77f56479ca3618833aac8ad7f9da101087a9f#rd',
  )
}

const resetRoomsState = () => {
  rooms.value.forEach((room) => {
    room.checked = false
    room.part = false
    room.schemes?.forEach((scheme) => {
      scheme.groups.forEach((group) => {
        group.checked = false
      })
    })
    if (room.schemes?.length) {
      room.activeScheme = room.schemes[0]
    }
  })
  homeworkForm.isCheckedSwitch = false
  homeworkForm.startTime = null
  homeworkForm.endTime = dayjs()
    .add(1, 'day')
    .hour(22)
    .minute(0)
    .second(0)
    .valueOf()
  homeworkForm.name = dayjs().format(
    `MM月DD日${
      props.homeworkName ? props.homeworkName : homeworkTypeName.value + '任务'
    }`,
  )
  homeworkForm.remark = null
}

const onCancel = () => {
  modalShow.value = false
  resetRoomsState()
}

const onSubmit = () => {
  const { name, isCheckedSwitch, endTime, startTime, remark } = homeworkForm
  const noCheckedRoom = !rooms.value.some((el) => el.checked)
  if (noCheckedRoom) {
    message.info('请选择班级或小组')
    return
  }
  if (isCheckedSwitch) {
    if (!startTime || !endTime) {
      message.info('请选择开始时间和截止时间')
      return
    }
    if (startTime <= Number(new Date())) {
      message.info('定时任务的开始时间应大于当前时间')
      return
    }
    if (endTime <= startTime) {
      message.info('截止时间应大于开始时间')
      return
    }
  }
  if (!endTime) {
    message.info('请选择截止时间')
    return
  }
  if (endTime <= Number(new Date())) {
    message.info('截止时间应大于当前时间')
    return
  }
  const units = rooms.value.reduce(
    (arr, room) => {
      if (room.checked) {
        const isAll = !room.hasGroup || !room.part
        arr.push({
          roomName: room.name,
          unitType: isAll ? 'group' : 'littleGroup',
          unitIds: isAll
            ? [room.groupId]
            : room.activeScheme?.groups
                .filter((group) => group.checked)
                .map((group) => group.id) || [],
        })
        return arr
      }
      return arr
    },
    [] as {
      roomName: string
      unitType: string
      unitIds: string[]
    }[],
  )
  const submitData = {
    units,
    name,
    startTime: isCheckedSwitch ? dayjs(startTime).format() : '',
    expiredDate: dayjs(endTime).format(),
    rooms: [],
    checkRoomsName: '',
    leaveMessage: remark,
  }
  resetRoomsState()
  modalShow.value = false
  emits('success', submitData)
}

const noAuthTips = () => {
  message.info(
    '完成认证后可修改，可在洋葱学园教师版APP内「我的-我的认证」里完成认证。',
  )
}
</script>

<style lang="scss" scoped>
.teacher-homework-modal-switch {
  width: 45px;
  height: 24px;

  &::v-deep(.n-switch__rail) {
    width: 45px;
    height: 24px;
  }

  &::v-deep(.n-switch__button) {
    top: 3px;
    width: 18px;
    height: 18px;
  }
}

.teacher-homework-modal-input {
  height: 40px;
  font-size: 14px;

  &::v-deep(.n-input__input) {
    font-size: 14px;
  }

  &::v-deep(.n-input__input-el) {
    height: 40px;
  }

  &::v-deep(.n-input__border) {
    border: 1px solid #c5c1d4;
    border-radius: 12px;
  }

  &::v-deep(.n-input__state-border) {
    border-radius: 12px;
  }
}

.teacher-homework-modal-date-picker {
  height: 40px;
  font-size: 14px;

  &::v-deep(.n-input) {
    height: 40px;
  }

  &::v-deep(.n-input__input-el) {
    height: 40px;
  }

  &::v-deep(.n-input__border) {
    border: 1px solid #c5c1d4;
    border-radius: 12px;
  }

  &::v-deep(.n-input__state-border) {
    border-radius: 12px;
  }
}
</style>

<style lang="scss">
.teacher-homework-modal.oiw-modal.n-card .n-card__content {
  margin-bottom: 0;
}
</style>
