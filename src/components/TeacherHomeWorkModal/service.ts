import request from '@/utils/request'

export interface RoomScheme {
  groups: {
    checked?: boolean
    id: string
    name: string
    members: {
      id: string
      memberId: string
    }[]
  }[]
  id: string
  name: string
  noGroupMembers: {
    id: string
    memberId: string
  }[]
}

export const getAdminRoomSchemesApi = async (roomId: string) => {
  return request.get<RoomScheme[]>(`/admin-room/${roomId}/schemes`)
}

// 教学班+行政班+AI课堂教学班
export const getTeacherRoomListApi2 = async (userId: string) => {
  return request.get<YcType.TeacherRoom[]>(
    `/admin-room/teachers/${userId}/all?type=all2`,
  )
}

// 行政班+AI课堂教学班; 如：【AI课堂】【答题卡】需要过滤普通教学班
export const getTeacherRoomListApi3 = async (userId: string) => {
  return request.get<YcType.TeacherRoom[]>(
    `/admin-room/teachers/${userId}/all?type=all4`,
  )
}
