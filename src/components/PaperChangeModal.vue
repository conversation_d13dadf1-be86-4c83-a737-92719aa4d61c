<template>
  <n-modal
    v-model:show="modalShow"
    closable
    :mask-closable="false"
    :auto-focus="false"
    :block-scroll="blockScroll"
    class="paper-change-modal"
  >
    <div class="paper-change-content">
      <div class="close-icon" @click="closeModal" />
      <div class="change-header">{{ title }}</div>
      <ul class="change-list">
        <li class="change-item">
          <div class="item-left">附带内容：</div>
          <div class="item-right">
            <n-radio-group v-model:value="modal.with">
              <n-space item-style="display: flex;">
                <n-radio value="WITH_NONE" label="不附加" />
                <n-radio value="WITH_ANSWER" label="答案" />
                <n-radio value="WITH_ANSWER_AND_EXPLAIN" label="答案+解析" />
              </n-space>
            </n-radio-group>
            <div class="tip">*添加在卷尾</div>
          </div>
        </li>
        <li class="change-item">
          <div class="item-left">纸张大小：</div>
          <div class="item-right">{{ modal.size }}</div>
        </li>
        <li class="change-item">
          <div class="item-left">文件格式：</div>
          <div class="item-right">
            <div v-if="type === 'preparation'">*.docx</div>
            <n-radio-group
              v-if="type === 'download'"
              v-model:value="modal.fileFormat"
            >
              <n-space
                v-if="
                  fileFormat.includes('FILE_FORMAT_WORD') &&
                  fileFormat.includes('FILE_FORMAT_PDF')
                "
                item-style="display: flex;"
              >
                <n-radio value="FILE_FORMAT_WORD" label="*.docx" />
                <n-radio value="FILE_FORMAT_PDF" label="*.pdf" />
              </n-space>
              <span v-else-if="fileFormat.includes('FILE_FORMAT_WORD')"
                >*.docx</span
              >
              <span v-else-if="fileFormat.includes('FILE_FORMAT_PDF')"
                >*.pdf</span
              >
            </n-radio-group>
          </div>
        </li>
      </ul>
      <div class="change-footer">
        <button class="left-btn" @click="handleCancel">{{ leftText }}</button>
        <button class="right-btn" @click="handleConfirm">
          {{ rightText }}
        </button>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
interface ModalData {
  with: 'WITH_NONE' | 'WITH_ANSWER' | 'WITH_ANSWER_AND_EXPLAIN'
  size: 'A4' | 'A3' | 'A5'
  fileFormat: 'FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF'
}
const props = withDefaults(
  defineProps<{
    show: boolean
    type: 'download' | 'preparation' | null
    fileFormat?: ('FILE_FORMAT_WORD' | 'FILE_FORMAT_PDF')[]
    blockScroll?: boolean
  }>(),
  {
    show: false,
    fileFormat: () => ['FILE_FORMAT_WORD', 'FILE_FORMAT_PDF'],
    blockScroll: true,
  },
)
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'confirm', modal: ModalData): void
  (e: 'cancel'): void
}>()
const modal = reactive<ModalData>({
  with: 'WITH_ANSWER',
  size: 'A4',
  fileFormat: 'FILE_FORMAT_WORD',
})

watch(
  () => props.show,
  (val) => {
    if (val) {
      modal.with = 'WITH_ANSWER'
      modal.size = 'A4'
      modal.fileFormat = 'FILE_FORMAT_WORD'
    }
  },
)

const modalShow = computed({
  get: () => props.show,
  set: (val) => emits('update:show', val),
})

const title = computed(() => {
  switch (props.type) {
    case 'download':
      return '下载题目'
    case 'preparation':
      return '加入备课'
    default:
      return ''
  }
})

const leftText = computed(() => {
  switch (props.type) {
    case 'download':
      return '取消'
    case 'preparation':
      return '取消'
    default:
      return ''
  }
})
const rightText = computed(() => {
  switch (props.type) {
    case 'download':
      return '下载'
    case 'preparation':
      return '加入备课'
    default:
      return ''
  }
})

const closeModal = () => {
  modalShow.value = false
  emits('cancel')
}

const handleCancel = () => {
  modalShow.value = false
  emits('cancel')
}
const handleConfirm = () => {
  modalShow.value = false
  console.log('modal', modal)
  emits('confirm', modal)
}
</script>

<style lang="scss" scoped>
.paper-change-modal {
  width: 490px;
  height: 316px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0px 3.2px 3.2px 0px rgba(80, 75, 100, 0.08);
}

.paper-change-content {
  position: relative;
  padding: 32px;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url('https://fp.yangcong345.com/onion-extension/333-0bf9ccbdb2613616d5e53b93d4625262.png')
      no-repeat center;
    background-size: cover;
  }

  .change-header {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    color: #393548;
  }

  .change-list {
    .change-item {
      display: flex;
      margin-top: 24px;

      .item-left {
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #57526c;
      }

      .item-right {
        margin-left: 16px;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #57526c;

        .tip {
          margin-top: 12px;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: normal;
          line-height: 12px;
          color: #9792ac;
        }
      }
    }
  }

  .change-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 32px;

    .left-btn {
      width: 104px;
      padding: 9px 24px;
      margin-right: 16px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      color: #393548;
      text-align: center;
      background: transparent;
      border: 1px solid #c5c1d4;
      border-radius: 12px;
    }

    .right-btn {
      width: 104px;
      padding: 9px 24px;
      margin-right: 16px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 600;
      color: #393548;
      text-align: center;
      background: #ffd633;
      border: none;
      border-radius: 12px;
    }
  }
}
</style>
