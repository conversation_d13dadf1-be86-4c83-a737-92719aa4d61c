import request from '@/utils/request'
interface SpecialCourseReq {
  /**
   * 学科id.
   */
  subjectId?: string
  /**
   * 学段.
   */
  stageId?: string
}

interface SpecialCourseRes {
  id?: string
  name?: string
  [k: string]: unknown
}

/**
 * @description 培优课列表.
 * https://yapi.yc345.tv/project/2673/interface/api/105540
 * <AUTHOR>
 * @date 2024-07-10
 * @export
 * @param {SpecialCourseReq} params
 * @returns {Promise<SpecialCourseRes>}
 */
export const getPreviewSpecialCourse = (
  params: SpecialCourseReq,
): Promise<{ data: SpecialCourseRes[] }> => {
  return request.get<{ data: SpecialCourseRes[] }>(
    `/teacher-desk/preview/special-course`,
    {
      params,
    },
  )
}
