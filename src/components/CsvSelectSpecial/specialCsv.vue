<template>
  <n-cascader
    class="oiw-cascader"
    :menu-props="{class: 'oiw-special-wrap'}"
    :value="csvValue"
    placeholder="请选择"
    expand-trigger="click"
    :options="options"
    check-strategy="child"
    :render-label="renderLabel"
    show-path
    value-field="treeId"
    size="large"
    label-field="name"
    remote
    @load="handleLoad"
    @update:value="onChange"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import useCvsStore from '@/store/csv'
import { useCvsTree } from '@/hooks/useCvs'
import { getPreviewSpecialCourse } from './service'
import { NPopover } from 'naive-ui'

const props = defineProps<{
  specialCourseId: string
}>()
// 定义emits类型
const emits = defineEmits<
  (
    e: 'change',
    val: {
      stageId: YcType.CsvId
      subjectId: YcType.CsvId
      specialId: string
    },
  ) => void
>()

const renderLabel = (option: any) => {
  return option && option.name.length > 8
    ? h(
        NPopover,
        {},
        {
          trigger: () => h('span', {}, `${option.name}`),
          default: () => h('span', {}, `${option.name}`),
        },
      )
    : h('span', {}, `${option.name}`)
}

// 初始化store和hooks
const csvStore = useCvsStore()
const csvTree = useCvsTree()
const csvValue = ref<string | undefined>()
const options = ref<any[]>([])

// 初始化数据
onMounted(async () => {
  await initOptions()
})

// 初始化选项列表
async function initOptions() {
  // 构建选项树结构
  options.value = csvTree.value.map((a) => ({
    id: `${a.id}`,
    treeId: `${a.id}`,
    name: a.name,
    isLeaf: false,
    children: a.children?.map((b) => ({
      id: `${b.id}`,
      treeId: `${a.id}/${b.id}`,
      name: b.name,
      isLeaf: false,
    })),
  }))

  // 设置默认选中的值
  const [defaultOption] = findDefaultOption()
  if (defaultOption) {
    const [stageId, subjectId] = defaultOption.treeId.split('/')
    const { data } = await getPreviewSpecialCourse({
      subjectId,
      stageId,
    })
    if (data && data.length > 0) {
      defaultOption.children = data.map((item: any) => ({
        ...item,
        treeId: `${defaultOption.treeId.split('/')[0]}/${
          defaultOption.treeId.split('/')[1]
        }/${item.id}`,
      }))
      const localSpecialCourse = defaultOption.children.find(
        (item: any) => item.id === props.specialCourseId,
      )
      if (props.specialCourseId && localSpecialCourse) {
        csvValue.value = defaultOption.children.find(
          (item: any) => item.id === props.specialCourseId,
        )?.treeId
      } else {
        csvValue.value = defaultOption.children[0]?.treeId
      }
      const hasContainPropsSpecialCourse = defaultOption.children.find(
        (el: any) => el.id === props.specialCourseId,
      )
      emits('change', {
        subjectId: defaultOption.subjectId || subjectId,
        stageId: defaultOption.stageId || stageId,
        specialId:
          hasContainPropsSpecialCourse?.id || defaultOption.children[0]?.id,
      })
    }
  }
}

// 查找默认选中的选项
function findDefaultOption(): any[] {
  return options.value.flatMap(
    (item) =>
      item.children?.filter(
        (element: any) =>
          `${csvStore.currentTextBook?.stageId}/${csvStore.currentTextBook?.subjectId}` ===
          element.treeId,
      ),
  )
}

// 处理值变化
function onChange(val: string) {
  const [stageId, subjectId, specialId] = val.split('/')
  if (specialId) {
    csvValue.value = val
    emits('change', {
      subjectId,
      stageId,
      specialId,
    })
  }
}

// 加载子选项
async function handleLoad(option: any) {
  if (option && option.treeId) {
    const [stageId, subjectId] = option.treeId.split('/')
    const { data } = await getPreviewSpecialCourse({ subjectId, stageId })
    if (data && data.length > 0) {
      option.children = data.map((item: any) => ({
        ...item,
        treeId: `${stageId}/${subjectId}/${item.id}`,
      }))
    } else {
      option.isLeaf = true
    }
  }
}
</script>

<style lang="scss">
.oiw-special-wrap {
  .n-cascader-submenu.n-cascader-submenu--virtual {
    width: 120px;
  }

  .n-cascader-submenu {
    .n-cascader-option {
      min-width: 100px;
    }
    min-width: 100px;
  }

  .n-cascader-submenu:nth-child(3) {
    width: 300px;
  }
}
</style>
