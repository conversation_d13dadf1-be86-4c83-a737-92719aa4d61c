<template>
  <OIWLoading v-if="loading" width="200px" height="200px" :show="loading" />
  <div v-else-if="listData && listData.length">
    <div
      v-for="(item, index) in listData"
      :key="index + '文件详情'"
      class="item-box"
    >
      <div class="item-info">
        <div class="name-box" :draggable="true" @click="openFolder(item)">
          <PPTXIcon v-if="item.type === 'PPT'" class="flex-none w-24px" />
          <FolderIcon v-if="item.type === 'FOLDER'" class="flex-none w-24px" />
          <span>{{ item.nameString || item.name }}</span>
        </div>
      </div>
      <div class="btn-box">
        <OIWButton
          v-if="item.type === 'PPT'"
          ghost
          size="small"
          type="info"
          @click="handlePreview(item)"
        >
          预览
        </OIWButton>
        <OIWButton
          v-if="item.type === 'PPT'"
          size="small"
          type="info"
          @click="insertPPT(item)"
        >
          插入
        </OIWButton>
      </div>
    </div>
    <div class="pagination-box-bottom">
      <div v-if="totalPage > 1" class="pagination-box">
        <n-pagination
          v-model:page="curPage"
          class="custom-pagination"
          :page-count="totalPage"
          :on-update:page="handleChangePage"
        />
      </div>
    </div>
  </div>
  <OIWStateBlock
    v-else
    class="mt-120px text-center"
    type="empty"
    title="暂无课件"
  />
</template>

<script setup lang="ts">
import { OIWButton, OIWLoading, OIWStateBlock } from '@guanghe-pub/onion-ui-web'
import useSlideLectureStore from './store'
import PPTXIcon from '~icons/yc/PPTXIcon'
import FolderIcon from '~icons/yc/folderIcon'
import type { PrepareTreeItemTypeWithIsChecked } from '@/pages/prepare/service'

const emits =
  defineEmits<(e: 'insert', val: PrepareTreeItemTypeWithIsChecked) => void>()
const store = useSlideLectureStore()
const { loading, listData, curPage, totalPage } = storeToRefs(store)
const openFolder = (item: PrepareTreeItemTypeWithIsChecked) => {
  if (item.type === 'FOLDER') {
    store.setFolderId(item.id as string)
  }
  if (item.type === 'PPT') {
    window.open(
      `/teacher-workbench/slide/edit?fileId=${item.fileId}&id=${
        item.id
      }&fileName=${item.nameString || item.name}`,
      '_blank',
    )
    // window.open(
    //   `/teacher-workbench/slide/preview?fileId=${item.fileId}&platform=prepare`,
    // )
  }
}

const handleChangePage = (page: number) => {
  store.getChangePage(page)
}

const insertPPT = (item: PrepareTreeItemTypeWithIsChecked) => {
  emits('insert', item)
}

const handlePreview = (item: PrepareTreeItemTypeWithIsChecked) => {
  window.open(
    `/teacher-workbench/slide/edit?fileId=${item.fileId}&id=${
      item.id
    }&fileName=${item.nameString || item.name}`,
    '_blank',
  )
}
</script>

<style lang="scss" scoped>
.pagination-box-bottom {
  background: #fff;

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 12px 0;
  }
}

.item-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64px;
  box-shadow: inset 0px -1px 0px 0px #dcd8e7;

  &:hover {
    background: #f4f6ff;
  }

  .item-info {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .name-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 0 5px;
    cursor: pointer;

    span {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #3d3d3d;
    }
  }

  .btn-box {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
