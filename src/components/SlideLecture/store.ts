import { acceptHMRUpdate, defineStore } from 'pinia'
import type {
  BreadcrumbItemType,
  PrepareTreeItemTypeWithIsChecked,
  SortType,
} from '@/pages/prepare/service'
import { getFolderDetailApi } from '@/pages/prepare/service'
import { getFileNameAndType } from '@/pages/prepare/utils'
import { useAuth } from '@/hooks/useAuth'

// 侧边栏资源管理 Store
const useSlideLectureStore = defineStore('SlideLecture', () => {
  const PAGE_SIZE = 20
  const { userId } = useAuth()

  const folderId = ref<string | undefined>(
    (window.localStorage.getItem(
      `slideLectureFolderId-${userId.value}`,
    ) as string) || '',
  )
  const breadcrumbs = ref<BreadcrumbItemType[]>([])
  const listData = ref<PrepareTreeItemTypeWithIsChecked[]>([])
  const curPage = ref(1)
  const totalPage = ref(0)
  const loading = ref(false)

  const getFolderDetail = async (
    isFromFirstPage: boolean,
    folderId?: string,
    sortBy?: 'PPT' | 'DOCX' | 'ALL',
  ) => {
    if (isFromFirstPage) {
      curPage.value = 1
    }
    const fileTypes = ['FOLDER']
    if (sortBy) {
      if (sortBy === 'ALL') {
        fileTypes.push('PPT', 'DOCX')
      } else {
        fileTypes.push(sortBy)
      }
    } else {
      fileTypes.push('PPT', 'DOCX')
    }
    loading.value = true
    const sortType = 'DEFAULT' as SortType
    const body = {
      folderId: folderId,
      fileTypes,
      sortType,
      page: curPage.value,
      pageSize: PAGE_SIZE,
    }
    const res = await getFolderDetailApi(body)
    if (res.breadcrumbs) {
      breadcrumbs.value = res.breadcrumbs
    }
    if (res.items) {
      listData.value = res.items.map((i) => {
        if (i.type === 'FOLDER') {
          return {
            ...i,
            isChecked: false,
            isNameEditState: false,
          }
        }
        const { fileName, fileType } = getFileNameAndType(i.name)
        return {
          ...i,
          isChecked: false,
          isNameEditState: false,
          nameString: fileName || i.name,
          typeString: fileType || '',
        }
      })
    }
    totalPage.value = res.totalPage
    loading.value = false
  }

  const getChangePage = async (page: number) => {
    curPage.value = page
    await getFolderDetail(false, folderId.value, 'PPT')
  }

  const setFolderId = (id: string) => {
    folderId.value = id
    window.localStorage.setItem(`slideLectureFolderId-${userId.value}`, id)
  }

  // 返回store暴露的属性和方法
  return {
    folderId,
    breadcrumbs,
    listData,
    curPage,
    totalPage,
    loading,
    getFolderDetail,
    getChangePage,
    setFolderId,
  }
})

// 启用热更新
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useSlideLectureStore, import.meta.hot))
}

export default useSlideLectureStore
