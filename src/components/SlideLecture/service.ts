import request from '@/utils/request'

export interface PostTriggerGeneratePdReq {
  /**
   * 文件ID.
   */
  id?: string
  [k: string]: unknown
}

export type PostTriggerGeneratePdRes = Record<string, unknown>

/**
 * @description 触发生成pdf
 * https://yapi.yc345.tv/project/2673/interface/api/126152
 * <AUTHOR>
 * @date 2025-05-22
 * @export
 * @param {PostTriggerGeneratePdReq} data
 * @returns {Promise<PostTriggerGeneratePdRes>}
 */
export const postTriggerGeneratePdfApi = (
  data: PostTriggerGeneratePdReq,
): Promise<PostTriggerGeneratePdRes> => {
  return request.post<PostTriggerGeneratePdRes>(
    `/teacher-desk/cloud-disk/trigger-generate-pdf`,
    data,
  )
}
