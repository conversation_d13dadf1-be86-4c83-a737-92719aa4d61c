<template>
  <div class="bread-container">
    <div v-if="isShowAllItem">
      <OIWBreadcrumb
        class="bread-box"
        :class="{ 'bread-box-multi': breadcrumbs.length }"
      >
        <OIWBreadcrumbItem text="我的备课" @click="handleToRoot" />
        <OIWBreadcrumbItem
          v-for="(item, index) in breadcrumbs"
          :key="index + '备课导航'"
          :text="item.name"
          @click="handleToFolder(item.id)"
        />
      </OIWBreadcrumb>
    </div>
    <div v-else>
      <OIWBreadcrumb
        class="bread-box"
        :class="{ 'bread-box-multi': breadcrumbs.length }"
      >
        <OIWBreadcrumbItem text="我的备课" @click="handleToRoot" />
        <OIWBreadcrumbItem>
          <n-dropdown :options="options" @select="handleSelect">
            <div class="bread-item">...</div>
          </n-dropdown>
        </OIWBreadcrumbItem>
        <OIWBreadcrumbItem
          :text="curFolder.name"
          @click="handleToFolder(curFolder.id)"
        />
      </OIWBreadcrumb>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { BreadcrumbItemType } from '@/pages/prepare/service'
import { OIWBreadcrumb, OIWBreadcrumbItem } from '@guanghe-pub/onion-ui-web'

const props = defineProps<{
  breadcrumbs: BreadcrumbItemType[]
}>()
const emits = defineEmits<(e: 'changeFolder', folderId: string) => void>()

const options = computed(() => {
  return props.breadcrumbs?.slice(0, -1).map((item) => {
    return {
      label: item.name,
      key: item.id,
    }
  })
})
const curFolder = computed(() => {
  return props.breadcrumbs?.slice(-1)[0]
})
const isShowAllItem = computed(() => {
  return props.breadcrumbs?.length < 3
})
const handleToFolder = (folderId: string) => {
  emits('changeFolder', folderId)
}
const handleSelect = (key: string) => {
  handleToFolder(key)
}
const handleToRoot = () => {
  emits('changeFolder', '')
}
</script>

<style lang="scss" scoped>
.bread-container {
  width: 80%;
  overflow: hidden;
}

.bread-box {
  /* stylelint-disable selector-class-pattern */
  ::v-deep(.oiw-breadcrumbItem) {
    flex: none;
    max-width: 200px;
    overflow: hidden;
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;
    text-overflow: ellipsis;
    white-space: nowrap;

    &::after {
      width: 24px;
      height: 24px;
      background: url('https://fp.yangcong345.com/onion-extension/bread-d5af4e6644bb86bcd69306ea09545167.png')
        no-repeat;
      background-size: contain;
    }

    span {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &.bread-box-multi {
    ::v-deep(.oiw-breadcrumbItem) {
      flex: none;
      max-width: 200px;
      overflow: hidden;
      font-size: 14px;
      font-weight: 600;
      line-height: 14px;
      text-overflow: ellipsis;
      white-space: nowrap;

      &::after {
        width: 12px;
        height: 12px;
        background: url('https://fp.yangcong345.com/onion-extension/bread-d5af4e6644bb86bcd69306ea09545167.png')
          no-repeat;
        background-size: contain;
      }

      span {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .bread-item {
      font-size: 14px;
      font-weight: 600;
      line-height: 14px;
      color: #8a869e;
      text-align: center;
      cursor: pointer;
    }
  }
}

.bread-item {
  font-size: 24px;
  font-weight: 600;
  line-height: 28px;
  color: #8a869e;
  text-align: center;
  cursor: pointer;
}
</style>
