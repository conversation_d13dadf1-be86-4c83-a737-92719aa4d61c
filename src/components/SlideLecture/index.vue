<template>
  <n-drawer
    v-model:show="drawerShow"
    :show-mask="false"
    width="544px"
    placement="right"
    :block-scroll="false"
  >
    <div class="p-24px">
      <div class="flex justify-between mb-24px">
        <FolderBreadcrumb
          :breadcrumbs="breadcrumbs"
          @change-folder="handleChangeFolder"
        />
        <CloseIcon class="cursor-pointer" @click="onClose" />
      </div>
      <div
        v-if="!folderId"
        class="mx-auto rounded-12px flex items-center px-16px h-48px bg-#F4F6FF"
      >
        <TipIcon class="mr-8px" />
        <div class="text-14px font-600 color-#393548 select-none">
          授课环节可以插入课件内容，请在「我的-备课」模块上传课件
        </div>
      </div>
      <FolderDetail @insert="onInsert" />
    </div>
  </n-drawer>
</template>

<script setup lang="ts">
import type { InsertParams } from '@/components/SideResource/types'
import FolderBreadcrumb from './FolderBreadcrumb.vue'
import FolderDetail from './FolderDetail.vue'
import CloseIcon from '~icons/yc/close-round-gray'
import useSlideLectureStore from './store'
import TipIcon from '~icons/yc/tip-blue'
import type { PrepareTreeItemTypeWithIsChecked } from '@/pages/prepare/service'
import { postTriggerGeneratePdfApi } from './service'

const props = defineProps<{
  show: boolean
}>()
const emits = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'add', val: InsertParams): void
}>()
const drawerShow = computed({
  get() {
    return props.show
  },
  set(val) {
    emits('update:show', val)
  },
})
const store = useSlideLectureStore()
const { breadcrumbs, folderId } = storeToRefs(store)

watch(
  () => drawerShow.value,
  (val) => {
    if (val) {
      store.getFolderDetail(true, folderId.value, 'PPT')
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => folderId.value,
  (val) => {
    store.getFolderDetail(true, val, 'PPT')
  },
)

const onClose = () => {
  drawerShow.value = false
}

const handleChangeFolder = (id: string) => {
  store.setFolderId(id)
}

const setGeneratePdf = async (item: PrepareTreeItemTypeWithIsChecked) => {
  await postTriggerGeneratePdfApi({ id: item.id as string })
}

const onInsert = (item: PrepareTreeItemTypeWithIsChecked) => {
  const data: InsertParams = {
    sourceType: 'PPTCourseware',
    sourceId: item.id as string,
    name: item.nameString || item.name,
    duration: 0,
    importFrom: 'MyCourseResource',
  }
  if (!item.pdfKey) {
    setGeneratePdf(item)
  }
  emits('add', data)
}
</script>
