import request from '@/utils/request'

export type ShareType = 'courseware' | 'ppt' | 'exam_paper' | 'word'

export interface CreateShareLink {
  resourceId: string
  resourceType: ShareType
  resourceName: string
  shareUserName: string
}

export interface Share {
  id: string
  resourceId: string
  resourceType: ShareType
  resourceName: string
  shareUserId: string
  shareUserName: string
  expiration: number
  state: 'confirmed' | 'unconfirmed'
  expiredAt: string
  hasPwd: boolean
}

export const createShareLinkApi = async (body: CreateShareLink) => {
  return request.post<Share>('/teacher-desk/share', body)
}

interface ConfrimShareBody {
  shareId: string
  expiration: number
  pw?: string
}

export const confrimShareLinkApi = async (body: ConfrimShareBody) => {
  return request.put<Share>('/teacher-desk/share', body)
}
