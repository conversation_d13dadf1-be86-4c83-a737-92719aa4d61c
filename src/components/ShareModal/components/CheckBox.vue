<template>
  <div class="flex items-center space-x-32px">
    <div
      v-for="option of options"
      :key="option.value"
      class="inline-flex items-center cursor-pointer"
      @click="emits('update:value', option.value)"
    >
      <CheckIcon v-if="value === option.value" />
      <UnCheckIcon v-else />
      <span class="ml-12px">{{ option.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import CheckIcon from '~icons/yc/check'
import UnCheckIcon from '~icons/yc/un-check'

defineProps<{
  options: {
    label: string
    value: string | number
  }[]
  value: string | number
}>()

const emits = defineEmits<(e: 'update:value', value: string | number) => void>()
</script>
