# ShareModal 分享弹窗

## 概述

ShareModal 是一个资源分享弹窗组件，支持生成分享链接、设置有效期、密码保护等功能。用于课件、试卷等教学资源的分享。

## 基本用法

```vue
<template>
  <div>
    <n-button @click="openShareModal">分享资源</n-button>
    
    <ShareModal 
      ref="shareModalRef"
      :shareParams="shareParams"
    />
  </div>
</template>

<script setup>
import ShareModal from '@/components/ShareModal/ShareModal.vue'

const shareModalRef = ref()
const shareParams = {
  resourceId: 'resource-123',
  resourceType: 'courseware',
  resourceName: '数学课件.pptx',
  from: 'prepareCenterList'
}

const openShareModal = () => {
  shareModalRef.value?.createShare()
}
</script>
```

## 完整配置示例

```vue
<template>
  <div class="share-container">
    <div class="resource-item">
      <h3>{{ resource.name }}</h3>
      <n-button @click="handleShare" type="primary">
        分享
      </n-button>
    </div>
    
    <ShareModal 
      ref="shareModal"
      :shareParams="currentShareParams"
    />
  </div>
</template>

<script setup>
import ShareModal from '@/components/ShareModal/ShareModal.vue'

const shareModal = ref()
const resource = ref({
  id: 'courseware-456',
  name: '初中数学-二次函数.pptx',
  type: 'courseware'
})

const currentShareParams = computed(() => ({
  resourceId: resource.value.id,
  resourceType: resource.value.type,
  resourceName: resource.value.name,
  from: 'slideEdit'
}))

const handleShare = async () => {
  try {
    await shareModal.value?.createShare()
  } catch (error) {
    console.error('分享失败:', error)
    message.error('分享失败，请重试')
  }
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| shareParams | 分享参数配置 | `CreateShare` | - |

### CreateShare 类型定义

```typescript
interface CreateShare {
  resourceId: string      // 资源ID
  resourceType: string    // 资源类型
  resourceName: string    // 资源名称
  from: string           // 来源页面
}
```

## 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| createShare | 创建分享链接并显示弹窗 | - | `Promise<void>` |

## 功能特性

### 1. 有效期设置
```vue
<!-- 组件内部支持多种有效期选项 -->
<script setup>
const timeOptions = [
  { label: '1天', value: 1 },
  { label: '7天', value: 7 },
  { label: '30天', value: 30 },
  { label: '永久', value: -1 }
]
</script>
```

### 2. 密码保护
- 支持开启/关闭密码保护
- 自动生成4位随机密码
- 密码与链接一起复制

### 3. 一键复制
```javascript
// 复制分享内容
const copyContent = computed(() => {
  const baseText = `Hi，我在洋葱学园中发现了一个不错的资源，快点击链接查看吧～`
  
  if (hasPwd.value) {
    return `${baseText}\n【分享链接】：${link.value}\n【密码】：${pwd.value}`
  } else {
    return `${baseText}\n【分享链接】${link.value}`
  }
})
```

## 使用场景

### 1. 课件分享

```vue
<template>
  <div class="courseware-list">
    <div 
      v-for="courseware in coursewareList"
      :key="courseware.id"
      class="courseware-item"
    >
      <h4>{{ courseware.name }}</h4>
      <n-button @click="shareCourseware(courseware)">
        分享
      </n-button>
    </div>
    
    <ShareModal 
      ref="shareModal"
      :shareParams="shareParams"
    />
  </div>
</template>

<script setup>
const shareParams = ref({})

const shareCourseware = (courseware) => {
  shareParams.value = {
    resourceId: courseware.id,
    resourceType: 'courseware',
    resourceName: courseware.name,
    from: 'coursewareList'
  }
  
  shareModal.value?.createShare()
}
</script>
```

### 2. 试卷分享

```vue
<template>
  <div class="paper-detail">
    <div class="paper-header">
      <h2>{{ paper.title }}</h2>
      <n-space>
        <n-button @click="downloadPaper">下载</n-button>
        <n-button @click="sharePaper" type="primary">分享</n-button>
      </n-space>
    </div>
    
    <ShareModal 
      ref="shareModal"
      :shareParams="paperShareParams"
    />
  </div>
</template>

<script setup>
const paper = ref({
  id: 'paper-789',
  title: '期中数学测试卷'
})

const paperShareParams = computed(() => ({
  resourceId: paper.value.id,
  resourceType: 'paper',
  resourceName: paper.value.title,
  from: 'paperDetail'
}))

const sharePaper = () => {
  shareModal.value?.createShare()
}
</script>
```

### 3. 批量分享

```vue
<template>
  <div class="batch-share">
    <div class="selection-bar">
      <n-checkbox 
        v-model:checked="selectAll"
        @update:checked="handleSelectAll"
      >
        全选
      </n-checkbox>
      <n-button 
        :disabled="selectedItems.length === 0"
        @click="batchShare"
      >
        批量分享 ({{ selectedItems.length }})
      </n-button>
    </div>
    
    <ShareModal 
      ref="shareModal"
      :shareParams="batchShareParams"
    />
  </div>
</template>

<script setup>
const selectedItems = ref([])
const batchShareParams = ref({})

const batchShare = () => {
  if (selectedItems.value.length === 1) {
    // 单个资源分享
    const item = selectedItems.value[0]
    batchShareParams.value = {
      resourceId: item.id,
      resourceType: item.type,
      resourceName: item.name,
      from: 'batchShare'
    }
  } else {
    // 多个资源打包分享
    batchShareParams.value = {
      resourceId: selectedItems.value.map(item => item.id).join(','),
      resourceType: 'batch',
      resourceName: `${selectedItems.value.length}个资源`,
      from: 'batchShare'
    }
  }
  
  shareModal.value?.createShare()
}
</script>
```

## API 接口

组件依赖以下 API 接口：

### createShareLinkApi
```typescript
// 创建分享链接
interface CreateShareRequest {
  resourceId: string
  resourceType: string
  resourceName: string
  shareUserName: string
}

interface CreateShareResponse {
  id: string          // 分享ID
  shareUrl: string    // 分享链接
  expireTime?: string // 过期时间
}
```

### confrimShareLinkApi
```typescript
// 确认分享（设置有效期和密码）
interface ConfirmShareRequest {
  shareId: string
  expiration: number  // 有效期（天数）
  password?: string   // 密码
}
```

## 样式定制

```css
.share-modal {
  /* 弹窗样式 */
}

.share-content {
  /* 内容区域样式 */
  padding: 16px;
}

.share-link-input {
  /* 链接输入框样式 */
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.copy-button {
  /* 复制按钮样式 */
  background-color: #1890ff;
  border-color: #1890ff;
}
```

## 最佳实践

### 1. 错误处理

```vue
<script setup>
const handleShare = async () => {
  try {
    setLoading(true)
    await shareModal.value?.createShare()
  } catch (error) {
    if (error.code === 'RESOURCE_NOT_FOUND') {
      message.error('资源不存在')
    } else if (error.code === 'PERMISSION_DENIED') {
      message.error('没有分享权限')
    } else {
      message.error('分享失败，请重试')
    }
  } finally {
    setLoading(false)
  }
}
</script>
```

### 2. 权限控制

```vue
<script setup>
const { hasSharePermission } = useAuth()

const canShare = computed(() => {
  return hasSharePermission(resource.value.type)
})
</script>

<template>
  <n-button 
    :disabled="!canShare"
    @click="handleShare"
  >
    {{ canShare ? '分享' : '无权限' }}
  </n-button>
</template>
```

### 3. 埋点统计

```vue
<script setup>
const handleShare = () => {
  // 埋点统计
  buryPoint('click_share_button', {
    resourceType: shareParams.value.resourceType,
    resourceId: shareParams.value.resourceId,
    from: shareParams.value.from
  })
  
  shareModal.value?.createShare()
}
</script>
```

## 注意事项

1. **资源权限**: 确保用户有分享该资源的权限
2. **链接有效性**: 分享链接的有效期管理
3. **密码安全**: 密码传输和存储的安全性
4. **网络异常**: 处理网络请求失败的情况

## 更新日志

### v1.0.0
- 初始版本，支持基本的资源分享功能
- 支持有效期设置和密码保护
- 集成一键复制和埋点统计
