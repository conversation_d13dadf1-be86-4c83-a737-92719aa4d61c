/* @jsxImportSource vue */
import { defineComponent, provide, ref } from 'vue'
import type { CreateShare } from './utils'
import { shareProvideInjectKey } from './utils'
import ShareModal from './ShareModal.vue'

export default defineComponent({
  name: 'ShareProvide',
  setup(props, { slots }) {
    const shareParams = ref<CreateShare>({
      resourceId: '',
      resourceType: 'courseware',
      resourceName: '',
      from: '',
    })

    const createShare = (params: CreateShare) => {
      shareParams.value = params
    }

    provide(shareProvideInjectKey, {
      createShare,
    })

    return () => (
      <>
        {slots.default?.()}
        <ShareModal shareParams={shareParams.value} />
      </>
    )
  },
})
