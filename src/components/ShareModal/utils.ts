import type { InjectionKey } from 'vue'
import type { ShareType } from './service'

const array = [
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'q',
  'r',
  's',
  't',
  'u',
  'v',
  'w',
  'x',
  'y',
  'z',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '0',
]

export const createPwd = () => {
  let pwd = ''
  while (pwd.length < 4) {
    pwd += array[Math.floor(Math.random() * (array.length - 1))]
  }
  return pwd
}

export function createInjectionKey<T>(key: string): InjectionKey<T> {
  return key as any
}

export interface CreateShare {
  resourceId: string
  resourceType: ShareType
  resourceName: string
  from:
    | ''
    | 'prepareCenterList'
    | 'slideEdit'
    | 'paperList'
    | 'paperCollection'
    | 'courseList'
  // prepareCenterList:备课中心课件列表-slideEdit:课件编辑页 paperList:资源中心公共资源试卷-paperCollection:资源中心我的收藏试卷 courseList:公共资源-课件列表
  extra?: any // 存放一些埋点参数
}

export const shareProvideInjectKey = createInjectionKey<{
  createShare: (params: CreateShare) => void
}>('shareProvideInjectKey')
