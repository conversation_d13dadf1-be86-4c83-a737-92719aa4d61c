<template>
  <OIWModal
    v-model:show="modalShow"
    size="large"
    title="分享资源"
    preset="card"
  >
    <div class="px-16px">
      <div class="flex items-center">
        <FileIcon class="w-48px h-48px" /><span
          class="color-#393548 text-16px ml-16px font-500"
          >{{ displayResourceName }}</span
        >
      </div>
      <div class="flex items-center mt-32px">
        <div class="w-56px mr-12px">有效期</div>
        <CheckBox
          :value="expirationValue"
          :options="timeOptions"
          @update:value="updateExpirationValue"
        />
      </div>
      <div class="flex items-center mt-24px">
        <div class="w-56px mr-12px">加密</div>
        <n-switch :value="hasPwd" @update:value="onPwdSwitch" />
        <template v-if="hasPwd">
          <div class="ml-32px">密码：</div>
          <div>{{ pwd }}</div>
        </template>
      </div>
      <div class="flex items-center mt-24px">
        <div class="w-56px mr-12px">分享链接</div>
        <div
          class="py-10px px-16px w-346px color-#57526C bg-#F4F6FF text-14px rounded-10px"
        >
          <n-ellipsis style="max-width: 80%">
            {{ link }}
          </n-ellipsis>
        </div>
      </div>
      <div class="flex justify-center mt-32px mb-16px">
        <OIWButton type="info" @click="onClipboard">{{ buttonText }}</OIWButton>
      </div>
    </div>
  </OIWModal>
</template>

<script lang="ts" setup>
import { OIWModal, OIWButton, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import FileIcon from '~icons/yc/file'
import CheckBox from './components/CheckBox.vue'
import { confrimShareLinkApi, createShareLinkApi } from './service'
import { useAuth } from '@/hooks/useAuth'
import type { CreateShare } from './utils'
import { createPwd } from './utils'
import copy from 'copy-to-clipboard'
import { getDynamicHost } from '@/utils/apiUrl'
import { buryPoint } from '@/utils/buryPoint'

const props = defineProps<{
  shareParams: CreateShare
}>()

const { nickName } = useAuth()

const displayResourceName = computed(() => {
  return props.shareParams.resourceName
    .replaceAll('.pptx', '')
    .replaceAll('.ppt', '')
    .replaceAll('.docx', '')
    .replaceAll('.doc', '')
})

const shareId = ref()
async function createShare() {
  modalShow.value = true
  const { shareParams } = props
  const shareRes = await createShareLinkApi({
    resourceId: shareParams.resourceId,
    resourceType: shareParams.resourceType,
    resourceName: shareParams.resourceName,
    shareUserName: nickName.value ?? '',
  })
  shareId.value = shareRes.id
}
const hasPwd = ref(false)
const onPwdSwitch = (val: boolean) => {
  hasPwd.value = val
}
const pwd = computed(() => {
  return hasPwd.value ? createPwd() : ''
})
const buttonText = computed(() => {
  return !hasPwd.value ? '复制分享链接' : '复制分享链接和密码'
})

const link = computed(() => {
  const url = `${getDynamicHost()}/school/activityH5/bench-share?id=${
    shareId.value
  }`
  return url
})

const message = useOIWMessage()
const onClipboard = async () => {
  // console.log('onClipboard', props.shareParams)
  if (!shareId.value) {
    message.warning('未获取到有效链接，请刷新页面重试')
    return
  }

  confrimShare()
  copy(
    !hasPwd.value
      ? `Hi，我在洋葱学园中发现了一个不错的资源，快点击链接查看吧～ \n【分享链接】${link.value}`
      : `Hi，我在洋葱学园中发现了一个不错的资源，快点击链接查看吧～\n【分享链接】：${link.value}【密码】：${pwd.value}`,
  )
  message.success(
    hasPwd.value
      ? '已成功复制链接和密码，快去分享吧～'
      : '已成功复制链接，快去分享吧～',
  )
  if (props.shareParams.from === 'prepareCenterList') {
    buryPoint('click_prepareCenter_share_pop1', {
      from: '1',
      courseId: props.shareParams.resourceId,
    })
  }
  if (props.shareParams.from === 'slideEdit') {
    buryPoint('click_prepareCenter_share_pop1', {
      from: '2',
      courseId: props.shareParams.resourceId,
    })
  }
  if (props.shareParams.from === 'paperList') {
    buryPoint('click_prepareCenter_share_pop3', {
      from: '1',
      examId: props.shareParams.resourceId,
    })
  }
  if (props.shareParams.from === 'paperCollection') {
    buryPoint('click_prepareCenter_share_pop3', {
      from: '2',
      examId: props.shareParams.resourceId,
    })
  }
  if (props.shareParams.from === 'courseList') {
    buryPoint('click_prepareCenter_share_pop2', {
      subjectId: parseInt(props.shareParams.extra.subjectId),
      stageId: parseInt(props.shareParams.extra.stageId),
      publisherId: parseInt(props.shareParams.extra.publisherId),
      semesterId: parseInt(props.shareParams.extra.semesterId),
      courseId: props.shareParams.resourceId,
    })
  }
}

async function confrimShare() {
  if (shareId.value) {
    await confrimShareLinkApi({
      shareId: shareId.value,
      expiration: expiration.value,
      pw: hasPwd.value ? pwd.value : undefined,
    })
  }
}

const modalShow = ref(false)
const expirationValue = ref('1')
const updateExpirationValue = (val: string | number) => {
  expirationValue.value = val as string
}

const expiration = computed(() => {
  return expirationValue.value ? parseInt(expirationValue.value) * 24 * 3600 : 0
})
const timeOptions = [
  {
    value: '',
    label: '永久',
  },
  {
    value: '30',
    label: '30天',
  },
  {
    value: '7',
    label: '7天',
  },
  {
    value: '1',
    label: '1天',
  },
]

const reset = () => {
  expirationValue.value = '1'
  hasPwd.value = false
  shareId.value = undefined
}

watch(
  () => props.shareParams,
  (val) => {
    if (
      val.resourceId &&
      val.resourceType &&
      val.resourceName &&
      modalShow.value === false
    ) {
      reset()
      createShare()
    }
  },
)
</script>

<style lang="scss" scoped>
.check-wrap {
  ::v-deep(.n-checkbox-box__border) {
    border-radius: 50%;
  }

  ::v-deep(.check-icon) {
    border-radius: 50%;
  }
}
</style>
