<template>
  <div class="h-100% w-100% relative">
    <div class="cover" />
    <div ref="webofficeEl" class="weboffice-container"></div>
  </div>
</template>

<script setup lang="ts">
import WebOfficeSDK from '@/assets/web-office-sdk-solution-v2.0.6/web-office-sdk-solution-v2.0.6.es.js'
import useAuthStore from '@/store/auth'
import { appId } from '@/utils/apiUrl.ts'
import { DINGTALK } from '@/utils/Ding.util.ts'
import { getQueryString } from '@guanghe-pub/onion-utils'

const props = defineProps<{
  fileId: string
  platform: 'prepare' | 'resourceCenter' | 'schoolResource' | 'template'
  sourceId: string
}>()
const store = useAuthStore()
const { token, me } = storeToRefs(store)
const webofficeEl = ref()

onMounted(async () => {
  const originWhiteListConfig =
    me.value?.type === DINGTALK
      ? ['dingtalk', 'https://o.wpsgo.com']
      : ['https://o.wpsgo.com']

  const instance = await WebOfficeSDK.init({
    officeType: 'p',
    appId,
    fileId: props.fileId,
    mount: webofficeEl.value,
    token: token.value,
    originWhiteList: originWhiteListConfig,
    origin: 'https://o.wpsgo.com',
    pptOptions: {
      isShowComment: false, // 是否显示评论相关入口
      isShowRemarkView: false, // 是否显示备注视图
    },
    commonOptions: {
      isShowTopArea: false, // 隐藏顶部区域（头部和工具栏）
      isShowHeader: false, // 隐藏头部区域
      isBrowserViewFullscreen: false, // 是否在浏览器区域全屏
      isIframeViewFullscreen: true, // 是否在 iframe 区域内全屏
    },
    customArgs: {
      updateAuth: 0, // 是否具有编辑权限，0-无 1-有
      sourceId: props.sourceId,
      platform: props.platform, // resourceCenter-资源中心 prepare-备课中心 // schoolResource-学校资源 // template-模板
    },
  })
  await instance.ready()
  if (getQueryString('isPlay') === 'true') {
    instance.Application.ActivePresentation.SlideShowSettings.Run()
  }
})
</script>

<style lang="scss">
.cover {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 40px;
  background: #fff;
}

.weboffice-container {
  width: 100%;
  height: 100%;
}
</style>
