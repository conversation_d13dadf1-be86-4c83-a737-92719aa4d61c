# PPTPreview PPT预览组件

## 概述

PPTPreview 是一个 PowerPoint 文件预览组件，支持 PPT/PPTX 文件的在线预览、页面导航、缩放等功能。提供了完整的幻灯片浏览体验。

## 基本用法

```vue
<template>
  <div>
    <PPTPreview 
      :pptUrl="pptFileUrl"
      :visible="showPreview"
      @close="handleClose"
      @pageChange="handlePageChange"
    />
  </div>
</template>

<script setup>
import PPTPreview from '@/components/PPTPreview/index.vue'

const pptFileUrl = ref('https://example.com/presentation.pptx')
const showPreview = ref(false)

const handleClose = () => {
  showPreview.value = false
}

const handlePageChange = (pageIndex) => {
  console.log('切换到第', pageIndex + 1, '页')
}
</script>
```

## 完整功能示例

```vue
<template>
  <div class="ppt-container">
    <div class="ppt-toolbar">
      <n-button @click="openPPT">预览PPT</n-button>
      <n-button @click="downloadPPT">下载PPT</n-button>
    </div>
    
    <PPTPreview 
      :pptUrl="currentPPT.url"
      :visible="previewVisible"
      :initialPage="startPage"
      :showToolbar="true"
      :allowDownload="true"
      @close="closePreview"
      @pageChange="onPageChange"
      @download="onDownload"
    />
  </div>
</template>

<script setup>
const currentPPT = ref({
  id: 'ppt-123',
  name: '数学课件.pptx',
  url: 'https://example.com/math-courseware.pptx',
  totalPages: 25
})

const previewVisible = ref(false)
const startPage = ref(0)

const openPPT = () => {
  previewVisible.value = true
}

const closePreview = () => {
  previewVisible.value = false
}

const onPageChange = (pageIndex, totalPages) => {
  console.log(`当前页: ${pageIndex + 1}/${totalPages}`)
  // 保存用户浏览进度
  saveBrowsingProgress(currentPPT.value.id, pageIndex)
}

const onDownload = () => {
  // 处理下载逻辑
  downloadFile(currentPPT.value.url, currentPPT.value.name)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| pptUrl | PPT文件URL | `string` | - |
| visible | 是否显示预览 | `boolean` | `false` |
| initialPage | 初始显示页面 | `number` | `0` |
| showToolbar | 是否显示工具栏 | `boolean` | `true` |
| allowDownload | 是否允许下载 | `boolean` | `false` |
| allowFullscreen | 是否允许全屏 | `boolean` | `true` |
| zoom | 初始缩放比例 | `number` | `1` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭预览时触发 | `() => void` |
| pageChange | 页面切换时触发 | `(pageIndex: number, totalPages: number) => void` |
| download | 点击下载时触发 | `() => void` |
| fullscreen | 进入/退出全屏时触发 | `(isFullscreen: boolean) => void` |
| zoom | 缩放变化时触发 | `(zoomLevel: number) => void` |

## 功能特性

### 1. 页面导航
- 支持上一页/下一页导航
- 支持页面缩略图导航
- 支持键盘快捷键（方向键）
- 支持页面跳转输入

### 2. 缩放功能
```vue
<script setup>
// 缩放控制
const zoomLevels = [0.5, 0.75, 1, 1.25, 1.5, 2]
const currentZoom = ref(1)

const zoomIn = () => {
  const currentIndex = zoomLevels.indexOf(currentZoom.value)
  if (currentIndex < zoomLevels.length - 1) {
    currentZoom.value = zoomLevels[currentIndex + 1]
  }
}

const zoomOut = () => {
  const currentIndex = zoomLevels.indexOf(currentZoom.value)
  if (currentIndex > 0) {
    currentZoom.value = zoomLevels[currentIndex - 1]
  }
}
</script>
```

### 3. 全屏模式
- 支持全屏预览
- 全屏状态下的导航控制
- ESC键退出全屏

## 使用场景

### 1. 课件预览

```vue
<template>
  <div class="courseware-list">
    <div 
      v-for="courseware in coursewareList"
      :key="courseware.id"
      class="courseware-item"
    >
      <div class="courseware-info">
        <h4>{{ courseware.name }}</h4>
        <p>{{ courseware.description }}</p>
      </div>
      <n-button @click="previewCourseware(courseware)">
        预览
      </n-button>
    </div>
    
    <PPTPreview 
      :pptUrl="selectedCourseware?.url"
      :visible="previewVisible"
      @close="previewVisible = false"
    />
  </div>
</template>
```

### 2. 教学展示

```vue
<template>
  <div class="teaching-display">
    <PPTPreview 
      :pptUrl="lessonPPT.url"
      :visible="teachingMode"
      :showToolbar="false"
      :allowFullscreen="true"
      @pageChange="onTeachingPageChange"
    />
  </div>
</template>

<script setup>
const onTeachingPageChange = (pageIndex) => {
  // 记录教学进度
  updateTeachingProgress(pageIndex)
  
  // 同步到学生端
  syncToStudents(pageIndex)
}
</script>
```

### 3. 作业布置

```vue
<template>
  <div class="homework-assignment">
    <PPTPreview 
      :pptUrl="homeworkPPT.url"
      :visible="assignmentPreview"
      :initialPage="targetPage"
      @pageChange="selectHomeworkPage"
    />
  </div>
</template>

<script setup>
const selectHomeworkPage = (pageIndex) => {
  // 选择作业页面
  selectedPages.value.push(pageIndex)
}
</script>
```

## 样式定制

```css
.ppt-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
}

.ppt-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.ppt-slide {
  max-width: 90%;
  max-height: 90%;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ppt-toolbar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  gap: 12px;
}

.ppt-nav-button {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.ppt-nav-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}
```

## 最佳实践

### 1. 性能优化

```vue
<script setup>
// 预加载相邻页面
const preloadAdjacentPages = (currentPage) => {
  const preloadPages = [currentPage - 1, currentPage + 1]
  preloadPages.forEach(pageIndex => {
    if (pageIndex >= 0 && pageIndex < totalPages.value) {
      preloadPage(pageIndex)
    }
  })
}

// 懒加载页面内容
const loadPageContent = async (pageIndex) => {
  if (!pageCache.has(pageIndex)) {
    const content = await fetchPageContent(pptUrl.value, pageIndex)
    pageCache.set(pageIndex, content)
  }
  return pageCache.get(pageIndex)
}
</script>
```

### 2. 错误处理

```vue
<script setup>
const handleLoadError = (error) => {
  console.error('PPT加载失败:', error)
  
  if (error.code === 'FILE_NOT_FOUND') {
    message.error('PPT文件不存在')
  } else if (error.code === 'UNSUPPORTED_FORMAT') {
    message.error('不支持的文件格式')
  } else {
    message.error('PPT加载失败，请重试')
  }
}
</script>
```

### 3. 键盘快捷键

```vue
<script setup>
const handleKeydown = (event) => {
  if (!visible.value) return
  
  switch (event.key) {
    case 'ArrowLeft':
      previousPage()
      break
    case 'ArrowRight':
      nextPage()
      break
    case 'Escape':
      closePreview()
      break
    case 'f':
    case 'F':
      toggleFullscreen()
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
```

## 注意事项

1. **文件格式**: 支持 PPT、PPTX 格式，需要服务端转换支持
2. **文件大小**: 大文件可能加载较慢，建议显示加载进度
3. **浏览器兼容**: 某些老版本浏览器可能不支持预览功能
4. **网络依赖**: 需要稳定的网络连接来加载PPT内容

## 更新日志

### v1.0.0
- 初始版本，支持基本的PPT预览功能
- 支持页面导航、缩放、全屏等功能
- 集成键盘快捷键和错误处理
