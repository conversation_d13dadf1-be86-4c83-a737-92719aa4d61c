import request from '@/utils/request'
import { apiSchoolDomain } from '@/utils/apiUrl'

export function createOptionLabel(index: number) {
  if (index < 0) {
    return ''
  }
  return String.fromCharCode(65 + index)
}

export interface Answer {
  userName: string
  userId: string
  isCorrect: boolean
  answers: string[]
}
export const getHomeworkUserAnsersApi = async (
  homeworkId: string,
  problemId: string,
) => {
  return request.get<{ userAnswers: Answer[] }>(
    `${apiSchoolDomain}/llearn-record/user-problems/user-problem-answers`,
    {
      params: {
        homeworkId,
        problemId,
      },
    },
  )
}

// 获取oss一次性上传token
export const getOSSToken = () => {
  return request.post<{ token: string }>(`${apiSchoolDomain}/oss/yc-oss/token`)
}

/**
 * 创建截图
 */
export const getClassSlideScreenshot = (
  id: string,
  key: string,
  domain: string,
) => {
  return request.post(
    `${apiSchoolDomain}/teacher-courseware/coursewares/screenshot`,
    { sourceId: id, key, domain },
  )
}

export const base64ToFile = (base64Data: any) => {
  // 构造返回file对象
  const uint8Array = base64ToUint8Array(base64Data)
  // 构造Blob对象
  const blob = new Blob([uint8Array], { type: 'image/png' })
  // 构造File对象
  const file = new File([blob], `screenshot-${new Date().getTime()}.png`, {
    type: 'image/png',
  })
  return file
}

/**
 * @description: 将base64数据转换为Uint8Array
 * @param {*} base64Data
 * @return {*}
 */
const base64ToUint8Array = (base64: any) => {
  /*
    atob函数用于将base64编码的数据转换为二进制（即原始）数据，并返回字符串，在浏览器环境中，atob函数可直接使用，无需使用 Buffer.from 函数转换
    const binaryStr = Buffer.from(base64, "base64").toString("binary");
  */
  const binaryStr = atob(base64.replace(/^data:image\/\w+;base64,/, ''))
  const uint8Array = new Uint8Array(binaryStr.length)
  for (let i = 0; i < binaryStr.length; i++) {
    uint8Array[i] = binaryStr.charCodeAt(i)
  }
  return uint8Array
}
