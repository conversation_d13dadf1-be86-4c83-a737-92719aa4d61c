<template>
  <div v-if="visible" ref="fullscreenRef" class="fullscreen-main">
    <div ref="fullScreenScrollRef" class="full-screen-scroll-box">
      <canvas
        id="canvas"
        class="canvas"
        :style="{ zIndex: onDrawing ? 101 : 99 }"
      />
      <n-row>
        <n-col :span="4">
          <div>
            <n-anchor
              affix
              position="fixed"
              listen-to=".fullscreenMain"
              :top="160"
              style="z-index: 1"
              ignore-gap
              :bound="24"
            >
              <p class="anchorTitle">题目导航</p>
              <div
                v-for="(anchor, index) in anchorList"
                :key="index"
                class="anchorItem"
              >
                <div v-if="anchor.outTitle" class="outTitle">
                  {{ anchor.outTitle }}
                </div>
                <div v-if="anchor.outText" class="outText">
                  {{ anchor.outText }}
                </div>
                <n-anchor-link
                  :href="`#classModel-${index}`"
                  :title="anchor.title"
                >
                  <div v-if="anchor.text">
                    {{ anchor.text }}
                  </div>
                </n-anchor-link>
              </div>
            </n-anchor>
          </div>
        </n-col>
        <n-col span="20">
          <div class="problem-box">
            <ProblemList
              v-for="(problem, index) in problems"
              :id="`classModel-${index}`"
              :key="index"
              :homework-id="homeworkId"
              :cur-problem="problem as any"
              :problem-id="problem.problemId || problem.id"
              :problem-no="index"
              :cur-img-width="curImgWidth"
              :style="{ fontSize: curFontSize + 'px' }"
            />
          </div>
        </n-col>
      </n-row>
      <ClassRoomBar
        :cur-img-width="curImgWidth"
        :cur-font-size="curFontSize"
        :viewport-ref="fullscreenRef"
        :source-id="homeworkId"
        @set-img-width="setImgWidth"
        @set-font-size="setFontSize"
        @close="quitFullScreen"
        @canvas-draw="canvasDraw"
        @undo-stroke="handleUndoStroke"
        @clear-canvas="handleClearCanvas"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import ProblemList from './problemList.vue'
import ClassRoomBar from './classRoomBar.vue'
import { useFullscreen } from '@vueuse/core'
import {
  addCanvasListener,
  removeCanvasListener,
  undoStroke,
  clearCanvas,
  redrawLines,
  resetLines,
} from './canvasDraw.util'
const props = defineProps<{
  visible: boolean
  homeworkId: string
  anchorList: any[]
  problems: any[]
}>()
const emits = defineEmits<(e: 'update:visible', val: boolean) => void>()
const fullscreenRef = ref<HTMLElement>()
const fullScreenScrollRef = ref<HTMLElement>()
const { isFullscreen, enter, exit } = useFullscreen(fullscreenRef)
const onDrawing = ref(false)
const curFontSize = ref(20)
const curImgWidth = ref(50)
const setFontSize = (val: any) => {
  curFontSize.value = val
}
const setImgWidth = (val: any) => {
  curImgWidth.value = val
}
onMounted(() => {
  resetLines()
})
watch(
  () => props.visible,
  (val) => {
    if (val) {
      nextTick(() => {
        enter()
        fullscreenRef.value?.scrollTo(0, 0)
      })
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => isFullscreen.value,
  (val) => {
    emits('update:visible', val)
  },
)

const quitFullScreen = () => {
  exit()
}

const resetCanvas = () => {
  const width = fullScreenScrollRef.value?.offsetWidth as number
  const height = fullScreenScrollRef.value?.offsetHeight as number
  const canvas = document.getElementById('canvas')
  canvas?.setAttribute('width', String(width))
  canvas?.setAttribute('height', String(height))
  redrawLines(canvas)
}

const canvasDraw = (drawing: boolean) => {
  resetCanvas()
  onDrawing.value = drawing
  const canvas = document.getElementById('canvas')
  if (drawing) {
    addCanvasListener(canvas)
  } else {
    removeCanvasListener(canvas)
  }
}

const handleUndoStroke = () => {
  const canvas = document.getElementById('canvas')
  undoStroke(canvas)
}

const handleClearCanvas = () => {
  const canvas = document.getElementById('canvas')
  clearCanvas(canvas)
}
</script>

<style lang="scss" scoped>
.fullscreen-main {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  background-color: white;

  .full-screen-scroll-box {
    position: relative;
    height: 100vh;
    padding: 40px;

    .canvas {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }

    .problem-box {
      padding-bottom: 60px;
    }
  }

  .anchorTitle {
    position: relative;
    padding-bottom: 10px;
    padding-left: 14px;
    font-size: 24px;
    font-weight: bold;
    color: #2c2c2c;
  }

  .anchorItem {
    padding: 5px 0;
  }

  .outTitle {
    padding-left: 16px;
    margin: 5px 0;
    font-size: 14px;
    font-weight: 700;
  }

  .outText {
    padding-left: 16px;
    margin-bottom: 10px;
    font-size: 14px;
  }
}
</style>
