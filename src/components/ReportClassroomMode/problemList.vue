<template>
  <ProblemRender
    class="report-problem-item"
    :problem-no="problemNo + 1"
    :current-problem="curProblem"
    :explains="isShowExplain"
    :analyze="isShowExplain"
  >
    <template #difficultyHeader>
      <div class="right-box">
        题目正确率：
        <span :class="[passRate <= 50 ? 'red' : 'bule']">{{ passRate }}%</span>
      </div>
    </template>
    <div class="report-option-list">
      <div class="option-item" @click="handleClickReport">
        {{ isShowReport ? '收起答案详情' : '学生答案详情' }}
        <img
          :class="{ rotate: isShowReport }"
          src="https://ps-static.yangcong345.com/arrow_down-94dbc423681272786f850971303e81fc.svg"
          alt=""
        />
      </div>
      <div class="option-item" @click="handleClickExplain">
        {{ isShowExplain ? '收起解析' : '展开解析' }}
        <img
          :class="{ rotate: isShowExplain }"
          src="https://ps-static.yangcong345.com/arrow_down-94dbc423681272786f850971303e81fc.svg"
          alt=""
        />
      </div>
    </div>
    <div v-if="isShowReport" class="report-list-wrap">
      <Loading v-if="loading" style="padding-top: 40px" :loading="loading" />
      <div v-else>
        <div
          v-if="!anserLen"
          :style="{ textAlign: 'center', paddingTop: '24px' }"
        >
          暂无学生完成
        </div>
        <div v-else>
          <div
            v-for="(item, index) in userGroups"
            :key="index"
            class="report-item"
          >
            <div v-if="item.list.length || curProblem.type !== 'single_choice'">
              <div class="report-item-anwser">
                <div class="anwserTitle">
                  <CheckOne
                    v-if="item.correct"
                    theme="multi-color"
                    size="20"
                    :fill="['#36B769', '#36B769', '#FFF', '#36B769']"
                    :stroke-width="2"
                    :style="{ marginRight: '8px' }"
                  />
                  <CloseOne
                    v-if="!item.correct"
                    theme="multi-color"
                    size="20"
                    :fill="['#F68162', '#F68162', '#FFF', '#F68162']"
                    :stroke-width="2"
                    :style="{ marginRight: '8px' }"
                  />
                  <span>{{ item.text }} ({{ item.list.length }})人</span>
                </div>
                <div class="userListBox">
                  <div v-for="user in item.list" :key="user" class="item">
                    {{ user }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ProblemRender>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ProblemRender } from '@guanghe-pub/onion-problem-render'
import { getHomeworkUserAnsersApi, createOptionLabel } from './service'
import { CheckOne, CloseOne } from '@icon-park/vue-next'

interface Group {
  correct: boolean
  text: string
  list: string[]
}

const props = defineProps<{
  curProblem: YcType.ProblemType
  homeworkId: string
  problemId: string
  problemNo: number
  curImgWidth: number
}>()
const emits = defineEmits<{
  (e: 'reportShow'): void
  (e: 'explanShow'): void
}>()
const isShowReport = ref(false)
const isShowExplain = ref(false)
const loading = ref(false)
const userGroups = ref<Group[] | null>(null)

const handleClickReport = () => {
  isShowExplain.value = false
  isShowReport.value = !isShowReport.value
  emits('reportShow')
}

const handleClickExplain = () => {
  isShowReport.value = false
  isShowExplain.value = !isShowExplain.value
  emits('explanShow')
}

const anserLen = computed(() => {
  return userGroups.value?.reduce((count, el) => {
    return count + el.list.length
  }, 0)
})

const passRate = computed(() => {
  return props.curProblem?.passRate || 0
})

const initReport = async () => {
  loading.value = true
  try {
    const { userAnswers } = await getHomeworkUserAnsersApi(
      props.homeworkId,
      props.problemId,
    )
    if (userAnswers) {
      if (props.curProblem.type === 'single_choice') {
        userGroups.value = props.curProblem.choices[0].map((choice, index) => {
          return {
            correct: choice.correct,
            text: `${
              choice.correct ? '正确答案' : '错误答案'
            }:${createOptionLabel(index)}`,
            list: userAnswers
              .filter((el) => el.answers[0] === choice.body)
              .map((el) => el.userName),
          }
        })
      } else {
        userGroups.value = [
          {
            correct: false,
            text: '错误答案',
            list: userAnswers
              .filter((el) => !el.isCorrect)
              .map((el) => el.userName),
          },
          {
            correct: true,
            text: '正确答案',
            list: userAnswers
              .filter((el) => el.isCorrect)
              .map((el) => el.userName),
          },
        ]
      }
    }
  } finally {
    loading.value = false
  }
}

watch(
  () => isShowReport.value,
  (val) => {
    if (val && !userGroups.value) {
      initReport()
    }
  },
)
watch(
  () => props.problemId,
  (val) => {
    userGroups.value = null
    if (isShowReport.value) {
      initReport()
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => props.curImgWidth,
  (val) => {
    const imgElements = document.querySelectorAll('.report-problem-item img')
    imgElements.forEach((img: any) => {
      img.style.width = val + '%'
      img.style.maxWidth = val + '%'
    })
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
.report-problem-item {
  padding-top: 20px;

  ::v-deep(.onion-problem-render__option) {
    display: flex;
    flex-wrap: wrap;
  }

  ::v-deep(.onion-problem-render__option--item) {
    width: 50%;
    border: none;
  }

  ::v-deep(.onion-problem-render__main) {
    font-size: 1em;
  }

  ::v-deep(.onion-problem-render__examBox-show) {
    display: none;
  }

  ::v-deep(.onion-problem-render__main img) {
    max-height: 200px;
  }

  ::v-deep(
      .onion-problem-render__examBox-show + .onion-problem-render__option
    ) {
    display: none;
  }

  .right-box {
    display: inline-flex;
    justify-content: flex-end;
    width: calc(100% - 8em);
    font-weight: 600;
    color: #343434;

    .red {
      color: #f54242;
    }

    .bule {
      color: #356cfe;
    }
  }

  .report-option-list {
    position: relative;
    z-index: 100;
    display: flex;
    justify-content: flex-end;

    .option-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      min-width: 80px;
      margin-right: 10px;
      font-size: 0.9em;
      font-weight: 700;
      color: #b3b3b3;
      white-space: nowrap;
      cursor: pointer;

      &:hover {
        color: #6e6e6e;
      }

      img {
        width: 8px;
        height: 5px;
        margin-top: 0;
        margin-left: 3px;
        transition: transform 0.2s;
        transform: rotate(0deg);

        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
  }

  .report-list-wrap {
    min-height: 40px;
    padding: 0 24px 24px;
    margin-top: 10px;
    background: #f9f9f9;

    .report-item-anwser {
      width: 100%;
      padding-top: 24px;
      font-size: inherit;

      .anwserTitle {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-size: inherit;
        font-weight: 500;
        color: #353638;
      }

      .userListBox {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        grid-row-gap: 20px;

        .item {
          font-size: inherit;
          font-weight: 400;
          color: #505153;
        }
      }
    }
  }
}
</style>
