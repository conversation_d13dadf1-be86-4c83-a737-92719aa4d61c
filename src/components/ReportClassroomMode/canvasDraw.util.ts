// @ts-nocheck
let lines = [] // 保存绘制的线条数据
let currentLine = [] // 保存当前绘制的线条
let isDrawing = false
let lastX = 0
let lastY = 0
export let brush = false
export let wipe = false
const strokeColor = '#F03D3D'
const strokeWeights = 10

let ctx

export const changeState = (type: 'brush' | 'wipe' | 'mouse') => {
  if (type === 'mouse') {
    brush = false
    wipe = false
  }
  if (type === 'brush') {
    brush = true
    wipe = false
  }
  if (type === 'wipe') {
    brush = false
    wipe = true
  }
}
export const addCanvasListener = (canvas) => {
  if (!ctx) {
    ctx = canvas.getContext('2d')
  }
  canvas.addEventListener('mousedown', startDrawing)
  canvas.addEventListener('mousemove', draw)
  canvas.addEventListener('mouseup', stopDrawing)
  canvas.addEventListener('mouseout', stopDrawing)

  canvas.addEventListener('touchstart', startDrawing)
  canvas.addEventListener('touchmove', draw)
  canvas.addEventListener('touchend', stopDrawing)
  // redrawLines()
  // isDrawing = false
}

export const removeCanvasListener = (canvas) => {
  canvas.removeEventListener('mousedown', startDrawing)
  canvas.removeEventListener('mousemove', draw)
  canvas.removeEventListener('mouseup', stopDrawing)
  canvas.removeEventListener('mouseout', stopDrawing)

  canvas.removeEventListener('touchstart', startDrawing)
  canvas.removeEventListener('touchmove', draw)
  canvas.removeEventListener('touchend', stopDrawing)
  // redrawLines()
  // isDrawing = false
}

function startDrawing(event, isRePaint = false) {
  isDrawing = true
  lastX = event.offsetX
  lastY = event.offsetY
  ctx.beginPath()
  ctx.fillStyle = strokeColor
  ctx.strokeStyle = strokeColor
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  if (brush) {
    ctx.arc(lastX, lastY, strokeWeights / 2, 0, 2 * Math.PI)
    ctx.fill()
  }
  ctx.closePath()
  if (brush && !isRePaint) {
    currentLine.push({ x: lastX, y: lastY, type: 'brush' })
  }
}

function draw(event) {
  event.preventDefault()
  if (!isDrawing) return
  let offsetX
  let offsetY
  if (event.touches) {
    const canvas = document.getElementById('canvas')
    const scrollTop = canvas.getBoundingClientRect().top
    // console.log('touch', scrollTop)
    offsetX = event.touches[0].pageX
    offsetY = event.touches[0].pageY - scrollTop
  } else {
    offsetX = event.offsetX
    offsetY = event.offsetY
  }
  if (brush) {
    ctx.lineWidth = strokeWeights
    ctx.strokeStyle = strokeColor
    ctx.globalCompositeOperation = 'source-over' // 在源图像（新的）上方显示目标（已有）图像。
    ctx.beginPath()
    ctx.moveTo(lastX, lastY)
    ctx.lineTo(offsetX, offsetY)
    ctx.stroke()
    ctx.arc(lastX, lastY, strokeWeights / 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.closePath()
    lastX = offsetX
    lastY = offsetY
    // 将当前线条的每一步坐标保存到数组中
    currentLine.push({ x: offsetX, y: offsetY, type: 'brush' })
  }
  if (wipe) {
    ctx.save()
    ctx.globalCompositeOperation = 'destination-out'
    ctx.lineWidth = 32
    ctx.beginPath()
    ctx.moveTo(lastX, lastY)
    ctx.lineTo(offsetX, offsetY)
    ctx.stroke()
    ctx.arc(lastX, lastY, strokeWeights / 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.closePath()
    lastX = offsetX
    lastY = offsetY
    currentLine.push({ x: offsetX, y: offsetY, type: 'wipe' })
  }
}
function stopDrawing() {
  // console.log('stopDrawing')
  isDrawing = false
  // 将当前线条保存到线条数组中
  if (currentLine.length > 0) {
    lines.push(currentLine)
  }
  // 清空当前线条数据
  currentLine = []
}

export function undoStroke(canvas) {
  if (!ctx) {
    ctx = canvas.getContext('2d')
  }
  if (lines.length === 0) return

  // 移除最后一步绘制的线条
  lines.pop()

  // 清空Canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 重新绘制剩余的线条
  redrawLines()
}

export function redrawLines(canvas?) {
  if (ctx) {
    ctx = canvas.getContext('2d')
  }
  if (lines.length === 0) return
  lines.forEach((line) => {
    if (line[0].type === 'brush') {
      ctx.lineWidth = strokeWeights
      ctx.strokeStyle = strokeColor
      ctx.globalCompositeOperation = 'source-over'
      startDrawing({ offsetX: line[0].x, offsetY: line[0].y }, true)
      ctx.beginPath()
      ctx.moveTo(line[0].x, line[0].y)
      for (let i = 1; i < line.length; i++) {
        ctx.lineTo(line[i].x, line[i].y)
      }
      ctx.stroke()
    }
    if (line[0].type === 'wipe') {
      ctx.lineWidth = 32
      ctx.globalCompositeOperation = 'destination-out'
      ctx.beginPath()
      ctx.moveTo(line[0].x, line[0].y)
      for (let i = 1; i < line.length; i++) {
        ctx.lineTo(line[i].x, line[i].y)
      }
      ctx.stroke()
    }
  })
  stopDrawing()
}

export function clearCanvas(canvas) {
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  ctx = null
  resetLines()
}

export function resetLines() {
  // 清空线条数据
  lines = []
}
