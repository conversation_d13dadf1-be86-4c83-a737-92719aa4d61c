<template>
  <div class="fullScreenBar" :style="{ opacity: showTouchBar ? 1 : 0 }">
    <div class="canvasDraw">
      <div
        :class="['icon', 'brush', brushState ? 'active' : '']"
        @click="clickDraw"
      />
      <div
        :class="['icon', 'wipe', wipeState ? 'active' : '']"
        @click="clickWipe"
      />
      <div :class="['icon', 'undo']" @click="undoStroke" />
      <div :class="['icon', 'clear']" @click="clearCanvas" />
      <div
        :class="[
          'icon',
          'shoot',
          screenshotSrc ? 'screenshotNot' : 'screenshot',
        ]"
        @click="screenshot"
      >
        <div v-if="screenshotSrc" class="thumbnail" @click="stopPropagation">
          <div class="closure" @click="thumbnailClosure" />
          <div class="imgItem">
            <img :src="screenshotSrc" alt="缩略图" />
          </div>
          <p>已生成截屏，课后可在“练习-上课截屏”中查看</p>
        </div>
      </div>
    </div>
    <div class="fontControl">
      <em>图片: {{ curImgWidth }}%</em>
      <img
        :class="{ imgIcon: true, isDisable: curImgWidth === minImgWidth }"
        src="https://fp.yangcong345.com/onion-extension/<EMAIL>"
        alt="icon"
        @click="handleImgWidth(curImgWidth - imgStep)"
      />
      <n-slider
        class="fontSlider"
        :value="curImgWidth"
        :min="minImgWidth"
        :max="maxImgWidth"
        :step="imgStep"
        @update:value="handleImgWidth"
      />
      <img
        :class="{ imgIcon: true, isDisable: curImgWidth === maxImgWidth }"
        src="https://fp.yangcong345.com/onion-extension/<EMAIL>"
        alt="icon"
        @click="handleImgWidth(curImgWidth + imgStep)"
      />
    </div>
    <div class="fontControl marginLeft">
      <em>字号: {{ curFontSize }}px</em>
      <img
        :class="{ imgIcon: true, isDisable: curFontSize === minFontSize }"
        src="https://fp.yangcong345.com/onion-extension/<EMAIL>"
        alt="icon"
        @click="handleFontSize(curFontSize - fontStep)"
      />
      <n-slider
        class="fontSlider"
        :value="curFontSize"
        :min="minFontSize"
        :max="maxFontSize"
        :step="fontStep"
        @update:value="handleFontSize"
      />
      <img
        :class="{ imgIcon: true, isDisable: curFontSize === maxFontSize }"
        src="https://fp.yangcong345.com/onion-extension/<EMAIL>"
        alt="icon"
        @click="handleFontSize(curFontSize + fontStep)"
      />
    </div>
    <div class="quit" @click="handleClose">退出课堂模式</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { isStage, isProd } from '@/utils/apiUrl'
import { changeState } from './canvasDraw.util'
import html2canvas from 'html2canvas'
import ycUpload from '@guanghe-pub/yc-upload'
import { base64ToFile, getOSSToken, getClassSlideScreenshot } from './service'

const props = defineProps<{
  curImgWidth: number
  curFontSize: number
  viewportRef: any
  sourceId: string
}>()
const emits = defineEmits<{
  (e: 'close'): void
  (e: 'undoStroke'): void
  (e: 'clearCanvas'): void
  (e: 'setFontSize', val: number): void
  (e: 'setImgWidth', val: number): void
  (e: 'canvasDraw', val: boolean): void
}>()
const { onFileInputChange } = ycUpload
const minFontSize = ref<any>(20)
const maxFontSize = ref<any>(56)
const fontStep = ref<any>(4)
const minImgWidth = ref<any>(10)
const maxImgWidth = ref<any>(100)
const imgStep = ref<any>(10)
const screenshotSrc = ref<any>('')
const showTouchBar = ref(true)
const brushState = ref(false)
const wipeState = ref(false)
const handleClose = () => {
  emits('close')
}
const handleFontSize = (val: number) => {
  if (minFontSize.value <= val && maxFontSize.value >= val) {
    emits('setFontSize', val)
  }
}
const handleImgWidth = (val: number) => {
  if (minImgWidth.value <= val && maxImgWidth.value >= val) {
    emits('setImgWidth', val)
  }
}
const clickWipe = () => {
  const temp = !wipeState.value
  changeState(temp ? 'wipe' : 'mouse')
  wipeState.value = temp
  brushState.value = false
  emits('canvasDraw', temp)
}
const clickDraw = () => {
  const temp = !brushState.value
  changeState(temp ? 'brush' : 'mouse')
  brushState.value = temp
  wipeState.value = false
  emits('canvasDraw', temp)
}
const undoStroke = () => {
  emits('undoStroke')
}
const clearCanvas = () => {
  emits('clearCanvas')
}
const thumbnailClosure = (e: any) => {
  e.stopPropagation()
  screenshotSrc.value = ''
}

const stopPropagation = (e: any) => {
  e.stopPropagation()
}
const fileUploadEnd = async (response: any, file: any, files: any) => {
  await getClassSlideScreenshot(
    props.sourceId,
    response.data.key,
    response.data.domain,
  )
}
const uploadPicturesToOss = async (e: any) => {
  const env = isProd ? 'prod' : isStage ? 'stage' : 'test'
  const getToken = async () => {
    const { token } = await getOSSToken()
    return token
  }
  try {
    onFileInputChange(e, {
      // 当前运行环境，值为：test | stage | prod，string类型，必填项
      env,
      // 上传文件前用来获取一次性上传token的方法，function类型，必填项
      getToken,
      webp: {
        webp: false,
      },
      filePath: 'b/classModeScreenShot', // 上传的文件夹路径，格式不能包含空格，不能以 '/'开头
      // 单个文件上传成功后的回调钩子
      fileUploadEndHandler: fileUploadEnd,
    })
  } catch (e) {
    // console.log(e)
  }
}
const screenshot = () => {
  if (screenshotSrc.value) return
  showTouchBar.value = false
  setTimeout(() => {
    html2canvas(props.viewportRef, {
      backgroundColor: null,
      useCORS: true,
    }).then((canvas) => {
      showTouchBar.value = true
      const dataImg = canvas.toDataURL('image/png')
      screenshotSrc.value = dataImg
      const file = base64ToFile(dataImg)
      const target = { type: 'file', files: [file] }
      uploadPicturesToOss({ target })
    })
  }, 100)
}
</script>

<style lang="scss" scoped>
.fullScreenBar {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 102;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

  .quit {
    display: inline-block;
    height: 48px;
    padding: 0 24px;
    margin-left: 12px;
    font-size: 18px;
    line-height: 48px;
    color: #f15c33;
    text-align: center;
    cursor: pointer;
    background-color: white;
    border: 1px solid #dddede;
    border-radius: 4px;

    &:hover {
      color: #244dd9;
    }
  }
}

.canvasDraw {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  margin-right: 16px;
  background: #ffffff;
  border: 1px solid #dddede;
  border-radius: 4px;

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;

    &:hover {
      background-color: #f6f6f6;
    }
  }

  .brush {
    background: url('https://fp.yangcong345.com/onion-extension/wqwq-70e6a11af6a8b16a0aaa547ef3558636.png')
      no-repeat;
    background-size: contain;

    &.active {
      background-color: #f6f6f6;
    }
  }

  .wipe {
    background: url('https://fp.yangcong345.com/onion-extension/cdscs-58e132281e393e97e903ff2ad119484d.png')
      no-repeat;
    background-size: contain;

    &.active {
      background-color: #f6f6f6;
    }
  }

  .undo {
    background: url('https://fp.yangcong345.com/onion-extension/vfd-4d76c19f96a99ee316d5f042207758c2.png')
      no-repeat;
    background-size: contain;
  }

  .clear {
    background: url('https://fp.yangcong345.com/onion-extension/cds-8bddd7dabce9be4e0a4b956487d3686e.png')
      no-repeat;
    background-size: contain;
  }

  .shoot {
    background: url('https://fp.yangcong345.com/onion-extension/bgd-0311ffa176539117e081742caa259f90.png')
      no-repeat;
    background-size: contain;
  }

  .screenshotNot {
    position: relative;
    background: url('https://fp.yangcong345.com/onion-extension/444-ab8f3fa66f65bca909a0b5af68b5b3b3.png')
      no-repeat;
    background-size: 100%;

    .thumbnail {
      position: absolute;
      top: -300px;
      box-sizing: border-box;
      width: 387px;
      height: 292px;
      padding: 30px 16px;
      background: url('https://fp.yangcong345.com/onion-extension/编组 5-b7179b012c348b3029f270160e856aec.png')
        no-repeat;
      background-size: 100% 100%;

      .closure {
        position: absolute;
        top: 10px;
        right: 27px;
        z-index: 10;
        width: 20px;
        height: 20px;
        cursor: pointer;
        background: url('https://fp.yangcong345.com/onion-extension/333-21c17d99af5f9cae77cf9d059da5e4e8.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .imgItem {
        width: 336px;
        height: 210px;
        margin: 0 auto;

        img {
          display: inline-block;
          width: 100%;
          height: 100%;
        }
      }

      p {
        margin: 10px 0 0;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        color: #ffffff;
      }
    }
  }
}

.fontControl {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 10px 16px;
  background: #ffffff;
  border: 1px solid #dddede;
  border-radius: 4px;

  &.marginLeft {
    margin-left: 10px;
  }

  em {
    display: inline-block;
    margin-right: 15px;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    color: #161b22;
  }

  .imgIcon {
    display: inline-block;
    width: 22px;
    height: 22px;
    margin-right: 8px;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }
  }

  .isDisable {
    color: #f3f3f3;
  }

  .fontSlider {
    position: relative;
    top: -2px;
    width: 120px;
  }
}
</style>
