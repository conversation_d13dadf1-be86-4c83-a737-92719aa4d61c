<template>
  <div>
    <ProblemRender
      :problemNo="index"
      :difficultyHeaderShow="false"
      :currentProblem="problem"
      mode="government"
      class="problem-render-government py-24px"
    >
      <template #header>
        <slot name="header"></slot>

        <span v-if="!tagConfig?.isHideBaseTags">
          <span
            v-if="problem.pool && poolText"
            class="problem-pool tag-base mr-8px"
            :class="
              problem.pool === 'goal'
                ? 'border-1px border-#5E80FF! color-#5E80FF!'
                : ''
            "
          >
            {{ poolText }}
          </span>
          <span
            v-if="problem.variantType && variantTypeText"
            class="type-text tag-base mr-8px"
            :class="
              problem.variantType && problem.variantType !== 'target'
                ? 'border-#FEA34599 color-#FEA345'
                : ''
            "
          >
            {{ variantTypeText }}
          </span>
          <span
            v-if="!tagConfig?.isHideProblemType && problemTypeText"
            class="type-text tag-base mr-8px"
          >
            {{ problemTypeText }}
          </span>
          <span class="difficulty-text tag-base">
            {{ difficultyText }}
          </span>
          <span
            v-if="tagConfig?.isShowCreatedName && problem.createdName"
            class="text-nowrap px-6px py-2px text-12px bg-#F7F7F9 rounded-4px ml-8px"
          >
            <span style="max-width: 95px" :title="problem.createdName">
              分享人：{{
                problem.createdName.length > 3
                  ? problem.createdName.substring(0, 3) + '...'
                  : problem.createdName
              }}
            </span>
            <span
              v-if="userId && problem.createdBy === userId"
              class="color-#FEA345"
            >
              (自己上传)
            </span>
          </span>
        </span>

        <slot name="topTags"></slot>
      </template>
      <div class="flex items-center mt-10px justify-between">
        <div class="tag-business">
          <span
            v-if="!tagConfig?.isHideUsageCount && problem.usageCount"
            class="bg-#F7F7F9"
          >
            使用{{ problem.usageCount }}次
          </span>
          <span
            v-if="!tagConfig?.isHideArrangementNum && problem.arrangementNum"
            class="bg-#F7F7F9"
          >
            布置次数：{{ problem.arrangementNum }}次
          </span>
          <span
            v-if="!tagConfig?.isHideAnswerNum && problem.answerNum"
            class="color-#393548 bg-#F7F7F9"
          >
            {{ problem.answerNum }}次作答
          </span>
          <span
            v-if="
              !tagConfig?.isHideAccuracy &&
              (problem.accuracy || problem.accuracy === 0)
            "
            :class="[
              problem.accuracy > 80
                ? 'color-#4ECC5E bg-#4ECC5E/15'
                : problem.accuracy > 40
                  ? 'color-#FEA345 bg-#FEA345/15'
                  : 'bg-#fa5a65/15 color-#FA5A65',
            ]"
          >
            正确率{{ problem.accuracy }}%
          </span>
          <span
            v-if="!tagConfig?.isHideCorrectRate && problem.correctRate"
            :class="[
              problem.correctRate > 80
                ? 'color-#4ECC5E bg-#4ECC5E/15'
                : problem.correctRate > 40
                  ? 'color-#FEA345 bg-#FEA345/15'
                  : 'bg-#fa5a65/15 color-#FA5A65',
            ]"
          >
            得分率{{ problem.correctRate }}%
          </span>
          <span
            v-if="!tagConfig?.isHideIsUsed && problem.isUsed"
            class="color-#5CA8F8"
            style="background: rgba(92, 168, 248, 0.15)"
          >
            近期已布置
          </span>

          <slot name="bottomTags"></slot>
        </div>
        <div class="flex items-center">
          <slot name="insertBefore"></slot>

          <n-popover
            v-if="showSchoolAiExplainButton"
            trigger="hover"
            arrow-class="paper-preferences-arrow-popover"
            content-class="paper-preferences-content-popover"
            class="paper-preferences-wrapper-popover"
            placement="top"
            :to="false"
            raw
          >
            <template #trigger>
              <span
                class="problem-explain problem-operation flex items-center ai-btn mr-8px"
                @click="toggleSchoolAIExplain"
              >
                <AiIcon class="w-16px h-16px mr-4px" />
                AI讲解
                <ArrowIcon
                  class="ml-2px w-10px h-10px"
                  :class="showSchoolAIExplain ? '' : 'rotate-180'"
                />
              </span>
            </template>
            <span>讲解由DeepSeek-R1深度推理模型生成</span>
          </n-popover>

          <span
            v-if="!isHideExplain"
            class="problem-explain problem-operation flex items-center cursor-pointer border border-solid border-#505153"
            @click="toggleExplain"
          >
            解析
            <ArrowIcon
              class="ml-2px w-10px h-10px"
              :class="showExplain ? '' : 'rotate-180'"
            />
          </span>

          <span
            v-if="
              !isHideFavorite &&
              (problem.favoriteCount || problem.favoriteCount === 0)
            "
            class="problem-operation ml-8px flex items-center cursor-pointer"
            @click="emits('onFavorite')"
          >
            <StarIcon
              v-if="isFavorite"
              class="w-16px h-16px cursor-pointer"
              color="#FFD633"
            />
            <StarBlankIcon
              v-else
              class="w-16px h-16px cursor-pointer"
              color="#FFD633"
            />
            <span class="ml-2px">收藏({{ problem.favoriteCount }})</span>
          </span>

          <ClassModeBtn
            v-if="!isHideClassMode"
            :problem="problem"
            :problem-no="index"
            :from="from"
          />

          <span v-if="booksIds">
            <span
              v-if="booksIds && booksIds.includes(problem.id)"
              class="problem-operation cursor-pointer ml-8px flex items-center color-#FA5A65"
              @click="emits('onAddToBooks')"
            >
              <DeleteIcon class="w-16px h-16px mr-4px" color="#FA5A65" />
              移出
            </span>
            <span
              v-else
              class="problem-operation cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
              @click="emits('onAddToBooks')"
            >
              <AddIcon class="w-16px h-16px mr-4px" color="#ffffff" />
              加入
            </span>
          </span>

          <span
            v-if="showInsertButton"
            class="min-w-74px py-4px rounded-8px justify-center select-none cursor-pointer ml-8px flex items-center bg-#5E80FF !border-[#5E80FF] color-#fff"
            @click="emits('onInsert')"
          >
            <AddIcon
              class="w-16px h-16px"
              :class="{ 'mr-4px': !insertCount }"
              color="#ffffff"
            />
            插入
            <span v-if="insertCount"> ({{ insertCount }}) </span>
          </span>

          <slot name="insert"></slot>
        </div>
      </div>
    </ProblemRender>

    <div v-if="showSchoolAIExplain" class="mb-24px">
      <div class="rounded-12px bg-#F7F7F9 p-16px text-14px!">
        <div class="flex justify-between mb-12px items-start">
          <span class="color-#9792AC">
            以下内容由DeepSeek-R1生成，或许不够准确，请以正确答案为准
          </span>
          <div class="flex items-center gap-16px">
            <n-popover
              v-if="schoolAIExplain?.canRegen"
              trigger="hover"
              arrow-class="paper-preferences-arrow-popover"
              content-class="paper-preferences-content-popover"
              class="paper-preferences-wrapper-popover"
              placement="top"
              raw
            >
              <template #trigger>
                <span
                  class="flex items-center cursor-pointer whitespace-nowrap ml-8px"
                  :class="
                    schoolAIExplain?.aiExplainProgress === 'ing'
                      ? 'color-#9792AC cursor-not-allowed'
                      : 'color-#5E80FF'
                  "
                  @click="regenerateAiExplain"
                >
                  <RedoIcon class="w-16px h-16px mr-4px" />
                  重新生成
                </span>
              </template>
              <span>新生成的讲解将覆盖原讲解</span>
            </n-popover>
          </div>
        </div>
        <ThinkModelRender
          v-if="schoolAIExplain?.aiExplainProgress === 'done'"
          :content="schoolAIExplain?.aiExplain"
          :thinkTime="schoolAIExplain?.latency / 1000"
        />
        <div
          v-else-if="schoolAIExplain?.aiExplainProgress === 'ing'"
          class="flex justify-center items-center h-full"
        >
          <span class="content-ing-icon" />
          <span class="content-ing-text"> 讲解生成中... </span>
        </div>
        <div
          v-else-if="
            ['failed', 'pending'].includes(
              schoolAIExplain?.aiExplainProgress || '',
            )
          "
          class="flex justify-center items-center h-full"
        >
          <span>
            {{ getSchoolAIExplainTip }}
          </span>
        </div>
      </div>
    </div>

    <div v-if="showExplain" class="mb-24px">
      <div class="rounded-12px bg-#F7F7F9 p-16px">
        <div v-if="problem.type && problem.type === 'combination'">
          <div
            v-for="(subproblem, subproblemIndex) in problem.subproblems"
            :key="subproblem.id"
            class="mb-16px border-b border-solid border-#C5C1D4 pb-16px"
          >
            <div class="mb-8px">第{{ subproblemIndex + 1 }}题:</div>
            <CorrectAnswer
              :style="{ fontSize: '14px' }"
              :currentProblem="subproblem"
              mode="government"
            />
            <AnalysisExplain
              :style="{ fontSize: '14px' }"
              :currentProblem="subproblem"
              mode="government"
            />
          </div>
        </div>
        <div v-else>
          <CorrectAnswer
            :style="{ fontSize: '14px' }"
            :currentProblem="problem"
            mode="government"
          />
          <AnalysisExplain
            :style="{ fontSize: '14px' }"
            :currentProblem="problem"
            mode="government"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ProblemRender,
  ExamProblemTypeEnums,
  DifficultyEnums,
  ProblemTypesEnums,
  CorrectAnswer,
  AnalysisExplain,
  ThinkModelRender,
} from '@guanghe-pub/onion-problem-render'
import StarIcon from '~icons/yc/star'
import StarBlankIcon from '~icons/yc/star-blank'
import ArrowIcon from '~icons/yc/arrow-up'
import AddIcon from '~icons/yc/add'
import DeleteIcon from '~icons/yc/minus'
import AiIcon from '~icons/yc/ai-1'
import RedoIcon from '~icons/yc/redo'
import ClassModeBtn from '@/components/ClassRoomMode/ClassModeBtn.vue'
import type { ProblemDetailType } from '@/pages/problem/utils'
import { buryPoint } from '@/utils/buryPoint.ts'
import { useSchoolAIExplain } from '@/hooks/useSchoolAIExplain.ts'
import { useAuth } from '@/hooks/useAuth.ts'

const props = defineProps<{
  problem: ProblemDetailType
  index: number
  isFavorite?: boolean
  from?: string // 页面来源，埋点使用
  isHideFavorite?: boolean // 是否隐藏【收藏】按钮，点击按钮触发onFavorite
  isHideClassMode?: boolean // 是否隐藏【课堂模式】按钮
  isHideExplain?: boolean // 是否隐藏【解析】按钮
  booksIds?: string[] // 选题本：选题本ID数组（业务页面中配置booksProblemStore使用）;如果传入了此数组则题目会显示选题本的操作按钮，点击按钮触发onAddToBooks
  tagConfig?: {
    isHideBaseTags?: boolean // 是否隐藏左上角标签数组
    isHideProblemType?: boolean // 是否隐藏【题型】标签
    isHideUsageCount?: boolean // 是否隐藏【使用次数】标签
    isHideArrangementNum?: boolean // 是否隐藏【布置次数】标签
    isHideAnswerNum?: boolean // 是否隐藏【作答次数】标签
    isHideAccuracy?: boolean // 是否隐藏【正确率】标签
    isHideCorrectRate?: boolean // 是否隐藏【得分率】标签
    isHideIsUsed?: boolean // 是否隐藏【近期已布置】标签
    isShowCreatedName?: boolean // 是否显示【分享人】标签
  }
  problemTypeConfig?: {
    // 题型配置; 默认逻辑：优先取examType字段，没有则取type字段
    problemOnlyType?: boolean // 是否仅使用习题的type字段
    isSchool?: boolean // 是否是校本题目, 校本题目仅有type字段，且有固定枚举文案
  }
  showSchoolAiExplainButton?: boolean // 是否显示题目的【AI讲解】按钮；【注意： 2025年7月新增CB题目的AI讲解，配合problemTypeConfig中的isSchool进行区分】
  showInsertButton?: boolean // 是否显示【插入】按钮，点击按钮触发onInsert
  insertCount?: number // 插入次数，不传或者为0时不显示
}>()

const emits = defineEmits<{
  (e: 'onFavorite'): void
  (e: 'onAddToBooks'): void
  (e: 'onInsert'): void
}>()

const { userId } = useAuth()

// 标签：难度
const difficultyText = computed(() => {
  return (
    DifficultyEnums.find(
      (item) => `${item.score}` === props.problem.difficulty?.toString(),
    )?.name || '其他'
  )
})

// 标签：题型
const problemTypeText = computed(() => {
  if (props.tagConfig?.isHideProblemType) {
    return ''
  }
  // 校本题目的题型处理逻辑
  if (props.problemTypeConfig?.isSchool) {
    if (!props.problem?.type) {
      return ''
    }
    if (props.problem?.type === 'exam') {
      return '简答题'
    }
    if (props.problem?.type === 'single_choice') {
      return '单选题'
    }
    const text = ProblemTypesEnums[props.problem?.type] || ''
    return text ? (text.endsWith('题') ? text : `${text}题`) : ''
  }
  // 仅使用type的题型处理逻辑
  if (props.problemTypeConfig?.problemOnlyType) {
    const text = ProblemTypesEnums[props.problem?.type] || ''
    return text ? (text.endsWith('题') ? text : `${text}题`) : ''
  }
  // 默认逻辑
  return ExamProblemTypeEnums[props.problem?.examType || props.problem.type]
})

// 标签：梯子题 目标题
const poolText = computed(() => {
  if (!props.problem?.pool) return ''
  if (props.problem.pool === 'step') {
    return '梯子题'
  }
  if (props.problem.pool === 'goal') {
    return '目标题'
  }
  return ''
})

// 标签：目标题 变式
const variantTypeText = computed(() => {
  if (!props.problem.variantType) return ''
  switch (props.problem.variantType) {
    case 'target':
      return '目标题'
    case 'condition':
      return '条件变式'
    case 'result':
      return '结论变式'
    case 'scene':
      return '情景变式'
    default:
      return ''
  }
})

// 按钮：解析
const showExplain = ref(false)
const toggleExplain = () => {
  showExplain.value = !showExplain.value
  // 埋点
  if (props.from === 'correctionNotebook') {
    buryPoint(
      'clickAIClassWrongBookPageButton',
      {
        button: 'explain',
      },
      'course',
    )
  }
}

// 按钮：题目AI解析 【注意：原仅支持校本题目，2025年7月新增CB题目的AI讲解，配合problemTypeConfig中的isSchool进行区分】
const isCBProblem = computed(() => {
  return props.problemTypeConfig?.isSchool !== undefined && !props.problemTypeConfig?.isSchool
})
const { getSchoolAiExplain, regenerateAiExplain, schoolAIExplain } =
  useSchoolAIExplain(
    props.problem?.id || props.problem?.problemId,
    isCBProblem.value,
  )
const showSchoolAIExplain = ref(false)
const toggleSchoolAIExplain = async () => {
  showSchoolAIExplain.value = !showSchoolAIExplain.value
  if (showSchoolAIExplain.value) {
    getSchoolAiExplain()
  }
  buryPoint(
    'clickTeacherWorkBenchAIExplainButton',
    {
      pageName: '其他',
    },
    'course',
  )
}
const getSchoolAIExplainTip = computed(() => {
  const aiStateText =
    schoolAIExplain.value?.aiExplainProgress === 'failed'
      ? '讲解生成失败'
      : '讲解未生成'
  if (isCBProblem.value) {
    return `${aiStateText}，请点击右上角的「重新生成」生成讲解`
  }
  return `${aiStateText} ${
    schoolAIExplain.value?.canRegen ? '' : '，若是您自己上传的题'
  }，您可点击右上角的「重新生成」生成讲解`
})
</script>

<style lang="scss">
.problem-operation {
  padding: 4px 12px;
  border: 1px solid #c5c1d4;
  border-radius: 8px;
}

.paper-preferences-wrapper-popover {
  padding: 6px 8px !important;
  background: #57526c !important;
  border-radius: 8px !important;

  .paper-preferences-arrow-popover {
    background: #57526c !important;
  }

  .paper-preferences-content-popover {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #ffffff;
    background: #57526c;

    .paper-preferences-popover-theme-list {
      display: grid;
      grid-template-columns: repeat(2, 61px);
      grid-gap: 8px;
      padding: 4px;

      .paper-preferences-popover-theme-item {
        width: 100%;
        overflow: hidden;
        font-size: 12px;
        font-weight: 600;
        line-height: 12px;
        color: #ffffff;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.problem-render-government {
  font-size: 14px !important;
}

::v-deep(.onion-problem-render__expand) {
  padding: 16px;
  margin-top: 20px;
  background: #f7f7f9;
  border-radius: 12px;

  .onion-problem-render__answer {
    margin-top: 0;
  }
}

::v-deep(
    .oiw-button--box.n-button--info-type.n-button--ghost:not(
        .n-button--disabled
      ):focus
  ) {
  color: #393548 !important;
  background: transparent !important;
}

::v-deep(
    .oiw-button--box.n-button--info-type.n-button--ghost:not(
        .n-button--disabled
      ):focus
      .n-button__border
  ) {
  border: 1px solid #d4d1dd !important;
}

::v-deep(.onion-problem-render__option--text) {
  font-size: 1em !important;
  color: #393548 !important;
}

::v-deep(.oiw-button--box.n-button--small-type) {
  min-width: 108px !important;
  height: 40px;
  padding: 0 16px;
  font-size: 14px !important;
  font-weight: 400 !important;
}

::v-deep(.onion-problem-render__option) {
  display: flex;
  flex-wrap: wrap;
}

::v-deep(.onion-problem-render__option--no) {
  font-size: 1em !important;
  color: #393548 !important;
  background: transparent !important;
}

::v-deep(.onion-problem-render__option--no-dot) {
  display: unset !important;
}

::v-deep(.onion-problem-render__option--item) {
  width: 50%;
  border: none !important;

  .onion-problem-render__option--text {
    img {
      max-width: 100%;
    }
  }
}

::v-deep(.onion-problem-render__main) {
  font-size: 14px !important;
}

::v-deep(.onion-problem-render__examBox-show) {
  display: none;
}

::v-deep(.onion-problem-render__main img) {
  max-height: 200px;
}

::v-deep(.multi-line) {
  border: unset !important;
  border-bottom: 1px solid #505153 !important;
  border-radius: 0 !important;

  &::after {
    content: none !important;
  }
}

::v-deep(.onion-problem-render__examBox-show + .onion-problem-render__option) {
  display: none !important;
}

.tag-base {
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 600;
  color: #393548;
  border: 1px solid #c5c1d4;
  border-radius: 4px;
}

.tag-business {
  span {
    padding: 4px 8px;
    margin-left: 8px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px;

    &:first-child {
      margin-left: 0;
    }
  }
}

.ai-btn {
  height: 31px;
  color: #fff;
  cursor: pointer;
  background: linear-gradient(
    138deg,
    rgb(149, 130, 244) 0%,
    rgb(100, 130, 255) 68%,
    rgb(104, 192, 249) 107%
  );
  border: none;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.content-ing-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background: url('https://fp.yangcong345.com/onion-extension/333-0deb3b0ae5925ec7ab9de06c7019a851.png')
    no-repeat center center;
  background-size: 100% 100%;
  animation: loading 1s linear infinite;
}

.content-ing-text {
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  color: #5e80ff;
}
</style>
