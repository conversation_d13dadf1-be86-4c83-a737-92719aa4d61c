import request from '@/utils/request.ts'

export interface SchoolAiExplain {
  aiExplainProgress: string // 生成ai讲解进度，ing：生成中、done：完成、failed：已失败.
  aiExplain: string // 生成ai讲解内容.
  latency: number // 生成ai讲解耗时，单位：秒.
  canRegen: boolean // 是否有重新生成的权限
}
const schoolAiExplainCache = new Map<string, SchoolAiExplain>()

/**
 * 获取校本题目ai讲解内容
 * problemType: SceneAiClassTaskProblem: ai课堂布置的任务题目场景. SceneSchoolProblem: 校本资源题目(非布置任务)场景. SceneCbProblem: cb资源题目(非布置任务)场景.
 */
export const getSchoolAiExplainApi = async (
  problemType: string,
  problemId: string,
  deleteCache: boolean,
) => {
  if (deleteCache) {
    schoolAiExplainCache.delete(problemId)
  }
  const cache = schoolAiExplainCache.has(problemId)
    ? schoolAiExplainCache.get(problemId)!
    : null
  if (cache && cache.aiExplain) {
    return cache
  } else {
    const res = await request.get<SchoolAiExplain>(
      '/teacher-ai-class/teacher/ai-explain',
      {
        params: {
          problemType,
          problemId,
        },
      },
    )
    if (res.aiExplainProgress === 'done') {
      schoolAiExplainCache.set(problemId, res)
    }
    return res
  }
}

/**
 * 重新生成校本题目ai讲解内容
 * problemType: SceneAiClassTaskProblem: ai课堂布置的任务题目场景. SceneSchoolProblem: 校本资源题目(非布置任务)场景.
 */
export const putSchoolAIExplainApi = async (params: {
  problemType: string
  problemId: string
}) => {
  return request.put<{ success: boolean }>(
    '/teacher-ai-class/teacher/ai-explain',
    params,
  )
}
