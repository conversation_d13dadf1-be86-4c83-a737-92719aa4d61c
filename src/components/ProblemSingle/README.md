# ProblemSingle 题目组件

## 概述

ProblemSingle 是一个功能完整的题目展示组件，支持题目渲染、收藏、课堂模式、AI讲解等多种功能。基于洋葱问题渲染器封装，提供了丰富的配置选项和交互功能。

## 基本用法

```vue
<template>
  <div>
    <ProblemSingle 
      :problem="problemData"
      :index="1"
      @onFavorite="handleFavorite"
    />
  </div>
</template>

<script setup>
import ProblemSingle from '@/components/ProblemSingle/index.vue'

const problemData = ref({
  id: '123',
  content: '题目内容...',
  type: 'single_choice',
  difficulty: 3,
  // ... 其他题目数据
})

const handleFavorite = () => {
  console.log('收藏题目')
}
</script>
```

## 完整功能示例

```vue
<template>
  <div>
    <ProblemSingle 
      :problem="problem"
      :index="index"
      :isFavorite="favoriteStatus"
      :from="'problemList'"
      :booksIds="selectedBooks"
      :tagConfig="tagConfig"
      :problemTypeConfig="problemTypeConfig"
      :showSchoolAiExplainButton="true"
      :showInsertButton="true"
      :insertCount="insertCount"
      @onFavorite="handleFavorite"
      @onAddToBooks="handleAddToBooks"
      @onInsert="handleInsert"
    />
  </div>
</template>

<script setup>
import ProblemSingle from '@/components/ProblemSingle/index.vue'

const problem = ref({
  id: '123',
  content: '解方程：2x + 3 = 7',
  type: 'single_choice',
  examType: 'choice',
  difficulty: 3,
  usageCount: 156,
  accuracy: 85.6
})

const index = ref(1)
const favoriteStatus = ref(false)
const selectedBooks = ref(['book1', 'book2'])
const insertCount = ref(3)

const tagConfig = {
  isHideBaseTags: false,
  isHideProblemType: false,
  isHideUsageCount: false,
  isShowCreatedName: true
}

const problemTypeConfig = {
  problemOnlyType: false,
  isSchool: false
}

const handleFavorite = () => {
  favoriteStatus.value = !favoriteStatus.value
}

const handleAddToBooks = () => {
  console.log('添加到选题本')
}

const handleInsert = () => {
  console.log('插入题目')
  insertCount.value++
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| problem | 题目数据对象 | `ProblemDetailType` | - |
| index | 题目序号 | `number` | - |
| isFavorite | 是否已收藏 | `boolean` | `false` |
| from | 页面来源（用于埋点） | `string` | - |
| isHideFavorite | 是否隐藏收藏按钮 | `boolean` | `false` |
| isHideClassMode | 是否隐藏课堂模式按钮 | `boolean` | `false` |
| isHideExplain | 是否隐藏解析按钮 | `boolean` | `false` |
| booksIds | 选题本ID数组 | `string[]` | - |
| tagConfig | 标签显示配置 | `TagConfig` | - |
| problemTypeConfig | 题型配置 | `ProblemTypeConfig` | - |
| showSchoolAiExplainButton | 是否显示AI讲解按钮 | `boolean` | `false` |
| showInsertButton | 是否显示插入按钮 | `boolean` | `false` |
| insertCount | 插入次数 | `number` | `0` |

### TagConfig 类型定义

```typescript
interface TagConfig {
  isHideBaseTags?: boolean        // 是否隐藏左上角标签数组
  isHideProblemType?: boolean     // 是否隐藏题型标签
  isHideUsageCount?: boolean      // 是否隐藏使用次数标签
  isHideArrangementNum?: boolean  // 是否隐藏布置次数标签
  isHideAnswerNum?: boolean       // 是否隐藏作答次数标签
  isHideAccuracy?: boolean        // 是否隐藏正确率标签
  isHideCorrectRate?: boolean     // 是否隐藏得分率标签
  isHideIsUsed?: boolean          // 是否隐藏近期已布置标签
  isShowCreatedName?: boolean     // 是否显示分享人标签
}
```

### ProblemTypeConfig 类型定义

```typescript
interface ProblemTypeConfig {
  problemOnlyType?: boolean  // 是否仅使用习题的type字段
  isSchool?: boolean         // 是否是校本题目
}
```

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onFavorite | 点击收藏按钮时触发 | `() => void` |
| onAddToBooks | 点击添加到选题本时触发 | `() => void` |
| onInsert | 点击插入按钮时触发 | `() => void` |

## 功能特性

### 1. 题目渲染
- 支持多种题型（选择题、填空题、解答题等）
- 支持数学公式、图片、表格等富文本内容
- 提供标准的题目布局和样式

### 2. 标签系统
```vue
<!-- 显示题目相关标签 -->
<template>
  <ProblemSingle 
    :problem="problem"
    :index="1"
    :tagConfig="{
      isHideProblemType: false,     // 显示题型
      isHideUsageCount: false,      // 显示使用次数
      isHideAccuracy: false,        // 显示正确率
      isShowCreatedName: true       // 显示分享人
    }"
  />
</template>
```

### 3. 交互功能
- **收藏功能**: 支持题目收藏/取消收藏
- **课堂模式**: 支持课堂展示模式
- **AI讲解**: 支持AI智能讲解
- **选题本**: 支持添加到选题本
- **插入功能**: 支持插入到文档或课件

### 4. 题型处理
```javascript
// 题型显示逻辑
const problemTypeText = computed(() => {
  // 校本题目的题型处理
  if (props.problemTypeConfig?.isSchool) {
    if (props.problem?.type === 'exam') return '简答题'
    if (props.problem?.type === 'single_choice') return '单选题'
    // ...
  }
  
  // 默认逻辑：优先使用examType，其次使用type
  return ExamProblemTypeEnums[props.problem?.examType || props.problem.type]
})
```

## 使用场景

### 1. 题目列表展示

```vue
<template>
  <div class="problem-list">
    <ProblemSingle 
      v-for="(problem, index) in problems"
      :key="problem.id"
      :problem="problem"
      :index="index + 1"
      :isFavorite="favoriteMap[problem.id]"
      @onFavorite="() => toggleFavorite(problem.id)"
    />
  </div>
</template>

<script setup>
const problems = ref([])
const favoriteMap = ref({})

const toggleFavorite = async (problemId) => {
  try {
    await toggleProblemFavorite(problemId)
    favoriteMap.value[problemId] = !favoriteMap.value[problemId]
  } catch (error) {
    message.error('操作失败')
  }
}
</script>
```

### 2. 试卷预览

```vue
<template>
  <div class="paper-preview">
    <h2>{{ paperTitle }}</h2>
    <div class="problems">
      <ProblemSingle 
        v-for="(problem, index) in paperProblems"
        :key="problem.id"
        :problem="problem"
        :index="index + 1"
        :isHideFavorite="true"
        :isHideClassMode="true"
        :tagConfig="{ isHideBaseTags: true }"
      />
    </div>
  </div>
</template>
```

### 3. 课堂展示

```vue
<template>
  <div class="classroom-display">
    <ProblemSingle 
      :problem="currentProblem"
      :index="1"
      :isHideFavorite="true"
      :isHideExplain="false"
      :showSchoolAiExplainButton="true"
      class="classroom-problem"
    />
  </div>
</template>

<style scoped>
.classroom-problem {
  font-size: 18px;
  line-height: 1.8;
}
</style>
```

### 4. 选题本管理

```vue
<template>
  <div class="book-management">
    <ProblemSingle 
      v-for="problem in bookProblems"
      :key="problem.id"
      :problem="problem"
      :index="problem.index"
      :booksIds="availableBooks"
      :showInsertButton="true"
      :insertCount="insertCounts[problem.id]"
      @onAddToBooks="handleAddToBooks"
      @onInsert="handleInsert"
    />
  </div>
</template>

<script setup>
const availableBooks = ref(['book1', 'book2', 'book3'])
const insertCounts = ref({})

const handleAddToBooks = (problemId) => {
  // 添加到选题本逻辑
}

const handleInsert = (problemId) => {
  insertCounts.value[problemId] = (insertCounts.value[problemId] || 0) + 1
}
</script>
```

## 样式定制

### 基础样式

```css
.problem-render-government {
  /* 题目容器样式 */
  padding: 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

.problem-tags {
  /* 标签容器样式 */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.problem-actions {
  /* 操作按钮容器样式 */
  display: flex;
  gap: 12px;
  margin-top: 16px;
}
```

### 自定义主题

```vue
<template>
  <ProblemSingle 
    :problem="problem"
    :index="1"
    class="custom-problem"
  />
</template>

<style scoped>
.custom-problem {
  --problem-bg: #f8f9fa;
  --problem-border: #e9ecef;
  --tag-color: #6c757d;
  --button-color: #007bff;
}

.custom-problem :deep(.problem-render-government) {
  background-color: var(--problem-bg);
  border: 1px solid var(--problem-border);
  border-radius: 8px;
}
</style>
```

## 最佳实践

### 1. 性能优化

```vue
<script setup>
// 使用 shallowRef 优化大量题目的渲染性能
const problems = shallowRef([])

// 虚拟滚动处理大量题目
const visibleProblems = computed(() => {
  const start = Math.max(0, scrollTop.value - bufferSize)
  const end = Math.min(problems.value.length, scrollTop.value + visibleCount + bufferSize)
  return problems.value.slice(start, end)
})
</script>
```

### 2. 状态管理

```vue
<script setup>
// 使用 Pinia 管理题目相关状态
const problemStore = useProblemStore()
const { favoriteProblems, bookProblems } = storeToRefs(problemStore)

const isFavorite = computed(() => 
  favoriteProblems.value.includes(props.problem.id)
)
</script>
```

### 3. 错误处理

```vue
<script setup>
const handleFavorite = async () => {
  try {
    setLoading(true)
    await problemStore.toggleFavorite(props.problem.id)
    message.success('操作成功')
  } catch (error) {
    console.error('收藏操作失败:', error)
    message.error('操作失败，请重试')
  } finally {
    setLoading(false)
  }
}
</script>
```

## 注意事项

1. **数据格式**: 确保传入的 problem 数据符合 ProblemDetailType 类型
2. **权限控制**: 根据用户权限显示/隐藏相应功能按钮
3. **性能考虑**: 大量题目时考虑使用虚拟滚动
4. **埋点统计**: 合理使用 from 参数进行用户行为统计

## 更新日志

### v1.0.0
- 初始版本，支持基本的题目展示和交互功能
- 支持多种题型和丰富的配置选项
- 集成收藏、课堂模式、AI讲解等功能
