<template>
  <div class="loading-nested-loading">
    <div v-if="loading" class="loading-wrap">
      <div class="loading-container">
        <span />
        <span />
        <span />
        <span />
        <span />
      </div>
    </div>
    <slot v-else />
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  loading: boolean
}>()
</script>

<style lang="scss" scoped>
.loading-nested-loading {
  position: relative;
}

.loading-wrap {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 2px;
    background-color: #1696ea;
    border-radius: 10px;
    transition: all 400ms ease;
    animation: wave 2.5s ease infinite;
  }

  span:nth-child(2) {
    animation-delay: 100ms;
  }

  span:nth-child(3) {
    animation-delay: 200ms;
  }

  span:nth-child(4) {
    animation-delay: 300ms;
  }

  span:nth-child(5) {
    animation-delay: 400ms;
  }
}

@keyframes wave {
  0%,
  100% {
    background-color: #1696ea;
    transform: translate(0, 0);
  }

  40% {
    opacity: 0;
    transform: translate(0, 20px);
  }

  10% {
    background-color: #e5f8ff;
    transform: translate(0, -20px);
  }
}
</style>
