<template>
  <div class="filter-item">
    <span
      class="label"
      :style="{ width: `${labelWidth}px`, lineHeight: `${labellineHeight}px` }"
      >{{ name }}</span
    >
    <div class="content"><slot></slot></div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  name: string
  labelWidth: string
  labellineHeight?: string
}>()
</script>

<style lang="scss">
.filter-item {
  display: grid;
  grid-template-areas:
    'label content'
    'label .';
  grid-template-rows: auto 1fr;
  grid-template-columns: auto minmax(0, 1fr);
  align-items: flex-start;
  font-size: 14px;
  font-weight: 500;

  .label {
    display: grid;
    grid-area: label;
    grid-template-columns: 1fr auto;
    align-items: start;
    height: 100%;
    line-height: 32px;
    color: #393548;
    white-space: nowrap;
  }

  .content {
    display: flex;
    grid-area: content;
    align-items: center;
    min-height: 32px;
  }
}
</style>
