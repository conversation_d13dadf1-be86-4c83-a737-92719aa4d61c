import request from '@/utils/request'

export interface TopicDetail {
  id: string
  name: string
  video: YcType.VideoType
  [key: string]: any
}

interface PostTopicDetailsReq {
  topicIds?: string[]
  [k: string]: unknown
}

interface PostTopicDetailsRes {
  data?: TopicDetail[]
  [k: string]: unknown
}

/**
 * @description 系统课-知识点详情.
 * https://yapi.yc345.tv/project/2673/interface/api/105582
 * <AUTHOR>
 * @date 2024-07-10
 * @export
 * @param {PostTopicDetailsReq} data
 * @returns {Promise<PostTopicDetailsRes>}
 */
export const postSystemTopicDetails = (
  data: PostTopicDetailsReq,
): Promise<PostTopicDetailsRes> => {
  return request.post<PostTopicDetailsRes>(
    `/teacher-desk/preview/system-topic-details`,
    data,
  )
}

/**
 * @description 培优课-知识点详情.
 * https://yapi.yc345.tv/project/2673/interface/api/105576
 * <AUTHOR>
 * @date 2024-07-10
 * @export
 * @param {PostTopicDetailsReq} data
 * @returns {Promise<PostTopicDetailsRes>}
 */
export const postSpecialTopicDetails = (
  data: PostTopicDetailsReq,
): Promise<PostTopicDetailsRes> => {
  return request.post<PostTopicDetailsRes>(
    `/teacher-desk/preview/special-topic-details`,
    data,
  )
}

export const signVideoApi = async (url: string) => {
  return request.post<{ url: string }>(
    '/course/video/signVideo',
    {
      url,
    },
    {
      decrypt: true,
    },
  )
}

export const getDownloadAddress = (
  sources: YcType.VideoType['VideoAddress'][],
) => {
  const downloadUrlList = sources
    .filter((source) => source.platform === 'mobile' && source.format === 'mp4')
    .map((source) => ({
      ...source,
      url: source.url.trim().replace(/^http:\/\//i, 'https://'),
    }))
  const downloadUrl = downloadUrlList.find((item) =>
    ['high', 'middle'].includes(item.clarity),
  )
  return downloadUrl?.url || ''
}
