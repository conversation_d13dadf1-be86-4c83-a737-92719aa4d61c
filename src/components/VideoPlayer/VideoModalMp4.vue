<template>
  <n-modal
    v-model:show="modalShow"
    preset="card"
    class="video-play-modal"
    :closable="false"
    :mask-closable="false"
    style="width: 920px; min-height: 538px"
    :to="to"
  >
    <div class="video-play-box">
      <VideoMp4
        :url="url"
        :videoId="videoId || ''"
        :baseVideo="baseVideo"
        :buryPointParams="buryPointParams"
        :videoClip="videoClip"
      />
      <CloseIcon class="close-round-icon" @click="modalShow = false" />
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import VideoMp4 from '@/components/VideoPlayer/VideoMp4.vue'
import CloseIcon from '~icons/yc/close-round'
import type {
  VideoPlayerProps,
  VideoClip,
} from '@/components/VideoPlayer/service.ts'

const props = defineProps<{
  show: boolean
  videoId: string | undefined
  url?: string // 可选的直接传入的URL
  to?: string
  baseVideo?: VideoPlayerProps
  videoClip?: VideoClip
  buryPointParams?: Record<string, any> // 埋点参数
}>()

const emits = defineEmits<(e: 'update:show', val: boolean) => void>()

const modalShow = computed<boolean>({
  get() {
    return props.show
  },
  set(val: boolean) {
    emits('update:show', val)
  },
})

onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    const fullscreenElement = document.fullscreenElement
    console.log('fullscreenchange', fullscreenElement)

    if (fullscreenElement) {
      const closeButton = document.createElement('div')
      closeButton.classList.add('video-fullscreen-close-btn')
      closeButton.onclick = () => {
        document.exitFullscreen()
        modalShow.value = false
      }
      fullscreenElement.appendChild(closeButton)
    } else {
      const closeButton = document.querySelectorAll(
        '.video-fullscreen-close-btn',
      )
      console.log('closeButton', closeButton)
      if (closeButton) {
        closeButton.forEach((item) => {
          item.setAttribute('style', 'display: none')
        })
      }
    }
  })
})
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', () => {})
})
</script>

<style lang="scss" scoped>
.video-play-modal {
  position: relative;

  .n-base-close {
    display: none;
  }

  .n-card-header {
    padding: 10px !important;
  }
}
</style>

<style lang="scss" scoped>
::v-deep(.video-fullscreen-close-btn) {
  position: absolute;
  right: 20px;
  bottom: 6px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: url('https://fp.yangcong345.com/middle/1.0.0/40关闭icon@2x-f9c60db4752967db1e7424df240bfd16__w.png')
    no-repeat;
  background-size: 40px 40px;
}

.video-play-box {
  width: 100%;
  height: 533px;
  margin: 10px auto;

  .close-round-icon {
    position: absolute;
    right: -70px;
    bottom: 0px;
  }

  ::v-deep(.video-js) {
    min-width: 872px !important;
    min-height: 533px !important;
  }
}
</style>
