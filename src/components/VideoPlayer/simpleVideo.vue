<template>
  <OIWLoading v-if="load" :show="load" width="200px" height="200px" />
  <VideoPlayer
    v-else
    autoGetAddress
    :videoId="videoInfo.video.id"
    :custom="customVideoData"
    :baseVideo="customBaseVideo"
    :interactions="videoInfo.video.interactions"
    :interactionImages="videoInfo.video.interactionImages"
    :video-subtitle-url="videoInfo.video.subtitleUrl"
    :buryPointParams="getBuryPointParams"
    :customVideo="customVideo"
    :handleDownload="handleDownloadVideo"
    :volume="0.6"
    :getPlayer="getPlayerRef"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  postSystemTopicDetails,
  postSpecialTopicDetails,
  signVideoApi,
  getDownloadAddress,
} from './simpleService'
import type { VideoClip } from './service.ts'
import type { VideoPlayerProps } from '@/components/VideoPlayer/service.ts'
import { OIWLoading, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import VideoPlayer from './index.vue'
import {
  checkWinDesktopGreaterOrEqual,
  isDesktop,
  pcGetRemoteUtil,
} from '@/utils/wdesk'
import { useAuth } from '@/hooks/useAuth'
import { getDynamicHost } from '@/utils/apiUrl'
import { getTopicHasAuthApi } from '@/service/auth.ts'

const props = defineProps<{
  topicId: string
  specialCourseId?: string | undefined
  clipId?: string | undefined
  baseVideo?: VideoPlayerProps
  buryPointParams?: Record<string, any> // 埋点参数
  videoClip?: VideoClip
  getPlayer?: (player: any) => void
}>()
const playerRef = ref<any>()
const { userAuth } = useAuth()
const load = ref(true)
const message = useOIWMessage()
const isDisable = computed(() => {
  return !(
    (window as any).inElectron &&
    (userAuth.value?.roles.includes('admin') ||
      userAuth.value?.roles.includes('tenantTeacher'))
  )
})
const defineBaseVideo = ref<VideoPlayerProps>({
  autoplay: true,
  controls: {
    showOnPause: true,
  },
  fluid: true,
  fullScreenButton: true,
  clarityButtonHint: {
    threshold: 5000,
  },
  customArgs: {
    touchPlayPause: true,
  },
})

const getBuryPointParams = computed(() => {
  if (props.buryPointParams) {
    return {
      videoScene: 'teacherWorkbench',
      ...props.buryPointParams,
    }
  }
  return { videoScene: 'teacherWorkbench' }
})

const customBaseVideo = computed(() => {
  if (props.baseVideo) {
    return {
      ...defineBaseVideo.value,
      ...props.baseVideo,
      disableDownloadButton: isDisable.value,
    }
  }
  return {
    ...defineBaseVideo.value,
    disableDownloadButton: isDisable.value,
  }
})

const dialog = useDialog()

const videoInfo = ref<any>()
const customVideo = computed(() => {
  if (props.videoClip) {
    return {
      videoClip: {
        start: props.videoClip.start || 0,
        end: props.videoClip.end || 0,
      },
    }
  }
  if (props.clipId && videoInfo.value.video?.keyPoints) {
    const keyIndex = videoInfo.value.video.keyPoints.findIndex(
      (keyPoint: any) => keyPoint.id === props.clipId,
    )
    if (keyIndex > -1) {
      if (keyIndex === videoInfo.value.video.keyPoints.length - 1) {
        return {
          videoClip: {
            start: videoInfo.value.video.keyPoints[keyIndex].time,
            end: videoInfo.value.video.duration,
          },
        }
      } else {
        return {
          videoClip: {
            start: videoInfo.value.video.keyPoints[keyIndex].time,
            end: videoInfo.value.video.keyPoints[keyIndex + 1].time,
          },
        }
      }
    }
  }
  return undefined
})
const customVideoData = computed(() => {
  if (!props.specialCourseId) {
    return { topicId: props.topicId }
  }
  if (props.specialCourseId) {
    return { topicId: props.topicId, specialCourseId: props.specialCourseId }
  }
  return {}
})

const fetchData = async (topicIds: string[]) => {
  try {
    let response
    if (!props.specialCourseId) {
      response = await postSystemTopicDetails({ topicIds })
    } else if (props.specialCourseId) {
      response = await postSpecialTopicDetails({ topicIds })
    }
    return response && response.data
  } catch (error) {
    console.error('Error fetching data:', error)
    return null
  }
}

const initData = async (isNeed = true) => {
  try {
    if (isNeed) {
      load.value = true
    }
    if (props.topicId) {
      const data = await fetchData([props.topicId])
      if (data) {
        videoInfo.value = data[0]
        const res = await getTopicHasAuthApi([props.topicId])
        const hasAuth =
          res && res.topics && res.topics[0] && res.topics[0]?.auth
        if (!hasAuth) {
          dialog.info({
            showIcon: false,
            title: '提示',
            content:
              '该微课为付费微课，请联系洋葱工作人员或者下载洋葱学园教师版APP付费后使用',
            positiveText: '确认',
          })
          return
        }
      }
    }
  } finally {
    load.value = false
  }
}

initData()

watch(
  () => props.topicId,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      initData()
    }
  },
)

watch(
  () => props.specialCourseId,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      initData()
    }
  },
)

watch(
  () => props.clipId,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      initData()
    }
  },
)

const handleDownloadVideo = (
  player: any,
  sources: YcType.VideoType['VideoAddress'][],
) => {
  const checkVersionIsOk = checkWinDesktopGreaterOrEqual()
  if (isDisable.value || !checkVersionIsOk) {
    const titleText = isDisable.value
      ? '您需要使用桌面版才能下载视频哦！'
      : '您当前的版本过旧，此功能无法使用，请升级到最新版本。'
    dialog.info({
      title: titleText,
      negativeText: '取消',
      positiveText: '下载桌面版',
      onPositiveClick: () => {
        const host = getDynamicHost()
        window.open(`${host}/school/activityH5/yc-win-download`)
      },
    })
  } else {
    const downloadAddress = getDownloadAddress(sources)
    handleDownload(downloadAddress)
  }
}

const handleDownload = async (downloadUrl: string) => {
  const remoteUtil = pcGetRemoteUtil()
  if (!isDesktop || !remoteUtil) {
    return
  }
  let videoDownloadState = ''
  const urlString = String(downloadUrl)

  if (remoteUtil.Exists(urlString)) {
    videoDownloadState = 'exists'
  } else {
    const res = await signVideoApi(urlString)
    if (!res || !res?.url) {
      return message.error('操作失败')
    }
    try {
      const url = res?.url
      const name = videoInfo.value.video.name
      const stageId = videoInfo.value.video.stageId
      const subjectId = videoInfo.value.video.subjectId
      const interactions = videoInfo.value.video.interactions
      const topicInfo = videoInfo.value
      // 深拷贝所有参数，确保它们是可克隆的
      const clonedInteractions = JSON.parse(JSON.stringify(interactions))
      const clonedTopicInfo = JSON.parse(JSON.stringify(topicInfo))
      // 确保所有必要的参数都有值
      if (!url || !name || !stageId || !subjectId || !topicInfo) {
        // console.error('Missing required parameters')
        return
      }
      // 调用 AddTask 函数
      const result = remoteUtil.AddTask(
        url,
        name,
        name,
        window.location.href,
        clonedInteractions,
        clonedTopicInfo,
        stageId,
        subjectId,
      )
      if (['exists', 'overload', 'success'].includes(result)) {
        videoDownloadState = result
      }
    } catch (error) {
      // console.error('Error in handleDownload:', error)
    }
  }
  if (videoDownloadState) {
    const textMap = {
      exists: '已下载过该视频！',
      overload: '超过最大下载数量限制',
      success: '视频已加入下载列表！',
    }
    const text = textMap[videoDownloadState as keyof typeof textMap]
    dialog.info({
      title: text,
      negativeText: '取消',
      positiveText: '查看列表',
      onPositiveClick: () => {
        window.navigateToOffline()
      },
    })
  }
}

const getPlayerRef = (player: any) => {
  playerRef.value = player
  props.getPlayer?.(player)
}
</script>
