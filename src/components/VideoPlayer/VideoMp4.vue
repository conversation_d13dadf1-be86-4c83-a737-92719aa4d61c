<template>
  <OIWLoading v-if="loading" :show="loading" width="200px" height="200px" />
  <VideoPlayer
    v-else
    style="min-width: 100%; min-height: 100%"
    :sources="videoSources"
    :baseVideo="customBaseVideo"
    :volume="0.6"
    :videoId="videoId"
    :buryPointParams="getBuryPointParams"
    :customVideo="customVideo"
    :getPlayer="getPlayerRef"
  />
</template>

<script setup lang="ts">
import { OIWLoading, useOIWMessage } from '@guanghe-pub/onion-ui-web'
import type { VideoPlayerProps, VideoClip } from './service'
import VideoPlayer from './index.vue'
import { useLoading } from '@/hooks/useLoading'
import { getSchoolVideoPlayUrlApi } from '@/pages/schoolResource/schoolVideo/service'
import type { AxiosError } from 'axios'

const props = defineProps<{
  url?: string
  videoId: string
  baseVideo?: VideoPlayerProps
  buryPointParams?: Record<string, any> // 埋点参数
  videoClip?: VideoClip
  getPlayer?: (player: any) => void
}>()
const playerRef = ref<any>()
const { loading, toggleLoading } = useLoading()
const message = useOIWMessage()

const finalUrl = ref('')

const defineBaseVideo = ref<VideoPlayerProps>({
  autoplay: true,
  controls: {
    showOnPause: true,
  },
  fullScreenButton: true,
  clarityButton: false,
  customArgs: {
    touchPlayPause: true,
  },
})

const customBaseVideo = computed(() => {
  if (props.baseVideo) {
    return {
      ...defineBaseVideo.value,
      ...props.baseVideo,
    }
  }
  return {
    ...defineBaseVideo.value,
  }
})

const getBuryPointParams = computed(() => {
  if (props.buryPointParams) {
    return {
      videoScene: 'teacherWorkbench',
      ...props.buryPointParams,
    }
  }
  return { videoScene: 'teacherWorkbench' }
})

const videoSources = computed(() => {
  return [
    {
      src: finalUrl.value,
      clarity: '标准',
      type: 'video/mp4',
    },
  ]
})

const customVideo = computed(() => {
  if (props.videoClip) {
    return {
      videoClip: {
        start: props.videoClip.start || 0,
        end: props.videoClip.end || 0,
      },
    }
  }
  return undefined
})

const getPlayerRef = (player: any) => {
  playerRef.value = player
  props.getPlayer?.(player)
}

async function fetchVideoUrl() {
  toggleLoading()
  if (props.url) {
    toggleLoading()
    finalUrl.value = props.url
    return
  }

  if (!props.videoId) {
    toggleLoading()
    console.warn('VideoMp4: 没有提供url或videoId')
    return
  }
  try {
    const res = await getSchoolVideoPlayUrlApi(props.videoId)
    finalUrl.value = res.url
  } catch (e) {
    if ((e as AxiosError).response?.status !== 409) {
      message.warning('获取视频播放地址失败')
    }
    console.error('获取视频URL失败:', e)
  } finally {
    toggleLoading()
  }
}

watch(
  () => [props.url, props.videoId],
  () => {
    fetchVideoUrl()
  },
  { immediate: true },
)
</script>
