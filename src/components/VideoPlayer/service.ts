import request from '@/utils/request'
import { apiDomain, isStage, isTest } from '@/utils/apiUrl'

export interface VideoClip {
  start?: number
  end?: number
}

interface AddressRes {
  videoList: {
    address: {
      clarity: string
      format: string
      index: number
      md5: string
      name: string
      platform: string
      url: string
    }[]
    custom: {
      authorization: boolean
      videoId: string
    }
  }[]
  playerVersion: string
}
interface CollectRes {
  createdTime: string
  id: string
  keyPointId: string
  userId: string
  videoId: string
}
interface CustomVideo {
  videoClip?: {
    start: number
    end: number
  }
}
interface ShareInfo {
  topicId: string
  chapterId: string
  userId: string
  apiPCDomain: string // *** 新增share 时的域名
  videoId: string // 视频id
  videoName: string // 视频名称
  videoThumbnail: string
}
type EventType =
  | 'dispose'
  | 'ready'
  | 'play'
  | 'sourceset'
  | 'ended'
  | 'error'
  | 'fullscreenchange'
  | 'timeupdate'
  | 'abort'
  | 'timeupdate'
  | 'seeked'
  | 'seeking'
  | 'durationchange'
  | 'firstplay'
interface Player {
  dispose: () => void
  ready: (fn: () => void) => void
  currentTime: (seconds?: number) => number
  getPlayingTime: () => number
  on: (type: EventType, fn: (args: any) => void) => void
  ended: () => boolean
  duration: () => number
  off: (type: EventType, fn: (args: any) => void) => void
}

export interface VideoPlayerProps {
  env?: string
  needUploadProcess?: boolean
  autoGetAddress?: boolean
  topicId?: string
  videoId?: string
  width?: number
  height?: number
  autoplay?: boolean
  controls?:
    | boolean
    | {
        alwaysShow?: boolean // 是否常显控制栏，设为 true 则控制栏始终显示，默认 false
        autoHideDelay?: number // 自动隐藏延迟时间（毫秒），默认 8000ms
        hideOnMouseLeave?: boolean // 鼠标离开时是否隐藏控制栏，默认 true
        showOnPause?: boolean // 暂停时是否显示控制栏，默认 true
      }
  fluid?: boolean
  textTrack?: string
  poster?: string
  interactions?: YcType.Interaction[]
  downloadButton?: boolean
  fullScreenButton?: boolean
  disableDownloadButton?: boolean
  startTime?: number
  guide?: boolean
  videoSubtitleUrl?: string
  sources?: YcType.VideoAddress[]
  customVideo?: CustomVideo
  keyPoints?: YcType.KeyPoint[]
  interactionImages?: any[]
  buryPointParams?: Record<string, any>
  extraParams?: any | undefined
  shareButton?: boolean
  shareInfo?: ShareInfo
  videoMarkButton?: boolean
  customDomainObj?: Record<string, string>
  clarityButton?: boolean
  clarityButtonHint?: {
    threshold?: number
  }
  customArgs?: {
    touchPlayPause?: boolean
    userId?: string
  }
  handleDownload?: (params: any) => void
  collectVideoClipHandler?: (keypointId: string) => void
  onError?: (e: any) => void
  getPlayer?: (player: Player) => void
  onShut?: () => void
}

export const customDomainObj = {
  test: 'https://school-test.yangcong345.com',
  stage: 'https://school-api-stage.yangcong345.com',
  production: 'https://school-api.yangcong345.com',
}

export const getEnv = (): string => {
  return import.meta.env.PROD
    ? isTest
      ? 'test'
      : isStage
        ? 'stage'
        : 'production'
    : 'test'
}

export const clarityConstant: Record<
  string,
  {
    text: string
    order: number
    default?: boolean
  }
> = {
  fullHigh: {
    text: '蓝光',
    order: 0,
  },
  high: {
    text: '超清',
    order: 1,
  },
  middle: {
    text: '高清',
    order: 2,
    default: true,
  },
  low: {
    text: '低清',
    order: 3,
  },
}

export const getAddressList = (sources: YcType.VideoAddress[]) => {
  return sources
    .filter((source) => source.platform === 'mobile' && source.format === 'hls')
    .map((source) => {
      return {
        src: source.url?.trim().replace(/^http:\/\//i, 'https:'),
        type: 'application/x-mpegURL',
        clarity: clarityConstant[source.clarity || 0].text,
        order: clarityConstant[source.clarity || 0].order,
      }
    })
    .sort((a, b) => a.order - b.order)
}
export interface VideoPlayerCom {
  getVideoProcess: () => {
    process: (number | null)[]
    currentTime: number
    playingTime: number
  }
}

export const getVideoTextTrackApi = async (url: string) => {
  return request.post<{
    url: string
  }>('/config/signVideo', { url }, { baseURL: apiDomain })
}

export const handleCollectKey = async (videoId: string, keyPointId: string) => {
  const keys = await getCollectKeysApi(videoId)
  if (keys) {
    const keyPoint = keys.find((key) => key.keyPointId === keyPointId)
    if (keyPoint) {
      deleteCollectKeyApi(videoId, keyPointId)
    } else {
      addCollectKeyApi(videoId, keyPointId)
    }
  }
}
/**
 * 获取收集密钥
 * @param videoId
 * @returns
 */
export const getCollectKeysApi = async (videoId: string) => {
  return request.get<CollectRes[]>(`/videos/keyPointsC?videoId=${videoId}`)
}
/**
 * 删除收藏
 * @param videoId
 * @param keyPointId
 * @returns
 */
export const deleteCollectKeyApi = async (
  videoId: string,
  keyPointId: string,
) => {
  return request.delete('/videos/keyPointC', {
    data: {
      videoId,
      keyPointId,
    },
  })
}

/**
 * 增加收藏
 * @param videoId
 * @param keyPointId
 * @returns
 */
export const addCollectKeyApi = (videoId: string, keyPointId: string) => {
  return request.post('/videos/keyPointC', { videoId, keyPointId })
}

export const getVideoAddressApi = async (
  videoId: string,
  custom: Record<string, string>,
) => {
  return request.post<AddressRes>('/teacher-desk/video/addresses', {
    videoList: [
      {
        videoId,
        custom,
      },
    ],
  })
}

export const getVideoDetailsApi = (ids: string[]) => {
  return request.post<{ videos: YcType.VideoType[] }>(
    '/videos/get-video-details',
    {
      ids,
    },
  )
}

/**
 * 视频交互题
 * */

export const videoInteractions = ref<YcType.Interaction[]>([])
export const getVideoInteractions = (interactions?: YcType.Interaction[]) => {
  const tempInteractions: YcType.Interaction[] = JSON.parse(
    JSON.stringify(interactions || []),
  )
  if (tempInteractions?.length) {
    tempInteractions.forEach(async (interaction) => {
      if (interaction.actionType === 'video_exercise') {
        const videoIds: string[] = []
        interaction.choices?.forEach((choice) => {
          if (choice.videoId) {
            videoIds.push(choice.videoId)
          }
        })
        interaction.branchInteractions?.forEach((i) => {
          i.choices.forEach((choice) => {
            if (choice.videoId) {
              videoIds.push(choice.videoId)
            }
          })
        })
        const videoDetails = await getVideoDetailsApi(videoIds)
        interaction.choices?.forEach((choice) => {
          const videoDetail = videoDetails.videos.filter(
            (d) => d.id === choice.videoId,
          )[0]
          choice.video = {
            sources: getAddressList(videoDetail.addresses),
            textTrack: '',
            videoId: choice.videoId,
          }
        })
        interaction.branchInteractions?.forEach((i) => {
          i.choices.forEach((choice) => {
            const videoDetail = videoDetails.videos.filter(
              (d) => d.id === choice.videoId,
            )[0]
            choice.video = {
              sources: getAddressList(videoDetail.addresses),
              textTrack: '',
              videoId: choice.videoId,
            }
          })
        })
      }
    })
    videoInteractions.value = tempInteractions
  } else {
    videoInteractions.value = []
  }
}

/**
 * 获取视频Keypoint点
 */
export const videoKeyPoints = ref<YcType.KeyPoint[]>([])
export const getVideoCollectKeys = (
  videoId: string,
  keyPoints?: YcType.KeyPoint[],
) => {
  if (keyPoints?.length) {
    return getCollectKeysApi(videoId).then((keys) => {
      const videoKeys = keyPoints.map((el) => {
        return {
          ...el,
          collect: keys.some((key) => key.keyPointId === el.id),
        }
      })
      videoKeyPoints.value = videoKeys
    })
  }
}
