<template>
  <div class="yc-pc-player-container">
    <YcPcPlayer
      v-if="playerData.sources && playerData.sources.length"
      :options="playerData"
      @init="playerInit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount } from 'vue'
import YcPcPlayer from '@guanghe/yc-pc-player-vue'
import '@guanghe/yc-pc-player-vue/dist/yc-pc-player.min.css'
import { getPcBaseBuryPointParams } from '@/utils/buryPoint'
import type { VideoPlayerProps } from './service'

import {
  getEnv,
  videoInteractions,
  getVideoInteractions,
  videoKeyPoints,
  getVideoCollectKeys,
  customDomainObj,
  getVideoTextTrackApi,
  getAddressList,
  getVideoAddressApi,
  handleCollectKey,
} from './service'

const props = defineProps<{
  baseVideo: VideoPlayerProps // 必要视频参数
  autoGetAddress?: boolean // 是否自动拉取地址
  sources?: YcType.VideoAddress[] // 视频地址
  videoId?: string // 视频id
  custom?: Record<string, any> // 获取地址自定义参数 同步课：{topicId} 培优课{topicId, specialCourseId}
  customVideo?:
    | {
        videoClip: {
          start: number
          end: number
        }
        [key: string]: any
      }
    | undefined // 视频切片参数
  keyPoints?: YcType.KeyPoint[] // 视频点位
  interactions?: YcType.Interaction[] // 互动
  interactionImages?: any[] // 互动图像
  videoSubtitleUrl?: string // 字幕
  needUploadProcess?: boolean // 是否需要进度上报
  buryPointParams?: Record<string, any> // 埋点参数
  extraParams?: any | undefined // 埋点额外参数
  volume?: number // 音量
  getPlayer?: (player: any) => void
  handleDownload?: (
    player: any,
    sources: YcType.VideoType['VideoAddress'][],
  ) => void
}>()

const emits = defineEmits<{
  (e: 'end', currentTime: number, playingTime: number): void
  (e: 'shut', currentTime: number, playingTime: number): void
  (
    e: 'uploadProcess',
    currentTime: number,
    playingTime: number,
    duration: number,
    process: number[],
  ): void
  (e: 'play'): void
}>()
const playerRef = ref<any>(undefined)
const textTrack = ref<string>('')
const env = getEnv()

// 埋点参数： 判断并删除 eventTime 属性,播放器中不能传入eventTime，会导致所有埋点的触发时间都变成一样的了
const videoBuryPointParams = computed(() => {
  const buryPointParams = props.buryPointParams
  if (buryPointParams?.eventTime !== undefined) {
    delete buryPointParams.eventTime
  }
  const extraParams = {
    ...props.extraParams,
    ...getPcBaseBuryPointParams(),
  }
  if (extraParams?.eventTime !== undefined) {
    delete extraParams.eventTime
  }
  // console.log('视频埋点参数：', {
  //   ...buryPointParams,
  //   extraParams,
  // })
  return {
    ...buryPointParams,
    businessPointType: 'videoType',
    extraParams,
  }
})

const videoSources = ref<YcType.VideoAddress[]>([])
const videoDownloadSources = ref<YcType.VideoAddress[]>([])
const videoSubtitleUrl = computed(() => {
  if (props.videoSubtitleUrl) {
    return props.videoSubtitleUrl.includes('.vtt')
      ? props.videoSubtitleUrl
      : `${props.videoSubtitleUrl}.vtt`
  }
  return ''
})
/**
 * 记录视频播放打点，每秒为单位
 */
const process = ref<(number | null)[]>([])

const onPlayHandler = () => {
  emits('play')
}

const firstplay = () => {
  if (
    playerRef.value &&
    process.value.length === 0 &&
    props.needUploadProcess
  ) {
    process.value = new Array(
      Math.floor(playerRef.value?.duration() || 0),
    ).fill(null)
  }
}
const timeupdate = () => {
  if (playerRef.value && props.needUploadProcess) {
    const currentTime = Math.floor(playerRef.value?.currentTime() || 0)
    if (process.value[currentTime - 1] === null && currentTime > 0) {
      process.value[currentTime - 1] = 1
    }
  }
  uploadProcess()
}
const resetProcess = () => {
  process.value = []
}
const uploadProcess = () => {
  if (playerRef.value) {
    const val = process.value.map((el) => (el === null ? 0 : el))
    const currentTime = parseInt(`${playerRef.value?.currentTime() || '0'}`, 10)
    const playingTime = parseInt(
      `${playerRef.value?.getPlayingTime() || '0'}`,
      10,
    )
    const duration = Math.floor(playerRef.value?.duration() || 0)
    emits('uploadProcess', currentTime, playingTime, duration, val)
  }
}

const onDestroy = () => {
  uploadProcess()
  resetProcess()
  playerRef.value?.off('firstplay', firstplay)
  playerRef.value?.off('timeupdate', timeupdate)
  playerRef.value = undefined
}

/**
 * 中断事件
 */
const handleShut = () => {
  if (playerRef.value) {
    const currentTime = parseInt(`${playerRef.value?.currentTime() || '0'}`, 10) // timepoint
    const playingTime = parseInt(
      `${playerRef.value?.getPlayingTime() || '0'}`,
      10,
    )
    emits('shut', currentTime, playingTime)
    uploadProcess()
    resetProcess()
  }
}

const playerData = computed((): VideoPlayerProps => {
  return {
    autoplay: props.baseVideo.autoplay || false,
    controls: props.baseVideo.controls || false,
    videoId: props.videoId || '',
    env,
    fluid: props.baseVideo.fluid || false,
    poster: props.baseVideo.poster || '',
    interactions: videoInteractions.value,
    startTime: props.baseVideo.startTime || undefined,
    guide: props.baseVideo.guide || false,
    width: props.baseVideo.width || 600,
    height: props.baseVideo.height || 336,
    shareButton: props.baseVideo.shareButton || false,
    shareInfo: props.baseVideo.shareInfo || undefined,
    downloadButton: props.baseVideo.downloadButton || false,
    disableDownloadButton: props.baseVideo.disableDownloadButton || false,
    interactionImages: props.interactionImages,
    sources: videoSources.value,
    keyPoints: videoKeyPoints.value,
    textTrack: textTrack.value,
    fullScreenButton: props.baseVideo.fullScreenButton || false,
    customDomainObj,
    customVideo: props.customVideo || undefined,
    buryPointParams: videoBuryPointParams.value,
    videoMarkButton: props.baseVideo.videoMarkButton || false,
    clarityButton: props.baseVideo.clarityButton === false ? false : true,
    clarityButtonHint: props.baseVideo.clarityButtonHint,
    customArgs: props.baseVideo.customArgs,
    collectVideoClipHandler: (keypointId: string) =>
      handleCollectKey(props.videoId || '', keypointId),
    handleDownload: (player: any) =>
      props.handleDownload?.(player, videoDownloadSources.value),
    onError: (e: any) => {
      // eslint-disable-next-line no-console
      console.error(e)
    },
    onShut: handleShut,
  }
})

const playerInit = async (player: any) => {
  playerRef.value = await player
  props.getPlayer?.(playerRef.value)
  playerRef.value.on('ready', () => {
    if (props.volume && props.volume <= 1) {
      playerRef.value.volume(props.volume)
    }
  })
  playerRef.value.on('play', onPlayHandler)
  playerRef.value.on('ended', handleEnd)
  playerRef.value.on('firstplay', firstplay)
  playerRef.value.on('timeupdate', timeupdate)
}

const getVideoSubtitle = () => {
  if (videoSubtitleUrl.value) {
    return getVideoTextTrackApi(videoSubtitleUrl.value).then((res) => {
      if (res.url) {
        textTrack.value = res.url
      }
    })
  }
}

const getVideoSources = async () => {
  if (
    props.autoGetAddress &&
    !props.videoId &&
    !props.custom &&
    import.meta.env.VITE_MODE === 'test'
  ) {
    /* eslint-disable */
    console.warn('播放器拉取视频失败，没有传递videoId或custom数据')
    return
  }

  if (props.videoId && props.autoGetAddress) {
    const res = await getVideoAddressApi(props.videoId, props.custom || {})
    if (res.videoList[0].address) {
      videoDownloadSources.value = res.videoList[0].address
      const videoAddresss = getAddressList(res.videoList[0].address)
      videoSources.value = videoAddresss
    }
  }
  if (props.sources) {
    videoSources.value = props.sources
    videoDownloadSources.value = props.sources
  }
}
/**
 * 播放完成事件
 * @param player
 */
const handleEnd = () => {
  if (playerRef.value) {
    uploadProcess()
    resetProcess()
    const currentTime = parseInt(`${playerRef.value?.currentTime() || '0'}`, 10) // timepoint
    const playingTime = parseInt(
      `${playerRef.value?.getPlayingTime() || '0'}`,
      10,
    )
    emits('end', currentTime, playingTime)
    if (props.customVideo && props.customVideo.videoClip) {
      playerRef.value?.currentTime(props.customVideo.videoClip.start)
    }
  }
}

const videoInit = async () => {
  playerRef.value = null
  videoSources.value = []
  await getVideoSubtitle()
  await getVideoCollectKeys(props.videoId || '', props.keyPoints || [])
  await getVideoInteractions(props.interactions || [])
  await getVideoSources()
}

onBeforeUnmount(() => {
  onDestroy()
})

videoInit()

watch(
  () => props.sources,
  (newVal) => {
    if (newVal) {
      videoInit()
    }
  },
)

watch(
  () => props.videoId,
  (newVal) => {
    if (newVal) {
      videoInit()
    }
  },
)
watch(
  () => props.customVideo,
  (newVal, oldVal) => {
    if (newVal) {
      videoInit()
    }
  },
)

defineExpose({
  videoInit,
})
</script>

<style lang="scss" scoped>
.yc-pc-player-container {
  width: 100%;

  ::v-deep(.vjs-interaction-bar-knk6hguj) {
    background: transparent !important;
  }
}
</style>
