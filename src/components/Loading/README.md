# Loading 加载组件

## 概述

Loading 是一个自定义的加载动画组件，提供了波浪式的加载效果。当 loading 为 true 时显示加载动画，为 false 时显示插槽内容。

## 基本用法

```vue
<template>
  <div>
    <Loading :loading="isLoading">
      <div>这里是需要加载的内容</div>
    </Loading>
  </div>
</template>

<script setup>
import Loading from '@/components/Loading.vue'

const isLoading = ref(true)

// 模拟异步加载
setTimeout(() => {
  isLoading.value = false
}, 2000)
</script>
```

## 与异步数据结合使用

```vue
<template>
  <div>
    <Loading :loading="loading">
      <div v-if="data">
        <h3>{{ data.title }}</h3>
        <p>{{ data.content }}</p>
      </div>
      <div v-else>
        暂无数据
      </div>
    </Loading>
  </div>
</template>

<script setup>
import Loading from '@/components/Loading.vue'
import { useLoading } from '@/hooks/useLoading'

const data = ref(null)
const { loading, withLoading } = useLoading()

const fetchData = withLoading(async () => {
  const response = await api.getData()
  data.value = response.data
})

onMounted(() => {
  fetchData()
})
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| loading | 是否显示加载状态 | `boolean` | - |

## Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 加载完成后显示的内容 | - |

## 动画效果

组件使用了 5 个圆点的波浪动画效果：
- 每个圆点依次延迟 100ms 开始动画
- 动画周期为 2.5 秒
- 包含颜色变化和位移效果

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.loading-wrap span {
  --loading-color: #1696ea;
  --loading-color-light: #e5f8ff;
  --loading-size: 10px;
  --loading-margin: 2px;
}
```

### 自定义颜色示例

```vue
<template>
  <Loading :loading="loading" class="custom-loading">
    <div>内容</div>
  </Loading>
</template>

<style scoped>
.custom-loading .loading-wrap span {
  background-color: #ff6b6b;
}

.custom-loading .loading-wrap span:nth-child(odd) {
  background-color: #4ecdc4;
}
</style>
```

## 与其他加载组件的对比

| 特性 | Loading 组件 | Naive UI Spin | Element Plus Loading |
|------|-------------|---------------|---------------------|
| 动画效果 | 波浪圆点 | 旋转圆环 | 旋转圆环 |
| 自定义性 | 高 | 中 | 中 |
| 体积 | 小 | 中 | 大 |
| 依赖 | 无 | Naive UI | Element Plus |

## 使用场景

1. **数据加载**: 等待 API 请求返回数据时
2. **页面切换**: 路由切换时的过渡效果
3. **文件上传**: 文件上传过程中的等待状态
4. **表单提交**: 表单提交时的等待反馈

## 最佳实践

### 1. 与 useLoading Hook 结合

```vue
<script setup>
import Loading from '@/components/Loading.vue'
import { useLoading } from '@/hooks/useLoading'

const { loading, withLoading } = useLoading()

const handleSubmit = withLoading(async () => {
  await api.submitForm()
})
</script>

<template>
  <Loading :loading="loading">
    <form @submit.prevent="handleSubmit">
      <!-- 表单内容 -->
    </form>
  </Loading>
</template>
```

### 2. 嵌套使用

```vue
<template>
  <Loading :loading="pageLoading">
    <div class="page-content">
      <Loading :loading="listLoading">
        <div class="list">
          <!-- 列表内容 -->
        </div>
      </Loading>
    </div>
  </Loading>
</template>
```

## 注意事项

1. 组件使用绝对定位覆盖整个容器
2. 加载状态下会阻止用户交互
3. 建议为容器设置最小高度，避免布局跳动
4. 动画使用 CSS3，在低版本浏览器中可能不显示

## 更新日志

### v1.0.0
- 初始版本，支持基本的波浪加载动画
- 支持插槽内容显示
- 支持 CSS 样式定制
