/**
 * 获取用户信息
 */

import request from '@/utils/request'

interface JobTitle {
  id: number
  name: string
}

interface LearningTime {
  video: number
  practice: number
}

interface Location2 {
  code: string
  name: string
}

interface Level {
  progress: number[]
  no: number
}

interface School {
  createTime: string
  _id: string
  name: string
  regionCode: string
  headLetter: string
  region1: string
  category: string
  region0: string
  geoLocation: string
  regionInfo: string[]
  location: Location
  id: number
  eduSchoolId: number
  stageId: number
  deleted: boolean
  provinceRegionCode: string
  cityRegionCode: string
}

export interface MeTypes {
  school: School
  registTime: string
  _id: string
  onionIdQrcodeStr: string
  region: string
  createTime: string
  updateTime: string
  avatarUrl: string
  from: string
  attribution: string
  frame: string
  id: string
  name: string
  email: string
  nickname: string
  phone: string
  channel: string
  type: string
  registEntranceId: string
  os: string
  role: 'teacher' | 'student' | 'parents' | 'visitor'
  countryCode: string
  onionId: string
  schoolYear: string
  realIdentity: string
  school_id: number
  verifiedByPhone: boolean
  level: Level
  location: Location2[]
  thirdPartyOauths: any[]
  rooms: any[]
  learningTime: LearningTime
  points: number
  coins: number
  attributionUpdateTime: number
  scores: number
  approveNicknameStatus: number
  trialType: number
  weekScores: number
  nicknameUpdateTime: string
  roles?: string[]
  stageId: number
  subjectId: number
  publisherId?: number
  jobTitle: JobTitle
  teacherOrganization: string
  teachingType: any
  inAccurateWhiteList: boolean
  gender: string
  showVIPContent: boolean
  isAdmin: boolean
}

export const getMeApi = () => {
  return request.get<MeTypes>('/me')
}
export const putUpdateMe = (data: Record<string, any>) => {
  return request.put<MeTypes>('/me', data)
}

export const getIsShowAIEntryApi = () => {
  return request.get<{ isShow: boolean }>('/teacher-desk/auth/show-ai')
}

/**
 * 获取用户权限
 */

interface Permission {
  id: number
  name: string
  enabled: boolean
  stageSubjects?: {
    stageId: string
    subjectId: string
  }[]
}
export interface UserAuth {
  roles: string[]
  permissions: Permission[]
}

let _cacheUserAuth: UserAuth | null = null

export const getUserAuthsApi = async () => {
  return (
    _cacheUserAuth ||
    request.get<UserAuth>('/user-auths').then((res) => {
      _cacheUserAuth = res
      return res
    })
  )
}

/**
 * 用户商品权限
 */
interface Vip {
  id: string
  expired: boolean
  expireTime: string
  authId: string
  sourceType: string
  sourceId: string
}

export interface AuthData {
  vip: Vip[]
  topic: Vip[]
  tenant: Vip[]
  publicTextbook: Vip[]
  examPaper: Vip[]
  topicTrial: Vip[]
  publicTextbookTrial: Vip[]
  specialCourseTrial: Vip[]
  specialCourse: Vip[]
  correction: Vip[]
  collectionCourses: Vip[]
}

export interface OrderAuth {
  userId: string
  auth: AuthData
  errcode: boolean
}

let _cacheAuth: OrderAuth | null = null

export const getUserOrderAuthApi = async () => {
  return (
    _cacheAuth ||
    request.get<OrderAuth>('/user-auths/order/auth').then((res) => {
      _cacheAuth = res
      return res
    })
  )
}

export interface SpecialCourse {
  coverImage: string
  id: string
  stageIds: number[]
  subjectIds: number[]
  topicCount: number
  type: string
}

let _cacheUserSpcicalCourse: SpecialCourse[] | null = null

export const getUserSpcicalCourseApi = async () => {
  return (
    _cacheUserSpcicalCourse ||
    request.get<SpecialCourse[]>('/course-tree/special-courses').then((res) => {
      _cacheUserSpcicalCourse = res
      return res
    })
  )
}

export interface TopicHasAuthType {
  topicId: string
  auth: boolean
}

// 获取当前知识点是否有权限，支持批量查询
export const getTopicHasAuthApi = async (topicIds: string[]) => {
  return request.post<{ topics: TopicHasAuthType[] }>(
    '/teacher-common/auth/check-topics-auth',
    {
      topicIds,
    },
  )
}

export interface CouserwareAuth {
  hasFine: boolean // 是否有查看精品权限.
}

let _cacheCouserwareAuth: CouserwareAuth | null = null

export const getCouserwareAuthApi = async ({
  stageId,
  subjectId,
}: {
  stageId: number
  subjectId: number
}) => {
  return (
    _cacheCouserwareAuth ||
    request
      .get<CouserwareAuth>('/teacher-desk/couserware/auth', {
        params: { stageId, subjectId },
      })
      .then((res) => {
        _cacheCouserwareAuth = res
        return res
      })
  )
}

export const getRealTeacherApi = async () => {
  return request.get<{
    isConfirm: boolean
    isAuth: boolean
  }>('/teacher/real-teacher')
}

export const getAuxiliaryExerciseAuth = async (schoolId: string) => {
  return request.get<{ data: { show: boolean } }>(
    '/teacher-ee/help-bad/is-show-seriallycorrect',
    {
      params: {
        schoolId,
      },
    },
  )
}

// 跳转解决方案2.0
export const bettwebAuthApi = async (schoolId: string) => {
  return request.post<{
    appId: string
    code: string
    webHost: string
    ssoLoginPath: string
  }>('/teacher/bett-sso-codes', {
    schoolId,
  })
}

// 获取教师是否有个性化学习任务权限
export const getEEV2AuthApi = async () => {
  return request.get<{ isShowSchoolLearningTask: boolean; isShow: boolean }>(
    `/teacher-ee/v2/teacher/is-show-plan`,
  )
}

// 获取教师权益中心配置的权限
export const getTeacherAuthCenterApi = async () => {
  return request.get<{
    privileges: string[]
  }>('/teacher-common/teacher-school/user-privileges')
}
