import request from '@/utils/request'

export const getSpecialCourseDetailFromIdApi = async <
  T = YcType.SpecialCourse3LevelRoot,
>(
  id: string,
) => {
  return request.get<T>(`/course/special-course/${id}`)
}

export interface SpecialCourseItem {
  id: string
  name: string
  type: string
  coverImage: string
  coverImagePc: string
  subjectIds: number[]
  stageIds: number[]
  userTypes: [string]
  regions: [string]
  childrens: [SpecialCourseItem]
  topicCount: number
}

export const getSpecialCourseListApi = async (
  stageId?: YcType.CsvId,
  subjectId?: YcType.CsvId,
) => {
  return request.get<SpecialCourseItem[]>('/course-tree/special-courses', {
    params: {
      stageId,
      subjectId,
    },
  })
}

export const getRecommendSpecialCourseApi = async (
  stageId: YcType.CsvId,
  subjectId: YcType.CsvId,
) => {
  return request.get<SpecialCourseItem[]>(
    '/course-tree/recommend-special-courses',
    {
      params: {
        stageId,
        subjectId,
      },
    },
  )
}
