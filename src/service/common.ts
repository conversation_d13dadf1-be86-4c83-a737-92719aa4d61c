/**
 * 公共接口
 */
import request from '@/utils/request'
import { apiSchoolDomain, apiDomain } from '@/utils/apiUrl'
import type { ProblemDetailType } from '@/pages/problem/problemList/utils'

export interface TextBookType {
  userId: string
  subjectId: number
  stageId: number
  publisherId: number
  semesterId: number
  updateTime: string
  course_id: string
  chapterId: string
}

export const getCurrentTextbookApi = () => {
  return request.get<TextBookType[]>(`${apiDomain}/user-current-textbook`)
}

export interface updateCurrentBody extends Record<string, any> {
  publisherId: YcType.CsvId
  semesterId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
}

export const updateCurrentTextbookApi = (data: updateCurrentBody) => {
  const transformData: Record<string, number> = {}
  Object.keys(data).forEach((key) => {
    transformData[key] = parseInt(data[key], 10)
  })
  return request.post(`${apiDomain}/user-current-textbook`, transformData)
}

/**
 * 学科学段
 */

export const getCvsApi = (resourceType?: 'new') => {
  return request.get<{
    data: YcType.Stage[]
  }>(`${apiSchoolDomain}/course-tree/stage-subjects-semesters`, {
    params: {
      resourceType,
    },
  })
}
// 试卷：exam_paper，微课：preview，习题：problem，校本微课：school_preview，校本题库：school_problem，校本试卷：school_exam_paper.
// 多种资源需要用“,”（英文）分隔.
export type FavoriteType =
  | 'exam_paper'
  | 'preview'
  | 'problem'
  | 'school_preview'
  | 'school_problem'
  | 'school_exam_paper'
  | 'problem,school_problem'
  | 'preview,school_preview'
  | 'exam_paper,school_exam_paper'
export type FavoriteSubType = 'system' | 'special' | 'system-point' | 'school'
export interface AddFavoriteBody {
  resourceType: FavoriteType
  resourceSubType?: FavoriteSubType
  resourceId: string
  resourceSubId?: string
  resourceName?: string
  userId: string
  subject?: YcType.CsvId
  stage?: YcType.CsvId
  publisher?: YcType.CsvId
  semester?: YcType.CsvId
  extra?: Record<string, any>
}

export interface FavoriteItem extends AddFavoriteBody {
  createdAt: string
  usageCount: number
  favoriteCount: number
}

export const addFavoriteApi = async (body: AddFavoriteBody) => {
  return request.post(`/teacher-desk/favorite`, body)
}

export interface DelFavoriteQuery {
  resourceSubId?: string
  resourceId: string
}

export const delFavoriteApi = (params: DelFavoriteQuery) => {
  return request.delete('/teacher-desk/favorite', {
    params,
  })
}

export interface FavoriteListQuery {
  resourceType: FavoriteType
  pageSize: number
  page: number
  resourceName?: string
  stage?: YcType.CsvId
  subject?: YcType.CsvId
  publisher?: YcType.CsvId
  semester?: YcType.CsvId
}

export const getFavoriteApi = (params: FavoriteListQuery) => {
  return request.get<{
    data: FavoriteItem[]
    page: number
    pageSize: number
    total: number
  }>('/teacher-desk/favorite/list', {
    params,
  })
}

interface FavoriteIdsQuery {
  resourceType: FavoriteType
  stage?: YcType.CsvId
  subject?: YcType.CsvId
  publisher?: YcType.CsvId
  semester?: YcType.CsvId
}

export const getFavoriteIdsApi = (params: FavoriteIdsQuery) => {
  return request.get<{
    data: string[]
  }>('/teacher-desk/favorite/ids', {
    params,
  })
}

type AuthSourceType =
  | 'TEACHER_DESK_ENTRY'
  | 'AI'
  | 'PUBLIC_RESOURCE_EDIT'
  | 'TEMPLATE_MANAGE'
  | 'FINE_COURSEWARE_USE'

export const authByShowSouretypeApi = async (sourceType: AuthSourceType) => {
  return request.get<{
    isShow: boolean
  }>('/teacher-desk/auth/show-by-source-type', {
    params: {
      sourceType,
    },
  })
}

interface KeypointData {
  videoId: string
  videoDurations: {
    id: number
    offset: number
  }[]
}

export interface KeypointScreenShot {
  id: number
  url: string
}

export const getVideoKeypointShotsApi = async (body: KeypointData) => {
  return request.post<{
    screenShots: KeypointScreenShot[]
    videoID: string
  }>(`/videos/screen-shot`, body)
}

export interface SystemLessonTopicDetail {
  practices: ProblemDetailType[][]
  video: YcType.VideoType
}

// 获取同步课
export const getSystemLessonTopicDetail = (params: {
  topicId: string
  semesterId: YcType.CsvId
  publisherId: YcType.CsvId
  subjectId: YcType.CsvId
  stageId: YcType.CsvId
  doneLevel: string
}) => {
  return request.get<SystemLessonTopicDetail>(
    `/course/topics/${params.topicId}/detail-video`,
    {
      params,
    },
  )
}

// 获取培优课
export const getSpicialCourseDetail = (topicId: string) => {
  return request.get<SystemLessonTopicDetail>(
    `/course/topics/${topicId}/detail-universal`,
  )
}

interface GetTeacherDeskCvsStageRes {
  stages?: {
    id: number
    name: string
    [k: string]: unknown
  }[]
  [k: string]: unknown
}

/**
 * @description 获取学段枚举.
 * https://yapi.yc345.tv/project/2673/interface/api/115238
 * <AUTHOR>
 * @date 2024-11-28
 * @export
 * @returns {Promise<GetTeacherDeskCvsStageRes>}
 */
export function getTeacherDeskCvsStage(): Promise<GetTeacherDeskCvsStageRes> {
  return request.get<GetTeacherDeskCvsStageRes>(
    `/teacher-desk/ai-user-test-paper/list-cvs-stage`,
    {},
  )
}
export interface GetTeacherDeskCvsSubjectRes {
  subjects?: {
    id: number
    name: string
    [k: string]: unknown
  }[]
  [k: string]: unknown
}

/**
 * @description 获取学科枚举.
 * https://yapi.yc345.tv/project/2673/interface/api/115245
 * <AUTHOR>
 * @date 2024-11-28
 * @export
 * @returns {Promise<GetTeacherDeskCvsSubjectRes>}
 */
export function getTeacherDeskCvsSubject(): Promise<GetTeacherDeskCvsSubjectRes> {
  return request.get<GetTeacherDeskCvsSubjectRes>(
    `/teacher-desk/ai-user-test-paper/list-cvs-subject`,
    {},
  )
}

export interface GetTeacherDeskCvsPublisherRes {
  publishers?: {
    id: number
    name: string
    [k: string]: unknown
  }[]
  [k: string]: unknown
}

/**
 * @description 获取教材版本枚举.
 * https://yapi.yc345.tv/project/2673/interface/api/115224
 * <AUTHOR>
 * @date 2024-11-28
 * @export
 * @returns {Promise<GetTeacherDeskCvsPublisherRes>}
 */
export function getTeacherDeskCvsPublisher(): Promise<GetTeacherDeskCvsPublisherRes> {
  return request.get<GetTeacherDeskCvsPublisherRes>(
    `/teacher-desk/ai-user-test-paper/list-cvs-publisher`,
    {},
  )
}

export interface GetTeacherDeskCvsSemesterRes {
  semesters?: {
    id: number
    name: string
    [k: string]: unknown
  }[]
  [k: string]: unknown
}

/**
 * @description 获取册别枚举.
 * https://yapi.yc345.tv/project/2673/interface/api/115231
 * <AUTHOR>
 * @date 2024-11-28
 * @export
 * @returns {Promise<GetTeacherDeskCvsSemesterRes>}
 */
export function getTeacherDeskCvsSemester(): Promise<GetTeacherDeskCvsSemesterRes> {
  return request.get<GetTeacherDeskCvsSemesterRes>(
    `/teacher-desk/ai-user-test-paper/list-cvs-semester`,
    {},
  )
}

/**
 * @description: 获取服务器时间
 * @return {*} https://yapi.yc345.tv/project/2740/interface/api/120113
 */
export const getServerTime = () => {
  return request.get<{ now: string }>(
    `${apiSchoolDomain}/teacher-ai-class/utils/server-time`,
  )
}
