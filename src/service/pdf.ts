import request from '@/utils/request'
import type { ProblemDetailType } from '@/pages/problem/utils.ts'

// 创建pdf时传入的data对象，次对象中包含后续模板页面渲染的全部数据
export interface CreatePdfDataType {
  problems?: ProblemDetailType[]
  pdfName?: string
  isShowExplain?: boolean // 是否附带 解析+答案
  isShowAnswer?: boolean // 是否附带 答案
  groups?: {
    examType?: string
    name?: string
    id?: string
    problems?: ProblemDetailType[]
  }[]
  pdfType?: 'paper' | 'problem'
}

export interface CreatePdfOptionType {
  printBackground?: boolean // 设置为“true”以打印背景图形, 一般设为false
  displayHeaderFooter?: boolean // 是否显示页眉和页脚，一般设为true
  headerTemplate?: string // 页眉模板
  footerTemplate?: string // 页脚模板
  // 页边距
  margin?: {
    top?: string
    right?: string
    bottom?: string
    left?: string
  }
}

// 创建pdf
export interface createPdfType {
  data: CreatePdfDataType // 模板中需要的全部数据
  fileName: string // 文件名称（不需要后缀）
  templateURL: string // 集群内的模板地址，示例： 'http://onionweb-next.teacherschool/pdf/problemsDownloadCenter'
  option: CreatePdfOptionType
}
export const createPdfApi = async (data: createPdfType) => {
  return request.post<{ id: string }>('/teacher-common/pdfs/pdf-task', data)
}

// 查询创建pdf任务状态
export interface PdfStatusType {
  id: string
  name: string
  url: string
  urlExpireTime: string
  progress: number
  createdAt: string
  updatedAt: string
  state: number // 上传状态 1.成功 2.失败
  extra: string
  fileName: string
}

export const getPdfLinkApi = async (id: string, app = 'bronn') => {
  return request.get<{ list: PdfStatusType[]; total: number }>(
    '/teacher-common/download-center/tasks',
    {
      params: {
        ids: id,
        app,
      },
    },
  )
}

export interface PostWordGenExamWordReq {
  /**
   * 题数据.
   */
  problems?: {
    /**
     * 题目id.
     */
    problemId?: string
    /**
     *  - ItemTypeNone: 无.
     *  - Video: 微课视频.
     *  - Problem: 题.
     *  - Clip: 微课片段.
     *  - SchoolVideo: 学校视频.
     *  - SchoolProblem: 学校题目.
     */
    sourceType?:
      | 'ItemTypeNone'
      | 'Video'
      | 'Problem'
      | 'Clip'
      | 'SchoolVideo'
      | 'SchoolProblem'
    [k: string]: unknown
  }[]
  /**
   * 文件名.
   */
  fileName?: string
  style?: {
    /**
     *  - AnswerOptNone: 无选项 不要使用.
     *  - AnswerOptAfter: 1:题后.
     *  - AnswerOptEnd: 2: 结尾.
     *  - AnswerOptHide: 3: 不显示.
     */
    answerOpt?:
      | 'AnswerOptNone'
      | 'AnswerOptAfter'
      | 'AnswerOptEnd'
      | 'AnswerOptHide'
    /**
     * 是否显示题目解释.
     */
    isExplain?: boolean
    /**
     * 正文字体大小 28: 四号 24: 小四号 21:五号.
     */
    textSize?: number
    /**
     * 是否显示分数.
     */
    isScore?: boolean
    [k: string]: unknown
  }
  /**
   * 是否加入云盘.
   */
  isCloudDisk?: boolean
  [k: string]: unknown
}

export interface PostWordGenExamWordRes {
  /**
   * 任务id.
   */
  id?: string
  [k: string]: unknown
}

/**
 * @description 生成试卷.
 * https://yapi.yc345.tv/project/2673/interface/api/125974
 * <AUTHOR>
 * @date 2025-05-08
 * @export
 * @param {PostWordGenExamWordReq} data
 * @returns {Promise<PostWordGenExamWordRes>}
 */
export const postWordGenExamWordApi = (
  data: PostWordGenExamWordReq,
): Promise<PostWordGenExamWordRes> => {
  return request.post<PostWordGenExamWordRes>(
    `/teacher-common/word/gen-exam-word`,
    data,
  )
}
