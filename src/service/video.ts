import request from '@/utils/request'

interface AddressRes {
  videoList: {
    address: {
      clarity: string
      format: string
      index: number
      md5: string
      name: string
      platform: string
      url: string
    }[]
    custom: {
      authorization: boolean
      videoId: string
    }
  }[]
  playerVersion: string
}

export const getVideoAddressApi = async (videoId: string, topicId: string) => {
  return request.post<AddressRes>('/videos/addresses', {
    videoList: [
      {
        videoId,
        custom: { topicId },
      },
    ],
  })
}

interface CollectRes {
  createdTime: string
  id: string
  keyPointId: string
  userId: string
  videoId: string
}
/**
 * 获取收藏目录
 * @param videoId
 * @returns
 */
export const getCollectKeysApi = async (videoId: string) => {
  return request.get<CollectRes[]>(`/videos/keyPointsC?videoId=${videoId}`)
}

/**
 * 删除收藏
 * @param videoId
 * @param keyPointId
 * @returns
 */
export const deleteCollectKeyApi = async (
  videoId: string,
  keyPointId: string,
) => {
  return request.delete('/videos/keyPointC', {
    data: {
      videoId,
      keyPointId,
    },
  })
}

/**
 * 增加收藏
 * @param videoId
 * @param keyPointId
 * @returns
 */
export const addCollectKeyApi = (videoId: string, keyPointId: string) => {
  return request.post('/videos/keyPointC', { videoId, keyPointId })
}

export const getVideoDetailsApi = (ids: string[]) => {
  return request.post<{ videos: YcType.VideoType['Video'][] }>(
    '/videos/get-video-details',
    { ids },
  )
}

// 获取用户视频打点列表
export interface VideoMark {
  time: number
  uuid: string
  videoId: string
  bowserCreateTimeStamp: number
}
export interface VideoMarkRes {
  createdAt: string
  id: string
  marks: VideoMark[]
  userId: string
  videoId: string
}
export const getVideoMarkApi = (videoId: string) => {
  return request.get<VideoMarkRes>(`/videomark?videoId=${videoId}`)
}

export const updateVideoMarkApi = (videoId: string, marks: VideoMark[]) => {
  return request.post<VideoMarkRes>('/videomark', { videoId, marks })
}
