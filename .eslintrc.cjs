module.exports = {
  extends: [
    './.eslintrc-auto-import.json',
    '@guanghe-pub/eslint-config-vue3',
    'prettier',
  ],
  globals: {
    defineProps: 'readonly',
    defineEmits: 'readonly',
    defineExpose: 'readonly',
    withDefaults: 'readonly',
    YcType: 'readonly',
  },
  rules: {
    'no-console': 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-unused-vars': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'vue/multi-word-component-names': 'off',
    'func-call-spacing': 'off',
    'max-nested-callbacks': 'off',
    'max-params': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/consistent-type-imports': 'error',
    complexity: 'off',
  },
}
