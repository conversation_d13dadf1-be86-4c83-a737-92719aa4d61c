import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import legacy from '@vitejs/plugin-legacy'
import Unocss from 'unocss/vite'
import { presetUno } from 'unocss'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import OnionOssVitePlugin from '@guanghe-pub/onion-oss-vite-plugin'
import Icons from 'unplugin-icons/vite'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
function fileResolve(...dir: string[]) {
  return resolve(__dirname, ...dir)
}
// https://vitejs.dev/config/
export default ({ mode }: { mode: string }) => {
  const env = loadEnv(mode, process.cwd())
  return defineConfig({
    base: env['VITE_APP_BASE_API'],
    define: {
      __APP_ENV__: JSON.stringify(env.VITE_APP_PROJECT_NAME),
    },
    build: {
      sourcemap: false,
      minify: true,
      target: 'es2015',
      reportCompressedSize: false,
      rollupOptions: {
        output: {
          manualChunks: {
            'naive-ui': ['naive-ui'],
            'onion-ui': [
              '@guanghe-pub/onion-ui-web',
              '@guanghe-pub/onion-problem-render',
            ],
            'vue-vendor': ['vue', 'vue-router', 'pinia', '@vueuse/core'],
            echarts: ['echarts'],
            utils: [
              'lodash-es',
              'dayjs',
              'axios',
              'crypto-js',
              '@guanghe-pub/onion-utils',
            ],
            'file-process': [
              'pdfjs-dist',
              'jszip',
              'file-saver',
              'html2canvas',
            ],
            player: ['@guanghe/yc-pc-player-vue'],
            animation: ['vue3-lottie'],
            icons: ['@icon-park/vue-next'],
          },
        },
      },
    },
    plugins: [
      legacy({
        targets: ['> 1%', 'last 2 versions', 'not dead', 'Chrome >= 49'],
        modernPolyfills: true,
      }),
      vue(),
      vueJsx(),
      Unocss({
        presets: [presetUno()],
        shortcuts: {
          'text-ellipsis': 'overflow-hidden whitespace-nowrap text-ellipsis',
        },
        rules: [
          [
            'ellipsis',
            {
              overflow: 'hidden',
              'text-overflow': 'ellipsis',
              'white-space': 'nowrap',
            },
          ],
        ],
      }),
      Icons({
        compiler: 'vue3',
        autoInstall: true,
        defaultClass: 'yc-icon',
        customCollections: {
          yc: FileSystemIconLoader('src/assets/svg', (svg) =>
            svg.replace(/^<svg /, '<svg fill="currentColor" '),
          ),
        },
        transform(svg, collection, icon) {
          //console.log(svg.replace(/^<svg /, '<svg fill="currentColor" '))
          // apply fill to this icon on this collection
          return svg.replace(/^<svg/, '<svg fill="currentColor" ')
        },
      }),
      AutoImport({
        eslintrc: {
          enabled: false,
          globalsPropValue: 'readonly',
        },
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            'naive-ui': [
              'useDialog',
              'useMessage',
              'useNotification',
              'useLoadingBar',
              'useModal',
            ],
          },
        ],
      }),
      Components({
        resolvers: [NaiveUiResolver()],
      }),
      OnionOssVitePlugin({
        output: resolve('dist/assets'),
        rootDir: env['VITE_APP_PROJECT_NAME'] + '/assets',
      }),
    ],
    resolve: {
      alias: {
        '@': fileResolve('src'),
      },
      extensions: ['.ts', '.js', '.jsx', '.tsx', '.json', '.vue', '.mjs'],
    },
    server: {
      host: '0.0.0.0',
      open: true,
    },
  })
}
