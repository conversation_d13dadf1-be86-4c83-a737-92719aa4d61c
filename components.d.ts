/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AIToolEntry: typeof import('./src/components/SideTools/components/AIToolEntry.vue')['default']
    AIToolModal: typeof import('./src/components/SideTools/components/AIToolModal.vue')['default']
    BackTop: typeof import('./src/components/BackTop.vue')['default']
    BooksEntry: typeof import('./src/components/SideTools/components/BooksEntry.vue')['default']
    BooksModal: typeof import('./src/components/SideTools/components/BooksModal.vue')['default']
    BriefDesc: typeof import('./src/components/SideResource/components/BriefDesc.vue')['default']
    CheckBox: typeof import('./src/components/ShareModal/components/CheckBox.vue')['default']
    ClassFullScreen: typeof import('./src/components/ClassRoomMode/ClassFullScreen.vue')['default']
    ClassModeBtn: typeof import('./src/components/ClassRoomMode/ClassModeBtn.vue')['default']
    ClassProblem: typeof import('./src/components/ClassRoomMode/ClassProblem.vue')['default']
    ClassRoomBar: typeof import('./src/components/ReportClassroomMode/classRoomBar.vue')['default']
    CollectionList: typeof import('./src/components/SideResource/Video/CollectionList.vue')['default']
    CollectionPaper: typeof import('./src/components/SideResource/ExamPaper/CollectionPaper.vue')['default']
    CommonPaper: typeof import('./src/components/SideResource/ExamPaper/CommonPaper.vue')['default']
    CommonProblemType: typeof import('./src/components/SideResource/Problem/CommonProblemType.vue')['default']
    CsvSelect: typeof import('./src/components/CsvSelect.vue')['default']
    ExamPaperDetail: typeof import('./src/components/SideResource/ExamPaper/ExamPaperDetail.vue')['default']
    ExamPaperType: typeof import('./src/components/SideResource/ExamPaper/ExamPaperType.vue')['default']
    FastReciteInfo: typeof import('./src/components/SlideFastRecite/FastReciteInfo.vue')['default']
    FastReciteList: typeof import('./src/components/SlideFastRecite/FastReciteList.vue')['default']
    FeedbackEntry: typeof import('./src/components/SideTools/components/FeedbackEntry.vue')['default']
    FeedbackModal: typeof import('./src/components/SideTools/components/FeedbackModal.vue')['default']
    FilterItem: typeof import('./src/components/FilterItem/FilterItem.vue')['default']
    FilterSelect: typeof import('./src/components/FilterSelects/FilterSelect.vue')['default']
    FolderBreadcrumb: typeof import('./src/components/SlideLecture/FolderBreadcrumb.vue')['default']
    FolderDetail: typeof import('./src/components/SlideLecture/FolderDetail.vue')['default']
    FolderDetial: typeof import('./src/components/SlideLecture/FolderDetial.vue')['default']
    ImagePasteDemo: typeof import('./src/components/ImagePasteDemo.vue')['default']
    ImagePasteExample: typeof import('./src/components/ImagePasteExample.vue')['default']
    InsertProblem: typeof import('./src/components/SideResource/insertProblem.vue')['default']
    Loading: typeof import('./src/components/Loading.vue')['default']
    MicroVideoList: typeof import('./src/components/SideResource/Video/MicroVideoList.vue')['default']
    NAnchor: typeof import('naive-ui')['NAnchor']
    NAnchorLink: typeof import('naive-ui')['NAnchorLink']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBackTop: typeof import('naive-ui')['NBackTop']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCol: typeof import('naive-ui')['NCol']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NImage: typeof import('naive-ui')['NImage']
    NIn: typeof import('naive-ui')['NIn']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNumberAnimation: typeof import('naive-ui')['NNumberAnimation']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopover: typeof import('naive-ui')['NPopover']
    NPopselect: typeof import('naive-ui')['NPopselect']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NRow: typeof import('naive-ui')['NRow']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSplit: typeof import('naive-ui')['NSplit']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTable: typeof import('naive-ui')['NTable']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NUpload: typeof import('naive-ui')['NUpload']
    PagePreview: typeof import('./src/components/PPTPreview/pagePreview.vue')['default']
    PaperChangeModal: typeof import('./src/components/PaperChangeModal.vue')['default']
    PaperItem: typeof import('./src/components/SideResource/components/PaperItem.vue')['default']
    PaperProblemType: typeof import('./src/components/SideResource/Problem/PaperProblemType.vue')['default']
    ParseText: typeof import('./src/components/ParseText.vue')['default']
    PPTPreview: typeof import('./src/components/PPTPreview/index.vue')['default']
    ProblemCollection: typeof import('./src/components/SideResource/Problem/ProblemCollection.vue')['default']
    ProblemList: typeof import('./src/components/ReportClassroomMode/problemList.vue')['default']
    ProblemSingle: typeof import('./src/components/ProblemSingle/index.vue')['default']
    ProblemType: typeof import('./src/components/SideResource/Problem/ProblemType.vue')['default']
    ReportClassroomMode: typeof import('./src/components/ReportClassroomMode/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SchoolPaper: typeof import('./src/components/SideResource/ExamPaper/SchoolPaper.vue')['default']
    SchoolProblem: typeof import('./src/components/SideResource/Problem/SchoolProblem.vue')['default']
    SchoolVideo: typeof import('./src/components/SideResource/Video/SchoolVideo.vue')['default']
    SchoolVideoCard: typeof import('./src/components/SideResource/Video/SchoolVideoCard.vue')['default']
    SchoolVideoDetail: typeof import('./src/components/SideResource/Video/SchoolVideoDetail.vue')['default']
    SchoolVideoPractice: typeof import('./src/components/SideResource/Video/SchoolVideoPractice.vue')['default']
    SelfClipModal: typeof import('./src/components/SideResource/Video/SelfClipModal.vue')['default']
    ShareModal: typeof import('./src/components/ShareModal/ShareModal.vue')['default']
    SideResource: typeof import('./src/components/SideResource/index.vue')['default']
    SideTools: typeof import('./src/components/SideTools/index.vue')['default']
    SimpleVideo: typeof import('./src/components/VideoPlayer/simpleVideo.vue')['default']
    SlideFastRecite: typeof import('./src/components/SlideFastRecite/index.vue')['default']
    SlideLecture: typeof import('./src/components/SlideLecture/index.vue')['default']
    SpecialCsv: typeof import('./src/components/CsvSelectSpecial/specialCsv.vue')['default']
    SpecialTree: typeof import('./src/components/SideResource/components/SpecialTree.vue')['default']
    SystemTree: typeof import('./src/components/SideResource/components/SystemTree.vue')['default']
    TeacherHomeWorkModal: typeof import('./src/components/TeacherHomeWorkModal/index.vue')['default']
    TopicVideoPractice: typeof import('./src/components/SideResource/Video/TopicVideoPractice.vue')['default']
    TreeItem: typeof import('./src/components/TeacherTree/treeItem.vue')['default']
    TreeList: typeof import('./src/components/TeacherTree/treeList.vue')['default']
    UpdateList: typeof import('./src/components/SideResource/Video/UpdateList.vue')['default']
    VideoCollection: typeof import('./src/components/SideResource/Video/VideoCollection.vue')['default']
    VideoDetail: typeof import('./src/components/SideResource/Video/VideoDetail.vue')['default']
    VideoDetailItem: typeof import('./src/components/SideResource/Video/VideoDetailItem.vue')['default']
    VideoList: typeof import('./src/components/SideResource/Video/VideoList.vue')['default']
    VideoModal: typeof import('./src/components/SideResource/Video/VideoModal.vue')['default']
    VideoModalMp4: typeof import('./src/components/VideoPlayer/VideoModalMp4.vue')['default']
    VideoMp4: typeof import('./src/components/VideoPlayer/VideoMp4.vue')['default']
    VideoPlayer: typeof import('./src/components/VideoPlayer/index.vue')['default']
    VideoType: typeof import('./src/components/SideResource/Video/VideoType.vue')['default']
    WrongBookType: typeof import('./src/components/SideResource/WrongBook/WrongBookType.vue')['default']
  }
}
