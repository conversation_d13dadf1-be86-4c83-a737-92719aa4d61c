<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>洋葱学园，智能学习科创企业</title>
    <meta
      name="keywords"
      content="洋葱学园，洋葱数学、初中数学，数学，数学学习，微课，微视频，翻转课堂、物理、化学、语文、英语、学习app、学习软件"
    />
    <meta
      name="description"
      content="洋葱学园是中国领先的智能学习科创公司，推出了AI智能学伴、智能助教、智能学习机及人机协同精准教学解决方案等多款软硬件产品。"
    />
    <meta name="wpk-bid" content="dta_2_141589" />
    <script>
      var isRenMin = window.location.href.indexOf('renminzhike') > 1
      document.title = isRenMin
        ? '人民智课-教育数字化专业服务机构'
        : '洋葱学园，智能学习科创企业'
      var newLinkElement = document.createElement('link')
      newLinkElement.rel = 'shortcut icon'
      newLinkElement.href = isRenMin
        ? 'https://fp.yangcong345.com/onion-extension/20231019-112734-78b3723955f0e347e97f63d86942b7d3.jpeg'
        : 'https://documents.yangcong345.com/webTitleIcon.png'

      // 获取 <head> 元素并添加新的 <link> 元素到其中
      var headElement = document.querySelector('head')
      headElement.appendChild(newLinkElement)
    </script>
    <script src="//fp.yangcong345.com/middle/7.10.5/polyfill.min.js"></script>
  </head>
  <body class="onion-scroll">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script
      src="https://fp.yangcong345.com/web-track-1.1.8/webTrack.js"
      crossorigin
    ></script>
    <script
      src="https://fp.yangcong345.com/fe-monitor-1.3.0/monitor.js"
      crossorigin
    ></script>
    <script type="module">
      var envMaps = {
        development: 'dev',
        test: 'dev',
        stage: 'stage',
        production: 'prod',
      }

      var env = envMaps[import.meta.env.MODE]
      OnionWebMonitor.run({
        appKey: 'teacher-workbench',
        env: env,
        abnormal: {
          report: {
            beforeSend: (error, errorType) => {
              var ignoreErrors = [
                'api/v4/events',
              ];
              if (errorType === 'jsError' && error.msg && ignoreErrors.some(msg => error.msg.includes(msg))) {
                if (env === 'dev') {
                  console.log('已过滤JS错误', error.msg)
                }
                return {};
              }
              return error;
            }
          }
        }
      })

      var isDingtalk = navigator && /DingTalk/.test(navigator.userAgent)
      if (isDingtalk && env === 'prod') {
        !(function (c, i, e, b) {
          var h = i.createElement('script')
          var f = i.getElementsByTagName('script')[0]
          h.type = 'text/javascript'
          h.crossorigin = true
          h.onload = function () {
            c[b] || (c[b] = new c.wpkReporter({ bid: 'dta_2_141589' }))
            c[b].installAll()
          }
          f.parentNode.insertBefore(h, f)
          h.src = e
        })(
          window,
          document,
          'https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js',
          '__wpk',
        )
      }
    </script>
  </body>
</html>
