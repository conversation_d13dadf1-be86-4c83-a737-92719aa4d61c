{"globals": {"Component": "readonly", "ComponentPublicInstance": "readonly", "ComputedRef": "readonly", "EffectScope": "readonly", "ExtractDefaultPropTypes": "readonly", "ExtractPropTypes": "readonly", "ExtractPublicPropTypes": "readonly", "InjectionKey": "readonly", "PropType": "readonly", "Ref": "readonly", "VNode": "readonly", "WritableComputedRef": "readonly", "acceptHMRUpdate": "readonly", "computed": "readonly", "createApp": "readonly", "createPinia": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "defineStore": "readonly", "effectScope": "readonly", "getActivePinia": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "h": "readonly", "inject": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "mapActions": "readonly", "mapGetters": "readonly", "mapState": "readonly", "mapStores": "readonly", "mapWritableState": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onBeforeMount": "readonly", "onBeforeRouteLeave": "readonly", "onBeforeRouteUpdate": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onErrorCaptured": "readonly", "onMounted": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "setActivePinia": "readonly", "setMapStoreSuffix": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "storeToRefs": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "toValue": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAttrs": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useDialog": "readonly", "useLink": "readonly", "useLoadingBar": "readonly", "useMessage": "readonly", "useNotification": "readonly", "useRoute": "readonly", "useRouter": "readonly", "useSlots": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly"}}