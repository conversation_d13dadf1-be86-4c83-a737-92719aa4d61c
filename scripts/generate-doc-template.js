#!/usr/bin/env node

import { writeFileSync, existsSync, mkdirSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 组件文档模板
const componentTemplate = (componentName) => `# ${componentName}

## 概述

${componentName} 组件的功能描述。

## 基本用法

\`\`\`vue
<template>
  <div>
    <${componentName} />
  </div>
</template>

<script setup>
import ${componentName} from '@/components/${componentName}.vue'
</script>
\`\`\`

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| - | - | - | - |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| - | - | - |

## Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 默认插槽 | - |

## 样式定制

组件支持通过 CSS 变量进行样式定制：

\`\`\`css
.${componentName.toLowerCase()} {
  /* 在这里添加 CSS 变量 */
}
\`\`\`

## 注意事项

1. 使用注意事项
2. 兼容性说明

## 更新日志

### v1.0.0
- 初始版本
`

// Hook 文档模板
const hookTemplate = (hookName) => `# ${hookName}

## 概述

\`${hookName}\` 是一个用于...的 Hook。

## 基本用法

\`\`\`typescript
import { ${hookName} } from '@/hooks/${hookName}'

export default {
  setup() {
    const { /* 返回值 */ } = ${hookName}()
    
    return {
      // 导出的值
    }
  }
}
\`\`\`

## API

### 参数

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| - | - | - | - |

### 返回值

| 名称 | 类型 | 说明 |
|------|------|------|
| - | - | - |

## 完整示例

\`\`\`vue
<template>
  <div>
    <!-- 使用示例 -->
  </div>
</template>

<script setup lang="ts">
import { ${hookName} } from '@/hooks/${hookName}'

// 使用 Hook
const { /* 返回值 */ } = ${hookName}()
</script>
\`\`\`

## 注意事项

1. 使用注意事项
2. 最佳实践

## 更新日志

### v1.0.0
- 初始版本
`

// 生成文档模板
function generateTemplate(type, name) {
  const srcDir = join(__dirname, '../src')
  let targetDir, template, filename
  
  if (type === 'component') {
    targetDir = join(srcDir, 'components', name)
    template = componentTemplate(name)
    filename = 'README.md'
  } else if (type === 'hook') {
    targetDir = join(srcDir, 'hooks')
    template = hookTemplate(name)
    filename = `${name}.md`
  } else {
    console.error('❌ 类型必须是 component 或 hook')
    process.exit(1)
  }
  
  // 创建目录（如果不存在）
  if (!existsSync(targetDir)) {
    mkdirSync(targetDir, { recursive: true })
  }
  
  const filePath = join(targetDir, filename)
  
  // 检查文件是否已存在
  if (existsSync(filePath)) {
    console.log(`⚠️  文档已存在: ${filePath}`)
    console.log('如需重新生成，请先删除现有文档')
    return
  }
  
  // 写入模板
  writeFileSync(filePath, template, 'utf8')
  console.log(`✅ 文档模板生成成功: ${filePath}`)
  console.log(`💡 请编辑文档内容，然后运行 'npm run docs:dev' 查看效果`)
}

// 命令行参数处理
const args = process.argv.slice(2)

if (args.length < 2) {
  console.log(`
📖 文档模板生成器

用法:
  node scripts/generate-doc-template.js <type> <name>

参数:
  type  文档类型 (component | hook)
  name  组件或 Hook 名称

示例:
  node scripts/generate-doc-template.js component MyButton
  node scripts/generate-doc-template.js hook useMyHook
`)
  process.exit(1)
}

const [type, name] = args
generateTemplate(type, name)
