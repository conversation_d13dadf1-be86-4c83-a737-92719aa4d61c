#!/usr/bin/env node

import { execSync } from 'child_process'
import { existsSync, readFileSync, statSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 检查 Git 状态
function getGitStatus() {
  try {
    const output = execSync('git status --porcelain', { encoding: 'utf8' })
    return output.split('\n').filter(line => line.trim())
  } catch (error) {
    console.error('❌ 无法获取 Git 状态:', error.message)
    return []
  }
}

// 检查是否有组件或 Hook 文件变更
function hasComponentOrHookChanges(gitStatus) {
  const relevantChanges = gitStatus.filter(line => {
    const file = line.substring(3) // 去掉状态标识
    return (
      file.startsWith('src/components/') ||
      file.startsWith('src/hooks/') ||
      file.includes('README.md') ||
      file.endsWith('.md')
    )
  })
  
  return relevantChanges
}

// 检查文档是否需要同步
function checkDocSync() {
  console.log('🔍 检查文档同步状态...')
  
  const gitStatus = getGitStatus()
  const relevantChanges = hasComponentOrHookChanges(gitStatus)
  
  if (relevantChanges.length === 0) {
    console.log('✅ 没有组件或 Hook 相关的变更')
    return true
  }
  
  console.log('📄 发现以下相关变更:')
  relevantChanges.forEach(change => {
    console.log(`   ${change}`)
  })
  
  // 检查是否有新的组件或 Hook 缺少文档
  const missingDocs = []
  
  relevantChanges.forEach(change => {
    const file = change.substring(3)
    const status = change.substring(0, 2)
    
    // 检查新增的组件是否有文档
    if (status.includes('A') && file.startsWith('src/components/') && file.endsWith('.vue')) {
      const componentDir = dirname(file)
      const docPath = join(componentDir, 'README.md')
      
      if (!existsSync(docPath)) {
        missingDocs.push({
          type: 'component',
          name: dirname(file).split('/').pop(),
          path: docPath
        })
      }
    }
    
    // 检查新增的 Hook 是否有文档
    if (status.includes('A') && file.startsWith('src/hooks/') && file.endsWith('.ts')) {
      const hookName = file.split('/').pop().replace('.ts', '')
      const docPath = `src/hooks/${hookName}.md`
      
      if (!existsSync(docPath)) {
        missingDocs.push({
          type: 'hook',
          name: hookName,
          path: docPath
        })
      }
    }
  })
  
  if (missingDocs.length > 0) {
    console.log('\n⚠️  发现缺少文档的组件或 Hook:')
    missingDocs.forEach(item => {
      console.log(`   ${item.type}: ${item.name} (缺少 ${item.path})`)
    })
    
    console.log('\n💡 建议操作:')
    missingDocs.forEach(item => {
      console.log(`   npm run docs:template ${item.type} ${item.name}`)
    })
    
    console.log('\n📖 或者运行以下命令生成所有缺失的文档模板:')
    missingDocs.forEach(item => {
      console.log(`npm run docs:template ${item.type} ${item.name}`)
    })
    
    return false
  }
  
  // 提醒运行文档构建
  console.log('\n💡 建议在提交前运行以下命令确保文档是最新的:')
  console.log('   npm run copy-assets')
  console.log('   npm run docs:build')
  
  return true
}

// 主函数
function main() {
  const isSync = checkDocSync()
  
  if (!isSync) {
    console.log('\n❌ 文档同步检查失败')
    console.log('请添加缺失的文档后再提交')
    process.exit(1)
  }
  
  console.log('\n✅ 文档同步检查通过')
}

// 如果作为脚本直接运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
