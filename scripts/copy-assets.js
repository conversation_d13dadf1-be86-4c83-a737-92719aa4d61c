#!/usr/bin/env node

import {
  copyFileSync,
  mkdirSync,
  existsSync,
  readdirSync,
  statSync,
  writeFileSync,
} from 'fs'
import { join, dirname, extname, basename } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 源目录和目标目录
const sourceDir = join(__dirname, '../src/assets')
const targetDir = join(__dirname, '../docs/public/assets')
const docsDir = join(__dirname, '../docs/components')

// 图标分类配置
const iconCategories = {
  basic: {
    name: '基础操作图标',
    keywords: [
      'add',
      'edit',
      'delete',
      'search',
      'close',
      'check',
      'copy',
      'minus',
    ],
    icons: [],
  },
  file: {
    name: '文件类型图标',
    keywords: ['ppt', 'word', 'file', 'folder', 'paper', 'text'],
    icons: [],
  },
  ai: {
    name: 'AI功能图标',
    keywords: ['ai'],
    icons: [],
  },
  education: {
    name: '教学相关图标',
    keywords: [
      'class',
      'exam',
      'question',
      'problem',
      'exercise',
      'wrong',
      'task',
    ],
    icons: [],
  },
  video: {
    name: '视频播放图标',
    keywords: ['play', 'video', 'ppt'],
    icons: [],
  },
  arrow: {
    name: '箭头导航图标',
    keywords: ['arrow', 'back', 'dropdown', 'sort', 'conner'],
    icons: [],
  },
  status: {
    name: '状态提示图标',
    keywords: [
      'success',
      'correct',
      'wrong',
      'warning',
      'info',
      'tip',
      'empty',
    ],
    icons: [],
  },
  star: {
    name: '收藏评分图标',
    keywords: ['star', 'like', 'thumb', 'collect', 'top'],
    icons: [],
  },
  function: {
    name: '功能操作图标',
    keywords: [
      'download',
      'upload',
      'share',
      'redo',
      'drag',
      'look',
      'eye',
      'insert',
    ],
    icons: [],
  },
  ui: {
    name: '界面元素图标',
    keywords: ['list', 'dot', 'three', 'bread', 'expand'],
    icons: [],
  },
  special: {
    name: '特殊功能图标',
    keywords: ['roll', 'customer', 'vip', 'used', 'fx', 'clip'],
    icons: [],
  },
  other: {
    name: '其他图标',
    keywords: [],
    icons: [],
  },
}

/**
 * 分类图标
 * @param {string} filename 文件名
 * @param {string} relativePath 相对路径
 */
function categorizeIcon(filename, relativePath) {
  const lowerName = filename.toLowerCase()

  // 遍历分类，找到匹配的关键词
  for (const [key, category] of Object.entries(iconCategories)) {
    if (key === 'other') continue // 跳过其他分类，最后处理

    const matched = category.keywords.some((keyword) =>
      lowerName.includes(keyword.toLowerCase()),
    )

    if (matched) {
      category.icons.push({
        filename,
        path: relativePath,
        name: basename(filename, extname(filename)),
      })
      return
    }
  }

  // 如果没有匹配到任何分类，放入其他分类
  iconCategories.other.icons.push({
    filename,
    path: relativePath,
    name: basename(filename, extname(filename)),
  })
}

/**
 * 递归复制目录并收集图标信息
 * @param {string} src 源目录
 * @param {string} dest 目标目录
 * @param {string} relativePath 相对路径
 */
function copyDirectory(src, dest, relativePath = '') {
  // 确保目标目录存在
  if (!existsSync(dest)) {
    mkdirSync(dest, { recursive: true })
  }

  // 读取源目录中的所有文件和子目录
  const items = readdirSync(src)

  items.forEach((item) => {
    // 忽略 web-office-sdk 相关目录
    if (item.includes('web-office-sdk')) {
      console.log(`⏭️  跳过目录: ${item}`)
      return
    }

    const srcPath = join(src, item)
    const destPath = join(dest, item)
    const stat = statSync(srcPath)
    const currentRelativePath = relativePath ? `${relativePath}/${item}` : item

    if (stat.isDirectory()) {
      // 如果是目录，递归复制
      copyDirectory(srcPath, destPath, currentRelativePath)
    } else if (stat.isFile()) {
      // 如果是文件，直接复制
      // 只复制图片和SVG文件
      const ext = item.toLowerCase().split('.').pop()
      if (['svg', 'png', 'jpg', 'jpeg', 'gif', 'webp', 'ico'].includes(ext)) {
        try {
          copyFileSync(srcPath, destPath)
          console.log(`✅ 复制文件: ${item}`)

          // 收集图标信息用于生成文档
          const iconPath = `/assets/${currentRelativePath}`
          if (ext === 'svg') {
            categorizeIcon(item, iconPath)
          }
        } catch (error) {
          console.error(`❌ 复制文件失败: ${item}`, error.message)
        }
      }
    }
  })
}

/**
 * 生成图标文档页面
 */
function generateIconsDoc() {
  console.log('📝 生成图标文档页面...')

  let docContent = `# 图标资源
  
## 使用方式
### 通过unplugin-icons使用
项目已配置了\`unplugin-icons\`插件，可以直接使用自定义图标：

\`\`\`typescript
// 在组件中自动导入
import IconAdd from '~icons/yc/add'
import IconEdit from '~icons/yc/edit'
\`\`\`

## SVG图标库
`

  // 生成各个分类的图标
  Object.entries(iconCategories).forEach(([key, category]) => {
    if (category.icons.length === 0) return

    docContent += `### ${category.name}\n\n<div class="icon-grid">\n`

    category.icons.forEach((icon) => {
      // 生成大驼峰命名的组件名
      const iconName = icon.name
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join('')

      // 检查是否已经以Icon结尾，避免重复添加
      let componentName = iconName.endsWith('Icon')
        ? iconName
        : `${iconName}Icon`

      // 处理命名冲突：为带Icon后缀的原始文件名添加特殊标识
      const conflictMappings = {
        arrowIcon: 'ArrowIconComponent',
        editIcon: 'EditIconComponent',
        correctIcon: 'CorrectIconComponent',
        folderIcon: 'FolderIconComponent',
      }

      if (conflictMappings[icon.name]) {
        componentName = conflictMappings[icon.name]
      }

      const componentCode = `import ${componentName} from '~icons/yc/${icon.name}'`

      docContent += `  <div class="icon-item" data-icon-name="${icon.name}" data-filename="${icon.filename}">
    <div class="icon-preview">
      <img src="${icon.path}" alt="${icon.name}" />
    </div>
    <div class="icon-info">
      <span class="icon-filename">${icon.filename}</span>
      <button class="copy-btn" data-copy-content="${componentCode}" title="复制组件引入代码">
        <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
          <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path>
          <path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
        </svg>
        复制
      </button>
    </div>
  </div>\n`
    })

    docContent += `</div>\n\n`
  })

  // 添加使用方式说明
  docContent += `
### 图标统计

- **总计**: ${Object.values(iconCategories).reduce(
    (sum, cat) => sum + cat.icons.length,
    0,
  )} 个SVG图标
${Object.entries(iconCategories)
  .map(([key, cat]) =>
    cat.icons.length > 0 ? `- **${cat.name}**: ${cat.icons.length} 个` : '',
  )
  .filter(Boolean)
  .join('\n')}

> 📅 最后更新时间: ${new Date().toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
  })}
>
> 🔄 此页面由构建脚本自动生成，每次运行 \`pnpm run docs:dev\` 或 \`pnpm run docs:build\` 时会自动更新

<style>
.logo-section {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
}

.logo-img {
  width: 160px;
  height: 40px;
  margin-bottom: 12px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
  margin: 20px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.icon-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  height: 40px;
}

.icon-preview img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.icon-item:hover .icon-preview img {
  transform: scale(1.1);
}

.icon-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.icon-filename {
  font-size: 12px;
  text-align: center;
  color: #606266;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  margin-bottom: 12px;
  line-height: 1.4;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  font-weight: 500;
  min-width: 60px;
  justify-content: center;
}

.copy-btn:hover {
  background: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.copy-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(64, 158, 255, 0.3);
}

.copy-btn.copied {
  background: #67c23a;
  transform: scale(1.05);
}

.copy-icon {
  fill: currentColor;
  flex-shrink: 0;
}

.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .icon-item {
    padding: 8px;
  }

  .icon-item img {
    width: 24px;
    height: 24px;
  }

  .icon-item span {
    font-size: 10px;
  }
}
</style>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  // 复制到剪贴板功能
  function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
      return navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((resolve, reject) => {
        try {
          const successful = document.execCommand('copy');
          textArea.remove();
          successful ? resolve() : reject(new Error('Copy command failed'));
        } catch (err) {
          textArea.remove();
          reject(err);
        }
      });
    }
  }

  // 显示复制成功提示
  function showCopySuccess(text) {
    const existingToast = document.querySelector('.copy-success');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = 'copy-success';
    toast.textContent = \`已复制: \${text.length > 50 ? text.substring(0, 50) + '...' : text}\`;
    document.body.appendChild(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.remove();
      }
    }, 2000);
  }

  // 为所有复制按钮绑定点击事件
  function handleCopyClick(e) {
    const button = e.target.closest('.copy-btn');
    if (!button) return;

    e.preventDefault();
    e.stopPropagation();

    const copyContent = button.getAttribute('data-copy-content');

    if (copyContent) {
      copyToClipboard(copyContent)
        .then(() => {
          // 显示成功提示
          showCopySuccess(copyContent);

          // 临时改变按钮样式和文本
          const originalHTML = button.innerHTML;
          button.innerHTML = \`
            <svg class="copy-icon" viewBox="0 0 16 16" width="12" height="12">
              <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.75.75 0 0 1 1.06-1.06L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"></path>
            </svg>
            已复制
          \`;
          button.classList.add('copied');

          setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('copied');
          }, 1500);
        })
        .catch(err => {
          console.error('复制失败:', err);
          // 降级到手动复制提示
          const fallbackText = \`请手动复制以下代码:\\n\\n\${copyContent}\`;
          if (window.prompt) {
            window.prompt(fallbackText, copyContent);
          } else {
            alert(fallbackText);
          }
        });
    }
  }

  // 绑定事件监听器
  document.addEventListener('click', handleCopyClick);

  // 清理函数
  return () => {
    document.removeEventListener('click', handleCopyClick);
  };
})
</script>
`

  // 写入文档文件
  const iconsDocPath = join(docsDir, 'icons.md')
  try {
    writeFileSync(iconsDocPath, docContent, 'utf8')
    console.log('✅ 图标文档页面生成完成!')
    console.log(`📄 文档路径: ${iconsDocPath}`)

    // 输出统计信息
    const totalIcons = Object.values(iconCategories).reduce(
      (sum, cat) => sum + cat.icons.length,
      0,
    )
    console.log(`📊 图标统计: 共 ${totalIcons} 个SVG图标`)
    Object.entries(iconCategories).forEach(([key, cat]) => {
      if (cat.icons.length > 0) {
        console.log(`   - ${cat.name}: ${cat.icons.length} 个`)
      }
    })
  } catch (error) {
    console.error('❌ 生成图标文档失败:', error.message)
  }
}

/**
 * 扫描组件文档
 */
function scanComponentDocs() {
  console.log('📖 扫描组件文档...')
  const componentsDir = join(__dirname, '../src/components')
  const componentDocs = []

  function scanDirectory(dir, relativePath = '') {
    if (!existsSync(dir)) return

    const items = readdirSync(dir)

    for (const item of items) {
      const fullPath = join(dir, item)
      const stat = statSync(fullPath)

      if (stat.isDirectory()) {
        // 递归扫描子目录
        scanDirectory(fullPath, relativePath ? `${relativePath}/${item}` : item)
      } else if (item === 'README.md') {
        // 找到组件文档
        const componentName = relativePath || basename(dirname(fullPath))
        componentDocs.push({
          name: componentName,
          path: fullPath,
          relativePath: relativePath ? `${relativePath}/README.md` : 'README.md'
        })
        console.log(`📄 发现组件文档: ${componentName}`)
      }
    }
  }

  scanDirectory(componentsDir)
  return componentDocs
}

/**
 * 扫描 Hooks 文档
 */
function scanHooksDocs() {
  console.log('🪝 扫描 Hooks 文档...')
  const hooksDir = join(__dirname, '../src/hooks')
  const hookDocs = []

  if (!existsSync(hooksDir)) return hookDocs

  const items = readdirSync(hooksDir)

  for (const item of items) {
    const fullPath = join(hooksDir, item)
    const stat = statSync(fullPath)

    if (stat.isDirectory()) {
      // 检查目录下的 README.md
      const readmePath = join(fullPath, 'README.md')
      if (existsSync(readmePath)) {
        hookDocs.push({
          name: item,
          path: readmePath,
          relativePath: `${item}/README.md`
        })
        console.log(`📄 发现 Hook 文档: ${item}`)
      }
    } else if (item.endsWith('.md')) {
      // 单独的 .md 文件
      const hookName = basename(item, '.md')
      hookDocs.push({
        name: hookName,
        path: fullPath,
        relativePath: item
      })
      console.log(`📄 发现 Hook 文档: ${hookName}`)
    }
  }

  return hookDocs
}

/**
 * 生成组件文档索引
 */
function generateComponentsIndex(componentDocs) {
  console.log('📝 生成组件文档索引...')

  let indexContent = `# 组件文档

## 组件列表

`

  componentDocs.forEach(doc => {
    indexContent += `- [${doc.name}](/components/${doc.name})\n`
  })

  indexContent += `

> 📅 最后更新时间: ${new Date().toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
  })}
>
> 🔄 此页面由构建脚本自动生成
`

  const indexPath = join(docsDir, 'components.md')
  writeFileSync(indexPath, indexContent, 'utf8')
  console.log('✅ 组件文档索引生成完成!')
}

/**
 * 生成 Hooks 文档索引
 */
function generateHooksIndex(hookDocs) {
  console.log('📝 生成 Hooks 文档索引...')

  let indexContent = `# Hooks 文档

## Hooks 列表

`

  hookDocs.forEach(doc => {
    indexContent += `- [${doc.name}](/hooks/${doc.name})\n`
  })

  indexContent += `

> 📅 最后更新时间: ${new Date().toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
  })}
>
> 🔄 此页面由构建脚本自动生成
`

  const indexPath = join(docsDir, 'hooks.md')
  writeFileSync(indexPath, indexContent, 'utf8')
  console.log('✅ Hooks 文档索引生成完成!')
}

/**
 * 同步组件和 Hooks 文档到 docs 目录
 */
function syncDocs(componentDocs, hookDocs) {
  console.log('🔄 同步文档到 docs 目录...')

  // 创建目标目录
  const componentsDocsDir = join(docsDir, 'components')
  const hooksDocsDir = join(docsDir, 'hooks')

  if (!existsSync(componentsDocsDir)) {
    mkdirSync(componentsDocsDir, { recursive: true })
  }

  if (!existsSync(hooksDocsDir)) {
    mkdirSync(hooksDocsDir, { recursive: true })
  }

  // 同步组件文档
  componentDocs.forEach(doc => {
    const targetPath = join(componentsDocsDir, `${doc.name}.md`)
    try {
      copyFileSync(doc.path, targetPath)
      console.log(`✅ 同步组件文档: ${doc.name}`)
    } catch (error) {
      console.error(`❌ 同步组件文档失败: ${doc.name}`, error.message)
    }
  })

  // 同步 Hooks 文档
  hookDocs.forEach(doc => {
    const targetPath = join(hooksDocsDir, `${doc.name}.md`)
    try {
      copyFileSync(doc.path, targetPath)
      console.log(`✅ 同步 Hook 文档: ${doc.name}`)
    } catch (error) {
      console.error(`❌ 同步 Hook 文档失败: ${doc.name}`, error.message)
    }
  })
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始复制资源文件...')
  console.log(`📁 源目录: ${sourceDir}`)
  console.log(`📁 目标目录: ${targetDir}`)

  try {
    // 清空图标分类数据
    Object.values(iconCategories).forEach((category) => {
      category.icons = []
    })

    // 复制文件并收集图标信息
    copyDirectory(sourceDir, targetDir)
    console.log('✅ 资源文件复制完成!')

    // 生成图标文档
    generateIconsDoc()

    // 扫描并同步组件和 Hooks 文档
    const componentDocs = scanComponentDocs()
    const hookDocs = scanHooksDocs()

    if (componentDocs.length > 0 || hookDocs.length > 0) {
      syncDocs(componentDocs, hookDocs)
      generateComponentsIndex(componentDocs)
      generateHooksIndex(hookDocs)
    }
  } catch (error) {
    console.error('❌ 复制过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 执行主函数
main()
