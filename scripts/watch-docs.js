#!/usr/bin/env node

import { watch } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 监控的目录
const watchDirs = [
  join(__dirname, '../src/components'),
  join(__dirname, '../src/hooks')
]

// 文档文件模式
const docFilePatterns = [
  /README\.md$/,
  /\.md$/
]

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 检查是否是文档文件
function isDocFile(filename) {
  return docFilePatterns.some(pattern => pattern.test(filename))
}

// 重新生成文档
const regenerateDocs = debounce(() => {
  console.log('📝 检测到文档变更，重新生成文档...')
  try {
    execSync('node scripts/copy-assets.js', { 
      stdio: 'inherit',
      cwd: join(__dirname, '..')
    })
    console.log('✅ 文档重新生成完成!')
  } catch (error) {
    console.error('❌ 文档生成失败:', error.message)
  }
}, 1000)

// 启动监控
function startWatching() {
  console.log('👀 开始监控文档变更...')
  
  watchDirs.forEach(dir => {
    console.log(`📁 监控目录: ${dir}`)
    
    watch(dir, { recursive: true }, (eventType, filename) => {
      if (filename && isDocFile(filename)) {
        console.log(`📄 文档变更: ${filename} (${eventType})`)
        regenerateDocs()
      }
    })
  })
  
  console.log('✅ 文档监控已启动')
  console.log('💡 提示: 修改组件或 Hook 的 README.md 文件时会自动重新生成文档')
}

// 启动监控
startWatching()

// 保持进程运行
process.on('SIGINT', () => {
  console.log('\n👋 停止文档监控')
  process.exit(0)
})
