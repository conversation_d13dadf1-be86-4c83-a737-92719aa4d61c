{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": false, "singleAttributePerLine": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false}