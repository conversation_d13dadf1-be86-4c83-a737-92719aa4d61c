{"compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "moduleDetection": "force", "noEmit": true, "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "strict": true, "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["@types/node", "naive-ui/volar", "unplugin-icons/types/vue"]}, "include": ["auto-import.d.ts", "components.d.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "*.d.ts"], "exclude": ["node_modules"]}