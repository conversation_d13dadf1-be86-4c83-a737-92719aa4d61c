---
description:
globs:
alwaysApply: false
---
# Vue3 项目开发规范和技术栈指南

## 技术栈概览

本项目使用以下主要技术：
- Vue 3 + TypeScript
- Pinia 状态管理
- Vue Router
- Naive UI + Onion UI Web 组件库
- SCSS 样式处理
- UnoCSS 原子化CSS
- DayJS 时间处理

## 项目结构

主要入口文件：
- [main.ts](mdc:src/main.ts) - 应用入口，包含全局配置
- [App.vue](mdc:src/App.vue) - 根组件

目录结构：
- `src/components/` - 公共组件
- `src/pages/` - 页面组件
- `src/stores/` - Pinia 状态管理
- `src/routers/` - 路由配置
- `src/utils/` - 工具函数
- `src/hooks/` - 自定义 Hooks
- `src/styles/` - 全局样式
- `src/types/` - TypeScript 类型定义
- `src/service/` - API 服务
- `src/assets/` - 静态资源

## 开发规范

### 组件开发规范
1. 使用 `<script setup lang="ts">` 语法
2. 组件名使用 PascalCase 命名
3. Props 必须指定类型
4. 优先使用组合式 API

### 样式规范
1. 使用 SCSS 预处理器
2. 遵循 BEM 命名规范
3. 优先使用 UnoCSS 原子化类
4. 主题相关样式使用 Naive UI 的 themeOverrides

### 状态管理
1. 使用 Pinia 进行状态管理
2. Store 按功能模块拆分
3. 异步操作放在 actions 中处理

### TypeScript 使用
1. 严格模式开发
2. 所有组件props必须定义类型
3. API 接口类型定义放在 types 目录

### 路由规范
1. 路由配置集中管理
2. 懒加载页面组件
3. 权限路由使用路由守卫

### UI 组件使用
1. 优先使用 Naive UI 组件
2. 自定义组件需要提供完整的类型定义
3. 弹窗类组件使用 Provider 包裹

## 注意事项
1. 所有的第三方窗口打开需要使用 windowOpen 代理
2. 日期相关操作统一使用 dayjs，locale 设置为 zh-cn
3. 主题定制通过 themeOverrides 统一管理
4. 权限相关逻辑统一通过 authStore 处理
